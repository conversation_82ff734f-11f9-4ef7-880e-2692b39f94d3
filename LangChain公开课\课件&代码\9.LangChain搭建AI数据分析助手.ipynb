{"cells": [{"cell_type": "markdown", "id": "e557c8c1-a146-4ddb-9744-4084d60d2702", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## <font face=\"仿宋\">课程说明："]}, {"cell_type": "markdown", "id": "df40c6dd-839e-44d5-b5c9-3257e011ab7c", "metadata": {}, "source": ["- 体验课内容节选自[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)完整版付费课程"]}, {"cell_type": "markdown", "id": "77265167-dd6d-44bd-99c7-b29873b27248", "metadata": {}, "source": ["&emsp;&emsp;体验课时间有限，若想深度学习大模型技术，欢迎大家报名由我主讲的[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)"]}, {"cell_type": "markdown", "id": "cdba8b05-05ff-48b1-8813-79378163ba85", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171642034.jpg\" alt=\"bef0897853f861af5f4211442be446b\" style=\"zoom:15%;\" />"]}, {"cell_type": "markdown", "id": "ddea8c99-89d3-4ed1-845f-a3a3da63d38b", "metadata": {}, "source": ["**[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)为【100+小时】体系大课，总共20大模块精讲精析，零基础直达大模型企业级应用！**"]}, {"cell_type": "markdown", "id": "4972c98c-23ce-40d7-9db8-d7680a4e9e95", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172010074.png\" alt=\"a55d48e952ed59f8d93e050594843bc\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "23612885-85ea-4b1d-be11-0665cc3d2fba", "metadata": {}, "source": ["部分项目成果演示"]}, {"cell_type": "code", "execution_count": 3, "id": "773d8af6-e97e-42f7-9ade-b3393d605f8f", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "markdown", "id": "cfbeadb4-10f6-4d59-ba37-a6a4c8f984f8", "metadata": {}, "source": ["- **MateGen项目演示**"]}, {"cell_type": "code", "execution_count": 4, "id": "2b42a3f8-7606-4ca9-984f-5d554b69e1af", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "710534e9-4ba9-4d3b-84ed-1ddefa94be0e", "metadata": {}, "source": ["- **智能客服项目演示**"]}, {"cell_type": "code", "execution_count": 5, "id": "5c305ba3-beb9-4d94-852f-94d259d922e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "9fcb734b-b7a9-4f46-8dcb-98e08a405d03", "metadata": {}, "source": ["- **Dify项目演示**"]}, {"cell_type": "code", "execution_count": 6, "id": "2232de45-7f02-4276-b987-f4a316467caf", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "294fb8c7-a757-455b-a664-c431a45ae4e4", "metadata": {}, "source": ["- **LangChain&LangGraph搭建Multi-Agnet**"]}, {"cell_type": "code", "execution_count": 7, "id": "87ed0f1d-9d6e-46e6-bef6-85ccfc5cb5d4", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "909ebf9c-3ae3-495e-804d-2f16bb52f3b5", "metadata": {}, "source": ["此外，若是对大模型底层原理感兴趣，也欢迎报名由我和菜菜老师共同主讲的[《2025大模型原理与实战课程》(夏季班)](https://ix9mq.xetslk.com/s/3VITgV)"]}, {"cell_type": "markdown", "id": "d564b2d7-058f-4829-a2ef-9e42fabda496", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171650709.png\" alt=\"4a11b7807056e9f5b281278c0e37dad\" style=\"zoom:20%;\" />"]}, {"cell_type": "markdown", "id": "1af38b77-8b72-4872-ad0d-4ce5cf64e667", "metadata": {}, "source": ["**两门大模型课程夏季班目前上新特惠+618年中钜惠双惠叠加，合购还有更多优惠哦~<span style=\"color:red;\">详细信息扫码添加助教，回复“大模型”，即可领取课程大纲&查看课程详情👇</span>**"]}, {"cell_type": "markdown", "id": "88547771-a2b1-4f02-acab-8753fe0dc55b", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171644321.jpeg\" alt=\"img\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "5a32042d-d553-4287-9a35-beb36ff6d34a", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101749045.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "acf1ebe3-3a63-417b-a44e-797698b47059", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "1c101505-4043-4333-b250-ddadff6b85e1", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 9.基于LangChain搭建AI数据分析智能体Data Agent"]}, {"cell_type": "markdown", "id": "88bcb6d2-80ae-4b61-beda-1b05db53c89f", "metadata": {}, "source": ["## 实战：智能数据分析可视化系统"]}, {"cell_type": "markdown", "id": "70107092-ac38-4535-9e48-fd25a2976be4", "metadata": {}, "source": ["&emsp;&emsp;接下来，我们进一步丰富智能问答系统的功能，接下来的案例中，我们构建一个基于 `Streamlit + LangChain + DashScope + DeepSeek` 的智能化数据分析助手，融合两个典型的企业级大模型应用场景：\n", "\n", "- PDF 智能问答：支持上传多个 PDF 文档，自动完成内容提取、文本切块、语义向量化，并构建 FAISS 本地检索库，结合大模型进行问答；\n", "- CSV 数据智能分析：通过自然语言指令分析结构化数据，包括统计查询、代码生成与图表绘制；"]}, {"cell_type": "markdown", "id": "836e4736-809d-41fd-a64b-9d3c12c66bea", "metadata": {}, "source": ["&emsp;&emsp;完整代码如下所示："]}, {"cell_type": "code", "execution_count": 7, "id": "6867da80-4e50-41be-ab5d-6130374d8a38", "metadata": {}, "outputs": [], "source": ["# ! pip install langchain_experimental matplotlib tabulate"]}, {"cell_type": "markdown", "id": "92b59350-72b5-4ae0-a922-aaf339337789", "metadata": {}, "source": ["```python\n", "    import streamlit as st\n", "    import pandas as pd\n", "    import os\n", "    from PyPDF2 import PdfReader\n", "    from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "    from langchain_core.prompts import ChatPromptTemplate\n", "    from langchain_community.vectorstores import FAISS\n", "    from langchain.tools.retriever import create_retriever_tool\n", "    from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "    from langchain_community.embeddings import DashScopeEmbeddings\n", "    from langchain.chat_models import init_chat_model\n", "    from langchain_experimental.tools import PythonAstREPLTool\n", "    import matplotlib\n", "    matplotlib.use('Agg')\n", "    import os\n", "    from dotenv import load_dotenv \n", "    load_dotenv(override=True)\n", "\n", "\n", "    DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "    dashscope_api_key = os.getenv(\"dashscope_api_key\")\n", "\n", "    # 设置环境变量\n", "    os.environ[\"KMP_DUPLICATE_LIB_OK\"] = \"TRUE\"\n", "\n", "    # 页面配置\n", "    st.set_page_config(\n", "        page_title=\"By九天Hector\",\n", "        page_icon=\"🤖\",\n", "        layout=\"wide\",\n", "        initial_sidebar_state=\"expanded\"\n", "    )\n", "\n", "    # 自定义CSS样式\n", "    st.markdown(\"\"\"\n", "    <style>\n", "        /* 主题色彩 */\n", "        :root {\n", "            --primary-color: #1f77b4;\n", "            --secondary-color: #ff7f0e;\n", "            --success-color: #2ca02c;\n", "            --warning-color: #ff9800;\n", "            --error-color: #d62728;\n", "            --background-color: #f8f9fa;\n", "        }\n", "        \n", "        /* 隐藏默认的Streamlit样式 */\n", "        #MainMenu {visibility: hidden;}\n", "        footer {visibility: hidden;}\n", "        header {visibility: hidden;}\n", "        \n", "        /* 标题样式 */\n", "        .main-header {\n", "            background: linear-gradient(90deg, #1f77b4, #ff7f0e);\n", "            -webkit-background-clip: text;\n", "            -webkit-text-fill-color: transparent;\n", "            font-size: 3rem;\n", "            font-weight: bold;\n", "            text-align: center;\n", "            margin-bottom: 2rem;\n", "        }\n", "        \n", "        /* 卡片样式 */\n", "        .info-card {\n", "            background: white;\n", "            padding: 1.5rem;\n", "            border-radius: 10px;\n", "            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n", "            margin: 1rem 0;\n", "            border-left: 4px solid var(--primary-color);\n", "        }\n", "        \n", "        .success-card {\n", "            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\n", "            border-left: 4px solid var(--success-color);\n", "        }\n", "        \n", "        .warning-card {\n", "            background: linear-gradient(135deg, #fff8e1, #fffbf0);\n", "            border-left: 4px solid var(--warning-color);\n", "        }\n", "        \n", "        /* 按钮样式 */\n", "        .stButton > button {\n", "            background: linear-gradient(45deg, #1f77b4, #2196F3);\n", "            color: white;\n", "            border: none;\n", "            border-radius: 8px;\n", "            padding: 0.5rem 1rem;\n", "            font-weight: 600;\n", "            transition: all 0.3s ease;\n", "            box-shadow: 0 2px 8px rgba(31, 119, 180, 0.3);\n", "        }\n", "        \n", "        .stButton > button:hover {\n", "            transform: translateY(-2px);\n", "            box-shadow: 0 4px 12px rgba(31, 119, 180, 0.4);\n", "        }\n", "        \n", "        /* Tab样式 */\n", "        .stTabs [data-baseweb=\"tab-list\"] {\n", "            gap: 8px;\n", "            background-color: #f8f9fa;\n", "            border-radius: 10px;\n", "            padding: 0.5rem;\n", "        }\n", "        \n", "        .stTabs [data-baseweb=\"tab\"] {\n", "            height: 60px;\n", "            background-color: white;\n", "            border-radius: 8px;\n", "            padding: 0 24px;\n", "            font-weight: 600;\n", "            border: 2px solid transparent;\n", "            transition: all 0.3s ease;\n", "        }\n", "        \n", "        .stTabs [aria-selected=\"true\"] {\n", "            background: linear-gradient(45deg, #1f77b4, #2196F3);\n", "            color: white !important;\n", "            border: 2px solid #1f77b4;\n", "        }\n", "        \n", "        /* 侧边栏样式 */\n", "        .css-1d391kg {\n", "            background: linear-gradient(180deg, #f8f9fa, #ffffff);\n", "        }\n", "        \n", "        /* 文件上传区域 */\n", "        .uploadedFile {\n", "            background: #f8f9fa;\n", "            border: 2px dashed #1f77b4;\n", "            border-radius: 10px;\n", "            padding: 1rem;\n", "            text-align: center;\n", "            margin: 1rem 0;\n", "        }\n", "        \n", "        /* 状态指示器 */\n", "        .status-indicator {\n", "            display: inline-flex;\n", "            align-items: center;\n", "            gap: 0.5rem;\n", "            padding: 0.5rem 1rem;\n", "            border-radius: 20px;\n", "            font-weight: 600;\n", "            font-size: 0.9rem;\n", "        }\n", "        \n", "        .status-ready {\n", "            background: #e8f5e8;\n", "            color: #2ca02c;\n", "            border: 1px solid #2ca02c;\n", "        }\n", "        \n", "        .status-waiting {\n", "            background: #fff8e1;\n", "            color: #ff9800;\n", "            border: 1px solid #ff9800;\n", "        }\n", "    </style>\n", "    \"\"\", unsafe_allow_html=True)\n", "\n", "    # 初始化embeddings\n", "    @st.cache_resource\n", "    def init_embeddings():\n", "        return DashScopeEmbeddings(\n", "            model=\"text-embedding-v1\", \n", "            dashscope_api_key=dashscope_api_key\n", "        )\n", "\n", "    # 初始化LLM\n", "    @st.cache_resource\n", "    def init_llm():\n", "        return init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "\n", "    # 初始化会话状态\n", "    def init_session_state():\n", "        if 'pdf_messages' not in st.session_state:\n", "            st.session_state.pdf_messages = []\n", "        if 'csv_messages' not in st.session_state:\n", "            st.session_state.csv_messages = []\n", "        if 'df' not in st.session_state:\n", "            st.session_state.df = None\n", "\n", "    # PDF处理函数\n", "    def pdf_read(pdf_doc):\n", "        text = \"\"\n", "        for pdf in pdf_doc:\n", "            pdf_reader = PdfReader(pdf)\n", "            for page in pdf_reader.pages:\n", "                text += page.extract_text()\n", "        return text\n", "\n", "    def get_chunks(text):\n", "        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "        chunks = text_splitter.split_text(text)\n", "        return chunks\n", "\n", "    def vector_store(text_chunks):\n", "        embeddings = init_embeddings()\n", "        vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)\n", "        vector_store.save_local(\"faiss_db\")\n", "\n", "    def check_database_exists():\n", "        return os.path.exists(\"faiss_db\") and os.path.exists(\"faiss_db/index.faiss\")\n", "\n", "    def get_pdf_response(user_question):\n", "        if not check_database_exists():\n", "            return \"❌ 请先上传PDF文件并点击'Submit & Process'按钮来处理文档！\"\n", "        \n", "        try:\n", "            embeddings = init_embeddings()\n", "            llm = init_llm()\n", "            \n", "            new_db = FAISS.load_local(\"faiss_db\", embeddings, allow_dangerous_deserialization=True)\n", "            retriever = new_db.as_retriever()\n", "            \n", "            prompt = ChatPromptTemplate.from_messages([\n", "                (\"system\", \"\"\"你是AI助手，请根据提供的上下文回答问题，确保提供所有细节，如果答案不在上下文中，请说\"答案不在上下文中\"，不要提供错误的答案\"\"\"),\n", "                (\"placeholder\", \"{chat_history}\"),\n", "                (\"human\", \"{input}\"),\n", "                (\"placeholder\", \"{agent_scratchpad}\"),\n", "            ])\n", "            \n", "            retrieval_chain = create_retriever_tool(retriever, \"pdf_extractor\", \"This tool is to give answer to queries from the pdf\")\n", "            agent = create_tool_calling_agent(llm, [retrieval_chain], prompt)\n", "            agent_executor = AgentExecutor(agent=agent, tools=[retrieval_chain], verbose=True)\n", "            \n", "            response = agent_executor.invoke({\"input\": user_question})\n", "            return response['output']\n", "            \n", "        except Exception as e:\n", "            return f\"❌ 处理问题时出错: {str(e)}\"\n", "\n", "    # CSV处理函数\n", "    def get_csv_response(query: str) -> str:\n", "        if st.session_state.df is None:\n", "            return \"请先上传CSV文件\"\n", "        \n", "        llm = init_llm()\n", "        locals_dict = {'df': st.session_state.df}\n", "        tools = [PythonAstREPLTool(locals=locals_dict)]\n", "        \n", "        system = f\"\"\"Given a pandas dataframe `df` answer user's query.\n", "        Here's the output of `df.head().to_markdown()` for your reference, you have access to full dataframe as `df`:\n", "        ```\n", "        {st.session_state.df.head().to_markdown()}\n", "        ```\n", "        Give final answer as soon as you have enough data, otherwise generate code using `df` and call required tool.\n", "        If user asks you to make a graph, save it as `plot.png`, and output GRAPH:<graph title>.\n", "        Example:\n", "        ```\n", "        plt.hist(df['Age'])\n", "        plt.xlabel('Age')\n", "        plt.ylabel('Count')\n", "        plt.title('Age Histogram')\n", "        plt.savefig('plot.png')\n", "        ``` output: GRAPH:Age histogram\n", "        Query:\"\"\"\n", "\n", "        prompt = ChatPromptTemplate.from_messages([\n", "            (\"system\", system),\n", "            (\"placeholder\", \"{chat_history}\"),\n", "            (\"human\", \"{input}\"),\n", "            (\"placeholder\", \"{agent_scratchpad}\"),\n", "        ])\n", "\n", "        agent = create_tool_calling_agent(llm=llm, tools=tools, prompt=prompt)\n", "        agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n", "        \n", "        return agent_executor.invoke({\"input\": query})['output']\n", "\n", "    def main():\n", "        init_session_state()\n", "        \n", "        # 主标题\n", "        st.markdown('<h1 class=\"main-header\">🤖 LangChain B站公开课 By九天Hector</h1>', unsafe_allow_html=True)\n", "        st.markdown('<div style=\"text-align: center; margin-bottom: 2rem; color: #666;\">集PDF问答与数据分析于一体的智能助手</div>', unsafe_allow_html=True)\n", "        \n", "        # 创建两个主要功能的标签页\n", "        tab1, tab2 = st.tabs([\"📄 PDF智能问答\", \"📊 CSV数据分析\"])\n", "        \n", "        # PDF问答模块\n", "        with tab1:\n", "            col1, col2 = st.columns([2, 1])\n", "            \n", "            with col1:\n", "                st.markdown(\"### 💬 与PDF文档对话\")\n", "                \n", "                # 显示数据库状态\n", "                if check_database_exists():\n", "                    st.markdown('<div class=\"info-card success-card\"><span class=\"status-indicator status-ready\">✅ PDF数据库已准备就绪</span></div>', unsafe_allow_html=True)\n", "                else:\n", "                    st.markdown('<div class=\"info-card warning-card\"><span class=\"status-indicator status-waiting\">⚠️ 请先上传并处理PDF文件</span></div>', unsafe_allow_html=True)\n", "                \n", "                # 聊天界面\n", "                for message in st.session_state.pdf_messages:\n", "                    with st.chat_message(message[\"role\"]):\n", "                        st.markdown(message[\"content\"])\n", "                \n", "                # 用户输入\n", "                if pdf_query := st.chat_input(\"💭 向PDF提问...\", disabled=not check_database_exists()):\n", "                    st.session_state.pdf_messages.append({\"role\": \"user\", \"content\": pdf_query})\n", "                    with st.chat_message(\"user\"):\n", "                        st.markdown(pdf_query)\n", "                    \n", "                    with st.chat_message(\"assistant\"):\n", "                        with st.spinner(\"🤔 AI正在分析文档...\"):\n", "                            response = get_pdf_response(pdf_query)\n", "                        st.markdown(response)\n", "                        st.session_state.pdf_messages.append({\"role\": \"assistant\", \"content\": response})\n", "            \n", "            with col2:\n", "                st.markdown(\"### 📁 文档管理\")\n", "                \n", "                # 文件上传\n", "                pdf_docs = st.file_uploader(\n", "                    \"📎 上传PDF文件\",\n", "                    accept_multiple_files=True,\n", "                    type=['pdf'],\n", "                    help=\"支持上传多个PDF文件\"\n", "                )\n", "                \n", "                if pdf_docs:\n", "                    st.success(f\"📄 已选择 {len(pdf_docs)} 个文件\")\n", "                    for i, pdf in enumerate(pdf_docs, 1):\n", "                        st.write(f\"• {pdf.name}\")\n", "                \n", "                # 处理按钮\n", "                if st.button(\"🚀 上传并处理PDF文档\", disabled=not pdf_docs, use_container_width=True):\n", "                    with st.spinner(\"📊 正在处理PDF文件...\"):\n", "                        try:\n", "                            raw_text = pdf_read(pdf_docs)\n", "                            if not raw_text.strip():\n", "                                st.error(\"❌ 无法从PDF中提取文本\")\n", "                                return\n", "                            \n", "                            text_chunks = get_chunks(raw_text)\n", "                            st.info(f\"📝 文本已分割为 {len(text_chunks)} 个片段\")\n", "                            \n", "                            vector_store(text_chunks)\n", "                            st.success(\"✅ PDF处理完成！\")\n", "                            st.balloons()\n", "                            st.rerun()\n", "                            \n", "                        except Exception as e:\n", "                            st.error(f\"❌ 处理PDF时出错: {str(e)}\")\n", "                \n", "                # 清除数据库\n", "                if st.button(\"🗑️ 清除PDF数据库\", use_container_width=True):\n", "                    try:\n", "                        import shutil\n", "                        if os.path.exists(\"faiss_db\"):\n", "                            shutil.rmtree(\"faiss_db\")\n", "                        st.session_state.pdf_messages = []\n", "                        st.success(\"数据库已清除\")\n", "                        st.rerun()\n", "                    except Exception as e:\n", "                        st.error(f\"清除失败: {e}\")\n", "        \n", "        # CSV数据分析模块\n", "        with tab2:\n", "            col1, col2 = st.columns([2, 1])\n", "            \n", "            with col1:\n", "                st.markdown(\"### 📈 数据分析对话\")\n", "                \n", "                # 显示数据状态\n", "                if st.session_state.df is not None:\n", "                    st.markdown('<div class=\"info-card success-card\"><span class=\"status-indicator status-ready\">✅ 数据已加载完成</span></div>', unsafe_allow_html=True)\n", "                else:\n", "                    st.markdown('<div class=\"info-card warning-card\"><span class=\"status-indicator status-waiting\">⚠️ 请先上传CSV文件</span></div>', unsafe_allow_html=True)\n", "                \n", "                # 聊天界面\n", "                for message in st.session_state.csv_messages:\n", "                    with st.chat_message(message[\"role\"]):\n", "                        if message[\"type\"] == \"dataframe\":\n", "                            st.dataframe(message[\"content\"])\n", "                        elif message[\"type\"] == \"image\":\n", "                            st.write(message[\"content\"])\n", "                            if os.path.exists('plot.png'):\n", "                                st.image('plot.png')\n", "                        else:\n", "                            st.markdown(message[\"content\"])\n", "                \n", "                # 用户输入\n", "                if csv_query := st.chat_input(\"📊 分析数据...\", disabled=st.session_state.df is None):\n", "                    st.session_state.csv_messages.append({\"role\": \"user\", \"content\": csv_query, \"type\": \"text\"})\n", "                    with st.chat_message(\"user\"):\n", "                        st.markdown(csv_query)\n", "                    \n", "                    with st.chat_message(\"assistant\"):\n", "                        with st.spinner(\"🔄 正在分析数据...\"):\n", "                            response = get_csv_response(csv_query)\n", "                        \n", "                        if isinstance(response, pd.DataFrame):\n", "                            st.dataframe(response)\n", "                            st.session_state.csv_messages.append({\"role\": \"assistant\", \"content\": response, \"type\": \"dataframe\"})\n", "                        elif \"GRAPH\" in str(response):\n", "                            text = str(response)[str(response).find(\"GRAPH\")+6:]\n", "                            st.write(text)\n", "                            if os.path.exists('plot.png'):\n", "                                st.image('plot.png')\n", "                            st.session_state.csv_messages.append({\"role\": \"assistant\", \"content\": text, \"type\": \"image\"})\n", "                        else:\n", "                            st.markdown(response)\n", "                            st.session_state.csv_messages.append({\"role\": \"assistant\", \"content\": response, \"type\": \"text\"})\n", "            \n", "            with col2:\n", "                st.markdown(\"### 📊 数据管理\")\n", "                \n", "                # CSV文件上传\n", "                csv_file = st.file_uploader(\"📈 上传CSV文件\", type='csv')\n", "                if csv_file:\n", "                    st.session_state.df = pd.read_csv(csv_file)\n", "                    st.success(f\"✅ 数据加载成功!\")\n", "                    \n", "                    # 显示数据预览\n", "                    with st.expander(\"👀 数据预览\", expanded=True):\n", "                        st.dataframe(st.session_state.df.head())\n", "                        st.write(f\"📏 数据维度: {st.session_state.df.shape[0]} 行 × {st.session_state.df.shape[1]} 列\")\n", "                \n", "                # 数据信息\n", "                if st.session_state.df is not None:\n", "                    if st.button(\"📋 显示数据信息\", use_container_width=True):\n", "                        with st.expander(\"📊 数据统计信息\", expanded=True):\n", "                            st.write(\"**基本信息:**\")\n", "                            st.text(f\"行数: {st.session_state.df.shape[0]}\")\n", "                            st.text(f\"列数: {st.session_state.df.shape[1]}\")\n", "                            st.write(\"**列名:**\")\n", "                            st.write(list(st.session_state.df.columns))\n", "                            st.write(\"**数据类型:**\")\n", "                            # 修复：将dtypes转换为字符串格式显示\n", "                            dtype_info = pd.DataFrame({\n", "                                '列名': st.session_state.df.columns,\n", "                                '数据类型': [str(dtype) for dtype in st.session_state.df.dtypes]\n", "                            })\n", "                            st.dataframe(dtype_info, use_container_width=True)\n", "                \n", "                # 清除数据\n", "                if st.button(\"🗑️ 清除CSV数据\", use_container_width=True):\n", "                    st.session_state.df = None\n", "                    st.session_state.csv_messages = []\n", "                    if os.path.exists('plot.png'):\n", "                        os.remove('plot.png')\n", "                    st.success(\"数据已清除\")\n", "                    st.rerun()\n", "        \n", "        # 底部信息\n", "        st.markdown(\"---\")\n", "        col1, col2, col3 = st.columns(3)\n", "        with col1:\n", "            st.markdown(\"**🔧 技术栈:**\")\n", "            st.markdown(\"• <PERSON><PERSON><PERSON><PERSON> • Streamlit • FAISS • DeepSeek\")\n", "        with col2:\n", "            st.markdown(\"**✨ 功能特色:**\")\n", "            st.markdown(\"• PDF智能问答 • 数据可视化分析\")\n", "        with col3:\n", "            st.markdown(\"**💡 使用提示:**\")\n", "            st.markdown(\"• 支持多文件上传 • 实时对话交互\")\n", "\n", "    if __name__ == \"__main__\":\n", "        main() \n", "```"]}, {"cell_type": "markdown", "id": "1f215294-6410-4b14-8bd8-25ac43f4ef19", "metadata": {}, "source": ["基于上述代码，我们能够实现如下功能：\n", "\n", "> 📄 **PDF 智能问答** + 📊 **CSV 数据分析与可视化**\n", "> ✔ 采用 LangChain 工具调用（Agent）\n", "> ✔ 集成 FAISS 检索（RAG）与 Pandas 代码执行\n", "> ✔ 前端使用 Streamlit 高度美化，体验完整\n", "\n", "---\n", "\n", "以下是各段代码解释：\n", "\n", "---\n", "\n", "#### 🧩 1. 模块导入与全局设置\n", "\n", "```python\n", "import streamlit as st\n", "import pandas as pd\n", "import os\n", "from dotenv import load_dotenv\n", "...\n", "```\n", "\n", "* 引入必要的模块：\n", "\n", "  * `streamlit`: 构建 Web 应用前端。\n", "  * `pandas`: 用于 CSV 数据分析。\n", "  * `PdfReader`: 提取 PDF 文本。\n", "  * `langchain` 相关模块用于智能问答 / 工具调用。\n", "* 加载 `.env` 的 API key；\n", "* 设置 `os.environ[\"KMP_DUPLICATE_LIB_OK\"]` 避免 MKL 多线程冲突。\n", "\n", "---\n", "\n", "#### 🎨 2. 页面配置与自定义 CSS\n", "\n", "```python\n", "st.set_page_config(...)\n", "st.markdown(\"\"\"<style> ... </style>\"\"\", unsafe_allow_html=True)\n", "```\n", "\n", "* 设置网页标题、图标、布局。\n", "* 自定义 CSS 美化：主题色、按钮动画、卡片式提示框、Tabs 标签样式等。\n", "\n", "📌 说明：这些样式让用户体验更加现代化，比如“PDF 已准备 ✅”就是用 `.info-card.success-card` 显示。\n", "\n", "---\n", "\n", "#### 🔧 3. 初始化 Embedding、LLM 与 Session\n", "\n", "```python\n", "@st.cache_resource\n", "def init_embeddings():\n", "    return Dash<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(...)\n", "\n", "@st.cache_resource\n", "def init_llm():\n", "    return init_chat_model(...)\n", "\n", "def init_session_state():\n", "    ...\n", "```\n", "\n", "* `init_embeddings`：使用阿里 DashScope 提供的 `text-embedding-v1`。\n", "* `init_llm`：初始化 DeepSeek 大模型。\n", "* `init_session_state`：使用 `st.session_state` 保存对话历史与 CSV 数据。\n", "\n", "---\n", "\n", "#### 📄 4. PDF 文档处理逻辑\n", "\n", "```python\n", "def pdf_read(pdf_doc): ...\n", "def get_chunks(text): ...\n", "def vector_store(text_chunks): ...\n", "```\n", "\n", "* 提取上传 PDF 内容 → 文本切片 → 向量存储。\n", "* 使用 `FAISS` 本地数据库持久化保存向量。\n", "* `check_database_exists()` 用于确认是否已有 FAISS 数据库。\n", "\n", "---\n", "\n", "#### 🧠 5. PDF 智能问答：RAG + Agent\n", "\n", "```python\n", "def get_pdf_response(user_question):\n", "    ...\n", "    prompt = ChatPromptTemplate(...)\n", "    retriever_tool = create_retriever_tool(...)\n", "    agent = create_tool_calling_agent(...)\n", "    ...\n", "```\n", "\n", "* 加载 FAISS 向量 → 构建 `retriever_tool`；\n", "* 设置 `system prompt`（要求准确、不要乱猜）；\n", "* 创建 LangChain Agent；\n", "* 调用大模型：用户输入问题 → Agent 判断是否用工具 → 检索 + 生成回答。\n", "\n", "---\n", "\n", "#### 📊 6. CSV 数据分析：代码执行工具 + 可视化\n", "\n", "```python\n", "def get_csv_response(query):\n", "    ...\n", "    locals_dict = {\"df\": st.session_state.df}\n", "    tools = [PythonAstREPLTool(locals=locals_dict)]\n", "    ...\n", "```\n", "\n", "* 用 `PythonAstREPLTool` 将 Pandas dataframe 注入 Agent 可调用作用域；\n", "* 支持“代码生成 + 执行 + 输出图表”；\n", "* 若结果中包含 `GRAPH:图名`，就显示 `plot.png` 图像；\n", "* 对应 `chat_message` 中 `text` / `dataframe` / `image` 三类消息渲染。\n", "\n", "---\n", "\n", "#### 🖥 7. Streamlit 主界面（Tabs 切换）\n", "\n", "```python\n", "tab1, tab2 = st.tabs([\"📄 PDF智能问答\", \"📊 CSV数据分析\"])\n", "```\n", "\n", "##### tab1：PDF 问答\n", "\n", "* 左侧：\n", "\n", "  * 聊天历史（markdown）\n", "  * 输入框（chat\\_input）\n", "  * 实时调用 `get_pdf_response`\n", "* 右侧：\n", "\n", "  * 上传 PDF\n", "  * 点击“上传并处理”\n", "  * 分片、向量化、持久化存储\n", "  * 清除数据库\n", "\n", "---\n", "\n", "##### tab2：CSV 分析\n", "\n", "* 左侧：\n", "\n", "  * 聊天历史 + 输入\n", "  * 如果回答为图表或表格会高亮展示\n", "* 右侧：\n", "\n", "  * 上传 CSV，存入 session\n", "  * 显示预览\n", "  * 显示表结构 / 列类型\n", "  * 清除 CSV / 图像缓存\n", "\n", "---\n", "\n", "#### 📌 8. 页面底部说明\n", "\n", "```python\n", "st.markdown(\"---\")\n", "```\n", "\n", "* 技术栈说明（LangChain、Streamlit、FAISS、DeepSeek）；\n", "* 提示说明；\n", "* 使用说明（支持多文件上传、图像分析等）。\n", "\n", "---\n", "\n", "#### ✅ 总结（核心功能架构）\n", "\n", "| 模块     | 技术组件                        | 说明               |\n", "| ------ | --------------------------- | ---------------- |\n", "| PDF 问答 | FAISS + Retriever Tool      | 构成 RAG 检索增强流程    |\n", "| CSV 分析 | PythonAstREPLTool + Pandas  | 实现代码生成 + 可视化     |\n", "| LLM    | DeepSeek Chat               | 统一 Agent 调用      |\n", "| 向量库    | DashScope Embedding + FAISS | 支持中文语义匹配         |\n", "| UI     | Streamlit + 自定义 CSS         | 提供多 Tab 页面与交互式聊天 |\n", "| 状态管理   | `st.session_state`          | 管理历史、数据、图片等上下文   |"]}, {"cell_type": "markdown", "id": "0021f419-692b-403c-8cd9-1de082c5fd3a", "metadata": {}, "source": ["&emsp;&emsp;这里不再重复赘述`PDF`智能问答的流程，重点说明`CSV`数据智能分析的流程。"]}, {"cell_type": "markdown", "id": "e151a5e4-ff4f-41cd-8852-08ba836390a6", "metadata": {}, "source": ["- Step 1. CSV 文件上传与 DataFrame 显示\n", "\n", "&emsp;&emsp;用户上传 .csv 文件后由 pandas.read_csv() 加载为 DataFrame，实时预览数据行列、列名、类型等信息。\n", "\n", "```python\n", "    st.session_state.df = pd.read_csv(csv_file)\n", "    st.dataframe(st.session_state.df.head())\n", "```"]}, {"cell_type": "markdown", "id": "6f988d70-7221-4327-a899-adbc8cbbccdb", "metadata": {}, "source": ["- Step 2. 构建代码执行工具 Agent\n", "\n", "&emsp;&emsp;构建系统提示，注入 DataFrame 的 .head() 输出增强语境理解，使用 PythonAstREPLTool 工具允许模型执行基于 df 的代码分析，通过 create_tool_calling_agent 构建分析 Agent，可执行筛选、分组、聚合等 pandas 操作，图表绘制（保存为 plot.png，关键词识别后渲染）。\n", "\n", "```python\n", "    tools = [PythonAstREPLTool(locals={\"df\": st.session_state.df})]\n", "```"]}, {"cell_type": "markdown", "id": "a3ed6c07-3e66-49de-abaf-6e5798071a70", "metadata": {}, "source": ["- Step 3. 图表识别与自动展示\n", "\n", "&emsp;&emsp;若模型返回内容中包含 \"GRAPH:\"，则自动读取 plot.png 并展示；支持 plt.hist()、plt.bar() 等可视化命令；会话记录中分类保存文本、图像与表格类型内容。"]}, {"cell_type": "markdown", "id": "2f19ecf4-7b56-4554-a96d-bacf29845a97", "metadata": {}, "source": ["&emsp;&emsp;完整的代码已经上传至百度网盘中的`data_analysis.py`文件中，大家可以扫描下方二维码免费领取"]}, {"cell_type": "markdown", "id": "b7ddf50d-adea-40d0-ad15-ae32e82c9266", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172005915.png\" alt=\"image-20250617200526877\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "7bf8421b-afc4-4342-9486-cf0352a2ce19", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "423541b5-028b-40de-b379-ef2615fe7069", "metadata": {}, "source": ["&emsp;&emsp;项目运行效果如下所示："]}, {"cell_type": "code", "execution_count": 2, "id": "ea47012d-9a82-4ef6-809d-ed8c54a619ae", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "code", "execution_count": 5, "id": "34a09a84-0407-4f32-b705-c129fa66bde0", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/LangChain%20%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%8F%AF%E8%A7%86%E5%8C%96.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/LangChain%20%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90%E5%8F%AF%E8%A7%86%E5%8C%96.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "b796a4e8-32b3-475d-ac6e-230806e83a35", "metadata": {}, "source": ["&emsp;&emsp;至此，我们就使用`Lang<PERSON>hain`快速构建了很多比较符合企业应用的智能应用示例，公开课时间有限，我们也仅能浅尝辄止，给大家提供一个快速上手`Lang<PERSON>hain`的思路。而实际上企业应用的需求会更加复杂和多元化，比如对我们现在构建的智能分析助手，需要手动切换是进行`PDF`还是`CSV`的分析，而实际上往往需要根据用户上传的文件类型或者用户提出的问题，由系统自动判断是进行`PDF`还是`CSV`的分析，并自动调用相应的工具。当然这种架构会带来更多的复杂性设计，比如下面的这个案例："]}, {"cell_type": "code", "execution_count": 6, "id": "8652091d-5be3-42bc-a9fc-cf3385456378", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}