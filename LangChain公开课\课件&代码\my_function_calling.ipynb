{"cells": [{"cell_type": "code", "execution_count": 1, "id": "073d19eb", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pandas as pd\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_experimental.tools import PythonAstREPLTool\n", "from langchain_core.output_parsers.openai_tools import JsonOutputKeyToolsParser\n", "from langchain_core.runnables import RunnableLambda\n", "from langchain_core.tools import tool\n", "from langchain_ollama import ChatOllama"]}, {"cell_type": "code", "execution_count": 2, "id": "af5561df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customerID</th>\n", "      <th>gender</th>\n", "      <th>SeniorCitizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>tenure</th>\n", "      <th>PhoneService</th>\n", "      <th>MultipleLines</th>\n", "      <th>InternetService</th>\n", "      <th>OnlineSecurity</th>\n", "      <th>...</th>\n", "      <th>DeviceProtection</th>\n", "      <th>TechSupport</th>\n", "      <th>StreamingTV</th>\n", "      <th>StreamingMovies</th>\n", "      <th>Contract</th>\n", "      <th>PaperlessBilling</th>\n", "      <th>PaymentMethod</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7590-VHVEG</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.85</td>\n", "      <td>29.85</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5575-GNVDE</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>34</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>56.95</td>\n", "      <td>1889.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7795-CFOCW</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>45</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>42.30</td>\n", "      <td>1840.75</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9237-HQITU</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   customerID  gender  SeniorCitizen Partner Dependents  tenure PhoneService  \\\n", "0  7590-VHVEG  Female              0     Yes         No       1           No   \n", "1  5575-GNVDE    Male              0      No         No      34          Yes   \n", "2  3668-QPYBK    Male              0      No         No       2          Yes   \n", "3  7795-CFOCW    Male              0      No         No      45           No   \n", "4  9237-HQITU  Female              0      No         No       2          Yes   \n", "\n", "      MultipleLines InternetService OnlineSecurity  ... DeviceProtection  \\\n", "0  No phone service             DSL             No  ...               No   \n", "1                No             DSL            Yes  ...              Yes   \n", "2                No             DSL            Yes  ...               No   \n", "3  No phone service             DSL            Yes  ...              Yes   \n", "4                No     Fiber optic             No  ...               No   \n", "\n", "  TechSupport StreamingTV StreamingMovies        Contract PaperlessBilling  \\\n", "0          No          No              No  Month-to-month              Yes   \n", "1          No          No              No        One year               No   \n", "2          No          No              No  Month-to-month              Yes   \n", "3         Yes          No              No        One year               No   \n", "4          No          No              No  Month-to-month              Yes   \n", "\n", "               PaymentMethod MonthlyCharges  TotalCharges Churn  \n", "0           Electronic check          29.85         29.85    No  \n", "1               Mailed check          56.95        1889.5    No  \n", "2               Mailed check          53.85        108.15   Yes  \n", "3  Bank transfer (automatic)          42.30       1840.75    No  \n", "4           Electronic check          70.70        151.65   Yes  \n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = pd.read_csv('WA_Fn-UseC_-Telco-Customer-Churn.csv')\n", "\n", "pd.set_option('max_colwidth',200)\n", "\n", "dataset.head(5)"]}, {"cell_type": "code", "execution_count": 3, "id": "8ce8b7ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 7043 entries, 0 to 7042\n", "Data columns (total 21 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   customerID        7043 non-null   object \n", " 1   gender            7043 non-null   object \n", " 2   SeniorCitizen     7043 non-null   int64  \n", " 3   Partner           7043 non-null   object \n", " 4   Dependents        7043 non-null   object \n", " 5   tenure            7043 non-null   int64  \n", " 6   PhoneService      7043 non-null   object \n", " 7   MultipleLines     7043 non-null   object \n", " 8   InternetService   7043 non-null   object \n", " 9   OnlineSecurity    7043 non-null   object \n", " 10  OnlineBackup      7043 non-null   object \n", " 11  DeviceProtection  7043 non-null   object \n", " 12  TechSupport       7043 non-null   object \n", " 13  StreamingTV       7043 non-null   object \n", " 14  StreamingMovies   7043 non-null   object \n", " 15  Contract          7043 non-null   object \n", " 16  PaperlessBilling  7043 non-null   object \n", " 17  PaymentMethod     7043 non-null   object \n", " 18  MonthlyCharges    7043 non-null   float64\n", " 19  TotalCharges      7043 non-null   object \n", " 20  Churn             7043 non-null   object \n", "dtypes: float64(1), int64(2), object(18)\n", "memory usage: 1.1+ MB\n"]}], "source": ["dataset.info()"]}, {"cell_type": "code", "execution_count": 4, "id": "46e918a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.1621468124378816)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('WA_Fn-UseC_-Telco-Customer-Churn.csv')\n", "tool = PythonAstREPLTool(locals={\"df\": df})\n", "tool.invoke(\"df['SeniorCitizen'].mean()\")"]}, {"cell_type": "code", "execution_count": 5, "id": "61a0edbc", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(64.76169246059918)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df['MonthlyCharges'].mean()"]}, {"cell_type": "code", "execution_count": 15, "id": "b35cab76", "metadata": {}, "outputs": [], "source": ["model  = ChatOllama(model=\"llama3.1:8b\", base_url=\"http://localhost:11434\")"]}, {"cell_type": "code", "execution_count": 16, "id": "a6750d4b", "metadata": {}, "outputs": [], "source": ["llm_with_tools = model.bind_tools([tool])"]}, {"cell_type": "code", "execution_count": 17, "id": "09fe2587", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='', additional_kwargs={}, response_metadata={'model': 'llama3.1:8b', 'created_at': '2025-08-15T07:47:21.350928Z', 'done': True, 'done_reason': 'stop', 'total_duration': 857959400, 'load_duration': 39955800, 'prompt_eval_count': 219, 'prompt_eval_duration': 116624800, 'eval_count': 32, 'eval_duration': 699889400, 'model_name': 'llama3.1:8b'}, id='run--805e4164-721e-4f69-9a7b-35a07090e22b-0', tool_calls=[{'name': 'python_repl_ast', 'args': {'query': \"import pandas as pd; print(df['MonthlyCharges'].mean())\"}, 'id': '45bb7ead-c556-418d-92bb-7d2f12cb9494', 'type': 'tool_call'}], usage_metadata={'input_tokens': 219, 'output_tokens': 32, 'total_tokens': 251})"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["response = llm_with_tools.invoke(\n", "    \"我有一张表，名为'df'，请帮我计算MonthlyCharges字段的均值。\"\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": 18, "id": "6e88c621", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': \"import pandas as pd; print(pd.DataFrame({'MonthlyCharges': [1, 2, 3]}). MonthlyCharges.mean())\"}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["parser = JsonOutputKeyToolsParser(key_name=tool.name, first_tool_only=True)\n", "llm_chain = llm_with_tools | parser\n", "llm_chain.invoke(\"我有一张表，名为'df'，请帮我计算MonthlyCharges字段的均值。\")"]}, {"cell_type": "code", "execution_count": 20, "id": "1cc79526", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': \"df['MonthlyCharges'].mean()\"}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["system = f\"\"\"\n", "你可以访问一个名为 `df` 的 pandas 数据框，你可以使用df.head().to_markdown() 查看数据集的基本信息， \\\n", "请根据用户提出的问题，编写 Python 代码来回答。只返回代码，不返回其他内容。只允许使用 pandas 和内置库。\n", "\"\"\"\n", "\n", "prompt = ChatPromptTemplate([\n", "    (\"system\", system),\n", "    (\"user\", \"{question}\")\n", "])\n", "\n", "code_chain = prompt | llm_with_tools | parser\n", "\n", "code_chain.invoke({\"question\": \"请帮我计算MonthlyCharges字段的均值。\"})"]}, {"cell_type": "code", "execution_count": 21, "id": "16802cf9", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(64.76169246059918)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["chain = prompt | llm_with_tools | parser | tool\n", "chain.invoke({\"question\": \"请帮我计算MonthlyCharges字段的均值。\"})"]}, {"cell_type": "code", "execution_count": 23, "id": "f8bfef6c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"gender_num\":{\"gender_num\":1.0,\"SeniorCitizen\":-0.0018737116,\"Churn_num\":-0.0086120951},\"SeniorCitizen\":{\"gender_num\":-0.0018737116,\"SeniorCitizen\":1.0,\"Churn_num\":0.1508893282},\"Churn_num\":{\"gender_num\":-0.0086120951,\"SeniorCitizen\":0.1508893282,\"Churn_num\":1.0}}'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对分类变量进行编码\n", "df['gender_num'] = df['gender'].map({'Male': 1, 'Female': 0})\n", "df['Churn_num'] = df['Churn'].map({'Yes': 1, 'No': 0})\n", "\n", "# 重新定义 tool，包含编码后的字段\n", "tool = PythonAstREPLTool(locals={\"df\": df})\n", "\n", "# 调用 chain\n", "chain.invoke({\"question\": \"请帮我分析 gender_num、SeniorCitizen 和 Churn_num 三个字段之间的相关关系。\"})"]}, {"cell_type": "code", "execution_count": 24, "id": "c95d033f", "metadata": {}, "outputs": [], "source": ["def code_print(res):\n", "    print(\"即将运行Python代码:\", res['query'])\n", "    return res"]}, {"cell_type": "code", "execution_count": 25, "id": "0f8f9ed2", "metadata": {}, "outputs": [], "source": ["res = code_chain.invoke({\"question\": \"请帮我计算MonthlyCharges字段的均值。\"})"]}, {"cell_type": "code", "execution_count": 26, "id": "43bc4066", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'query': \"df['MonthlyCharges'].mean()\"}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["res"]}, {"cell_type": "code", "execution_count": 27, "id": "b04b830b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["即将运行Python代码: df['MonthlyCharges'].mean()\n"]}], "source": ["print(\"即将运行Python代码:\", res['query'])"]}, {"cell_type": "code", "execution_count": 28, "id": "4e779733", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "def code_print(res):\n", "    print(\"即将运行Python代码:\", res['query'])\n", "    return res\n", "\n", "print_node = RunnableLambda(code_print)"]}, {"cell_type": "code", "execution_count": 29, "id": "481dcb0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["即将运行Python代码: df['MonthlyCharges'].mean()\n"]}, {"data": {"text/plain": ["np.float64(64.76169246059918)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["print_code_chain = prompt | llm_with_tools | parser | print_node | tool\n", "print_code_chain.invoke({\"question\": \"请帮我计算MonthlyCharges字段的均值。\"})"]}, {"cell_type": "code", "execution_count": 36, "id": "7aa47405", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(64.76169246059918)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df['MonthlyCharges'].mean()"]}, {"cell_type": "code", "execution_count": 37, "id": "2f39f218", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["即将运行Python代码: df['MonthlyCharges'].idxmax()\n"]}, {"data": {"text/plain": ["4586"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["print_code_chain = prompt | llm_with_tools | parser | print_node | tool\n", "print_code_chain.invoke({\"question\": \"请问MonthlyCharges取值最高的用户ID是？\"})"]}, {"cell_type": "code", "execution_count": 1, "id": "de5a102f", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)\n", "\n", "OPENWEATHER_API_KEY = os.getenv(\"OPENWEATHER_API_KEY\")\n", "\n", "# print(OPENWEATHER_API_KEY)"]}, {"cell_type": "code", "execution_count": 2, "id": "56343193", "metadata": {}, "outputs": [], "source": ["import requests,json"]}, {"cell_type": "code", "execution_count": 4, "id": "b785abee", "metadata": {}, "outputs": [], "source": ["def get_weather(adcode):\n", "    \"\"\"\n", "    查询即时天气函数\n", "    :param adcode: 城市编码（如510100表示成都）\n", "    :return: 返回天气信息的JSON字符串\n", "    \"\"\"\n", "    api = os.getenv(\"GAODEWEATHER_API_Key\")  # 你的高德API key\n", "    # print(f\"使用的高德API Key: {api}\")\n", "    url = f\"https://restapi.amap.com/v3/weather/weatherInfo?city={adcode}&key={api}\"\n", "    response = requests.get(url)\n", "    data = response.json()\n", "    return json.dumps(data, ensure_ascii=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "4d268789", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"status\": \"1\", \"count\": \"1\", \"info\": \"OK\", \"infocode\": \"10000\", \"lives\": [{\"province\": \"四川\", \"city\": \"成都市\", \"adcode\": \"510100\", \"weather\": \"多云\", \"temperature\": \"33\", \"winddirection\": \"东\", \"windpower\": \"≤3\", \"humidity\": \"50\", \"reporttime\": \"2025-08-15 17:02:47\", \"temperature_float\": \"33.0\", \"humidity_float\": \"50.0\"}]}\n"]}], "source": ["data = get_weather(510100)# 成都\n", "print(data)"]}, {"cell_type": "code", "execution_count": 16, "id": "b7024c3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'province': '四川', 'city': '成都市', 'adcode': '510100', 'weather': '多云', 'temperature': '33', 'winddirection': '东', 'windpower': '≤3', 'humidity': '50', 'reporttime': '2025-08-15 17:02:47', 'temperature_float': '33.0', 'humidity_float': '50.0'}]\n", "天气： 多云\n", "温度： 33.0\n"]}], "source": ["data_dict = json.loads(data)\n", "print(data_dict[\"lives\"])\n", "print(\"天气：\", data_dict[\"lives\"][0][\"weather\"])\n", "print(\"温度：\", data_dict[\"lives\"][0][\"temperature_float\"])"]}, {"cell_type": "code", "execution_count": 17, "id": "f8aa2a7f", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import ChatOllama\n", "\n", "model = ChatOllama(model=\"llama3.1:8b\", base_url=\"http://localhost:11434\")"]}, {"cell_type": "code", "execution_count": 18, "id": "c2671367", "metadata": {}, "outputs": [], "source": ["# 定义 天气查询 工具函数\n", "tools = [get_weather]\n", "\n", "# 将工具绑定到模型\n", "llm_with_tools = model.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 19, "id": "c28572fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='' additional_kwargs={} response_metadata={'model': 'llama3.1:8b', 'created_at': '2025-08-15T09:18:46.8557596Z', 'done': True, 'done_reason': 'stop', 'total_duration': 2732239900, 'load_duration': 2141801900, 'prompt_eval_count': 197, 'prompt_eval_duration': 143705300, 'eval_count': 19, 'eval_duration': 446141000, 'model_name': 'llama3.1:8b'} id='run--7a874a19-5131-4afe-9861-42678d2e4d16-0' tool_calls=[{'name': 'get_weather', 'args': {'adcode': '110000'}, 'id': 'e60abbe6-c40d-4348-9387-b20786103b05', 'type': 'tool_call'}] usage_metadata={'input_tokens': 197, 'output_tokens': 19, 'total_tokens': 216}\n"]}], "source": ["response = llm_with_tools.invoke(\"你好， 请问北京的天气怎么样？\")\n", "\n", "print(response)"]}, {"cell_type": "code", "execution_count": 20, "id": "8387ff94", "metadata": {}, "outputs": [{"data": {"text/plain": ["{}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["response.additional_kwargs"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}