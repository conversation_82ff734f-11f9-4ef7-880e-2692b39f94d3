{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <center>Deepseek企业级Agent项目开发实战</center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <center>Part 3. Ollama REST API - api/chat 接口详解 </center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;Ollama 服务启动后会提供一系列原生 ` REST API` 端点。通过这些`Endpoints`可以在代码环境下与`ollama`启动的大模型进行交互、管理模型和获取相关信息。其中两个`endpoint` 是最重要的，分别是：\n", "  - <font color=\"red\">**POST /api/generate**</font>\n", "  - <font color=\"red\">**POST /api/chat**</font>\n", "\n", "&emsp;&emsp;其他端点情况：\n", "  - POST /api/create   \n", "  - POST /api/tags\n", "  - POST /api/show\n", "  - POST /api/copy\n", "  - DELETE /api/delete\n", "  - POST /api/pull\n", "  - POST /api/push\n", "  - POST /api/embed\n", "  - GET /api/ps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. /api/chat 接口参数概览\n", "\n", "&emsp;&emsp;该接口使用提供的模型在聊天中生成下一条消息。与 `/api/generate` 的参数基本一致，但是在请求的参数上会根据聊天场景进行调整。主要调整的是：\n", "- 不再使用 `prompt` 参数，而是使用 `messages` 参数。\n", "- 新增了 `tools` 参数，用于支持工具调用。\n", "\n", "&emsp;&emsp;其可以使用的具体参数如下所示，"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>常规参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名       | 类型      | 描述                                                         |\n", "| ------------ | --------- | ------------------------------------------------------------ |\n", "| **model**    | *(必需)*  | 模型名称。                                                   |\n", "| <font color=\"red\">**messages**</font> | *(必需)*  | 聊天的消息，用于保持聊天记忆。                               |\n", "| <font color=\"red\">**tools**</font>    | *(可选)*  | JSON 中的工具列表，供模型使用（如果支持）。                 |\n", "\n", "</div>\n", "\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>消息对象字段</font></p>\n", "<div class=\"center\">\n", "\n", "| 字段名       | 描述                                                         |\n", "| ------------ | ------------------------------------------------------------ |\n", "| <font color=\"red\">**role**</font>     | 消息的角色，可以是 `system`、`user`、`assistant` 或 `tool`。 |\n", "| <font color=\"red\">**content**</font>  | 消息的内容。                                                 |\n", "| **images**   | *(可选)* 要在消息中包含的图像列表（适用于多模态模型，如 llava）。 |\n", "| **tool_calls** | *(可选)* 模型希望使用的 JSON 中的工具列表。               |\n", "\n", "</div>\n", "\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>高级参数 (可选)</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名       | 描述                                                         |\n", "| ------------ | ------------------------------------------------------------ |\n", "| **format**   | 返回响应的格式。格式可以是 `json` 或 JSON 模式。            |\n", "| <font color=\"red\">**options**</font>  | 文档中列出的其他模型参数，例如 `temperature`。              |\n", "| **stream**   | 如果为 `false`，响应将作为单个响应对象返回，而不是对象流。  |\n", "| <font color=\"red\">**keep_alive**</font> | 控制模型在请求后保持加载的时间（默认：5分钟）。           |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["其中，Options参数说明：\n", "\n", "| 参数名 | 描述 | 值类型 | 示例用法 |\n", "| --------------- | ------------------------------------------------------------ | ------ | ---------------------- |\n", "| mirostat | 启用 Mirostat 采样以控制困惑度。（默认：0，0 = 禁用，1 = Mirostat，2 = Mirostat 2.0） | int | mirostat 0 |\n", "| mirostat_eta| 影响算法对生成文本反馈的响应速度。较低的学习率会导致调整较慢，而较高的学习率会使算法更具响应性。（默认：0.1） | float | mirostat_eta 0.1 |\n", "| mirostat_tau| 控制输出的连贯性和多样性之间的平衡。较低的值会导致更集中和连贯的文本。（默认：5.0） | float | mirostat_tau 5.0 |\n", "| <font color=\"red\">num_ctx</font> | 设置用于生成下一个标记的上下文窗口大小。（默认：2048）, 影响的是模型可以一次记住的最大 token 数量。 | int | num_ctx 4096|\n", "| repeat_last_n| 设置模型回溯的范围以防止重复。（默认：64，0 = 禁用，-1 = num_ctx） | int | repeat_last_n 64 |\n", "| repeat_penalty| 设置惩罚重复的强度。较高的值（例如 1.5）会更强烈地惩罚重复，而较低的值（例如 0.9）会更宽松。（默认：1.1） | float | repeat_penalty 1.1 |\n", "| <font color=\"red\">temperature</font> | 模型的温度。增加温度会使模型的回答更具创造性。（默认：0.8） | float | temperature 0.7 |\n", "| seed | 设置用于生成的随机数种子。将其设置为特定数字将使模型对相同提示生成相同的文本。（默认：0） | int | seed 42 |\n", "| <font color=\"red\">stop</font> | 设置使用的停止序列。当遇到此模式时，LLM 将停止生成文本并返回。可以通过在 modelfile 中指定多个单独的停止参数来设置多个停止模式。 | string | stop \"AI assistant:\" |\n", "| <font color=\"red\">num_predict</font> | 生成文本时要预测的最大标记数。（默认：-1，无限生成）,影响模型最大可以生成的 token 数量。 | int | num_predict 42 |\n", "| top_k | 降低生成无意义文本的概率。较高的值（例如 100）会给出更多样化的答案，而较低的值（例如 10）会更保守。（默认：40） | int | top_k 40 |\n", "| top_p | 与 top-k 一起工作。较高的值（例如 0.95）会导致更具多样性的文本，而较低的值（例如 0.5）会生成更集中和保守的文本。（默认：0.9） | float | top_p 0.9 |\n", "| min_p | top_p 的替代方案，旨在确保质量和多样性之间的平衡。参数 p 表示考虑标记的最小概率，相对于最可能标记的概率。例如，p=0.05 时，最可能的标记概率为 0.9，值小于 0.045 的 logits 会被过滤掉。（默认：0.0） | float | min_p 0.05 |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. requests 调用方法"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp; `/api/chat` 依然还是可以`requests`库进行调用。如下所示："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-06T05:54:52.6018645Z\",\n", "  \"message\": {\n", "    \"role\": \"assistant\",\n", "    \"content\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的一般读者，想快速了解AI的基本概念和应用。\\n\\n这个query很明确，但“简短”这个词有点模糊——是三句话概括？还是500字的小概述？从经验判断，“简短”通常对应基础科普级别，所以我会控制在150-200字左右，既要全面又要精炼。\\n\\n用户可能刚接触AI领域，需要先建立认知框架。ta没指定具体方向，说明想要的是通用型介绍而非专业细分内容。我应该从定义入手，然后自然过渡到发展现状、核心能力类型和典型应用场景这几个关键维度。\\n\\n考虑到用户潜在需求：也许在写报告开头段落？或者只是好奇想扫盲？所以回复要避免学术术语堆砌，用“机器学习”代替“深度学习算法”，但保留技术严谨性。可以适当加入生活化例子帮助理解，比如推荐系统和自动驾驶。\\n\\n需要平衡客观性和吸引力——既不能像论文那样枯燥，又要保持信息准确度。最后补充一句展望未来的话会比较好，这样能让短小的介绍更有启发性。\\n</think>\\n好的！以下是一个关于人工智能的简短介绍：\\n\\n**人工智能（Artificial Intelligence, AI）** 是计算机科学的一个分支，旨在创造能够模拟人类智能行为和思维过程的系统。它通过赋予机器学习、推理、感知和决策的能力，使它们能执行复杂的任务。\\n\\n随着技术的发展，AI已经从简单的规则基础系统演变为基于深度学习的大规模神经网络模型，这些模型在图像识别、自然语言处理等领域取得了显著突破。无论是推荐视频算法还是自动驾驶汽车的核心逻辑，人工智能正在改变我们的生活，并推动各行各业的智能化进程。\\n\\n未来，人工智能有望进一步增强人机协作能力，在医疗诊断、科学研究和智能城市管理等方面发挥重要作用，同时我们也要关注其带来的伦理和社会影响。\"\n", "  },\n", "  \"done_reason\": \"stop\",\n", "  \"done\": true,\n", "  \"total_duration\": 11268549900,\n", "  \"load_duration\": 2438805600,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 149670700,\n", "  \"eval_count\": 401,\n", "  \"eval_duration\": 8679569800\n", "}\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 设置 API 端点\n", "chat_url = \"http://192.168.0.130:11434/api/chat\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "chat_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",  # 消息角色，用户发送的消息\n", "            \"content\": \"请生成一个关于人工智能的简短介绍。\"  # 用户的消息内容\n", "        }\n", "    ],\n", "    \"tools\": [],  # 如果有工具可以在这里添加\n", "    \"stream\": False,  # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "}\n", "\n", "# 调用聊天接口\n", "response_chat = requests.post(chat_url, json=chat_payload)\n", "if response_chat.status_code == 200:\n", "    chat_response = response_chat.json()\n", "    print(\"生成响应:\", json.dumps(chat_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_chat.status_code, response_chat.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;返回的响应中包含以下参数，其对应的描述如下："]}, {"cell_type": "markdown", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>响应参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名                  | 描述                                                         |\n", "| ----------------------- | ------------------------------------------------------------ |\n", "| **total_duration**      | 单次响应花费的总时间                                          |\n", "| **load_duration**       | 加载模型花费的时间                                   |\n", "| **prompt_eval_count**   | 提示中的token数                                               |\n", "| **prompt_eval_duration**| 评估提示所花费的时间（以纳秒为单位）                                 |\n", "| **eval_count**          | 响应中的token数                                               |\n", "| **eval_duration**       | 生成响应的时间（以纳秒为单位）                              |\n", "| **context**             | 在此响应中使用的对话的编码，可以在下一个请求中发送以保持对话记忆 |\n", "| **response**            | 空响应是流的，如果未流式传输，则将包含完整的响应             |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;重点关注以下几个参数："]}, {"cell_type": "markdown", "metadata": {}, "source": ["- <font color=\"red\">**message**</font> "]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;在 `/chat` 接口中，返回的模型响应结果存放在 `message` 中， 同样对于 `DeepSeek-R1` 模型，`response` 字段中包含<think> 标签和正常文本，<think> 标签用于表示模型的思考过程或内部推理，而正常的文本则是模型生成的实际输出内容。注意：非推理类模型的返回结果中没有<think></think>标识。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的一般读者，想快速了解AI的基本概念和应用。\\n\\n这个query很明确，但“简短”这个词有点模糊——是三句话概括？还是500字的小概述？从经验判断，“简短”通常对应基础科普级别，所以我会控制在150-200字左右，既要全面又要精炼。\\n\\n用户可能刚接触AI领域，需要先建立认知框架。ta没指定具体方向，说明想要的是通用型介绍而非专业细分内容。我应该从定义入手，然后自然过渡到发展现状、核心能力类型和典型应用场景这几个关键维度。\\n\\n考虑到用户潜在需求：也许在写报告开头段落？或者只是好奇想扫盲？所以回复要避免学术术语堆砌，用“机器学习”代替“深度学习算法”，但保留技术严谨性。可以适当加入生活化例子帮助理解，比如推荐系统和自动驾驶。\\n\\n需要平衡客观性和吸引力——既不能像论文那样枯燥，又要保持信息准确度。最后补充一句展望未来的话会比较好，这样能让短小的介绍更有启发性。\\n</think>\\n好的！以下是一个关于人工智能的简短介绍：\\n\\n**人工智能（Artificial Intelligence, AI）** 是计算机科学的一个分支，旨在创造能够模拟人类智能行为和思维过程的系统。它通过赋予机器学习、推理、感知和决策的能力，使它们能执行复杂的任务。\\n\\n随着技术的发展，AI已经从简单的规则基础系统演变为基于深度学习的大规模神经网络模型，这些模型在图像识别、自然语言处理等领域取得了显著突破。无论是推荐视频算法还是自动驾驶汽车的核心逻辑，人工智能正在改变我们的生活，并推动各行各业的智能化进程。\\n\\n未来，人工智能有望进一步增强人机协作能力，在医疗诊断、科学研究和智能城市管理等方面发挥重要作用，同时我们也要关注其带来的伦理和社会影响。'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_response[\"message\"]['content']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;可以通过简单的字符串操作来分离 <think> 标签中的思考内容和正常的文本内容，代码如下："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["思考内容:\n", " 嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的一般读者，想快速了解AI的基本概念和应用。\n", "\n", "这个query很明确，但“简短”这个词有点模糊——是三句话概括？还是500字的小概述？从经验判断，“简短”通常对应基础科普级别，所以我会控制在150-200字左右，既要全面又要精炼。\n", "\n", "用户可能刚接触AI领域，需要先建立认知框架。ta没指定具体方向，说明想要的是通用型介绍而非专业细分内容。我应该从定义入手，然后自然过渡到发展现状、核心能力类型和典型应用场景这几个关键维度。\n", "\n", "考虑到用户潜在需求：也许在写报告开头段落？或者只是好奇想扫盲？所以回复要避免学术术语堆砌，用“机器学习”代替“深度学习算法”，但保留技术严谨性。可以适当加入生活化例子帮助理解，比如推荐系统和自动驾驶。\n", "\n", "需要平衡客观性和吸引力——既不能像论文那样枯燥，又要保持信息准确度。最后补充一句展望未来的话会比较好，这样能让短小的介绍更有启发性。\n", "\n", "正常内容:\n", " 好的！以下是一个关于人工智能的简短介绍：\n", "\n", "**人工智能（Artificial Intelligence, AI）** 是计算机科学的一个分支，旨在创造能够模拟人类智能行为和思维过程的系统。它通过赋予机器学习、推理、感知和决策的能力，使它们能执行复杂的任务。\n", "\n", "随着技术的发展，AI已经从简单的规则基础系统演变为基于深度学习的大规模神经网络模型，这些模型在图像识别、自然语言处理等领域取得了显著突破。无论是推荐视频算法还是自动驾驶汽车的核心逻辑，人工智能正在改变我们的生活，并推动各行各业的智能化进程。\n", "\n", "未来，人工智能有望进一步增强人机协作能力，在医疗诊断、科学研究和智能城市管理等方面发挥重要作用，同时我们也要关注其带来的伦理和社会影响。\n"]}], "source": ["# 提取 <think> 标签中的内容\n", "think_start = chat_response[\"message\"]['content'].find(\"<think>\")\n", "think_end = chat_response[\"message\"]['content'].find(\"</think>\")\n", "\n", "if think_start != -1 and think_end != -1:\n", "    think_content = chat_response[\"message\"]['content'][think_start + len(\"<think>\"):think_end].strip()\n", "else:\n", "    think_content = \"No think content found.\"\n", "\n", "# 提取正常的文本内容\n", "normal_content = chat_response[\"message\"]['content'][think_end + len(\"</think>\"):].strip()\n", "\n", "# 打印结果\n", "print(\"思考内容:\\n\", think_content)\n", "print(\"\\n正常内容:\\n\", normal_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;其他的重点参数和 `/generation` 参数使用方法也保持一致，示例代码如下："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-06T06:02:17.0915352Z\",\n", "  \"message\": {\n", "    \"role\": \"assistant\",\n", "    \"content\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。这个需求看起来简单直接，但可能隐含了几层含义。\\n\\n用户可能是刚接触AI领域的新手，想快速了解基本概念；也可能是老师或科普工作者，在准备教学材料时需要概括性内容。考虑到用了“简短”这个词，说明ta更倾向于获取核心要点而非深度细节。\\n\\n从专业角度来说，介绍人工智能应该包含几个关键维度：定义、发展历史、主要分支（能力建立和通用智能）、典型应用领域以及对人类社会的影响。不过用户要的是“简短”，所以需要精炼地选取最具代表性的内容。\\n\\n我注意到用户没有指定技术方向或应用场景，这意味着ta可能想要一个全面但平衡的概述，而不是针对某个特定领域的深入说明。因此回复应该避免过度聚焦于某一方面，比如只谈机器学习而忽略其他分支。\\n\\n在安全性方面要特别注意两点：一是不能夸大AI的能力（尤其通用智能尚未实现），二是要强调人类监督的重要性——这既符合事实也符合中国对人工智能发展的指导方针中“以人为本”的要求。\\n\\n用户可能期待一个清晰明了的结构，所以我决定用分段式回复来增强可读性。第一段定义基础概念很重要，能帮助建立认知框架；然后按时间线简要说明发展脉络可以展现历史纵深感；提到机器学习和深度学习作为主要技术分支是为了聚焦关键技术；最后强调应用价值和社会影响能让内容更有温度。\\n\\n需要控制好信息密度——既不能太浅显得敷衍，也不能太深导致新用户难以理解。用“自动化”、“决策支持”这些直观词汇比直接说算法模型更易懂。提到医疗、交通等跨领域应用是为了展示AI的广泛影响力，但避免涉及具体技术参数。\\n\\n结尾处关于人类智慧与创造力的部分很重要：这既是在平衡看待AI技术，也能帮助用户理解人机协作而非替代的关系。\\n</think>\\n当然！以下是关于人工智能的一个简短介绍：\\n\\n---\\n\\n**人工智能简介**\\n\\n人工智能（Artificial Intelligence, 简称 AI）是计算机科学的一个分支，旨在创建能够模拟人类智能的系统。通过赋予机器学习、推理和决策能力，AI 技术不断推动自动化与智能化的发展。\\n\\nAI 的发展大致经历了符号主义、连接主义等不同阶段，并逐渐形成了两大主要方向：**狭义人工智能（弱人工智能）** 和 **通用人工智能（强人工智能）** 。前者专注于解决特定问题，如图像识别或语音翻译；后者则致力于构建能够像人类一样执行广泛任务的智能体。\\n\\n近年来，深度学习和大数据技术使 AI 在自然语言处理、自动驾驶等领域取得了突破性进展。尽管目前仍处于快速发展阶段，但 AI 已经开始深刻改变医疗、金融、交通等众多行业，并有望在未来进一步重塑社会运作方式。\\n\\n然而，在享受其便利的同时，我们也需要关注人工智能带来的伦理挑战和技术风险，确保技术始终服务于人类的福祉与智慧发展。\\n\\n---\\n\\n如果你希望我针对某个具体方面进行扩展（如应用场景或技术原理），也可以告诉我！\"\n", "  },\n", "  \"done_reason\": \"stop\",\n", "  \"done\": true,\n", "  \"total_duration\": 16745281300,\n", "  \"load_duration\": 2071816100,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 160394700,\n", "  \"eval_count\": 634,\n", "  \"eval_duration\": 14511631800\n", "}\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "chat_url = \"http://192.168.0.130:11434/api/chat\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "chat_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",  # 消息角色，用户发送的消息\n", "            \"content\": \"请生成一个关于人工智能的简短介绍。\"  # 用户的消息内容\n", "        }\n", "    ],\n", "    \"tools\": [],  # 如果有工具可以在这里添加\n", "    \"stream\": False,  # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "    \"keep_alive\": \"10m\",   # 设置模型在请求后保持加载的时间\n", "    \"options\":{\n", "        \"temperature\": 0.7,   \n", "        \"num_ctx\":2048,\n", "        \"num_predict\": 4096,\n", "    }\n", "}\n", "\n", "# 调用聊天接口\n", "response_chat = requests.post(chat_url, json=chat_payload)\n", "if response_chat.status_code == 200:\n", "    chat_response = response_chat.json()\n", "    print(\"生成响应:\", json.dumps(chat_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_chat.status_code, response_chat.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;流式输出代码也要针对`/chat`接口的返回响应格式做略微的修改："]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的人，想快速了解AI的基本概念。\n", "\n", "这个请求很简单直接，但“简短”这个词很重要，说明用户不想要长篇大论的技术细节，而是希望抓住核心要点。我应该避免深入算法原理或具体应用案例，保持概述性质。\n", "\n", "考虑到人工智能的普及程度，很多非专业人士对它都有模糊认知，所以介绍时需要先定义AI是什么，再区分它的子领域（比如机器学习），最后说明它和人类智能的区别。这样既能满足新手的理解需求，又不会显得太浅显给有基础的人看。\n", "\n", "用户可能还想知道AI能做什么、不能做什么，所以在描述应用范围时要平衡举例和概括性语言。“下棋”这个例子不错，因为它既展示了AI的强项（计算），也暗示了它的局限（缺乏直觉）。\n", "\n", "最后加上“改变世界”的总结应该能让大多数读者感到共鸣，即使他们只是随便问问。不过如果用户是专业人士，可能会觉得这个结尾太泛泛而谈……但根据查询的简洁性判断，ta更可能是寻求入门级内容。\n", "</think>\n", "好的，这是一个关于人工智能的简短介绍：\n", "\n", "# 人工智能简介\n", "\n", "**人工智能（Artificial Intelligence, 简称 AI）** 是计算机科学的一个分支，旨在创造能够执行通常需要人类智能的任务的机器或系统。它不仅仅是模仿人脑，而是更广泛地探索如何使机器具备类似学习、推理、感知和决策的能力。\n", "\n", "AI的核心领域包括：\n", "\n", "1.  **机器学习（Machine Learning, ML）：** 让计算机从数据中学习模式，并做出预测或决策，而无需显式编程所有规则。它是实现人工智能的关键技术之一。\n", "2.  **深度学习（Deep Learning）：** 这是机器学习的一个子集，使用多层神经网络来模拟人脑处理复杂信息的方式，在图像识别、语音处理等领域取得了巨大突破。\n", "\n", "AI的应用范围非常广泛，从简单的自动化任务到复杂的模式识别。它与人类智能的区别在于，AI 的目标不是完全复制人类意识，而是实现特定的智能功能（例如下棋、翻译或驾驶）。人工智能正在以前所未有的方式改变我们的世界，在提高效率、解决复杂问题和创造新体验方面潜力巨大。"]}], "source": ["import requests  # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",\n", "    \"stream\": True,  # 启用流式输出\n", "    \"options\": {\n", "        \"temperature\": 0.6,\n", "        \"keep_alive\": \"10m\"\n", "    }\n", "}\n", "\n", "# 调用生成接口\n", "with requests.post(generate_url, json=generate_payload, stream=True) as response_generate:\n", "    if response_generate.status_code == 200:\n", "        # 逐行读取流式响应\n", "        for line in response_generate.iter_lines():\n", "            if line:  # 确保行不为空\n", "                # 解析 JSON 响应\n", "                generate_response = json.loads(line)\n", "                \n", "                # 提取并打印 response 字段\n", "                if \"response\" in generate_response:\n", "                    print(generate_response[\"response\"], end='')  # end='' 防止换行\n", "                if generate_response.get(\"done\", False):\n", "                    break  # 如果 done 为 True，结束循环\n", "    else:\n", "        print(\"生成请求失败:\", response_generate.status_code, response_generate.text)"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}