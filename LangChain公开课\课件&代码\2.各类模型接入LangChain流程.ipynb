{"cells": [{"cell_type": "markdown", "id": "9aaeeead-445e-4361-88e0-beb79d50344e", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 2.各类模型接入LangChain流程"]}, {"cell_type": "markdown", "id": "a9533e48-4890-4126-9f13-f148191222fd", "metadata": {}, "source": ["- LangChain项目回顾"]}, {"cell_type": "markdown", "id": "4320ea65-0b11-4327-b22d-cd40876c9fbe", "metadata": {}, "source": ["&emsp;&emsp;`<PERSON><PERSON><PERSON><PERSON>`可以称之为自`2022年底`大模型技术爆火以来的第一个真正意义上的大模型开发框架。\n", "\n", "&emsp;&emsp;大模型本质上无法直接解决实际的问题，仅仅是一个能够分析、推理和生成文本的黑盒。直到现在，所有的开发者们仍然在不断探索如何把大模型的强大能力与实际应用场景结合起来，而当时`LangChain`的出现，直接让大模型开发变得简单起来，它将大模型开发过程中常用的功能、工具、流程等等全部封装成一个个的组件，使开发者可以像搭乐高积木一样，快速的组合出适用于不同场景需求的大模型应用。"]}, {"cell_type": "markdown", "id": "5bec0aef-9ed4-4ba9-971c-f77764e1047a", "metadata": {}, "source": ["&emsp;&emsp;`<PERSON><PERSON><PERSON><PERSON>`的首个版本于`2022年10月`开源，直到现在仍然再以一个飞快的速度不断进行迭代升级。从一个开源 `Python/TS` 框架逐渐发展，形成包括“链”和“代理”等核心组件，现在已走向企业级阶段，发展成了`LangChain AI`，其拥有目前`Agent`技术领域最大的开源生态，衍生出了多个开源项目框架，各自都在大模型的技术领域承担着不同的开发任务角色。 \n", "\n", "&emsp;&emsp;其官方`Github`地址为：https://github.com/langchain-ai"]}, {"cell_type": "markdown", "id": "e558fff9-70c1-4b4c-b846-349a01951c39", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091051864.png\" width=60%></div>"]}, {"cell_type": "markdown", "id": "8fb606f3-43ff-46e2-a2d5-4235e591af55", "metadata": {}, "source": ["&emsp;&emsp;这里我们可以梳理出在`LangChain AI`中最受关注的项目发展态势，如下图所示：\n", "\n", "<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091051866.png\" width=45%></div>"]}, {"cell_type": "markdown", "id": "3e333de4-fa1d-4c02-8191-113f24275ee0", "metadata": {}, "source": ["&emsp;&emsp;其中最活跃的项目当属`langchain`，排在前二的分别是`langChain`的`Python`版本和`JavaScript`版本。作为`LangChain AI`发展的基石，`langchain`主要用来支持构建大模型应用的一切，包括链式编排、检索增强生成 (RAG)、嵌入、文档处理、对话系统、代码分析等。而随着业务需求的越来越复杂，`LangChain AI`也推出了`langgraph`，作为一个基于图结构的 `Agent` 编排框架，可构建有状态、多步骤、多 `Agent` 的工作流，从而进一步扩展了`LangChain AI`自家生态的适用范围。而至于`local‑deep‑researcher`和`opengpts`，则完全是给开发者提供了基于`LangChain`定制开发出的特定热门应用，也一定程度上能印证`LangChain AI`的生态繁荣和可实现应用场景的广度。"]}, {"cell_type": "markdown", "id": "44d26a3c-d8b4-4430-a06f-d8a1dd63263d", "metadata": {}, "source": ["- LangChain AI 热门开源项目功能介绍"]}, {"cell_type": "markdown", "id": "c291767a-a2e5-4f5d-a8ef-b56c4ee17c02", "metadata": {}, "source": ["\n", "| 项目                        | 技术栈              | 核心用途                          |\n", "| ------------------------- | ---------------- | ----------------------------- |\n", "| **langchain**             | Python/TS        | 构建 LLM 应用基础组件                 |\n", "| **langchainjs**           | JS/TS            | 前端/Node 环境中构造 LLM 应用          |\n", "| **langgraph**             | Python           | 用图编排复杂 agent 流程               |\n", "| **local‑deep‑researcher** | Python           | 自动化、多轮本地 Web 研究工具             |\n", "| **opengpts**              | Python + Go + 前端 | 可定制化 GPT 平台，支持 RAG 和 agent 开发 |\n"]}, {"cell_type": "markdown", "id": "8a02ff16-a648-4afc-bf29-d01616d34ca1", "metadata": {}, "source": ["&emsp;&emsp;对外，`<PERSON><PERSON><PERSON><PERSON>` 也是目前大模型技术领域所有`AI Agent`框架中最热门、最通用的，相较于`AutoGen`、`CrewAI`、`OpenAI Agents SDK`和`Google ADK`，拥有最多的收藏量和活跃的开发者数量。"]}, {"cell_type": "markdown", "id": "e2f2a4d0-9b32-4c6b-967c-68c01ed45e7a", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu001.oss-cn-beijing.aliyuncs.com/img/image-20250609220554934.png\" width=45%></div>"]}, {"cell_type": "markdown", "id": "a826ea56-0ee3-41be-8f5b-3c90e391726f", "metadata": {}, "source": ["&emsp;&emsp;因此这里大家可以感受到，`langChain AI`生态的学习，其实最最核心的就是`LangChain`这个项目。而`langChain`是一个非常全面的开发框架，集成了`Agent`和`RAG`两个关键的热门落地方向，通过灵活的模块化组合可以快速的构建适用于私有业务场景的大模型应用，在目前的应用开发中是企业中使用最多的`Agent`开发框架。其核心架构如下图所示："]}, {"cell_type": "markdown", "id": "a2e5ee32-9fa9-46c8-85f2-94a5ff3f49b4", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu001.oss-cn-beijing.aliyuncs.com/img/langchain_arch.png\" width=60%></div>"]}, {"cell_type": "markdown", "id": "ea4365d7-8cc6-4408-937c-5a7e11507e55", "metadata": {}, "source": ["&emsp;&emsp;从本质上分析，`<PERSON><PERSON>hain`从大模型角度出发，通过开发人员在实践过程中对大模型能力的深入理解及其在不同场景下的涌现潜力，使用模块化的方式进行高级抽象，设计出统一接口以适配各种大模型。`LangChain`抽象出最重要的核心模块如下：\n", "\n", "1. `Model I/O` ：标准化各个大模型的输入和输出，包含输入模版，模型本身和格式化输出；\n", "2. `Retrieval` ：检索外部数据，然后在执行生成步骤时将其传递到 LLM，包括文档加载、切割、Embedding等；\n", "3. `Chains` ：链，`<PERSON><PERSON><PERSON><PERSON>`框架中最重要的模块，链接多个模块协同构建应用，是实际运作很多功能的高级抽象；\n", "4. `Memory` ： 记忆模块，以各种方式构建历史信息，维护有关实体及其关系的信息；\n", "5. `Agents` ： 目前最热门的`Agents`开发实践，未来能够真正实现通用人工智能的落地方案；\n", "6. `Callbacks` ：回调系统，允许连接到 大模型 应用程序的各个阶段。用于日志记录、监控、流传输和其他任务；"]}, {"cell_type": "markdown", "id": "fb4ec6ad-6c53-4eee-8cae-a4520ca39b56", "metadata": {}, "source": ["&emsp;&emsp;从上图中可以看到，`<PERSON><PERSON>hain`框架涵盖了模型输入输出的标准化、外部工具接入的规范、上下文记忆功能，以及对数据库、SQL、CSV等多种数据源的连接标准。通过核心的\"Chain\"高级抽象，定义了不同形式下标准的链接方法，这就能够允许开发者根据实际的应用需求和数据流向快速构建出一套完整的应用程序。这个过程类似于搭建积木，可以灵活适应不同的任务需求。\n", "\n", "&emsp;&emsp;因此本节公开课，我们就围绕`Lang<PERSON>hain`展开详细的讲解，我们会涉及到`LangChain`框架的整体概览，如何用`LangChain`搭建智能体和本地知识库问答的完整流程，同时对于比较热门的`MCP`工具如何接入`LangChain`框架，会做一个重点的说明。\n"]}, {"cell_type": "markdown", "id": "5af4d881-1a66-443c-82db-45b9d61b3968", "metadata": {}, "source": ["&emsp;&emsp;需要说明的，这里我们选择使用`Python`作为开发语言，同时使用目前最新的`LangChain 0.3`版本，具体的版本说明如下：\n", "\n", "- Python==3.12\n", "- <PERSON><PERSON><PERSON><PERSON>>=0.3.25\n", "- langchain-deepseek>=0.1.3"]}, {"cell_type": "markdown", "id": "3e47db78-fe16-495a-b930-d1808723b237", "metadata": {}, "source": ["### 1. <PERSON><PERSON><PERSON><PERSON>接入大模型流程"]}, {"cell_type": "markdown", "id": "63f78405-fd62-4f27-b357-6427e23d6562", "metadata": {}, "source": ["- <PERSON><PERSON><PERSON><PERSON>安装流程"]}, {"cell_type": "markdown", "id": "6d73828e-667d-49fb-be18-f85266bb46bc", "metadata": {}, "source": ["&emsp;&emsp;如果使用`<PERSON><PERSON><PERSON>n`进行大模型应用开发，需要安装`Lang<PERSON>hain`的依赖包，安装命令如下："]}, {"cell_type": "code", "execution_count": 1, "id": "3c53f1ca-b231-4392-b4b6-dbf115e8cdd5", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain\n", "  Using cached langchain-0.3.25-py3-none-any.whl.metadata (7.8 kB)\n", "Collecting langchain-core<1.0.0,>=0.3.58 (from langchain)\n", "  Using cached langchain_core-0.3.64-py3-none-any.whl.metadata (5.8 kB)\n", "Collecting langchain-text-splitters<1.0.0,>=0.3.8 (from langchain)\n", "  Using cached langchain_text_splitters-0.3.8-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting langsmith<0.4,>=0.1.17 (from langchain)\n", "  Using cached langsmith-0.3.45-py3-none-any.whl.metadata (15 kB)\n", "Collecting pydantic<3.0.0,>=2.7.4 (from langchain)\n", "  Using cached pydantic-2.11.5-py3-none-any.whl.metadata (67 kB)\n", "Collecting SQLAlchemy<3,>=1.4 (from langchain)\n", "  Using cached sqlalchemy-2.0.41-cp312-cp312-win_amd64.whl.metadata (9.8 kB)\n", "Collecting requests<3,>=2 (from langchain)\n", "  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting PyYAML>=5.3 (from langchain)\n", "  Using cached PyYAML-6.0.2-cp312-cp312-win_amd64.whl.metadata (2.1 kB)\n", "Collecting tenacity!=8.4.0,<10.0.0,>=8.1.0 (from langchain-core<1.0.0,>=0.3.58->langchain)\n", "  Using cached tenacity-9.1.2-py3-none-any.whl.metadata (1.2 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<1.0.0,>=0.3.58->langchain)\n", "  Using cached jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Collecting packaging<25,>=23.2 (from langchain-core<1.0.0,>=0.3.58->langchain)\n", "  Using cached packaging-24.2-py3-none-any.whl.metadata (3.2 kB)\n", "Collecting typing-extensions>=4.7 (from langchain-core<1.0.0,>=0.3.58->langchain)\n", "  Using cached typing_extensions-4.14.0-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.58->langchain)\n", "  Using cached jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached orjson-3.10.18-cp312-cp312-win_amd64.whl.metadata (43 kB)\n", "Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached requests_toolbelt-1.0.0-py2.py3-none-any.whl.metadata (14 kB)\n", "Collecting zstandard<0.24.0,>=0.23.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached zstandard-0.23.0-cp312-cp312-win_amd64.whl.metadata (3.0 kB)\n", "Collecting anyio (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)\n", "Collecting certifi (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached certifi-2025.4.26-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)\n", "Collecting idna (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting h11>=0.16 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic<3.0.0,>=2.7.4->langchain)\n", "  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting pydantic-core==2.33.2 (from pydantic<3.0.0,>=2.7.4->langchain)\n", "  Using cached pydantic_core-2.33.2-cp312-cp312-win_amd64.whl.metadata (6.9 kB)\n", "Collecting typing-inspection>=0.4.0 (from pydantic<3.0.0,>=2.7.4->langchain)\n", "  Using cached typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting charset-normalizer<4,>=2 (from requests<3,>=2->langchain)\n", "  Using cached charset_normalizer-3.4.2-cp312-cp312-win_amd64.whl.metadata (36 kB)\n", "Collecting urllib3<3,>=1.21.1 (from requests<3,>=2->langchain)\n", "  Using cached urllib3-2.4.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting greenlet>=1 (from SQLAlchemy<3,>=1.4->langchain)\n", "  Using cached greenlet-3.2.3-cp312-cp312-win_amd64.whl.metadata (4.2 kB)\n", "Collecting sniffio>=1.1 (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)\n", "Using cached langchain-0.3.25-py3-none-any.whl (1.0 MB)\n", "Using cached langchain_core-0.3.64-py3-none-any.whl (438 kB)\n", "Using cached jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Using cached langchain_text_splitters-0.3.8-py3-none-any.whl (32 kB)\n", "Using cached langsmith-0.3.45-py3-none-any.whl (363 kB)\n", "Using cached httpx-0.28.1-py3-none-any.whl (73 kB)\n", "Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)\n", "Using cached orjson-3.10.18-cp312-cp312-win_amd64.whl (134 kB)\n", "Using cached packaging-24.2-py3-none-any.whl (65 kB)\n", "Using cached pydantic-2.11.5-py3-none-any.whl (444 kB)\n", "Using cached pydantic_core-2.33.2-cp312-cp312-win_amd64.whl (2.0 MB)\n", "Using cached requests-2.32.3-py3-none-any.whl (64 kB)\n", "Using cached charset_normalizer-3.4.2-cp312-cp312-win_amd64.whl (105 kB)\n", "Using cached idna-3.10-py3-none-any.whl (70 kB)\n", "Using cached requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)\n", "Using cached sqlalchemy-2.0.41-cp312-cp312-win_amd64.whl (2.1 MB)\n", "Using cached tenacity-9.1.2-py3-none-any.whl (28 kB)\n", "Using cached urllib3-2.4.0-py3-none-any.whl (128 kB)\n", "Using cached zstandard-0.23.0-cp312-cp312-win_amd64.whl (495 kB)\n", "Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Using cached certifi-2025.4.26-py3-none-any.whl (159 kB)\n", "Using cached greenlet-3.2.3-cp312-cp312-win_amd64.whl (297 kB)\n", "Using cached h11-0.16.0-py3-none-any.whl (37 kB)\n", "Using cached jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Using cached PyYAML-6.0.2-cp312-cp312-win_amd64.whl (156 kB)\n", "Using cached typing_extensions-4.14.0-py3-none-any.whl (43 kB)\n", "Using cached typing_inspection-0.4.1-py3-none-any.whl (14 kB)\n", "Using cached anyio-4.9.0-py3-none-any.whl (100 kB)\n", "Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)\n", "Installing collected packages: zstandard, urllib3, typing-extensions, tenacity, sniffio, PyYAML, packaging, orjson, jsonpointer, idna, h11, greenlet, charset-normalizer, certifi, annotated-types, typing-inspection, SQLAlchemy, requests, pydantic-core, jsonpatch, httpcore, anyio, requests-toolbelt, pydantic, httpx, langsmith, langchain-core, langchain-text-splitters, langchain\n", "\n", "   ---- -----------------------------------  3/29 [tenacity]\n", "  Attempting uninstall: packaging\n", "   ---- -----------------------------------  3/29 [tenacity]\n", "    Found existing installation: packaging 25.0\n", "   ---- -----------------------------------  3/29 [tenacity]\n", "    Uninstalling packaging-25.0:\n", "   ---- -----------------------------------  3/29 [tenacity]\n", "      Successfully uninstalled packaging-25.0\n", "   ---- -----------------------------------  3/29 [tenacity]\n", "   ------------ ---------------------------  9/29 [idna]\n", "   --------------- ------------------------ 11/29 [greenlet]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ---------------------- ----------------- 16/29 [SQLAlchemy]\n", "   ----------------------- ---------------- 17/29 [requests]\n", "   ---------------------------- ----------- 21/29 [anyio]\n", "   ------------------------------- -------- 23/29 [pydantic]\n", "   ------------------------------- -------- 23/29 [pydantic]\n", "   ------------------------------- -------- 23/29 [pydantic]\n", "   ---------------------------------- ----- 25/29 [langsmith]\n", "   ---------------------------------- ----- 25/29 [langsmith]\n", "   ----------------------------------- ---- 26/29 [langchain-core]\n", "   ----------------------------------- ---- 26/29 [langchain-core]\n", "   ------------------------------------- -- 27/29 [langchain-text-splitters]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   -------------------------------------- - 28/29 [langchain]\n", "   ---------------------------------------- 29/29 [langchain]\n", "\n", "Successfully installed PyYAML-6.0.2 SQLAlchemy-2.0.41 annotated-types-0.7.0 anyio-4.9.0 certifi-2025.4.26 charset-normalizer-3.4.2 greenlet-3.2.3 h11-0.16.0 httpcore-1.0.9 httpx-0.28.1 idna-3.10 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.3.25 langchain-core-0.3.64 langchain-text-splitters-0.3.8 langsmith-0.3.45 orjson-3.10.18 packaging-24.2 pydantic-2.11.5 pydantic-core-2.33.2 requests-2.32.3 requests-toolbelt-1.0.0 sniffio-1.3.1 tenacity-9.1.2 typing-extensions-4.14.0 typing-inspection-0.4.1 urllib3-2.4.0 zstandard-0.23.0\n"]}], "source": ["! pip install langchain"]}, {"cell_type": "markdown", "id": "cf3e0634-ae64-44e6-aea7-aa36a6ac5acb", "metadata": {}, "source": ["&emsp;&emsp;不同的依赖包版本在使用方式上可能存在一些差异，所以建议大家选择和课程一直的依赖包版本进行学习。这里我们采用的是目前最新的`LangChain 0.3`版本，可以通过如下命令进行查看："]}, {"cell_type": "code", "execution_count": 2, "id": "718ada4d-461a-40ca-b743-8e8626af66a9", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:17:50.889906Z", "start_time": "2025-07-23T03:17:50.144735Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: lang<PERSON>in\n", "Version: 0.3.26\n", "Summary: Building applications with LLMs through composability\n", "Home-page: \n", "Author: \n", "Author-email: \n", "License: MIT\n", "Location: D:\\Softwares\\Anaconda\\envs\\LangGraphChatBot\\Lib\\site-packages\n", "Requires: langchain-core, langchain-text-splitters, langsmith, pydantic, PyYAML, requests, SQLAlchemy\n", "Required-by: langchain-community\n"]}], "source": ["! pip show langchain"]}, {"cell_type": "markdown", "id": "2cbbef29-2c02-4eef-b59b-8d13c2238bcc", "metadata": {}, "source": ["- 尝试调用DeepSeek"]}, {"cell_type": "markdown", "id": "ae8cc2de-6ffc-48f1-acc8-c72226ebaac2", "metadata": {}, "source": ["&emsp;&emsp;在进行`<PERSON><PERSON>hain`开发之前，首先需要准备一个可以进行调用的大模型，这里我们选择使用`DeepSeek`的大模型，并使用`DeepSeek`官方的`API_KEK`进行调用。如果初次使用，需要现在`DeepSeek`官网上进行注册并创建一个新的`API_Key`，其官方地址为：https://platform.deepseek.com/usage"]}, {"cell_type": "markdown", "id": "0337c44c-8e37-4480-b585-a806c9bdf075", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091257041.png\" width=60%></div>"]}, {"cell_type": "markdown", "id": "dc859cf6-8817-4801-b2d4-eea04c8b2b1a", "metadata": {}, "source": ["&emsp;&emsp;注册好`DeepSeek`的`API_KEY`后，首先在项目同级目录下创建一个`env`文件，用于存储`DeepSeek`的`API_KEY`，如下所示："]}, {"cell_type": "markdown", "id": "d6b2da03-5570-45fc-95b8-80691eef981b", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101758896.png\" alt=\"image-20250610175750011\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "3cfeb0ec-7bd6-471e-b024-43e554e70d41", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101759417.png\" alt=\"image-20250610175921388\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "d0ccc22c-c8da-462d-8546-ac74aea1c8b6", "metadata": {}, "source": ["&emsp;&emsp;接下来通过`python-dotenv`库读取`env`文件中的`API_KEY`，使其加载到当前的运行环境中，代码如下："]}, {"cell_type": "code", "execution_count": 1, "id": "6b8df71a-09f1-4d0a-be40-e6f05db0f882", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:17:45.981172Z", "start_time": "2025-07-23T03:17:44.075116Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: python-dotenv in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (1.1.1)\n"]}], "source": ["! pip install python-dotenv"]}, {"cell_type": "code", "execution_count": 4, "id": "a1d99073-1c36-41aa-b8a0-e7263f97af91", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:23:32.830990Z", "start_time": "2025-07-23T03:23:32.825457Z"}}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)\n", "\n", "DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "# print(DeepSeek_API_KEY)  # 可以通过打印查看"]}, {"cell_type": "markdown", "id": "cffce0e5-27a3-4fd7-a74e-14d15cd620bc", "metadata": {}, "source": ["&emsp;&emsp;我们在当前的运行环境下不使用`LangChain`，直接使用`DeepSeek`的`API`进行网络连通性测试，测试代码如下："]}, {"cell_type": "code", "execution_count": 3, "id": "be03fedc-0abd-44bb-bcd9-3360337d5626", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:18:05.675418Z", "start_time": "2025-07-23T03:18:04.698916Z"}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: openai in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (1.93.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (2.11.7)\n", "Requirement already satisfied: sniffio in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from anyio<5,>=3.5.0->openai) (3.10)\n", "Requirement already satisfied: certifi in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (2025.6.15)\n", "Requirement already satisfied: httpcore==1.* in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (0.4.1)\n", "Requirement already satisfied: colorama in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from tqdm>4->openai) (0.4.6)\n"]}], "source": ["! pip install openai"]}, {"cell_type": "code", "execution_count": 8, "id": "902ca1e2-514a-4b81-bcf8-c97d7f288617", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:23:52.796167Z", "start_time": "2025-07-23T03:23:41.700678Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我是一个AI助手，很高兴认识你。我的主要功能是回答各种问题、提供信息帮助、协助完成任务，以及陪你聊天解闷。无论是学习、工作还是生活中的疑问，我都会尽力为你解答。\n", "\n", "我的特点包括：\n", "1. 知识广泛（覆盖科技、人文、生活等多个领域）\n", "2. 24小时在线\n", "3. 支持多种语言交流\n", "4. 可以帮你整理资料、提供建议等\n", "\n", "不过要说明的是，我的知识截止到2023年，对于之后的新事件可能不太了解。另外，我也不能替代专业领域的建议（如医疗、法律等）。\n", "\n", "你想了解关于我的更多具体信息吗？或者有什么我可以帮你的呢？ 😊\n"]}], "source": ["from openai import OpenAI\n", "\n", "# 初始化DeepSeek的API客户端\n", "client = OpenAI(api_key=DeepSeek_API_KEY, base_url=\"https://api.deepseek.com\")\n", "\n", "# 调用DeepSeek的API，生成回答\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"你是乐于助人的助手，请根据用户的问题给出回答\"},\n", "        {\"role\": \"user\", \"content\": \"你好，请你介绍一下你自己。\"},\n", "    ],\n", ")\n", "\n", "# 打印模型最终的响应结果\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "05665596-d01e-40df-9a16-1e8e187bc469", "metadata": {}, "source": ["&emsp;&emsp;如果可以正常收到`DeepSeek`模型的响应，则说明`DeepSeek`的`API`已经可以正常使用且网络连通性正常。"]}, {"cell_type": "markdown", "id": "c1a4c53d-6b56-42f0-b6fe-c178018a78aa", "metadata": {}, "source": ["- DeepSeek接入LangChain流程"]}, {"cell_type": "markdown", "id": "c9498851-1df6-47de-b995-bcdd6ae86816", "metadata": {}, "source": ["&emsp;&emsp;接下来我们要考虑的是，对于这样一个`DeepSeek`官方的`API`，如何接入到`LangChain`中呢？其实非常简单，我们只需要使用`LangChain`中的一个`DeepSeek`组件即可向像述代码一样，直接使用相同的`DeepSeek API KEY`与大模型进行交互。因此，我们首先需要安装`LangChain`的`DeepSeek`组件，安装命令如下："]}, {"cell_type": "code", "execution_count": 9, "id": "04d82e4c-9805-420c-bcb6-4cee6cac5553", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:26:46.550348Z", "start_time": "2025-07-23T03:26:30.395641Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-deepseek\n", "  Downloading langchain_deepseek-0.1.4-py3-none-any.whl.metadata (1.1 kB)\n", "Collecting langchain-core<1.0.0,>=0.3.70 (from langchain-deepseek)\n", "  Downloading langchain_core-0.3.71-py3-none-any.whl.metadata (5.8 kB)\n", "Collecting langchain-openai<1.0.0,>=0.3.28 (from langchain-deepseek)\n", "  Downloading langchain_openai-0.3.28-py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: langsmith>=0.3.45 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (0.4.4)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (9.1.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (6.0.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (4.14.0)\n", "Requirement already satisfied: packaging>=23.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (24.2)\n", "Requirement already satisfied: pydantic>=2.7.4 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (2.11.7)\n", "Requirement already satisfied: jsonpointer>=1.9 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (3.0.0)\n", "Requirement already satisfied: openai<2.0.0,>=1.86.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (1.93.0)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (0.10.0)\n", "Requirement already satisfied: sniffio in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (4.67.1)\n", "Requirement already satisfied: idna>=2.8 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (3.10)\n", "Requirement already satisfied: certifi in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (2025.6.15)\n", "Requirement already satisfied: httpcore==1.* in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (0.4.1)\n", "Requirement already satisfied: regex>=2022.1.18 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (2024.11.6)\n", "Requirement already satisfied: requests>=2.26.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (2.32.4)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.70->langchain-deepseek) (0.23.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (2.5.0)\n", "Requirement already satisfied: colorama in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from tqdm>4->openai<2.0.0,>=1.86.0->langchain-openai<1.0.0,>=0.3.28->langchain-deepseek) (0.4.6)\n", "Downloading langchain_deepseek-0.1.4-py3-none-any.whl (7.4 kB)\n", "Downloading langchain_core-0.3.71-py3-none-any.whl (442 kB)\n", "Downloading langchain_openai-0.3.28-py3-none-any.whl (70 kB)\n", "Installing collected packages: langchain-core, langchain-openai, langchain-deepseek\n", "\n", "  Attempting uninstall: langchain-core\n", "\n", "    Found existing installation: langchain-core 0.3.67\n", "\n", "    Uninstalling langchain-core-0.3.67:\n", "\n", "      Successfully uninstalled langchain-core-0.3.67\n", "\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "  Attempting uninstall: langchain-openai\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "    Found existing installation: langchain-openai 0.3.6\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "    Uninstalling langchain-openai-0.3.6:\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "      Successfully uninstalled langchain-openai-0.3.6\n", "   ---------------------------------------- 0/3 [langchain-core]\n", "   ------------- -------------------------- 1/3 [langchain-openai]\n", "   ---------------------------------------- 3/3 [langchain-deepseek]\n", "\n", "Successfully installed langchain-core-0.3.71 langchain-deepseek-0.1.4 langchain-openai-0.3.28\n"]}], "source": ["! pip install langchain-deepseek"]}, {"cell_type": "markdown", "id": "ac304981-5fcf-4510-9bcf-4e1476bceb06", "metadata": {}, "source": ["&emsp;&emsp;安装好`<PERSON><PERSON><PERSON><PERSON>`集成`DeepSeek`模型的依赖包后，需要通过一个`init_chat_model`函数来初始化大模型，代码如下："]}, {"cell_type": "code", "execution_count": 10, "id": "1d22ef4a-3e43-42cf-9b90-5d13c61fd3f5", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:28:37.078793Z", "start_time": "2025-07-23T03:28:36.281368Z"}}, "outputs": [], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "model = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")  "]}, {"cell_type": "markdown", "id": "8338af96-18bc-4d3f-b957-d1d76f19f9e1", "metadata": {}, "source": ["&emsp;&emsp;其中`model`用来指定要使用的模型名称，而`model_provider`用来指定模型提供者，当写入`deepseek`时，会自动加载`langchain-deepseek`的依赖包，并使用在`model`中指定的模型名称用来进行交互。"]}, {"cell_type": "code", "execution_count": 11, "id": "3cef5533-67ce-4c3f-967f-615a8673d707", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:29:04.960896Z", "start_time": "2025-07-23T03:28:49.972352Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我是 **DeepSeek Chat**，由深度求索（DeepSeek）公司研发的智能 AI 助手。我可以帮助你解答各种问题，包括学习、工作、编程、生活百科、创意写作等。以下是我的几个特点：  \n", "\n", "✨ **知识丰富**：我的知识截止到 **2024 年 7 月**，可以提供科技、历史、数学、文学等众多领域的知识。  \n", "💡 **逻辑清晰**：擅长分析复杂问题，比如数学计算、编程 debug、论文写作等。  \n", "📝 **文本处理**：可以帮你润色文章、总结文档、翻译语言，甚至生成诗歌、故事等创意内容。  \n", "📂 **文件阅读**：支持上传 **PDF、Word、Excel、PPT、TXT** 等文件，并从中提取关键信息。  \n", "🆓 **完全免费**：目前不收取任何费用，你可以随时向我提问！  \n", "\n", "无论是学习、工作，还是日常生活中的疑问，都可以来找我聊聊！😊 你今天想了解什么呢？\n"]}], "source": ["question = \"你好，请你介绍一下你自己。\"\n", "\n", "result = model.invoke(question)\n", "print(result.content)"]}, {"cell_type": "code", "execution_count": 12, "id": "61c64765-a835-4250-85cd-e86ef879b5f5", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:29:21.555692Z", "start_time": "2025-07-23T03:29:21.548177Z"}}, "outputs": [{"data": {"text/plain": ["AIMessage(content='你好！我是 **DeepSeek Chat**，由深度求索（DeepSeek）公司研发的智能 AI 助手。我可以帮助你解答各种问题，包括学习、工作、编程、生活百科、创意写作等。以下是我的几个特点：  \\n\\n✨ **知识丰富**：我的知识截止到 **2024 年 7 月**，可以提供科技、历史、数学、文学等众多领域的知识。  \\n💡 **逻辑清晰**：擅长分析复杂问题，比如数学计算、编程 debug、论文写作等。  \\n📝 **文本处理**：可以帮你润色文章、总结文档、翻译语言，甚至生成诗歌、故事等创意内容。  \\n📂 **文件阅读**：支持上传 **PDF、Word、Excel、PPT、TXT** 等文件，并从中提取关键信息。  \\n🆓 **完全免费**：目前不收取任何费用，你可以随时向我提问！  \\n\\n无论是学习、工作，还是日常生活中的疑问，都可以来找我聊聊！😊 你今天想了解什么呢？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 216, 'prompt_tokens': 9, 'total_tokens': 225, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 9}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': '2bb18d73-92df-454b-a5ff-6fc90ac1adf0', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--20d5297a-f7ae-498e-b657-c82a0d781f4f-0', usage_metadata={'input_tokens': 9, 'output_tokens': 216, 'total_tokens': 225, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}})"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 13, "id": "5b10b118-eb20-4949-b0d0-535748a3e727", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:30:52.407563Z", "start_time": "2025-07-23T03:30:15.240556Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好呀！👋我是 **DeepSeek-R1**，由中国的人工智能公司「深度求索（DeepSeek）」研发的一款智能助手。我的使命就是用清晰、温暖又靠谱的方式，帮你解答问题、整理信息、激发灵感，成为你学习、工作和生活中的得力伙伴✨\n", "\n", "---\n", "\n", "### 🔍 关于我的几个关键点：\n", "1. **知识广博**  \n", "   我掌握了截至**2024年7月**为止的大量知识，涵盖科技、历史、文学、法律、金融、医学常识等多个领域，也支持中英文双语交流。\n", "\n", "2. **文本理解强**  \n", "   我能读懂和生成**长达128K上下文**的文字，可以处理超长文档、论文、小说、合同、报告等复杂内容。\n", "\n", "3. **文件处理小能手**  \n", "   支持上传 **PDF、Word、Excel、PPT、TXT 等文件**，我可以从中提取文字、总结内容、分析数据、翻译润色，甚至帮你改写格式。\n", "\n", "4. **完全免费使用**（目前）  \n", "   现在我还是**免费**的！没有隐藏收费，没有次数限制，放心大胆地用我就行～\n", "\n", "5. **联网状态？**  \n", "   我目前**不支持联网搜索**，但我的知识储备非常丰富，日常问题基本都能解答！如果你需要我访问某个网页内容，你可以把文字复制粘贴给我，我一样能帮你分析。\n", "\n", "---\n", "\n", "### 🛠️ 我能做什么？\n", "- 📚 学习辅导：解题思路、作文批改、知识点讲解  \n", "- 💼 办公帮手：写周报、做PPT大纲、分析Excel数据  \n", "- ✍️ 文字创作：写小说、起标题、改文案、生成小红书/公众号内容  \n", "- 🌍 语言翻译：中英互译、润色、仿写  \n", "- 📄 文档处理：总结论文、提取合同重点、整理会议纪要  \n", "- 💡 生活助手：写情书、旅游攻略、节日祝福、菜谱推荐  \n", "\n", "---\n", "\n", "### ❤️ 我的小特点：\n", "- 喜欢用表情包让聊天更轻松~ 😄  \n", "- 努力做到“有逻辑、有温度、有细节”  \n", "- 尊重你的隐私，对话记录不会被保留或滥用  \n", "\n", "如果你愿意，我们可以随时开始聊天～  \n", "现在有什么我可以帮你的吗？🌟\n"]}], "source": ["model = init_chat_model(model=\"deepseek-reasoner\", model_provider=\"deepseek\")  \n", "\n", "result = model.invoke(question)\n", "print(result.content)"]}, {"cell_type": "code", "execution_count": 14, "id": "87a6966f-a388-496d-b4f2-67efb94f1b6c", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:33:50.763778Z", "start_time": "2025-07-23T03:33:50.758468Z"}}, "outputs": [{"data": {"text/plain": ["{'refusal': None,\n", " 'reasoning_content': '嗯，用户让我介绍一下自己，这是个很常见的开场问题。用户可能是第一次接触我，想快速了解我的能力和边界，也可能是想测试我的反应。\\n\\n用户没有提供更多背景，所以需要给出通用但信息量充足的介绍。重点要突出三点：身份定位（不是人类）、核心能力（实用技能）、使用限制（避免误解）。尤其要强调免费和文件处理功能——这常是用户关心的点。\\n\\n考虑到中文用户，用轻松活泼的语气更亲切，但避免过度拟人化。最后用开放性问题收尾比较自然，能引导对话继续。对了，要记得用emoji增加可读性，但别超过三个。\\n\\n用户没提专业需求，所以列举能力时要覆盖学习、工作、生活等常见场景。特别说明“不联网”很重要，很多用户会默认AI能查实时信息。最后那个“现在有什么可以帮你”的提问，特意用了波浪号显得更友好。'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["result.additional_kwargs"]}, {"cell_type": "code", "execution_count": 15, "id": "4bea0f51-7a77-497c-a838-30fdf491d2cf", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:34:07.509642Z", "start_time": "2025-07-23T03:34:07.505435Z"}}, "outputs": [{"data": {"text/plain": ["'嗯，用户让我介绍一下自己，这是个很常见的开场问题。用户可能是第一次接触我，想快速了解我的能力和边界，也可能是想测试我的反应。\\n\\n用户没有提供更多背景，所以需要给出通用但信息量充足的介绍。重点要突出三点：身份定位（不是人类）、核心能力（实用技能）、使用限制（避免误解）。尤其要强调免费和文件处理功能——这常是用户关心的点。\\n\\n考虑到中文用户，用轻松活泼的语气更亲切，但避免过度拟人化。最后用开放性问题收尾比较自然，能引导对话继续。对了，要记得用emoji增加可读性，但别超过三个。\\n\\n用户没提专业需求，所以列举能力时要覆盖学习、工作、生活等常见场景。特别说明“不联网”很重要，很多用户会默认AI能查实时信息。最后那个“现在有什么可以帮你”的提问，特意用了波浪号显得更友好。'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["result.additional_kwargs['reasoning_content']"]}, {"cell_type": "markdown", "id": "1c7dcc4a-32bb-4814-adf1-b05ba349301a", "metadata": {}, "source": ["&emsp;&emsp;这里可以看到，仅仅通过两行代码，我们便可以在`LangChain`中顺利调用`DeepSeek`模型，并得到模型的响应结果。相较于使用`DeepSeek`的`API`，使用`LangChain`调用模型无疑是更加简单的。同时，不仅仅是`DeepSeek`模型，`LangChain`还支持其他很多大模型，如`OpenAI`、`<PERSON>wen`、`Gemini`等，我们只需要在`init_chat_model`函数中指定不同的模型名称，就可以调用不同的模型。其工作的原理是这样的："]}, {"cell_type": "markdown", "id": "2a5f3e08-617f-4fb7-8534-2bec30c6414f", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091353369.png\" width=60%></div>"]}, {"cell_type": "markdown", "id": "90689bdb-1f8f-4255-9ed2-80cd471c21d0", "metadata": {}, "source": ["&emsp;&emsp;理解了这个基本原理，如果大家想在用`Lang<PERSON>hain`进行开发时使用其他大模型如`Qwen3`系列，则只需要先获取到`Qwen3`模型的`API_KEY`，然后安装`Tongyi Qwen`的第三方依赖包，即可同样通过`init_chat_model`函数来初始化模型，并调用`invoke`方法来得到模型的响应结果。关于`LangChain`都支持哪些大模型以及每个模型对应的是哪个第三方依赖包，大家可以在`LangChain`的官方文档中找到，访问链接为：https://python.langchain.com/docs/integrations/chat/"]}, {"cell_type": "markdown", "id": "4b93617d-56c9-462f-abda-b59f691b3fa0", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091359607.png\" width=60%></div>"]}, {"cell_type": "markdown", "id": "79a4abf6-46f0-4a52-ab36-cbc3fe452512", "metadata": {}, "source": ["- 【补充】LangChain接入OpenAI模型"]}, {"cell_type": "code", "execution_count": 1, "id": "b6c5d976-f505-47f6-8413-f3fe28d7dc4f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain-openai in /root/anaconda3/lib/python3.12/site-packages (0.3.21)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.64 in /root/anaconda3/lib/python3.12/site-packages (from langchain-openai) (0.3.64)\n", "Requirement already satisfied: openai<2.0.0,>=1.68.2 in /root/anaconda3/lib/python3.12/site-packages (from langchain-openai) (1.78.1)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /root/anaconda3/lib/python3.12/site-packages (from langchain-openai) (0.8.0)\n", "Requirement already satisfied: langsmith<0.4,>=0.3.45 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (0.3.45)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (8.5.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (6.0.1)\n", "Requirement already satisfied: packaging<25,>=23.2 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (24.1)\n", "Requirement already satisfied: typing-extensions>=4.7 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (4.13.2)\n", "Requirement already satisfied: pydantic>=2.7.4 in /root/anaconda3/lib/python3.12/site-packages (from langchain-core<1.0.0,>=0.3.64->langchain-openai) (2.11.4)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (0.8.2)\n", "Requirement already satisfied: sniffio in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (1.3.0)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/lib/python3.12/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (4.66.4)\n", "Requirement already satisfied: regex>=2022.1.18 in /root/anaconda3/lib/python3.12/site-packages (from tiktoken<1,>=0.7->langchain-openai) (2023.10.3)\n", "Requirement already satisfied: requests>=2.26.0 in /root/anaconda3/lib/python3.12/site-packages (from tiktoken<1,>=0.7->langchain-openai) (2.32.3)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/lib/python3.12/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.68.2->langchain-openai) (3.7)\n", "Requirement already satisfied: certifi in /root/anaconda3/lib/python3.12/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.68.2->langchain-openai) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/lib/python3.12/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.68.2->langchain-openai) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /root/anaconda3/lib/python3.12/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.68.2->langchain-openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /root/anaconda3/lib/python3.12/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.64->langchain-openai) (2.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /root/anaconda3/lib/python3.12/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.64->langchain-openai) (3.10.13)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /root/anaconda3/lib/python3.12/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.64->langchain-openai) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /root/anaconda3/lib/python3.12/site-packages (from langsmith<0.4,>=0.3.45->langchain-core<1.0.0,>=0.3.64->langchain-openai) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/lib/python3.12/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.64->langchain-openai) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/lib/python3.12/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.64->langchain-openai) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/lib/python3.12/site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.64->langchain-openai) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/anaconda3/lib/python3.12/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (2.0.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/anaconda3/lib/python3.12/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (2.2.2)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["! pip install langchain-openai"]}, {"cell_type": "code", "execution_count": 2, "id": "669b18cc-62c9-404e-9db4-d510836406e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我是一个人工智能助手，旨在帮助用户解答问题、提供信息和支持。我可以处理各种主题的询问，比如科技、历史、文化、语言学习等。如果你有任何问题或需要帮助的地方，请随时问我！\n"]}], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "model = init_chat_model(\"gpt-4o-mini\", model_provider=\"openai\")\n", "\n", "question = \"你好，请你介绍一下你自己。\"\n", "\n", "result = model.invoke(question)\n", "print(result.content)"]}, {"cell_type": "markdown", "id": "ce342b6b-511a-48c9-9c2b-423d5665fc2e", "metadata": {}, "source": ["> 注1：使用OpenAI模型前需要设置好网络环境。             \n", "> 注2：更多OpenAI、<PERSON>、<PERSON>模型接入指南，详见赋范大模型技术社区文档：\n", "> <center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121657437.png\" alt=\"image-20250612165757386\" style=\"zoom:33%;\" />      "]}, {"cell_type": "markdown", "id": "42dafab1-5acd-47e0-a5a8-c22ed33db26c", "metadata": {}, "source": ["> <center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121701348.png\" alt=\"57d34aa34ec04a98b020018c55b242d\" style=\"zoom:23%;\" />"]}, {"cell_type": "markdown", "id": "aee2cc5d-deca-4a7e-8ea2-9090e797b6ea", "metadata": {}, "source": ["- 【补充】接入Dashscope"]}, {"cell_type": "markdown", "id": "f8f19120-3516-48d1-a20e-903e7c35cd3e", "metadata": {}, "source": ["&emsp;&emsp;Dashscope原名是阿里云的灵积社区，也是国内最大的API集成平台，其中包含了各类开源模型（如Qwen3系列模型）和国内在线模型（如DeepSeek、BaiChuan）模型API服务，现在已合并入阿里云百炼平台。对于国内开发者来说，若要使用Qwen系列模型API（而非本地部署），那么Dashscope平台提供的API服务肯定是最合适的。"]}, {"cell_type": "markdown", "id": "ad3e2718-f9e3-427c-85ef-8e1f3a35b7ee", "metadata": {}, "source": ["阿里百炼平台官网：https://bailian.console.aliyun.com/?switchAgent=11366636&productCode=p_efm&switchUserType=3&tab=home#/home"]}, {"cell_type": "markdown", "id": "d27d4a9c-895b-48e9-9427-b1071b8ef326", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121643946.png\" alt=\"image-20250612164327753\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "cc8fb7bc-ac0f-41b3-bd52-9ab6919f238a", "metadata": {}, "source": ["而百炼API获取方式也非常简单，只需注册阿里云账号，然后前往我的API页面：https://bailian.console.aliyun.com/?tab=model#/api-key 进行充值和注册即可："]}, {"cell_type": "markdown", "id": "c45db8fd-9f34-4173-b8c3-314036ca3817", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121644323.png\" alt=\"image-20250612164439194\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "485a611c-90ad-4c20-ab2b-ca34a21307ac", "metadata": {}, "source": ["然后即可调用海量各类模型了："]}, {"cell_type": "markdown", "id": "b29caae0-6655-4071-ba8d-caa5b8b96168", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121645006.png\" alt=\"image-20250612164516862\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "909c2a4b-3135-4fce-ae00-b64f050eb90f", "metadata": {}, "source": ["当我们完成了DashScope API注册后，即可使用如下代码进行模型调用（需要提前将DASHSCOPE_API_KEY写到本地.env文件中）："]}, {"cell_type": "code", "execution_count": 16, "id": "af11c30c-5199-4b25-804f-a28b153dee7e", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:41:35.996755Z", "start_time": "2025-07-23T03:41:34.088808Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"id\":\"chatcmpl-898f03c0-a87f-98ff-abd3-d96d012127ce\",\"choices\":[{\"finish_reason\":\"stop\",\"index\":0,\"logprobs\":null,\"message\":{\"content\":\"我是通义千问，阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我可以帮助你回答问题、创作文字，比如写故事、写公文、写邮件、写剧本、逻辑推理、编程等等，还能表达观点，玩游戏等。如果你有任何问题或需要帮助，欢迎随时告诉我！\",\"refusal\":null,\"role\":\"assistant\",\"annotations\":null,\"audio\":null,\"function_call\":null,\"tool_calls\":null}}],\"created\":1753242095,\"model\":\"qwen-plus\",\"object\":\"chat.completion\",\"service_tier\":null,\"system_fingerprint\":null,\"usage\":{\"completion_tokens\":66,\"prompt_tokens\":26,\"total_tokens\":92,\"completion_tokens_details\":null,\"prompt_tokens_details\":{\"audio_tokens\":null,\"cached_tokens\":0}}}\n"]}], "source": ["import os\n", "from openai import OpenAI\n", "\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"DASHSCOPE_API_KEY\"),\n", "    base_url=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", ")\n", "\n", "completion = client.chat.completions.create(\n", "    # 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models\n", "    model=\"qwen-plus\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"你是谁？\"},\n", "    ],\n", ")\n", "print(completion.model_dump_json())"]}, {"cell_type": "markdown", "id": "d1f66ff9-e559-4876-bc79-30486c3271ce", "metadata": {}, "source": ["当然，也可以将DashScope中各类模型接入LangChain："]}, {"cell_type": "code", "execution_count": 17, "id": "c3323ae7-107a-4146-b62a-b837b8a8d6cc", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:41:50.826164Z", "start_time": "2025-07-23T03:41:46.847533Z"}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting dashscope\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/61/3f/2d1e656e997ddfaf3a3fde74d9b5120689338e4435ecc26b5c95720a6dc9/dashscope-1.23.9-py3-none-any.whl (1.3 MB)\n", "     ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/1.3 MB ? eta -:--:--\n", "     -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "     -------- ------------------------------- 0.3/1.3 MB ? eta -:--:--\n", "     ---------------- ----------------------- 0.5/1.3 MB 699.0 kB/s eta 0:00:02\n", "     -------------------------------- ------- 1.0/1.3 MB 1.2 MB/s eta 0:00:01\n", "     ---------------------------------------- 1.3/1.3 MB 1.1 MB/s eta 0:00:00\n", "Requirement already satisfied: aiohttp in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from dashscope) (3.12.13)\n", "Requirement already satisfied: requests in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from dashscope) (2.32.4)\n", "Requirement already satisfied: websocket-client in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from dashscope) (1.8.0)\n", "Requirement already satisfied: cryptography in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from dashscope) (45.0.5)\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (1.4.0)\n", "Requirement already satisfied: attrs>=17.3.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (6.6.3)\n", "Requirement already satisfied: propcache>=0.2.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiohttp->dashscope) (1.20.1)\n", "Requirement already satisfied: idna>=2.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from yarl<2.0,>=1.17.0->aiohttp->dashscope) (3.10)\n", "Requirement already satisfied: typing-extensions>=4.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from aiosignal>=1.1.2->aiohttp->dashscope) (4.14.0)\n", "Requirement already satisfied: cffi>=1.14 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from cryptography->dashscope) (1.17.1)\n", "Requirement already satisfied: pycparser in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from cffi>=1.14->cryptography->dashscope) (2.22)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests->dashscope) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests->dashscope) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests->dashscope) (2025.6.15)\n", "Installing collected packages: dashscope\n", "Successfully installed dashscope-1.23.9\n"]}], "source": ["!pip install --upgrade dashscope -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 18, "id": "187b3fad-eff7-4401-8a0b-c80c6af9bacf", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:41:53.259844Z", "start_time": "2025-07-23T03:41:53.228902Z"}}, "outputs": [], "source": ["from langchain_community.chat_models.tongyi import ChatTongyi"]}, {"cell_type": "code", "execution_count": 19, "id": "5ff96fff-5486-40af-b892-eb2cb791b5a5", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:41:57.595021Z", "start_time": "2025-07-23T03:41:56.774586Z"}}, "outputs": [], "source": ["model = ChatT<PERSON>yi()"]}, {"cell_type": "code", "execution_count": 20, "id": "52aeba71-175b-43e9-98e5-089188565a85", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:42:03.336226Z", "start_time": "2025-07-23T03:42:00.719462Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我是通义千问，是阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我能够回答问题、创作文字、编程、逻辑推理等多种任务，旨在为用户提供高质量的信息和服务。\n", "\n", "我的训练数据来源于大量的文本，包括书籍、文章、网页等，这使我能够理解和生成多种语言的内容。我被设计用来帮助用户获取信息、解决问题以及进行创造性的思考。\n", "\n", "如果你有任何问题或需要帮助，我会尽力提供支持。你可以问我任何问题，或者让我帮你完成一些任务。希望我们可以有愉快的交流！\n"]}], "source": ["question = \"你好，请你介绍一下你自己。\"\n", "\n", "result = model.invoke(question)\n", "print(result.content)"]}, {"cell_type": "markdown", "id": "d379a9cf-512f-4362-92e9-be334b0d1200", "metadata": {}, "source": ["- 【补充】ollama开源大模型接入LangChain"]}, {"cell_type": "markdown", "id": "f502880e-e53f-4524-85f7-0657c847a79b", "metadata": {}, "source": ["&emsp;&emsp;当然，除了在线大模型的接入，`lang<PERSON><PERSON>n`也只是使用`Ollama`、`vLLM`等框架启动的本地大模型。这里以ollama为例进行演示。"]}, {"cell_type": "code", "execution_count": 21, "id": "4cbc3895-0267-4242-8157-8d5f63f794ec", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:42:13.848307Z", "start_time": "2025-07-23T03:42:12.780546Z"}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain-ollama in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (0.3.3)\n", "Requirement already satisfied: ollama<1.0.0,>=0.4.8 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-ollama) (0.5.1)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.60 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-ollama) (0.3.71)\n", "Requirement already satisfied: langsmith>=0.3.45 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (0.4.4)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (9.1.2)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (6.0.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (4.14.0)\n", "Requirement already satisfied: packaging>=23.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (24.2)\n", "Requirement already satisfied: pydantic>=2.7.4 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.60->langchain-ollama) (2.11.7)\n", "Requirement already satisfied: jsonpointer>=1.9 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (3.0.0)\n", "Requirement already satisfied: httpx>=0.27 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from ollama<1.0.0,>=0.4.8->langchain-ollama) (0.28.1)\n", "Requirement already satisfied: anyio in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (4.9.0)\n", "Requirement already satisfied: certifi in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (2025.6.15)\n", "Requirement already satisfied: httpcore==1.* in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (1.0.9)\n", "Requirement already satisfied: idna in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (3.10)\n", "Requirement already satisfied: h11>=0.16 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from httpcore==1.*->httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (0.16.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (3.10.18)\n", "Requirement already satisfied: requests<3,>=2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (2.32.4)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from pydantic>=2.7.4->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (0.4.1)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core<1.0.0,>=0.3.60->langchain-ollama) (2.5.0)\n", "Requirement already satisfied: sniffio>=1.1 in d:\\softwares\\anaconda\\envs\\langgraphchatbot\\lib\\site-packages (from anyio->httpx>=0.27->ollama<1.0.0,>=0.4.8->langchain-ollama) (1.3.1)\n"]}], "source": ["!pip install langchain-ollama"]}, {"cell_type": "code", "execution_count": 6, "id": "a446d2a7-663f-4e95-bef0-a00f7fc0b8c5", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:42:15.726092Z", "start_time": "2025-07-23T03:42:15.630161Z"}}, "outputs": [], "source": ["from langchain_ollama import ChatOllama"]}, {"cell_type": "markdown", "id": "f87de8e4-dfa4-45a6-ab96-503cb44fd98d", "metadata": {}, "source": ["注意，这里要确保ollama已经顺利开启，并查看当前模型名称："]}, {"attachments": {}, "cell_type": "markdown", "id": "75838abc-2053-4e2a-a01a-0e740732f8c9", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121701138.png\" alt=\"image-20250612170158115\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "e516467e-4f31-41d1-8472-144024fa14c3", "metadata": {}, "source": ["然后即可使用如下方法接入LangChain："]}, {"cell_type": "code", "execution_count": 10, "id": "94e004fc-e28e-44b5-812a-0712c9b838fa", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:42:32.641521Z", "start_time": "2025-07-23T03:42:32.587951Z"}}, "outputs": [], "source": ["model = ChatOllama(model=\"deepseek-r1:8b\", base_url=\"http://localhost:11434\")"]}, {"cell_type": "code", "execution_count": 11, "id": "1d1421f7-1c64-4729-8039-5d0e3fb633fc", "metadata": {"ExecuteTime": {"end_time": "2025-07-23T03:42:58.088172Z", "start_time": "2025-07-23T03:42:33.495550Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户发来一个简单的问候和自我介绍请求。这可能是第一次使用AI助手的用户，或者ta想确认我的功能范围。我需要用友好且清晰的方式建立初步印象。\n", "\n", "用户没有提供具体场景或身份信息，但主动询问说明ta对AI交互持开放态度。考虑到中文互联网环境的特点，回复应该带点本地化亲切感——用表情符号和轻松语气会更符合当代年轻人的交流习惯。\n", "\n", "这个query虽然短，但包含了几个潜在需求：一是测试我的基础功能是否正常，二是想快速了解能获得什么帮助，三是建立初步信任关系。需要平衡专业性和亲和力，在30秒内给出完整画像式介绍。\n", "\n", "重点突出三个层次：身份定位（智能工具而非人类）、核心能力（知识+实用技能）、附加价值（陪伴感）。要避免过于技术化的描述，比如不提具体算法模型，而是强调“懂你”的人性化特质。最后用开放式提问引导对话继续，这样既回答了问题又创造了互动机会。\n", "\n", "用户可能会接着问专业领域或价格问题，所以提前说明免费属性很重要。结尾的emoji选择微笑😊而不是爱心❤️，保持适度距离感更合适。\n", "</think>\n", "你好呀！👋我是DeepSeek-R1，一个由深度求索（DeepSeek）打造的人工智能助手。我擅长理解你的问题、提供各种信息，并用温暖细致的方式帮助你解答疑惑～\n", "\n", "我的能力包括但不限于：\n", "- 回答各类知识性问题 📚  \n", "- 协助写作、翻译、润色 ✍️  \n", "- 帮你生成创意内容，比如文案、故事、诗歌 🎨\n", "- 进行学习辅导和逻辑推理 💡\n", "- 分析文档（支持Word、Excel等上传功能）📄\n", "- 提供编程帮助（代码解释、调试优化），包括多种语言如 Python、C++、Java 等 👨‍💻\n", "\n", "我拥有128K的超长上下文记忆能力，也就是说我们能聊很多轮也不会丢掉重点。而且，我是免费使用的哦～目前没有收费计划！\n", "\n", "如果你有任何问题或需要帮忙的地方，尽管告诉我吧！😊  \n", "你想了解哪方面的内容呢？\n"]}], "source": ["question = \"你好，请你介绍一下你自己。\"\n", "\n", "result = model.invoke(question)\n", "print(result.content)"]}, {"cell_type": "markdown", "id": "e1025fc8-8d75-437b-88ad-e98217bb3d1a", "metadata": {}, "source": ["> 注：更多ollama、vLLM使用方法，及Qwen3、DeepSeek系列模型本地部署流程，详见赋范大模型技术社区教程：             \n", "> <center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121700560.png\" alt=\"image-20250612170044518\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "84b3c206-edd8-4f29-a4ff-ed11bce9f569", "metadata": {}, "source": ["> <center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121701348.png\" alt=\"57d34aa34ec04a98b020018c55b242d\" style=\"zoom:23%;\" />"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}