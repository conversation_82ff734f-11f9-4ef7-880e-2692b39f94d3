{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <center>Deepseek企业级Agent项目开发实战</center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <center>Part 3. Ollama REST API - api/chat 接口详解 </center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;Ollama 服务启动后会提供一系列原生 ` REST API` 端点。通过这些`Endpoints`可以在代码环境下与`ollama`启动的大模型进行交互、管理模型和获取相关信息。其中两个`endpoint` 是最重要的，分别是：\n", "  - <font color=\"red\">**POST /api/generate**</font>\n", "  - <font color=\"red\">**POST /api/chat**</font>\n", "\n", "&emsp;&emsp;其他端点情况：\n", "  - POST /api/create   \n", "  - POST /api/tags\n", "  - POST /api/show\n", "  - POST /api/copy\n", "  - DELETE /api/delete\n", "  - POST /api/pull\n", "  - POST /api/push\n", "  - POST /api/embed\n", "  - GET /api/ps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. /api/chat 接口参数概览\n", "\n", "&emsp;&emsp;该接口使用提供的模型在聊天中生成下一条消息。与 `/api/generate` 的参数基本一致，但是在请求的参数上会根据聊天场景进行调整。主要调整的是：\n", "- 不再使用 `prompt` 参数，而是使用 `messages` 参数。\n", "- 新增了 `tools` 参数，用于支持工具调用。\n", "\n", "&emsp;&emsp;其可以使用的具体参数如下所示，"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>常规参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名       | 类型      | 描述                                                         |\n", "| ------------ | --------- | ------------------------------------------------------------ |\n", "| **model**    | *(必需)*  | 模型名称。                                                   |\n", "| <font color=\"red\">**messages**</font> | *(必需)*  | 聊天的消息，用于保持聊天记忆。                               |\n", "| <font color=\"red\">**tools**</font>    | *(可选)*  | JSON 中的工具列表，供模型使用（如果支持）。                 |\n", "\n", "</div>\n", "\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>消息对象字段</font></p>\n", "<div class=\"center\">\n", "\n", "| 字段名       | 描述                                                         |\n", "| ------------ | ------------------------------------------------------------ |\n", "| <font color=\"red\">**role**</font>     | 消息的角色，可以是 `system`、`user`、`assistant` 或 `tool`。 |\n", "| <font color=\"red\">**content**</font>  | 消息的内容。                                                 |\n", "| **images**   | *(可选)* 要在消息中包含的图像列表（适用于多模态模型，如 llava）。 |\n", "| **tool_calls** | *(可选)* 模型希望使用的 JSON 中的工具列表。               |\n", "\n", "</div>\n", "\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>高级参数 (可选)</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名       | 描述                                                         |\n", "| ------------ | ------------------------------------------------------------ |\n", "| **format**   | 返回响应的格式。格式可以是 `json` 或 JSON 模式。            |\n", "| <font color=\"red\">**options**</font>  | 文档中列出的其他模型参数，例如 `temperature`。              |\n", "| **stream**   | 如果为 `false`，响应将作为单个响应对象返回，而不是对象流。  |\n", "| <font color=\"red\">**keep_alive**</font> | 控制模型在请求后保持加载的时间（默认：5分钟）。           |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["其中，Options参数说明：\n", "\n", "| 参数名 | 描述 | 值类型 | 示例用法 |\n", "| --------------- | ------------------------------------------------------------ | ------ | ---------------------- |\n", "| mirostat | 启用 Mirostat 采样以控制困惑度。（默认：0，0 = 禁用，1 = Mirostat，2 = Mirostat 2.0） | int | mirostat 0 |\n", "| mirostat_eta| 影响算法对生成文本反馈的响应速度。较低的学习率会导致调整较慢，而较高的学习率会使算法更具响应性。（默认：0.1） | float | mirostat_eta 0.1 |\n", "| mirostat_tau| 控制输出的连贯性和多样性之间的平衡。较低的值会导致更集中和连贯的文本。（默认：5.0） | float | mirostat_tau 5.0 |\n", "| <font color=\"red\">num_ctx</font> | 设置用于生成下一个标记的上下文窗口大小。（默认：2048）, 影响的是模型可以一次记住的最大 token 数量。 | int | num_ctx 4096|\n", "| repeat_last_n| 设置模型回溯的范围以防止重复。（默认：64，0 = 禁用，-1 = num_ctx） | int | repeat_last_n 64 |\n", "| repeat_penalty| 设置惩罚重复的强度。较高的值（例如 1.5）会更强烈地惩罚重复，而较低的值（例如 0.9）会更宽松。（默认：1.1） | float | repeat_penalty 1.1 |\n", "| <font color=\"red\">temperature</font> | 模型的温度。增加温度会使模型的回答更具创造性。（默认：0.8） | float | temperature 0.7 |\n", "| seed | 设置用于生成的随机数种子。将其设置为特定数字将使模型对相同提示生成相同的文本。（默认：0） | int | seed 42 |\n", "| <font color=\"red\">stop</font> | 设置使用的停止序列。当遇到此模式时，LLM 将停止生成文本并返回。可以通过在 modelfile 中指定多个单独的停止参数来设置多个停止模式。 | string | stop \"AI assistant:\" |\n", "| <font color=\"red\">num_predict</font> | 生成文本时要预测的最大标记数。（默认：-1，无限生成）,影响模型最大可以生成的 token 数量。 | int | num_predict 42 |\n", "| top_k | 降低生成无意义文本的概率。较高的值（例如 100）会给出更多样化的答案，而较低的值（例如 10）会更保守。（默认：40） | int | top_k 40 |\n", "| top_p | 与 top-k 一起工作。较高的值（例如 0.95）会导致更具多样性的文本，而较低的值（例如 0.5）会生成更集中和保守的文本。（默认：0.9） | float | top_p 0.9 |\n", "| min_p | top_p 的替代方案，旨在确保质量和多样性之间的平衡。参数 p 表示考虑标记的最小概率，相对于最可能标记的概率。例如，p=0.05 时，最可能的标记概率为 0.9，值小于 0.045 的 logits 会被过滤掉。（默认：0.0） | float | min_p 0.05 |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. requests 调用方法"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp; `/api/chat` 依然还是可以`requests`库进行调用。如下所示："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:49:53.9497781Z\",\n", "  \"message\": {\n", "    \"role\": \"assistant\",\n", "    \"content\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是在快速了解这个概念，或者需要一段简洁的文字用于某个场景。\\n\\n用户没有指定长度或深度要求，所以“简短”应该是主观判断的范围。考虑到通用性，我决定提供基础定义、发展背景和核心能力三个层次的内容，在80-120字之间比较合适——既能概括要点又不会太冗长。\\n\\n这个查询很简单直接，但背后可能有几种情况：可能是学生写作业前查资料，也可能是职场人士需要快速科普素材。如果是前者可能还需要更学术化的表述；如果是后者则要突出实用性。不过既然用户没提具体要求，默认采用通用口径比较稳妥。\\n\\n在内容组织上，我决定先定义概念（人工智能是什么），再说明发展背景（为什么诞生），最后列举能力范围——这样符合认知逻辑：从本质到应用层层递进。\\n</think>\\n好的，这是一个关于人工智能的简短介绍：\\n\\n**人工智能 (Artificial Intelligence, AI)** 是计算机科学的一个分支，旨在创建能够模拟人类智能行为和思维方式的系统。它利用算法分析数据、识别模式，并基于这些能力做出决策或预测。\\n\\nAI 起源于对逻辑推理的研究，如今已广泛应用于机器翻译、图像识别、语音助手等众多领域，极大地提高了效率并创造了新的可能性。其核心目标是让机器能够学习、适应和解决复杂问题，模仿甚至超越人类在特定任务上的智能水平。\"\n", "  },\n", "  \"done_reason\": \"stop\",\n", "  \"done\": true,\n", "  \"total_duration\": 6321977900,\n", "  \"load_duration\": 27587600,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 5674100,\n", "  \"eval_count\": 306,\n", "  \"eval_duration\": 6288189400\n", "}\n"]}], "source": ["import requests\n", "import json\n", "\n", "# 设置 API 端点\n", "chat_url = \"http://192.168.0.130:11434/api/chat\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "chat_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",  # 消息角色，用户发送的消息\n", "            \"content\": \"请生成一个关于人工智能的简短介绍。\"  # 用户的消息内容\n", "        }\n", "    ],\n", "    \"tools\": [],  # 如果有工具可以在这里添加\n", "    \"stream\": False,  # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "}\n", "\n", "# 调用聊天接口\n", "response_chat = requests.post(chat_url, json=chat_payload)\n", "if response_chat.status_code == 200:\n", "    chat_response = response_chat.json()\n", "    print(\"生成响应:\", json.dumps(chat_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_chat.status_code, response_chat.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;返回的响应中包含以下参数，其对应的描述如下："]}, {"cell_type": "markdown", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>响应参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名                  | 描述                                                         |\n", "| ----------------------- | ------------------------------------------------------------ |\n", "| **total_duration**      | 单次响应花费的总时间                                          |\n", "| **load_duration**       | 加载模型花费的时间                                   |\n", "| **prompt_eval_count**   | 提示中的token数                                               |\n", "| **prompt_eval_duration**| 评估提示所花费的时间（以纳秒为单位）                                 |\n", "| **eval_count**          | 响应中的token数                                               |\n", "| **eval_duration**       | 生成响应的时间（以纳秒为单位）                              |\n", "| **context**             | 在此响应中使用的对话的编码，可以在下一个请求中发送以保持对话记忆 |\n", "| **response**            | 空响应是流的，如果未流式传输，则将包含完整的响应             |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;重点关注以下几个参数："]}, {"cell_type": "markdown", "metadata": {}, "source": ["- <font color=\"red\">**message**</font> "]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;在 `/chat` 接口中，返回的模型响应结果存放在 `message` 中， 同样对于 `DeepSeek-R1` 模型，`response` 字段中包含<think> 标签和正常文本，<think> 标签用于表示模型的思考过程或内部推理，而正常的文本则是模型生成的实际输出内容。注意：非推理类模型的返回结果中没有<think></think>标识。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是在快速了解这个概念，或者需要一段简洁的文字用于某个场景。\\n\\n用户没有指定长度或深度要求，所以“简短”应该是主观判断的范围。考虑到通用性，我决定提供基础定义、发展背景和核心能力三个层次的内容，在80-120字之间比较合适——既能概括要点又不会太冗长。\\n\\n这个查询很简单直接，但背后可能有几种情况：可能是学生写作业前查资料，也可能是职场人士需要快速科普素材。如果是前者可能还需要更学术化的表述；如果是后者则要突出实用性。不过既然用户没提具体要求，默认采用通用口径比较稳妥。\\n\\n在内容组织上，我决定先定义概念（人工智能是什么），再说明发展背景（为什么诞生），最后列举能力范围——这样符合认知逻辑：从本质到应用层层递进。\\n</think>\\n好的，这是一个关于人工智能的简短介绍：\\n\\n**人工智能 (Artificial Intelligence, AI)** 是计算机科学的一个分支，旨在创建能够模拟人类智能行为和思维方式的系统。它利用算法分析数据、识别模式，并基于这些能力做出决策或预测。\\n\\nAI 起源于对逻辑推理的研究，如今已广泛应用于机器翻译、图像识别、语音助手等众多领域，极大地提高了效率并创造了新的可能性。其核心目标是让机器能够学习、适应和解决复杂问题，模仿甚至超越人类在特定任务上的智能水平。'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_response[\"message\"]['content']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;可以通过简单的字符串操作来分离 <think> 标签中的思考内容和正常的文本内容，代码如下："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["思考内容:\n", " 嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是在快速了解这个概念，或者需要一段简洁的文字用于某个场景。\n", "\n", "用户没有指定长度或深度要求，所以“简短”应该是主观判断的范围。考虑到通用性，我决定提供基础定义、发展背景和核心能力三个层次的内容，在80-120字之间比较合适——既能概括要点又不会太冗长。\n", "\n", "这个查询很简单直接，但背后可能有几种情况：可能是学生写作业前查资料，也可能是职场人士需要快速科普素材。如果是前者可能还需要更学术化的表述；如果是后者则要突出实用性。不过既然用户没提具体要求，默认采用通用口径比较稳妥。\n", "\n", "在内容组织上，我决定先定义概念（人工智能是什么），再说明发展背景（为什么诞生），最后列举能力范围——这样符合认知逻辑：从本质到应用层层递进。\n", "\n", "正常内容:\n", " 好的，这是一个关于人工智能的简短介绍：\n", "\n", "**人工智能 (Artificial Intelligence, AI)** 是计算机科学的一个分支，旨在创建能够模拟人类智能行为和思维方式的系统。它利用算法分析数据、识别模式，并基于这些能力做出决策或预测。\n", "\n", "AI 起源于对逻辑推理的研究，如今已广泛应用于机器翻译、图像识别、语音助手等众多领域，极大地提高了效率并创造了新的可能性。其核心目标是让机器能够学习、适应和解决复杂问题，模仿甚至超越人类在特定任务上的智能水平。\n"]}], "source": ["# 提取 <think> 标签中的内容\n", "think_start = chat_response[\"message\"]['content'].find(\"<think>\")\n", "think_end = chat_response[\"message\"]['content'].find(\"</think>\")\n", "\n", "if think_start != -1 and think_end != -1:\n", "    think_content = chat_response[\"message\"]['content'][think_start + len(\"<think>\"):think_end].strip()\n", "else:\n", "    think_content = \"No think content found.\"\n", "\n", "# 提取正常的文本内容\n", "normal_content = chat_response[\"message\"]['content'][think_end + len(\"</think>\"):].strip()\n", "\n", "# 打印结果\n", "print(\"思考内容:\\n\", think_content)\n", "print(\"\\n正常内容:\\n\", normal_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;其他的重点参数和 `/generation` 参数使用方法也保持一致，示例代码如下："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:53:32.3587905Z\",\n", "  \"message\": {\n", "    \"role\": \"assistant\",\n", "    \"content\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。\\n\\n首先分析这个query的关键点：它明确要求“简短”，所以内容要简洁精炼，不能过于冗长。“人工智能”是核心主题，需要覆盖基本定义、发展背景和主要应用领域。\\n\\n接下来思考如何组织信息。开头应该先给出明确定义，接着提到历史起源以增加深度，然后列举关键子领域来展示广度，最后说明现实影响并避免夸大技术能力。\\n</think>\\n好的，这是一个关于人工智能的简短介绍：\\n\\n**人工智能（AI）** 是计算机科学的一个分支，旨在创造能够执行通常需要人类智能的任务的机器和系统。它的目标是研发出能模拟、延伸和扩展人类智慧的技术与应用。\\n\\n核心领域包括：\\n1.  **机器学习：** 让机器通过数据自主地学习和改进。\\n2.  **深度学习：** 使用多层神经网络处理复杂模式，尤其擅长图像识别、语音理解等。\\n3.  **自然语言处理：** 实现计算机对人类语言的理解与生成。\\n\\n人工智能正在快速发展，并已应用于我们生活的方方面面，例如智能搜索、推荐系统、自动驾驶、语音助手和医疗影像分析。它潜力巨大，但也需要谨慎地应对相关的伦理和社会挑战。\"\n", "  },\n", "  \"done_reason\": \"stop\",\n", "  \"done\": true,\n", "  \"total_duration\": 7666153700,\n", "  \"load_duration\": 2341396900,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 126680700,\n", "  \"eval_count\": 264,\n", "  \"eval_duration\": 5197573200\n", "}\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "chat_url = \"http://192.168.0.130:11434/api/chat\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "chat_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",  # 消息角色，用户发送的消息\n", "            \"content\": \"请生成一个关于人工智能的简短介绍。\"  # 用户的消息内容\n", "        }\n", "    ],\n", "    \"tools\": [],  # 如果有工具可以在这里添加\n", "    \"stream\": False,  # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "    \"keep_alive\": \"10m\",   # 设置模型在请求后保持加载的时间\n", "    \"options\":{\n", "        \"temperature\": 0.7,   \n", "        \"num_ctx\":2048,\n", "        \"num_predict\": 4096,\n", "    }\n", "}\n", "\n", "# 调用聊天接口\n", "response_chat = requests.post(chat_url, json=chat_payload)\n", "if response_chat.status_code == 200:\n", "    chat_response = response_chat.json()\n", "    print(\"生成响应:\", json.dumps(chat_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_chat.status_code, response_chat.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;流式输出代码也要针对`/chat`接口的返回响应格式做略微的修改："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的新手，想快速了解AI的基本概念和应用。\n", "\n", "这个查询很直接，说明用户需要的是清晰简洁的基础知识概述。考虑到“简短”的要求，回复应该控制在核心要点上，避免过于技术性的细节。不过还是要确保信息准确，不能为了简单而牺牲质量。\n", "\n", "人工智能是个很大的话题，从定义到发展再到影响都有很多层面可以展开。但既然用户要的是简介，那就聚焦在这几个关键点：什么是AI、它如何工作、主要应用领域和潜在风险。这样既能满足查询需求，又不会显得太浅显或太深奥。\n", "\n", "用户可能对AI只有模糊的印象，所以要用通俗易懂的语言解释专业概念。比如“机器学习”这种术语需要简单说明是让计算机从数据中自主学习。\n", "\n", "回复结构可以按照逻辑顺序：先定义人工智能这个整体概念，然后分解到不同分支（如机器学习、深度学习），再讲应用领域，最后提一下挑战和影响。这样用户能有一个系统的认知框架。\n", "\n", "考虑到现代人注意力有限，在开头用一句话概括是最有效的。接着可以用“尽管……但”的转折句解释AI的复杂性，让读者知道虽然名字吓人，但实际上可以理解成计算机科学的不同分支。\n", "</think>\n", "好的，这是一个关于人工智能的简短介绍：\n", "\n", "**人工智能（Artificial Intelligence, AI）** 是一门研究如何使计算机模拟人类智能行为和思维过程的科学技术。它的目标是创建能够执行通常需要人类智慧的任务的机器系统。\n", "\n", "核心在于让机器具备学习、推理、感知、理解语言以及解决复杂问题的能力，从而在某些领域甚至超越人类专家。\n", "\n", "AI主要分为两大类：\n", "\n", "1.  **弱人工智能：** 指专注于执行特定任务的人工智能，如语音识别或图像识别。\n", "2.  **强人工智能（目前尚在探索中）：** 指能够像人类一样理解、学习和应用知识的通用人工智能系统。\n", "\n", "实现AI的关键技术包括机器学习（ML），特别是深度学习（DL）。这些技术依赖于数据，并让计算机通过分析数据来自主地学习模式和做出决策，而不是用明确硬化的指令编程。例如：\n", "\n", "*   **自然语言处理：** 让机器理解和生成人类语言。\n", "*   **计算机视觉：** 让机器“看见”并理解图像内容。\n", "\n", "人工智能正在深刻改变我们的生活、工作和社会结构，并在医疗、金融、交通、教育等多个领域展现出巨大潜力，但也伴随着伦理和隐私等挑战。"]}], "source": ["import requests  # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",\n", "    \"stream\": True,  # 启用流式输出\n", "    \"options\": {\n", "        \"temperature\": 0.6,\n", "        \"keep_alive\": \"10m\"\n", "    }\n", "}\n", "\n", "# 调用生成接口\n", "with requests.post(generate_url, json=generate_payload, stream=True) as response_generate:\n", "    if response_generate.status_code == 200:\n", "        # 逐行读取流式响应\n", "        for line in response_generate.iter_lines():\n", "            if line:  # 确保行不为空\n", "                # 解析 JSON 响应\n", "                generate_response = json.loads(line)\n", "                \n", "                # 提取并打印 response 字段\n", "                if \"response\" in generate_response:\n", "                    print(generate_response[\"response\"], end='')  # end='' 防止换行\n", "                if generate_response.get(\"done\", False):\n", "                    break  # 如果 done 为 True，结束循环\n", "    else:\n", "        print(\"生成请求失败:\", response_generate.status_code, response_generate.text)"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}