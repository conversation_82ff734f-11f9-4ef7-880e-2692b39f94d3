! pip install langchain langchain_ollama langchain-deepseek dotenv -i https://pypi.tuna.tsinghua.edu.cn/simple

def split_answer(result):
    # print(result.find("<think>\n")+len("<think>\n"), result.find("\n</think>"))

    think_start = result.find("<think>\n") + len("<think>\n")
    think_end = result.find("\n</think>")
    think_content = result[think_start:think_end].strip()
    answer_content = result[think_end + len("\n</think>"):].strip()
    return think_content, answer_content

from langchain_ollama import ChatOllama
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser
model = ChatOllama(model="deepseek-r1:7b", base_url="http://localhost:11434",temperature=0.6)

city_prompt = PromptTemplate(
    input_variables=["city"],
    template=(
        "你是一个中国地名标准化助手。"
        "用户可能输入城市的中文、拼音或英文名称，也可能带有“市”等后缀。"
        "请将用户输入的中国省份或城市名称，模糊匹配为标准的城市名称，只输出标准名称，不要多余输出。"
        "例如：输入“成都”、“成都市”、“Chengdu”，都输出“成都市”。"
        "输入“广东省”，输出“广东省”。"
        "输入“香港”，输出“香港特别行政区”。"
        "输入：{city}"
    )
)

str_output_parser = StrOutputParser()

city_chain = city_prompt | model | str_output_parser
_, answer = split_answer(city_chain.invoke({"city": "澳门"}))
print(answer)

city = answer.strip()
city

from langchain.chat_models import init_chat_model
import os
from dotenv import load_dotenv 
load_dotenv(override=True)

DeepSeek_API_KEY = os.getenv("DEEPSEEK_API_KEY")
# print(DeepSeek_API_KEY)  # 可以通过打印查看
model = init_chat_model(model="deepseek-chat", model_provider="deepseek")  
city_prompt = PromptTemplate(
    input_variables=["city"],
    template=(
        "你是一个中国地名标准化助手。"
        "用户可能输入城市的中文、拼音或英文名称，也可能带有“市”等后缀。"
        "请将用户输入的中国省份或城市名称，模糊匹配为标准的城市名称，只输出标准名称，不要多余输出。"
        "例如：输入“成都”、“成都市”、“Chengdu”，都输出“成都市”。"
        "输入“广东省”，输出“广东省”。"
        "输入“香港”，输出“香港特别行政区”。"
        "输入：{city}"
    )
)

str_output_parser = StrOutputParser()

city_chain = city_prompt | model | str_output_parser
user_input = input("请输入城市名称：")
print(city_chain.invoke({"city": user_input}))
city = city_chain.invoke({"city": user_input})

city

!pip install -qU openpyxl pandas -i https://pypi.tuna.tsinghua.edu.cn/simple

import numpy as np
import pandas as pd

dataset = pd.read_excel('AMap_adcode_citycode.xlsx', engine='openpyxl')

pd.set_option('max_colwidth',200)

dataset.head(5)

dataset.info()

city_map = {}
for idx, row in dataset.iterrows():
    city_map[row['中文名']] = row['adcode']


# for idx, row in dataset.iterrows():
#     print(row['中文名'], row['adcode'])  # 假设有'中文名'和'adcode'列

print(len(city_map))

city_adcode = ""

if city in city_map:
    city_adcode = city_map[city]
    print(f"城市：{city}，adcode：{city_adcode}")

import requests, json

def get_weather(adcode):
    """
    查询即时天气函数
    :param adcode: 城市编码（如510100表示成都）
    :return: 返回天气信息的JSON字符串
    """
    api = os.getenv("GAODEWEATHER_API_Key")  # 你的高德API key
    # print(f"使用的高德API Key: {api}")
    url = f"https://restapi.amap.com/v3/weather/weatherInfo?city={adcode}&key={api}"
    response = requests.get(url)
    data = response.json()
    return json.dumps(data, ensure_ascii=False)

data = get_weather(city_adcode)# 成都
print(data)

data_dict = json.loads(data)
print(data_dict["lives"])
print("时间：", data_dict["lives"][0]["reporttime"])
print("城市：", data_dict["lives"][0]["city"])
print("天气：", data_dict["lives"][0]["weather"])
print("温度：", data_dict["lives"][0]["temperature_float"])
print("风向：", data_dict["lives"][0]["winddirection"])
print("湿度：", data_dict["lives"][0]["humidity_float"])