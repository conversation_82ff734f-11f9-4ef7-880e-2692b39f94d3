{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "[Runable组件API]([https://python.langchain.com/api_reference/core/runnables/langchain_core.runnables.base.Runnable.html])", "id": "beb73753b73fda97"}, {"metadata": {}, "cell_type": "markdown", "source": ["### 调用一个运行接口\n", "`Runnable.invoke()` / `Runnable.ainvoke()`"], "id": "5261440a730bab66"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:10:36.991705Z", "start_time": "2025-07-14T07:10:36.470304Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "runnable = RunnableLambda(lambda x: str(x))\n", "runnable.invoke(5)\n", "\n", "# Async variant:\n", "# await runnable.ainvoke(5)"], "id": "a9acd9d17cec3643", "outputs": [{"data": {"text/plain": ["'5'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": ["### 批量运行\n", "`Runnable.batch()` / `Runnable.abatch()`"], "id": "51ff40f6cb1b9d14"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:19:05.384950Z", "start_time": "2025-07-14T07:19:05.377950Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "runnable = RunnableLambda(lambda x: str(x))\n", "runnable.batch([7, 8, 9])\n", "\n", "# Async variant:\n", "# await runnable.abatch([7, 8, 9])"], "id": "32ab7c8ac357751c", "outputs": [{"data": {"text/plain": ["['7', '8', '9']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": ["### 流式运行\n", "`Runnable.stream()` / `Runnable.astream()`"], "id": "f1c5140a0d96486b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:19:54.791586Z", "start_time": "2025-07-14T07:19:54.787597Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "\n", "def func(x):\n", "    for y in x:\n", "        yield str(y)\n", "\n", "\n", "runnable = RunnableLambda(func)\n", "\n", "for chunk in runnable.stream(range(5)):\n", "    print(chunk)\n", "\n", "# Async variant:\n", "# async for chunk in await runnable.astream(range(5)):\n", "#     print(chunk)"], "id": "dd6dc563d12f17d3", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": ["### 组合运行\n", "管道操作符 `|`"], "id": "351f575358156680"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:25:09.220187Z", "start_time": "2025-07-14T07:25:09.213528Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "runnable2 = RunnableLambda(lambda x: [x] * 2)\n", "\n", "chain = runnable1 | runnable2\n", "\n", "chain.invoke(2)"], "id": "a3d3a3ddb599b87c", "outputs": [{"data": {"text/plain": ["[{'foo': 2}, {'foo': 2}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {}, "cell_type": "markdown", "source": ["### 并行调用运行\n", "`RunnableParallel`"], "id": "65d4417841e33b90"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:25:39.884215Z", "start_time": "2025-07-14T07:25:39.877324Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnableParallel\n", "\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "runnable2 = RunnableLambda(lambda x: [x] * 2)\n", "\n", "chain = RunnableParallel(first=runnable1, second=runnable2)\n", "\n", "chain.invoke(2)"], "id": "f673f4ce27a521e", "outputs": [{"data": {"text/plain": ["{'first': {'foo': 2}, 'second': [2, 2]}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": ["### 将任何函数转换为可运行\n", "`RunnableLambda`"], "id": "5e5988f235a0c2c9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:26:20.301850Z", "start_time": "2025-07-14T07:26:20.296889Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "\n", "def func(x):\n", "    return x + 5\n", "\n", "\n", "runnable = RunnableLambda(func)\n", "runnable.invoke(2)"], "id": "a875063b4b7e4ac3", "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "execution_count": 9}, {"metadata": {}, "cell_type": "markdown", "source": ["### 合并输入和输出字典\n", "`RunnablePassthrough.assign`"], "id": "206dd4e0b05c4c5a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:26:42.233933Z", "start_time": "2025-07-14T07:26:42.219458Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnablePassthrough\n", "\n", "runnable1 = RunnableLambda(lambda x: x[\"foo\"] + 7)\n", "\n", "chain = RunnablePassthrough.assign(bar=runnable1)\n", "\n", "chain.invoke({\"foo\": 10})"], "id": "1c4b0faa929e85d7", "outputs": [{"data": {"text/plain": ["{'foo': 10, 'bar': 17}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": ["### 将输入字典包含在输出字典中\n", "`RunnablePassthrough`"], "id": "6a8df68b23f7b629"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:27:07.582984Z", "start_time": "2025-07-14T07:27:07.576535Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON>,\n", "    RunnablePassthrough,\n", ")\n", "\n", "runnable1 = RunnableLambda(lambda x: x[\"foo\"] + 7)\n", "\n", "chain = RunnableParallel(bar=runnable1, baz=RunnablePassthrough())\n", "\n", "chain.invoke({\"foo\": 10})"], "id": "b8b48419303e7d9e", "outputs": [{"data": {"text/plain": ["{'bar': 17, 'baz': {'foo': 10}}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": ["### 添加默认调用参数\n", "`Runnable.bind`"], "id": "6fe9f3f6770c6ed0"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:36:38.392096Z", "start_time": "2025-07-14T07:36:38.386768Z"}}, "cell_type": "code", "source": ["from typing import Optional\n", "from langchain_core.runnables import RunnableLambda\n", "\n", "# 1. 定义一个 Python 函数 func\n", "def func(main_arg: dict, other_arg: Optional[str] = None) -> dict:\n", "    # 这个函数接收两个参数：\n", "    # - main_arg: 一个字典，这是主要的输入。\n", "    # - other_arg: 一个可选的字符串，默认为 None。\n", "    # 函数被类型标注为返回一个字典。\n", "    if other_arg: # 检查 other_arg 是否存在（非 None 且非空字符串）\n", "        return {**main_arg, **{\"foo\": other_arg}}  # 如果 other_arg 有值，则创建一个新的字典并返回\n", "    return main_arg  # 否则（如果 other_arg 为 None 或空字符串），直接返回 main_arg\n", "\n", "# 2. 将 func 转换为 LangChain 的 Runnable 组件\n", "runnable1 = RunnableLambda(func)  # runnable1 现在是一个可执行的 LangChain 组件，它包装了 func 函数。\n", "\n", "# 3. 使用 .bind() 方法绑定参数\n", "bound_runnable1 = runnable1.bind(other_arg=\"bye\")\n", "# .bind() 方法是 LCEL 的一个重要特性。\n", "# 它的作用是创建一个新的 Runnable (bound_runnable1)，\n", "# 这个新的 Runnable 在被调用时，other_arg 参数会被自动设置为 \"bye\"。\n", "# 也就是说，你为 func 函数的 other_arg 参数设置了一个默认的、固定的值，\n", "# 每次调用 bound_runnable1 时，这个值都会被使用。\n", "\n", "# 4. 调用绑定后的 Runnable\n", "result = bound_runnable1.invoke({\"bar\": \"hello\"})\n", "# 这里调用 bound_runnable1，并传入一个字典 {\"bar\": \"hello\"} 作为 main_arg。\n", "\n", "# 内部执行流程：\n", "# 1. func 函数被调用，其参数如下：\n", "#    - main_arg = {\"bar\": \"hello\"} (来自 invoke 的输入)\n", "#    - other_arg = \"bye\" (来自 .bind() 的绑定)\n", "# 2. 进入 func 函数体：\n", "#    - if other_arg: 条件为 True (因为 \"bye\" 是一个真值)。\n", "#    - 执行 return {**main_arg, **{\"foo\": other_arg}}\n", "#      - 这将展开为：{**{\"bar\": \"hello\"}, **{\"foo\": \"bye\"}}\n", "#      - 最终合并成一个新的字典 {\"bar\": \"hello\", \"foo\": \"bye\"}\n", "# 3. func 返回 {\"bar\": \"hello\", \"foo\": \"bye\"}\n", "# 4. bound_runnable1.invoke 的结果就是这个字典。\n", "\n", "print(result) # 输出: {'bar': 'hello', 'foo': 'bye'}"], "id": "46f3ff36ebe93cf9", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'bar': 'hello', 'foo': 'bye'}\n"]}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["### 添加后备选项\n", "`Runnable.with_fallbacks`"], "id": "82e3de8ec55eaf07"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:28:13.209329Z", "start_time": "2025-07-14T07:28:13.199834Z"}}, "cell_type": "code", "source": ["# 添加后备选项\n", "from langchain_core.runnables import RunnableLambda\n", "\n", "# 定义第一个 Runnable (runnable1)，它期望输入是字符串，否则会报错\n", "runnable1 = RunnableLambda(lambda x: x + \"foo\")\n", "\n", "# 定义第二个 Runnable (runnable2)，作为后备。\n", "# 它首先将输入转换为字符串，使其更健壮，不易报错。\n", "runnable2 = RunnableLambda(lambda x: str(x) + \"foo\")\n", "\n", "# 构建链：将 runnable2 设置为 runnable1 的后备选项。\n", "# 这意味着如果 runnable1 执行失败（抛出异常），系统会自动尝试执行 runnable2。\n", "chain = runnable1.with_fallbacks([runnable2])\n", "\n", "# 调用链并传入一个整数 5。\n", "# 预期行为：\n", "# 1. runnable1 尝试执行 5 + \"foo\"，这将导致 TypeError。\n", "# 2. 由于 runnable1 失败，后备机制启动，转而执行 runnable2。\n", "# 3. runnable2 接收 5，将其转换为 \"5\"，然后拼接 \"foo\"，结果为 \"5foo\"。\n", "# 4. 链返回 \"5foo\"。\n", "chain.invoke(5)"], "id": "9efb311908fa8ce", "outputs": [{"data": {"text/plain": ["'5foo'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "execution_count": 13}, {"metadata": {}, "cell_type": "markdown", "source": ["### 添加重试\n", "`Runnable.with_retry`"], "id": "a8f640fae002af11"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:49:08.865646Z", "start_time": "2025-07-14T07:49:07.801246Z"}}, "cell_type": "code", "source": ["# 添加重试\n", "from langchain_core.runnables import RunnableLambda\n", "\n", "# 定义一个全局计数器，用于跟踪 func 被调用的次数\n", "counter = -1\n", "\n", "\n", "# 定义一个模拟可能失败的函数\n", "def func(x):\n", "    global counter\n", "    counter += 1  # 每次调用时递增计数器\n", "    print(f\"attempt with {counter=}\")  # 打印当前是第几次尝试\n", "    # 第一次尝试时 (counter=0) 会发生 ZeroDivisionError\n", "    # 之后的尝试 (counter >= 1) 将会成功\n", "    return x / counter\n", "\n", "\n", "# 将 func 函数封装成一个 Runnable，并为其添加重试机制。\n", "# with_retry(stop_after_attempt=2) 表示如果 func 失败，\n", "# 它会最多尝试重新执行 1 次 (总共执行 2 次：原始尝试 + 1 次重试)。\n", "chain = RunnableLambda(func).with_retry(stop_after_attempt=2)\n", "\n", "# 调用链并传入输入 2。\n", "# 预期流程：\n", "# 1. 第一次尝试 (counter=0): 2 / 0 导致 ZeroDivisionError。\n", "# 2. 触发重试机制。\n", "# 3. 第二次尝试 (counter=1): 2 / 1 成功，返回 2.0。\n", "# 链的最终结果将是 2.0。\n", "chain.invoke(2)"], "id": "72317cff5993ee5e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["attempt with counter=0\n", "attempt with counter=1\n"]}, {"data": {"text/plain": ["2.0"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}, {"metadata": {}, "cell_type": "markdown", "source": ["### 配置可运行执行\n", "`RunnableConfig`"], "id": "9b2a2eee7e8f46af"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:55:30.145069Z", "start_time": "2025-07-14T07:55:30.137400Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnableParallel\n", "\n", "# 定义第一个 RunnableLambda：它接收一个输入 x，并将其封装在一个字典的 \"foo\" 键下。\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "\n", "# 定义第二个 RunnableLambda：它接收一个输入 x，并返回一个包含 x 两次的列表。\n", "runnable2 = RunnableLambda(lambda x: [x] * 2)\n", "\n", "# 定义第三个 RunnableLambda：它接收一个输入 x，并将其转换为字符串。\n", "runnable3 = RunnableLambda(lambda x: str(x))\n", "\n", "# 使用 RunnableParallel 将上述三个 Runnable 并行组合。\n", "# - first=runnable1：将 runnable1 的结果作为最终输出字典的 \"first\" 键的值。\n", "# - second=runnable2：将 runnable2 的结果作为最终输出字典的 \"second\" 键的值。\n", "# - third=runnable3：将 runnable3 的结果作为最终输出字典的 \"third\" 键的值。\n", "# 当 chain 被调用时，它的输入会同时传递给 runnable1, runnable2, runnable3。\n", "chain = RunnableParallel(first=runnable1, second=runnable2, third=runnable3)\n", "\n", "# 调用链并传入输入 7。\n", "# config={\"max_concurrency\": 2}：这是一个配置选项，限制了并行执行的最大并发数。\n", "# 尽管有三个 runnable 并行，但同一时间最多只有两个会运行。\n", "# 预期结果：\n", "# - runnable1(7) 返回 {\"foo\": 7}\n", "# - runnable2(7) 返回 [7, 7]\n", "# - runnable3(7) 返回 \"7\"\n", "# 最终链的输出将是一个字典，包含这三个结果：\n", "# {'first': {'foo': 7}, 'second': [7, 7], 'third': '7'}\n", "chain.invoke(7, config={\"max_concurrency\": 2})"], "id": "728b2f7208a67bba", "outputs": [{"data": {"text/plain": ["{'first': {'foo': 7}, 'second': [7, 7], 'third': '7'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["### 将默认配置添加到可运行项\n", "`Runnable.with_config`"], "id": "22602700387a5fa"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:56:13.522049Z", "start_time": "2025-07-14T07:56:13.513211Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnableParallel\n", "\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "runnable2 = RunnableLambda(lambda x: [x] * 2)\n", "runnable3 = RunnableLambda(lambda x: str(x))\n", "\n", "chain = RunnableParallel(first=runnable1, second=runnable2, third=runnable3)\n", "configured_chain = chain.with_config(max_concurrency=2)\n", "\n", "chain.invoke(7)"], "id": "721697c79f6cfef5", "outputs": [{"data": {"text/plain": ["{'first': {'foo': 7}, 'second': [7, 7], 'third': '7'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "execution_count": 18}, {"metadata": {}, "cell_type": "markdown", "source": ["### c使可运行属性可配置\n", "`Runnable.with_configurable_fields`"], "id": "e9ca0e1f5cb5ff81"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:57:18.482367Z", "start_time": "2025-07-14T07:57:18.463376Z"}}, "cell_type": "code", "source": ["from typing import Any, Optional\n", "\n", "from langchain_core.runnables import (\n", "    Configu<PERSON><PERSON><PERSON>,\n", "    RunnableConfig,\n", "    RunnableSerializable,\n", ")\n", "\n", "\n", "class FooRunnable(RunnableSerializable[dict, dict]):\n", "    output_key: str\n", "\n", "    def invoke(\n", "        self, input: Any, config: Optional[RunnableConfig] = None, **kwargs: Any\n", "    ) -> list:\n", "        return self._call_with_config(self.subtract_seven, input, config, **kwargs)\n", "\n", "    def subtract_seven(self, input: dict) -> dict:\n", "        return {self.output_key: input[\"foo\"] - 7}\n", "\n", "\n", "runnable1 = FooRunnable(output_key=\"bar\")\n", "configurable_runnable1 = runnable1.configurable_fields(\n", "    output_key=ConfigurableField(id=\"output_key\")\n", ")\n", "\n", "configurable_runnable1.invoke(\n", "    {\"foo\": 10}, config={\"configurable\": {\"output_key\": \"not bar\"}}\n", ")"], "id": "891d31d6e5bb9f1", "outputs": [{"data": {"text/plain": ["{'not bar': 3}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {}, "cell_type": "markdown", "source": ["### 使链组件可配置\n", "`Runnable.with_configurable_alternatives`"], "id": "1e70d4103c3fb9d1"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:57:47.018476Z", "start_time": "2025-07-14T07:57:47.006559Z"}}, "cell_type": "code", "source": ["from typing import Any, Optional\n", "\n", "from langchain_core.runnables import RunnableConfig, RunnableLambda, RunnableParallel\n", "\n", "\n", "class ListRunnable(RunnableSerializable[Any, list]):\n", "    def invoke(\n", "        self, input: Any, config: Optional[RunnableConfig] = None, **kwargs: Any\n", "    ) -> list:\n", "        return self._call_with_config(self.listify, input, config, **kwargs)\n", "\n", "    def listify(self, input: Any) -> list:\n", "        return [input]\n", "\n", "\n", "class StrRunnable(RunnableSerializable[Any, str]):\n", "    def invoke(\n", "        self, input: Any, config: Optional[RunnableConfig] = None, **kwargs: Any\n", "    ) -> list:\n", "        return self._call_with_config(self.strify, input, config, **kwargs)\n", "\n", "    def strify(self, input: Any) -> str:\n", "        return str(input)\n", "\n", "\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "\n", "configurable_runnable = ListRunnable().configurable_alternatives(\n", "    ConfigurableField(id=\"second_step\"), default_key=\"list\", string=StrRunnable()\n", ")\n", "chain = runnable1 | configurable_runnable\n", "\n", "chain.invoke(7, config={\"configurable\": {\"second_step\": \"string\"}})"], "id": "b8bed956d314780e", "outputs": [{"data": {"text/plain": ["\"{'foo': 7}\""]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {}, "cell_type": "markdown", "source": "### 根据输入动态构建链", "id": "61595cd807d40754"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T07:58:07.246668Z", "start_time": "2025-07-14T07:58:07.239678Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda, RunnableParallel\n", "\n", "runnable1 = RunnableLambda(lambda x: {\"foo\": x})\n", "runnable2 = RunnableLambda(lambda x: [x] * 2)\n", "\n", "chain = RunnableLambda(lambda x: runnable1 if x > 6 else runnable2)\n", "\n", "chain.invoke(7)"], "id": "bf7bb36bdda1d9f1", "outputs": [{"data": {"text/plain": ["{'foo': 7}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "execution_count": 21}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}