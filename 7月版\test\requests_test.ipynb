{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4afd34be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 GET 请求 ---\n", "GET 请求状态码: 200\n", "GET 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"args\": {\n", "    \"key1\": \"value1\",\n", "    \"key2\": \"value2\"\n", "  },\n", "  \"headers\": {\n", "    \"Accept\": \"*/*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"python-requests/2.32.4\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-689066fa-1cdd415d35929f66747bf73e\"\n", "  },\n", "  \"origin\": \"**************\",\n", "  \"url\": \"https://httpbin.org/get?key1=value1&key2=value2\"\n", "}\n", "------------------------------\n"]}], "source": ["import requests\n", "import json\n", "\n", "# ==============================================================================\n", "# 1. 发送 GET 请求\n", "# ==============================================================================\n", "# GET 是最常见的请求类型，用于从服务器获取数据。\n", "print(\"--- 测试 GET 请求 ---\")\n", "try:\n", "    # 附带参数的 GET 请求\n", "    params = {'key1': 'value1', 'key2': 'value2'}\n", "    response_get = requests.get('https://httpbin.org/get', params=params)\n", "    \n", "    # 检查响应状态码\n", "    print(f\"GET 请求状态码: {response_get.status_code}\")\n", "    \n", "    # 检查是否成功（200 OK）\n", "    if response_get.status_code == 200:\n", "        print(\"GET 请求成功！\")\n", "        # 打印响应内容（JSON 格式）\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_get.json(), indent=2))\n", "        \n", "except requests.exceptions.RequestException as e:\n", "    print(f\"GET 请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 2, "id": "704acffd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 POST 请求 ---\n", "POST 请求状态码: 200\n", "POST 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"args\": {},\n", "  \"data\": \"\",\n", "  \"files\": {},\n", "  \"form\": {\n", "    \"job\": \"AI assistant\",\n", "    \"name\": \"<PERSON>\"\n", "  },\n", "  \"headers\": {\n", "    \"Accept\": \"*/*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Content-Length\": \"28\",\n", "    \"Content-Type\": \"application/x-www-form-urlencoded\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"python-requests/2.32.4\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-6890672a-23719cc51d80ef5c04dda61c\"\n", "  },\n", "  \"json\": null,\n", "  \"origin\": \"**************\",\n", "  \"url\": \"https://httpbin.org/post\"\n", "}\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 2. 发送 POST 请求\n", "# ==============================================================================\n", "# POST 请求通常用于向服务器提交数据，例如创建新资源。\n", "print(\"--- 测试 POST 请求 ---\")\n", "try:\n", "    # 提交表单数据\n", "    data_post = {'name': '<PERSON>', 'job': 'AI assistant'}\n", "    response_post = requests.post('https://httpbin.org/post', data=data_post)\n", "\n", "    print(f\"POST 请求状态码: {response_post.status_code}\")\n", "    if response_post.status_code == 200:\n", "        print(\"POST 请求成功！\")\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_post.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"POST 请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 3, "id": "6b5e1544", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 POST JSON 请求 ---\n", "POST JSON 请求状态码: 200\n", "POST JSON 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"args\": {},\n", "  \"data\": \"{\\\"id\\\": 123, \\\"status\\\": \\\"active\\\"}\",\n", "  \"files\": {},\n", "  \"form\": {},\n", "  \"headers\": {\n", "    \"Accept\": \"*/*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Content-Length\": \"31\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"python-requests/2.32.4\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-68906763-6dfc77eb63c45a802fed7097\"\n", "  },\n", "  \"json\": {\n", "    \"id\": 123,\n", "    \"status\": \"active\"\n", "  },\n", "  \"origin\": \"**************\",\n", "  \"url\": \"https://httpbin.org/post\"\n", "}\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 3. 发送带 JSON 数据的 POST 请求\n", "# ==============================================================================\n", "# 使用 json 参数可以更方便地发送 JSON 格式的数据。\n", "print(\"--- 测试 POST JSON 请求 ---\")\n", "try:\n", "    json_data = {'id': 123, 'status': 'active'}\n", "    response_post_json = requests.post('https://httpbin.org/post', json=json_data)\n", "    \n", "    print(f\"POST JSON 请求状态码: {response_post_json.status_code}\")\n", "    if response_post_json.status_code == 200:\n", "        print(\"POST JSON 请求成功！\")\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_post_json.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"POST JSON 请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 4, "id": "90bbca2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 PUT 请求 ---\n", "PUT 请求状态码: 200\n", "PUT 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"args\": {},\n", "  \"data\": \"\",\n", "  \"files\": {},\n", "  \"form\": {\n", "    \"name\": \"Updated Gemini\"\n", "  },\n", "  \"headers\": {\n", "    \"Accept\": \"*/*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Content-Length\": \"19\",\n", "    \"Content-Type\": \"application/x-www-form-urlencoded\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"python-requests/2.32.4\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-68906947-01f86cf02519ec9100c0d7fe\"\n", "  },\n", "  \"json\": null,\n", "  \"origin\": \"**************\",\n", "  \"url\": \"https://httpbin.org/put\"\n", "}\n", "------------------------------\n", "--- 测试 DELETE 请求 ---\n", "DELETE 请求状态码: 200\n", "DELETE 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"args\": {},\n", "  \"data\": \"\",\n", "  \"files\": {},\n", "  \"form\": {},\n", "  \"headers\": {\n", "    \"Accept\": \"*/*\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Content-Length\": \"0\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"python-requests/2.32.4\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-68906948-185cf175717b672e3361fb13\"\n", "  },\n", "  \"json\": null,\n", "  \"origin\": \"**************\",\n", "  \"url\": \"https://httpbin.org/delete\"\n", "}\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 4. 发送 PUT 请求\n", "# ==============================================================================\n", "# PUT 请求通常用于更新服务器上的现有资源。\n", "print(\"--- 测试 PUT 请求 ---\")\n", "try:\n", "    data_put = {'name': 'Updated Gemini'}\n", "    response_put = requests.put('https://httpbin.org/put', data=data_put)\n", "\n", "    print(f\"PUT 请求状态码: {response_put.status_code}\")\n", "    if response_put.status_code == 200:\n", "        print(\"PUT 请求成功！\")\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_put.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"PUT 请求失败: {e}\")\n", "print(\"-\" * 30)\n", "\n", "# ==============================================================================\n", "# 5. 发送 DELETE 请求\n", "# ==============================================================================\n", "# DELETE 请求用于从服务器上删除资源。\n", "print(\"--- 测试 DELETE 请求 ---\")\n", "try:\n", "    response_delete = requests.delete('https://httpbin.org/delete')\n", "    \n", "    print(f\"DELETE 请求状态码: {response_delete.status_code}\")\n", "    if response_delete.status_code == 200:\n", "        print(\"DELETE 请求成功！\")\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_delete.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"DELETE 请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 5, "id": "1b43dd84", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试带自定义 Headers 的请求 ---\n", "Headers 请求状态码: 200\n", "Headers 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"headers\": {\n", "    \"Accept\": \"application/json\",\n", "    \"Accept-Encoding\": \"gzip, deflate, zstd\",\n", "    \"Host\": \"httpbin.org\",\n", "    \"User-Agent\": \"My Custom User Agent\",\n", "    \"X-Amzn-Trace-Id\": \"Root=1-6890773a-5c9eef9b77bb9a093a8010df\"\n", "  }\n", "}\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 6. 设置请求头（Headers）\n", "# ==============================================================================\n", "# 自定义 Headers 可以用于身份验证、指定数据类型等。\n", "print(\"--- 测试带自定义 Headers 的请求 ---\")\n", "try:\n", "    headers = {\n", "        'User-Agent': 'My Custom User Agent',\n", "        'Accept': 'application/json'\n", "    }\n", "    response_headers = requests.get('https://httpbin.org/headers', headers=headers)\n", "    \n", "    print(f\"Headers 请求状态码: {response_headers.status_code}\")\n", "    if response_headers.status_code == 200:\n", "        print(\"Headers 请求成功！\")\n", "        print(\"响应 JSON 内容:\")\n", "        print(json.dumps(response_headers.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"Headers 请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 6, "id": "72fdd48c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试请求超时 ---\n", "超时请求状态码: 200\n", "请求成功，没有超时。\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 7. 设置请求超时（Timeout）\n", "# ==============================================================================\n", "# timeout 参数可以防止程序因网络问题长时间阻塞。\n", "print(\"--- 测试请求超时 ---\")\n", "try:\n", "    # 设置一个 5 秒的超时\n", "    response_timeout = requests.get('https://httpbin.org/delay/2', timeout=5)\n", "    print(f\"超时请求状态码: {response_timeout.status_code}\")\n", "    print(\"请求成功，没有超时。\")\n", "    \n", "    # 尝试设置一个短于延迟时间的超时，会引发异常\n", "    # requests.get('https://httpbin.org/delay/5', timeout=2)\n", "\n", "except requests.exceptions.Timeout:\n", "    print(\"请求超时！这正是我们预期的行为。\")\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"请求失败: {e}\")\n", "print(\"-\" * 30)"]}, {"cell_type": "code", "execution_count": 7, "id": "93ea9dd8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 Session 会话 ---\n", "第一次 Session 请求状态码: 200\n", "第二次 Session 请求状态码: 200\n", "Session 请求成功！\n", "响应 JSON 内容:\n", "{\n", "  \"cookies\": {\n", "    \"name\": \"session_cookie\"\n", "  }\n", "}\n", "------------------------------\n"]}], "source": ["# ==============================================================================\n", "# 8. 使用 Session 会话\n", "# ==============================================================================\n", "# Session 对象可以保持跨请求的某些参数（如 Cookies），提高效率。\n", "print(\"--- 测试 Session 会话 ---\")\n", "try:\n", "    with requests.Session() as session:\n", "        # 第一次请求，设置一个 cookie\n", "        response_session_1 = session.get('https://httpbin.org/cookies/set?name=session_cookie')\n", "        print(f\"第一次 Session 请求状态码: {response_session_1.status_code}\")\n", "\n", "        # 第二次请求，会自动带上之前设置的 cookie\n", "        response_session_2 = session.get('https://httpbin.org/cookies')\n", "        print(f\"第二次 Session 请求状态码: {response_session_2.status_code}\")\n", "        \n", "        if response_session_2.status_code == 200:\n", "            print(\"Session 请求成功！\")\n", "            print(\"响应 JSON 内容:\")\n", "            print(json.dumps(response_session_2.json(), indent=2))\n", "\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"Session 请求失败: {e}\")\n", "print(\"-\" * 30)"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}