{"cells": [{"cell_type": "markdown", "id": "c961f4ab-2988-4634-b99c-bbdf4c65709e", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## <font face=\"仿宋\">课程说明："]}, {"cell_type": "markdown", "id": "0a8b055d-c970-4180-a2e5-7a9a7b2f4aaa", "metadata": {}, "source": ["- 体验课内容节选自[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)完整版付费课程"]}, {"cell_type": "markdown", "id": "b9f5e446-f94d-4d93-9557-3cfbce9e9e2c", "metadata": {}, "source": ["&emsp;&emsp;体验课时间有限，若想深度学习大模型技术，欢迎大家报名由我主讲的[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)"]}, {"cell_type": "markdown", "id": "22bc80af-27b1-4722-af4f-92fda1fe822e", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171642034.jpg\" alt=\"bef0897853f861af5f4211442be446b\" style=\"zoom:15%;\" />"]}, {"cell_type": "markdown", "id": "039499cd-85ce-49fe-94f9-50f360e32aed", "metadata": {}, "source": ["**[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)为【100+小时】体系大课，总共20大模块精讲精析，零基础直达大模型企业级应用！**"]}, {"cell_type": "markdown", "id": "7919e1b4-aa00-46c1-b133-23f4c6452e90", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172010074.png\" alt=\"a55d48e952ed59f8d93e050594843bc\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "19ad27b8-8482-4c8f-97fd-ffb1dcc87ad2", "metadata": {}, "source": ["部分项目成果演示"]}, {"cell_type": "code", "execution_count": 3, "id": "5298d88f-b5f4-4e68-94f9-786015b492ca", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "markdown", "id": "761d95a6-bb19-43d0-9577-cc01698a0e56", "metadata": {}, "source": ["- **MateGen项目演示**"]}, {"cell_type": "code", "execution_count": 4, "id": "74e14308-cddc-4580-b1af-f73bcb5ad5ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "cda805a4-cb05-4b25-ace0-67821e3f0761", "metadata": {}, "source": ["- **智能客服项目演示**"]}, {"cell_type": "code", "execution_count": 5, "id": "8aa1dc1f-1035-4942-8de8-9ebd04bcb18d", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "9a67e667-54cc-423d-bd28-f6719564a8bd", "metadata": {}, "source": ["- **Dify项目演示**"]}, {"cell_type": "code", "execution_count": 6, "id": "ef332221-f28e-47e5-9dc1-1463eecd1bc0", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "ef3946b7-84fb-40ca-aa36-0e74f709c67e", "metadata": {}, "source": ["- **LangChain&LangGraph搭建Multi-Agnet**"]}, {"cell_type": "code", "execution_count": 7, "id": "2a6798da-f605-41eb-95e8-10703eeb36f1", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "cd8f3f63-e667-474b-bd58-04a2f6949ba3", "metadata": {}, "source": ["此外，若是对大模型底层原理感兴趣，也欢迎报名由我和菜菜老师共同主讲的[《2025大模型原理与实战课程》(夏季班)](https://ix9mq.xetslk.com/s/3VITgV)"]}, {"cell_type": "markdown", "id": "3fc98169-2c23-4f48-add8-fc8bfd8a59b9", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171650709.png\" alt=\"4a11b7807056e9f5b281278c0e37dad\" style=\"zoom:20%;\" />"]}, {"cell_type": "markdown", "id": "23b31b6e-d610-4970-9290-c4fe6cba48ab", "metadata": {}, "source": ["**两门大模型课程夏季班目前上新特惠+618年中钜惠双惠叠加，合购还有更多优惠哦~<span style=\"color:red;\">详细信息扫码添加助教，回复“大模型”，即可领取课程大纲&查看课程详情👇</span>**"]}, {"cell_type": "markdown", "id": "71e52488-d9c7-423b-ad09-4d27ab854759", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171644321.jpeg\" alt=\"img\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "54cf6f6b-8bbe-4b93-846e-fff2ae34897b", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101749045.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "14d6f9a0-bcdc-4b15-9a68-8931c097cc6f", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "e121737c-febb-4fa4-9281-50643cdd38e7", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 8.LangChain RAG知识库检索系统开发实战"]}, {"cell_type": "markdown", "id": "24717eeb-0435-4b84-a83f-bd2cb1c6be5f", "metadata": {}, "source": ["## 一、LangChain 实现本地知识库问答"]}, {"cell_type": "markdown", "id": "5cbb716d-f09e-4f9e-a33d-4984e2fdfa6d", "metadata": {}, "source": ["&emsp;&emsp;供`Agents`在处理复杂任务的某个阶段使用，这其实是一种更为复杂的应用架构——Agent + RAG。"]}, {"cell_type": "markdown", "id": "701403fb-1a66-46fd-bb01-90e726e2b37b", "metadata": {}, "source": ["## 1.1 RAG基础概念入门"]}, {"cell_type": "markdown", "id": "a05cd24d-a99d-4532-91e1-bb11d2fb4b71", "metadata": {}, "source": ["\n", "&emsp;&emsp;假设现在我们有一个偌大的知识库，当想从该知识库中去检索最相关的内容时，最简单的方法是：接收到一个查询（Query），就直接在知识库中进行搜索。这种做法其实是可行的，但存在两个关键的问题：\n", "1. 假设提问的Query的答案出现在一篇文章中，去知识库中找到一篇与用户输入相关的文章是很容易的，但是我们将检索到的这整篇文章直接放入`Prompt`中并不是最优的选择，因为其中一定会包含非常多无关的信息，而无效信息越多，对大模型后续的推理影响越大。\n", "\n", "2. 任何一个大模型都存在最大输入的Token限制，一个流程中可能涉及多次检索，每次检索都会产生相应的上下文，无法容纳如此多的信息。"]}, {"cell_type": "markdown", "id": "e5772ad5-b626-44e6-89a3-b1a43a7974ae", "metadata": {}, "source": ["<div align=center><img src=\"https://snowball101.oss-cn-beijing.aliyuncs.com/img/202404101639353.png\" width=80%></div>"]}, {"cell_type": "markdown", "id": "3249a610-6a12-4b9c-a1ef-2a1262122637", "metadata": {}, "source": ["&emsp;&emsp;解决上述两个问题的方式是：把存放着原始数据的知识库（Knowledge）中的每一个raw data，切分成一个一个的小块，这些小块可以是一个段落，也可以是数据库中某个索引对应的值。这个切分过程被称为“分块”（chunking），如下述流程所示："]}, {"cell_type": "markdown", "id": "1eea13a6-ef77-4fd2-98f4-d7e7a7a8c3d6", "metadata": {}, "source": ["<div align=center><img src=\"https://snowball101.oss-cn-beijing.aliyuncs.com/img/202404101759694.png\" width=80%></div>"]}, {"cell_type": "markdown", "id": "1f665742-ffb5-4dbf-9a12-2bf2ea338958", "metadata": {}, "source": ["&emsp;&emsp;以第一个原始数据为例（raw data 1），通过一些特定的方法进行切分，一个完整的内容会被分割成 chunk1 ~ chunk4。采取相同的方法，继续对`raw data 2`、`raw data 3`直至`raw data n`进行切分。完成这一过程后，我们最终得到的是一个充满分块数据（chunks）的新的知识库（repository），其中每一项都是一个单独的chunk。例如，如果原始文档共有10个，那么经过切分，可能会产生出100个chunks。\n", "\n", "&emsp;&emsp;完成这一转化后，当再次接收到一个查询（Query）时，就会在更新后的知识库（repository）中进行搜索，这时检索的范围就不再是某个完整的文档，而是其中的某一个部分，返回的是一个或多个特定的chunk，这样返回的信息量就会更小且更精确。随后，这些被检索到的chunk会被加入到Prompt中，作为上下文信息与用户原始的Query共同输入到大模型进行处理，以生成最终的回答。"]}, {"cell_type": "markdown", "id": "fe3fdd04-1590-4e9f-a6b7-55830bb0ccfb", "metadata": {}, "source": ["&emsp;&emsp;在上述将原始数据（raw data）转化为chunk的过程中，就会包含构建RAG的第一部分开发工作：这包括如果做数据清洗，如去除停用词、标点符号等。此外，还涉及如何选择合适的`split`方法来进行数据切分的一系列技术。"]}, {"cell_type": "markdown", "id": "0648df67-e086-4cda-9c40-f9756c57aae4", "metadata": {}, "source": ["&emsp;&emsp;接下来面临的问题是，尽管所有数据已经被切割成一个个`chunk`，其存储形式还是以字符串形式存在，如果想从`repository`中匹配到与输入的query相关的chunks，比较两句话是否相似，看一句话中相同字有几个，这显然是行不通的。我们需要获取的是句子所蕴含的深层含义，而非仅仅是表面的字面相似度。因此，大家也能想到，在NLP中去计算文本相似度的有效的方法就是Embedding，即将这些chunks转换成向量（vector）形式。所以流程会丰富如下："]}, {"cell_type": "markdown", "id": "8d362b0d-06ad-4474-a3d7-b876592c0e92", "metadata": {}, "source": ["<div align=center><img src=\"https://snowball101.oss-cn-beijing.aliyuncs.com/img/202404101823060.png\" width=80%></div>"]}, {"cell_type": "markdown", "id": "e2466553-0202-4060-807c-fd29e86244aa", "metadata": {}, "source": ["&emsp;&emsp;如上所示，解决搜索效率和计算相似度优化算法的答案就是：向量数据库。同时也产生了构建RAG的第三部分工作：我们要去了解和学习如何选择、使用向量数据库。\n", "\n", "&emsp;&emsp;最终整体流程就如上图所示，一个基础的RAG架构会只要包含以下几方面的开发工作：\n", "\n", "1. 如何将原始数据转化成chunks；\n", "2. 如何将chunks转化成Vector；\n", "3. 如何选择计算向量相似度的算法；\n", "4. 如何利用向量数据库提升搜索效率；\n", "5. 如何把找到的chunks与原始query拼接在一起，产生最终的Prompt；"]}, {"cell_type": "markdown", "id": "bf727d0b-279b-4dde-903a-e78430de2597", "metadata": {}, "source": ["&emsp;&emsp;而上述流程，其实更像是一个自由拼接的结果，比如不同的文档类型可以选择不同的文档解析器，也可以选择不同的`Vector`数据库，甚至可以自由选择`Embedding`模型和`Vector`数据库的组合。其自由程度非常高，如下图所示："]}, {"cell_type": "markdown", "id": "f31ebdfa-21c8-4a24-9074-30b2c4d579a9", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091835680.png\" width=80%></div>"]}, {"cell_type": "markdown", "id": "7aedee30-2b05-4477-8df6-c31f5923b0e5", "metadata": {}, "source": ["## 1.2 从零到一搭建小型RAG系统入门教程"]}, {"cell_type": "markdown", "id": "cde32e02-47dc-4f71-b803-691fd305bc2e", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171955565.png\" alt=\"image-20250617195516494\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "31840b1f-0c6a-40d3-8622-be8cd549aa18", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171816930.png\" alt=\"57d34aa34ec04a98b020018c55b242d\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "f814954a-57e2-4a3b-ae1a-463c03601ff7", "metadata": {}, "source": ["## 1.3 实战：支持 PDF 文件解析的智能RAG 问答"]}, {"cell_type": "markdown", "id": "d8003071-85b5-4c4a-ad27-d9fea25fff7b", "metadata": {}, "source": ["&emsp;&emsp;理解了在`<PERSON><PERSON>hain`中构建`RAG`的基本原理后，我们就可以开始动手实践了。接下来的案例中，我们通过 `Streamlit` 前端界面，结合 `Lang<PERSON>hain` 框架 与 `DashScope` 向量嵌入服务，实现了一个轻量化的 `RAG（Retrieval-Augmented Generation）` 智能问答系统，支持上传多个 `PDF` 文档，系统将自动完成文本提取、分块、向量化，并构建基于 `FAISS` 的检索数据库。用户随后可以在页面中输入任意问题，系统会调用大语言模型（如 `DeepSeek-Chat`）对 `PDF` 内容进行语义理解和回答生成。"]}, {"cell_type": "markdown", "id": "6ffcd081-144c-4590-a1f2-2f7b675148eb", "metadata": {}, "source": ["&emsp;&emsp;其完整代码如下所示："]}, {"cell_type": "code", "execution_count": 6, "id": "e37c5fbe-d9b7-4915-a3d6-c7cd49f80b39", "metadata": {}, "outputs": [], "source": ["# ! pip install streamlit PyPDF2 dashscope faiss-cpu"]}, {"cell_type": "markdown", "id": "50dc0d85-984d-49f5-a1d1-2498e36be31b", "metadata": {}, "source": ["```python\n", "    import streamlit as st\n", "    from PyPDF2 import PdfReader\n", "    from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "    from langchain_core.prompts import ChatPromptTemplate\n", "    from langchain_community.vectorstores import FAISS\n", "    from langchain.tools.retriever import create_retriever_tool\n", "    from langchain.agents import AgentExecutor, create_tool_calling_agent\n", "    from langchain_community.embeddings import DashScopeEmbeddings\n", "    from langchain.chat_models import init_chat_model\n", "    import os\n", "    from dotenv import load_dotenv \n", "    load_dotenv(override=True)\n", "\n", "\n", "    DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "    dashscope_api_key = os.getenv(\"dashscope_api_key\")\n", "\n", "    os.environ[\"KMP_DUPLICATE_LIB_OK\"]=\"TRUE\"\n", "\n", "\n", "    embeddings = DashScopeEmbeddings(\n", "        model=\"text-embedding-v1\", dashscope_api_key=dashscope_api_key\n", "    )\n", "\n", "    def pdf_read(pdf_doc):\n", "        text = \"\"\n", "        for pdf in pdf_doc:\n", "            pdf_reader = PdfReader(pdf)\n", "            for page in pdf_reader.pages:\n", "                text += page.extract_text()\n", "        return text\n", "\n", "\n", "    def get_chunks(text):\n", "        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "        chunks = text_splitter.split_text(text)\n", "        return chunks\n", "\n", "    def vector_store(text_chunks):\n", "        vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)\n", "        vector_store.save_local(\"faiss_db\")\n", "\n", "    def get_conversational_chain(tools, ques):\n", "        llm = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "        prompt = ChatPromptTemplate.from_messages([\n", "            (\n", "                \"system\",\n", "                \"\"\"你是AI助手，请根据提供的上下文回答问题，确保提供所有细节，如果答案不在上下文中，请说\"答案不在上下文中\"，不要提供错误的答案\"\"\",\n", "            ),\n", "            (\"placeholder\", \"{chat_history}\"),\n", "            (\"human\", \"{input}\"),\n", "            (\"placeholder\", \"{agent_scratchpad}\"),\n", "        ])\n", "        \n", "        tool = [tools]\n", "        agent = create_tool_calling_agent(llm, tool, prompt)\n", "        agent_executor = AgentExecutor(agent=agent, tools=tool, verbose=True)\n", "        \n", "        response = agent_executor.invoke({\"input\": ques})\n", "        print(response)\n", "        st.write(\"🤖 回答: \", response['output'])\n", "\n", "    def check_database_exists():\n", "        \"\"\"检查FAISS数据库是否存在\"\"\"\n", "        return os.path.exists(\"faiss_db\") and os.path.exists(\"faiss_db/index.faiss\")\n", "\n", "    def user_input(user_question):\n", "        # 检查数据库是否存在\n", "        if not check_database_exists():\n", "            st.error(\"❌ 请先上传PDF文件并点击'Submit & Process'按钮来处理文档！\")\n", "            st.info(\"💡 步骤：1️⃣ 上传PDF → 2️⃣ 点击处理 → 3️⃣ 开始提问\")\n", "            return\n", "        \n", "        try:\n", "            # 加载FAISS数据库\n", "            new_db = FAISS.load_local(\"faiss_db\", embeddings, allow_dangerous_deserialization=True)\n", "            \n", "            retriever = new_db.as_retriever()\n", "            retrieval_chain = create_retriever_tool(retriever, \"pdf_extractor\", \"This tool is to give answer to queries from the pdf\")\n", "            get_conversational_chain(retrieval_chain, user_question)\n", "            \n", "        except Exception as e:\n", "            st.error(f\"❌ 加载数据库时出错: {str(e)}\")\n", "            st.info(\"请重新处理PDF文件\")\n", "\n", "    def main():\n", "        st.set_page_config(\"🤖 LangChain B站公开课 By九天Hector\")\n", "        st.header(\"🤖 LangChain B站公开课 By九天Hector\")\n", "        \n", "        # 显示数据库状态\n", "        col1, col2 = st.columns([3, 1])\n", "        \n", "        with col1:\n", "            if check_database_exists():\n", "            pass\n", "            else:\n", "                st.warning(\"⚠️ 请先上传并处理PDF文件\")\n", "        \n", "        with col2:\n", "            if st.button(\"🗑️ 清除数据库\"):\n", "                try:\n", "                    import shutil\n", "                    if os.path.exists(\"faiss_db\"):\n", "                        shutil.rmtree(\"faiss_db\")\n", "                    st.success(\"数据库已清除\")\n", "                    st.rerun()\n", "                except Exception as e:\n", "                    st.error(f\"清除失败: {e}\")\n", "\n", "        # 用户问题输入\n", "        user_question = st.text_input(\"💬 请输入问题\", \n", "                                    placeholder=\"例如：这个文档的主要内容是什么？\",\n", "                                    disabled=not check_database_exists())\n", "\n", "        if user_question:\n", "            if check_database_exists():\n", "                with st.spinner(\"🤔 AI正在分析文档...\"):\n", "                    user_input(user_question)\n", "            else:\n", "                st.error(\"❌ 请先上传并处理PDF文件！\")\n", "\n", "        # 侧边栏\n", "        with st.sidebar:\n", "            st.title(\"📁 文档管理\")\n", "            \n", "            # 显示当前状态\n", "            if check_database_exists():\n", "                st.success(\"✅ 数据库状态：已就绪\")\n", "            else:\n", "                st.info(\"📝 状态：等待上传PDF\")\n", "            \n", "            st.markdown(\"---\")\n", "            \n", "            # 文件上传\n", "            pdf_doc = st.file_uploader(\n", "                \"📎 上传PDF文件\", \n", "                accept_multiple_files=True,\n", "                type=['pdf'],\n", "                help=\"支持上传多个PDF文件\"\n", "            )\n", "            \n", "            if pdf_doc:\n", "                st.info(f\"📄 已选择 {len(pdf_doc)} 个文件\")\n", "                for i, pdf in enumerate(pdf_doc, 1):\n", "                    st.write(f\"{i}. {pdf.name}\")\n", "            \n", "            # 处理按钮\n", "            process_button = st.button(\n", "                \"🚀 提交并处理\", \n", "                disabled=not pdf_doc,\n", "                use_container_width=True\n", "            )\n", "            \n", "            if process_button:\n", "                if pdf_doc:\n", "                    with st.spinner(\"📊 正在处理PDF文件...\"):\n", "                        try:\n", "                            # 读取PDF内容\n", "                            raw_text = pdf_read(pdf_doc)\n", "                            \n", "                            if not raw_text.strip():\n", "                                st.error(\"❌ 无法从PDF中提取文本，请检查文件是否有效\")\n", "                                return\n", "                            \n", "                            # 分割文本\n", "                            text_chunks = get_chunks(raw_text)\n", "                            st.info(f\"📝 文本已分割为 {len(text_chunks)} 个片段\")\n", "                            \n", "                            # 创建向量数据库\n", "                            vector_store(text_chunks)\n", "                            \n", "                            st.success(\"✅ PDF处理完成！现在可以开始提问了\")\n", "                            st.balloons()\n", "                            st.rerun()\n", "                            \n", "                        except Exception as e:\n", "                            st.error(f\"❌ 处理PDF时出错: {str(e)}\")\n", "                else:\n", "                    st.warning(\"⚠️ 请先选择PDF文件\")\n", "            \n", "            # 使用说明\n", "            with st.expander(\"💡 使用说明\"):\n", "                st.markdown(\"\"\"\n", "                **步骤：**\n", "                1. 📎 上传一个或多个PDF文件\n", "                2. 🚀 点击\"Submit & Process\"处理文档\n", "                3. 💬 在主页面输入您的问题\n", "                4. 🤖 AI将基于PDF内容回答问题\n", "                \n", "                **提示：**\n", "                - 支持多个PDF文件同时上传\n", "                - 处理大文件可能需要一些时间\n", "                - 可以随时清除数据库重新开始\n", "                \"\"\")\n", "\n", "    if __name__ == \"__main__\":\n", "        main()\n", "```"]}, {"cell_type": "markdown", "id": "9163cdb9-29b1-43a4-8219-bb571555fbbb", "metadata": {}, "source": ["基于此，我们能够实现：\n", "\n", "* **LangChain 的多模块能力**（向量搜索 + Agent工具）\n", "* **Streamlit 前端交互**\n", "* **FAISS 向量数据库**\n", "* **DashScope Embedding + DeepSeek 模型接入**\n", "* 并完成了完整的 RAG（检索增强生成）流程\n", "\n", "以下是各部分功能实现代码讲解：\n", "\n", "#### 🔧 1. 导入库 & 环境初始化\n", "\n", "```python\n", "import streamlit as st\n", "from PyPDF2 import PdfReader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "...\n", "load_dotenv(override=True)\n", "```\n", "\n", "* `Streamlit` 用于构建网页界面。\n", "* `PyPDF2` 用来读取 PDF 文本。\n", "* `load_dotenv()` 加载 `.env` 中的 API Key，例如：\n", "\n", "  ```dotenv\n", "  DEEPSEEK_API_KEY=sk-xxx\n", "  DASHSCOPE_API_KEY=xxx\n", "  ```\n", "\n", "---\n", "\n", "#### 🔐 2. 加载 API 密钥与设置环境变量\n", "\n", "```python\n", "DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "dashscope_api_key = os.getenv(\"dashscope_api_key\")\n", "os.environ[\"KMP_DUPLICATE_LIB_OK\"]=\"TRUE\"\n", "```\n", "\n", "* 从环境变量中读取 DashScope 和 DeepSeek API。\n", "* 设置 `KMP_DUPLICATE_LIB_OK` 避免某些 MKL 多线程报错。\n", "\n", "---\n", "\n", "#### 🧠 3. 初始化向量 Embedding 模型\n", "\n", "```python\n", "embeddings = DashScopeEmbeddings(\n", "    model=\"text-embedding-v1\", dashscope_api_key=dashscope_api_key\n", ")\n", "```\n", "\n", "* 用阿里云 DashScope 提供的 `text-embedding-v1` 将文本转为向量表示，用于相似度搜索。\n", "\n", "---\n", "\n", "#### 📄 4. 处理 PDF 文本与向量化逻辑\n", "\n", "```python\n", "def pdf_read(pdf_doc):\n", "    ...\n", "def get_chunks(text):\n", "    ...\n", "def vector_store(text_chunks):\n", "    ...\n", "```\n", "\n", "* `pdf_read`：逐页读取 PDF 内容并拼接。\n", "* `get_chunks`：将长文本切片为多个段落（chunk），每段 1000 字，重叠 200 字。\n", "* `vector_store`：用 FAISS 建立向量索引，并保存到本地 `faiss_db/`。\n", "\n", "---\n", "\n", "#### 🔁 5. Agent对话链 + 工具调用（核心 RAG）\n", "\n", "```python\n", "def get_conversational_chain(tools, ques):\n", "    llm = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "    ...\n", "    agent_executor = AgentExecutor(...)\n", "    response = agent_executor.invoke({\"input\": ques})\n", "    ...\n", "```\n", "\n", "* 初始化 DeepSeek 模型为 Agent。\n", "* 使用 <PERSON><PERSON><PERSON><PERSON> 的 `create_tool_calling_agent` 构造 Agent，输入：\n", "\n", "  * prompt（你设定的系统角色）\n", "  * 工具（retriever 工具）\n", "* `AgentExecutor.invoke`：LangChain 自动判断是否调用工具，完成“读取上下文 → 查询 → 回答”流程。\n", "\n", "---\n", "\n", "#### 🔍 6. 用户提问逻辑（调用 FAISS）\n", "\n", "```python\n", "def user_input(user_question):\n", "    ...\n", "    new_db = FAISS.load_local(\"faiss_db\", embeddings, ...)\n", "    retriever = new_db.as_retriever()\n", "    retrieval_chain = create_retriever_tool(retriever, \"pdf_extractor\", ...)\n", "    get_conversational_chain(retrieval_chain, user_question)\n", "```\n", "\n", "* 加载本地 FAISS 向量库；\n", "* 将其转为 LangChain 的检索工具；\n", "* 交由 Agent 调用完成回答。\n", "\n", "---\n", "\n", "#### 🧠 7. 检查数据库是否存在\n", "\n", "```python\n", "def check_database_exists():\n", "    return os.path.exists(\"faiss_db\") and os.path.exists(\"faiss_db/index.faiss\")\n", "```\n", "\n", "简单检查本地是否已有向量化数据。\n", "\n", "---\n", "\n", "#### 🌐 8. 主界面逻辑（Streamlit）\n", "\n", "```python\n", "def main():\n", "    st.set_page_config(...)\n", "    ...\n", "```\n", "\n", "* 页面标题与界面配置。\n", "* `st.columns` 分栏：左边显示提示，右边放置“清空数据库”按钮。\n", "* 主输入框：`st.text_input(\"请输入问题\")`\n", "\n", "  * 只有当数据库存在时才能提问。\n", "* 侧边栏：\n", "\n", "  * PDF 上传器；\n", "  * 提交按钮（处理上传的 PDF → 分片 → 向量化 → 存储）。\n", "\n", "---\n", "\n", "#### 🎯 9. 提交 PDF 后执行的逻辑\n", "\n", "```python\n", "if process_button:\n", "    raw_text = pdf_read(pdf_doc)\n", "    ...\n", "    text_chunks = get_chunks(raw_text)\n", "    vector_store(text_chunks)\n", "```\n", "\n", "* 当点击“提交并处理”后：\n", "\n", "  1. 读取上传的 PDF；\n", "  2. 切片文本；\n", "  3. 向量化入库；\n", "  4. 弹出气球提示，并 `st.rerun()` 刷新页面状态。\n", "\n", "---\n", "\n", "#### 📎 项目结构总结\n", "\n", "| 模块                | 说明                             |\n", "| ----------------- | ------------------------------ |\n", "| 🧾 PDF解析          | 读取用户上传的 PDF                    |\n", "| ✂️ 文本切片           | 按段落分割内容                        |\n", "| 📊 向量化            | DashScope Embedding + FAISS 建库 |\n", "| 🔁 查询接口           | 用户输入 → 召回相关 chunk              |\n", "| 🤖 DeepSeek Agent | 调用检索工具并给出回答                    |\n", "| 💻 UI层            | Streamlit 实现全部交互               |"]}, {"cell_type": "markdown", "id": "1407c5e8-16e1-403a-81c2-f321428431d8", "metadata": {}, "source": ["&emsp;&emsp;其中LangChain RAG核心功能相关代码如下："]}, {"cell_type": "markdown", "id": "7b36c438-3978-4fa2-b69d-d04b6b367991", "metadata": {}, "source": ["- Step 1：PDF 文件上传与文本提取\n", "\n", "&emsp;&emsp;使用 `st.file_uploader()` 组件支持多文件上传，并通过 `PyPDF2.PdfReader` 对每页内容进行提取，组合为整体文本。\n", "\n", "```python\n", "    def pdf_read(pdf_doc):\n", "        text = \"\"\n", "        for pdf in pdf_doc:\n", "            pdf_reader = PdfReader(pdf)\n", "            for page in pdf_reader.pages:\n", "                text += page.extract_text()\n", "        return text\n", "```"]}, {"cell_type": "markdown", "id": "8068eb1f-4d84-4dfc-b571-100ee39e0e59", "metadata": {}, "source": ["- Step 2：文本分块与向量数据库构建\n", "\n", "&emsp;&emsp;使用 `RecursiveCharacterTextSplitter` 将长文档切割为固定长度（1000字）+ 重叠（200字）的小块，将文本块通过 `DashScopeEmbeddings` 嵌入为向量，使用 `FAISS` 本地存储向量数据库。\n", "\n", "\n", "```python\n", "    chunks = text_splitter.split_text(text)\n", "    vector_store = FAISS.from_texts(chunks, embedding=embeddings)\n", "    vector_store.save_local(\"faiss_db\")\n", "```\n"]}, {"cell_type": "markdown", "id": "5f94bca5-be94-45f2-a069-6652f3890a1b", "metadata": {}, "source": ["- Step 3：用户提问与语义检索\n", "\n", "&emsp;&emsp;通过 `Streamlit` 获取用户输入问题，如果向量数据库存在，则加载 `FAISS` 检索器，使用 `create_retriever_tool()` 构建 `<PERSON><PERSON><PERSON>n` 工具，交由 `AgentExecutor` 执行，自动调用检索器并生成答案。\n", "\n", "```python\n", "    retrieval_chain = create_retriever_tool(retriever, ...)\n", "    agent = create_tool_calling_agent(llm, [retrieval_chain], prompt)\n", "    response = agent_executor.invoke({\"input\": ques})\n", "```"]}, {"cell_type": "markdown", "id": "1acc6a03-e2aa-4f0d-a20e-d1626cb68af5", "metadata": {}, "source": ["&emsp;&emsp;完整的代码已经上传至百度网盘中的`langchain_rag.py`文件中，大家可以扫描下方二维码免费领取"]}, {"cell_type": "markdown", "id": "611dfb71-1c97-40d9-9ebe-44f56d0b6160", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172002839.png\" alt=\"image-20250617200256788\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "db7e69a2-74d9-4585-a7c6-ce1d215b55a7", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "fb0bdb65-3d63-4afd-98e8-f2d703e4f326", "metadata": {}, "source": ["&emsp;&emsp;项目运行效果如下所示："]}, {"cell_type": "code", "execution_count": 2, "id": "2460cdf5-9b16-4a00-864e-6bcfaba31a98", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "code", "execution_count": 3, "id": "52f2352d-0a9c-408c-a57f-b1c74c65cfc9", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/LangChain%20RAG.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/LangChain%20RAG.mp4\", width=800, height=400)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}