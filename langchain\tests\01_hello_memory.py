from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
)
from langchain_core.chat_history import InMemoryChatMessageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser

# 1. 定义提示模板（保留对话历史占位符）
prompt = ChatPromptTemplate.from_messages([
    SystemMessagePromptTemplate.from_template(
        "The following is a friendly conversation between a human and an AI. "
        "The AI is talkative and provides lots of specific details from its context. "
        "If the AI does not know the answer to a question, it truthfully says it does not know."
    ),
    MessagesPlaceholder(variable_name="history"),  # 用于存储对话历史
    HumanMessagePromptTemplate.from_template("{input}")  # 接收用户输入
])

# 2. 初始化模型
llm = ChatOllama(model="qwen3:8b", temperature=0.7)

# 3. 构建基础链（提示模板 → 模型 → 输出解析器）
base_chain = prompt | llm | StrOutputParser()

# 4. 配置对话记忆（使用内存存储历史消息）
# 存储对话历史的容器（可替换为数据库存储，如Redis）
chat_history = InMemoryChatMessageHistory()

# 将基础链与记忆结合，形成带历史的对话链
chain = RunnableWithMessageHistory(
    base_chain,  # 基础链
    lambda session_id: chat_history,  # 提供对话历史（这里固定一个会话，实际可按session_id区分用户）
    input_messages_key="input",  # 输入中用户消息的key
    history_messages_key="history"  # 提示模板中历史消息的占位符key
)

# 5. 调用对话链（首次对话）
result = chain.invoke(
    {"input": "你好呀，我是小明"},
    config={"configurable": {"session_id": "unique-session-id"}}  # 会话ID（可自定义，用于区分不同对话）
)
print("AI:", result)

# 6. 多轮对话（自动记忆历史）
result = chain.invoke(
    {"input": "还记得我叫什么吗？"},
    config={"configurable": {"session_id": "unique-session-id"}}
)
print("AI:", result)