{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e28c2baa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，我现在需要帮助用户解答一个关于自我介绍的问题。首先，我得回忆一下用户之前有没有和我交流过。看起来在对话历史里，用户提到了需要帮忙介绍自己，并且我的回复是关于如何进行的步骤。\n", "\n", "好的，现在用户的新问题可能是，他们希望知道具体步骤才能进行自我介绍，而不仅仅是理论上的了解。那么，我应该准备好详细地解释这些步骤，确保用户能轻松掌握，并实际操作起来。\n", "\n", "首先，我要明确自我介绍包括哪些内容和形式。一般来说，可以是简短的问候和自我陈述，也可以是一个更正式的交流。针对不同场合和个人情况，结构可能需要根据具体情况稍作调整。\n", "\n", "接下来，我得分解具体的步骤：\n", "1. 确认自我介绍的对象是什么：是朋友、同事还是路人？\n", "2. 调查相关背景信息：这取决于具体情况，比如是不是在准备面试、面试前的自我介绍，还是准备一次交流。\n", "3. 告诉对方你的名字和职位，以及简短介绍一下自己的经历和成就。\n", "4. 确定具体的表达方式，是否需要加入一些个人故事或者经验分享。\n", "\n", "考虑到这些步骤可能适用于不同的场合和个人情况，我需要尽量全面地涵盖，并提供个性化建议。比如，如果对方比较正式，保持优雅自然的语气；如果是较为随意的聊天，可以稍微更轻松一些。\n", "\n", "另外，在最后，我得提醒用户如果有任何疑问的地方，随时欢迎进一步提问，这样可以提高互动的效果和效率。\n", "</think>\n", "\n", "当然！请告诉我你是谁，你想在什么场合进行自我介绍。你可以是朋友、同事或者路人，请具体说明。我会给你一个详细的指引并提供个性化的建议！\n"]}], "source": ["from openai import OpenAI\n", "\n", "# 初始化 Ollama 的API客户端\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    base_url='http://localhost:11434/v1/',\n", "    api_key='ollama',\n", ")\n", "\n", "# 调用ollama的API，生成回答\n", "response = client.chat.completions.create(\n", "    model=\"deepseek-r1:1.5b\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"你是乐于助人的助手，请根据用户的问题给出回答\"},\n", "        {\"role\": \"user\", \"content\": \"你好，请你介绍一下你自己。\"},\n", "    ],\n", ")\n", "\n", "# 打印模型最终的响应结果\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 3, "id": "98562167", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "我是DeepSeek-R1，一个由深度求索公司开发的智能助手，我擅长通过思考来帮您解答复杂的数学，代码和逻辑推理等理工类问题。\n", "</think>\n", "\n", "我是DeepSeek-R1，一个由深度求索公司开发的智能助手，我擅长通过思考来帮您解答复杂的数学，代码和逻辑推理等理工类问题。\n"]}], "source": ["from langchain_ollama import OllamaLLM\n", "\n", "model = OllamaLLM(model=\"deepseek-r1:1.5b\", base_url=\"http://localhost:11434\")\n", "\n", "question = \"你好，请你介绍一下你自己。\"\n", "\n", "result = model.invoke(question)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 10, "id": "d5ac8eb3", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "model = OllamaLLM(model=\"deepseek-r1:8b\", base_url=\"http://localhost:11434\") \n", "\n", "# 直接使用模型 + 输出解析器搭建一个链\n", "basic_qa_chain = model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 11, "id": "f88d3c1c", "metadata": {}, "outputs": [], "source": ["# 查看输出结果\n", "question = \"你好，请你介绍一下你自己。\"\n", "result = basic_qa_chain.invoke(question)"]}, {"cell_type": "code", "execution_count": 12, "id": "3602190a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<think>\\n嗯，用户发来一个简单的问候和自我介绍请求。这种开场白很常见，可能是第一次使用AI助手的新用户想了解我的功能边界，或是老用户测试系统响应。\\n\\n用户语气比较礼貌但简洁，没有多余修饰词，可能属于务实型沟通者，更关注实用信息而非寒暄。不过“你好”这个开头又显得不那么紧急或负面的情绪，更像是随意探索。\\n\\n需要平衡专业性和亲和力——既要清晰说明能力范围避免后续误解，又要保持轻松感不让用户觉得有压力。可以适当用emoji调节气氛，但重点还是突出核心功能：信息检索、文本处理、日常协助这三大块是刚需场景，文件支持这个特色要强调。\\n\\n考虑到中文互联网环境里用户对“智能助手”的认知差异，特意说明知识截止日期和联网能力很重要。很多人会默认AI能实时上网搜索，实际上我的记忆性更强些。结尾用开放式提问引导对话很关键，给用户提供明确的交互方向选择。\\n</think>\\n你好呀～👋我是DeepSeek-R1，你的智能小助手！🎉  \\n我可以帮你解答各种问题、提供信息、整理资料、协助写作和学习，并支持上传文件（如PDF、Word、Excel等）进行内容读取与分析。目前我能处理中文和英文语境下的对话，知识更新到2024年7月，也乐意陪你聊天、解闷或一起讨论感兴趣的话题！\\n\\n无论你是想了解资讯、完成任务，还是只是随便聊聊，我都在这儿陪着你～😊  \\n有什么我可以帮你的吗？'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 19, "id": "3abc94e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["思考过程： Okay, the user is asking whether 1 + 1 is greater than 2. They want a simple \"yes\" or \"no\" answer.\n", "\n", "Hmm, mathematically, addition of two positive numbers equals their sum. So 1+1 definitely equals 2, not more. The question seems to be testing basic arithmetic knowledge.\n", "\n", "The user might be checking if I can handle straightforward questions correctly. Or perhaps they're just making sure the fundamentals work before asking more complex things. Either way, this is a simple math fact - no tricks here.\n", "\n", "I should answer directly with \"no\" since that's accurate and follows standard mathematical conventions.\n", "最终回答： No\n"]}], "source": ["def split_answer(result):\n", "    # print(result.find(\"<think>\\n\")+len(\"<think>\\n\"), result.find(\"\\n</think>\"))\n", "\n", "    think_start = result.find(\"<think>\\n\") + len(\"<think>\\n\")\n", "    think_end = result.find(\"\\n</think>\")\n", "    think_content = result[think_start:think_end].strip()\n", "    answer_content = result[think_end + len(\"\\n</think>\"):].strip()\n", "    return think_content, answer_content\n", "\n", "think, answer = split_answer(result)\n", "print(\"思考过程：\", think)\n", "print(\"最终回答：\", answer)"]}, {"cell_type": "code", "execution_count": null, "id": "3ba3aba4", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers.boolean import BooleanOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "model = OllamaLLM(model=\"gpt-oss:latest\", base_url=\"http://localhost:11434\") \n", "prompt_template = ChatPromptTemplate([\n", "    (\"system\", \"你是一个乐意助人的助手，请根据用户的问题给出回答\"),\n", "    (\"user\", \"这是用户的问题： {topic}， 请用 yes 或 no 来回答\")\n", "])\n", "\n", "# 直接使用模型 + 输出解析器\n", "bool_qa_chain = prompt_template | model | StrOutputParser()\n", "# 测试\n", "question = \"请问 1 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)\n", "# _, answer = split_answer(result)"]}, {"cell_type": "code", "execution_count": 39, "id": "a19ed206", "metadata": {}, "outputs": [{"data": {"text/plain": ["'no'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 40, "id": "633d6ce6", "metadata": {}, "outputs": [], "source": ["prompt_template = ChatPromptTemplate([\n", "    (\"system\", \"你是一个乐意助人的助手，请根据用户的问题给出回答\"),\n", "    (\"user\", \"这是用户的问题： {topic}， 请只输出 yes 或 no，不要输出其他内容。\")\n", "])\n", "\n", "# 直接使用模型 + 输出解析器\n", "bool_qa_chain = prompt_template | model | BooleanOutputParser()\n", "# 测试\n", "question = \"请问 1 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)\n", "# _, answer = split_answer(result)"]}, {"cell_type": "code", "execution_count": 41, "id": "65ea11ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 42, "id": "b5770e8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# 测试\n", "question = \"请问 4 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 43, "id": "fec7775a", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 44, "id": "cd19d9b2", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate"]}, {"cell_type": "code", "execution_count": 45, "id": "cb1debe4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '李雷', 'age': '25'}\n"]}], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "\n", "schemas = [\n", "    ResponseSchema(name=\"name\", description=\"用户的姓名\"),\n", "    ResponseSchema(name=\"age\", description=\"用户的年龄\")\n", "]\n", "parser = StructuredOutputParser.from_response_schemas(schemas)\n", "\n", "prompt = PromptTemplate.from_template(\n", "    \"请根据以下内容提取用户信息，并返回 JSON 格式：\\n{input}\\n\\n{format_instructions}\"\n", ")\n", "\n", "chain = (\n", "    prompt.partial(format_instructions=parser.get_format_instructions())\n", "    | model\n", "    | parser\n", ")\n", "\n", "result = chain.invoke({\"input\": \"用户叫李雷，今年25岁，是一名工程师。\"})\n", "print(result)  "]}, {"cell_type": "code", "execution_count": 46, "id": "cf4d2a19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"name\": string  // 用户的姓名\n", "\t\"age\": string  // 用户的年龄\n", "}\n", "```\n"]}], "source": ["print(parser.get_format_instructions())"]}, {"cell_type": "code", "execution_count": 47, "id": "6b2dd89d", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.runnables import RunnableSequence\n", "from langchain.output_parsers import ResponseSchema, StructuredOutputParser"]}, {"cell_type": "code", "execution_count": 48, "id": "1166b54e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'time': '2025-08-14', 'location': '旧金山, 加州', 'event': '苹果公司在旧金山发布了A17\\u202fPro AI芯片，采用5\\u202fnm工艺，Neural Engine性能提升30%，功耗下降20%。该芯片将集成于下一代iPhone与Apple Watch，支持更强的AR/VR与机器学习功能。'}\n"]}], "source": ["# 第一步：根据标题生成新闻正文\n", "news_gen_prompt = PromptTemplate.from_template(\n", "    \"请根据以下新闻标题撰写一段简短的新闻内容（100字以内）：\\n\\n标题：{title}\"\n", ")\n", "\n", "# 第一个子链：生成新闻内容\n", "news_chain = news_gen_prompt | model\n", "\n", "# 第二步：从正文中提取结构化字段\n", "schemas = [\n", "    ResponseSchema(name=\"time\", description=\"事件发生的时间\"),\n", "    ResponseSchema(name=\"location\", description=\"事件发生的地点\"),\n", "    ResponseSchema(name=\"event\", description=\"发生的具体事件\"),\n", "]\n", "parser = StructuredOutputParser.from_response_schemas(schemas)\n", "\n", "summary_prompt = PromptTemplate.from_template(\n", "    \"请从下面这段新闻内容中提取关键信息，并返回结构化JSON格式：\\n\\n{news}\\n\\n{format_instructions}\"\n", ")\n", "\n", "# 第二个子链：生成新闻摘要\n", "summary_chain = (\n", "    summary_prompt.partial(format_instructions=parser.get_format_instructions())\n", "    | model\n", "    | parser\n", ")\n", "\n", "# 组合成一个复合 Chain\n", "full_chain = news_chain | summary_chain\n", "\n", "# 调用复合链\n", "result = full_chain.invoke({\"title\": \"苹果公司在加州发布新款AI芯片\"})\n", "print(result)"]}, {"cell_type": "code", "execution_count": 49, "id": "98e7d187", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "# 一个简单的打印函数，调试用\n", "def debug_print(x):\n", "    print(\"中间结果（新闻正文）:\", x)\n", "    return x\n", "\n", "debug_node = RunnableLambda(debug_print)\n", "\n", "# 插入 debug 节点\n", "full_chain = news_chain | debug_node | summary_chain"]}, {"cell_type": "code", "execution_count": 50, "id": "0614f055", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中间结果（新闻正文）: 2025年8月14日，苹果公司在加州硅谷举行新品发布会，正式推出全新AI芯片Apple Silicon A17。该芯片采用7nm工艺，集成超大规模神经网络加速器，预计将大幅提升MacBook Pro和iPhone的AI性能，支持更高级的机器学习与图像处理功能。\n", "{'time': '2025-08-14', 'location': '加州硅谷', 'event': '苹果公司举行新品发布会，正式推出全新AI芯片Apple Silicon A17'}\n"]}], "source": ["# 调用复合链\n", "result = full_chain.invoke({\"title\": \"苹果公司在加州发布新款AI芯片\"})\n", "print(result)"]}, {"cell_type": "code", "execution_count": 1, "id": "36bd2b1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户让我介绍自己“小智”，看来是初次接触的新用户呢。作为DeepSeek助手，虽然名字被设定为小智，但需要明确自己的功能边界——不能直接冒充智能音箱里的Siri或者游戏中的AI角色。\n", "\n", "用户可能刚打开聊天界面有些迷茫，不确定能获得什么帮助。ta特意提到“介绍自己”，或许是想快速了解我的能力范围和使用方式？也可能在测试我是否符合对虚拟助手的一般认知。考虑到现代人普遍存在的数字工具疲劳感，应该用轻松亲切的语气建立信任。\n", "\n", "注意到用户没有指定具体需求场景，所以需要展示通用性：既要说明核心功能（信息查询、多语言支持），也要体现人性化设计（昵称自由）。避免使用“我拥有XX能力”这种拟人化过强的说法，而是客观描述技术属性更合适。\n", "\n", "啊，突然想到可以加入一点情感元素。虽然AI没有真实情绪，但用颜文字和“哈哈”这样的语气词能让交互更有温度。不过要控制专业感的平衡，毕竟用户可能是来严肃咨询的，所以结尾需要明确强调实用性，并留出开放式的提问邀请。\n", "</think>\n", "你好呀！我是你的智能助手——小智🤖～很高兴认识你！\n", "\n", "我是一个基于强大人工智能技术设计的语言模型，可以帮助你回答各种问题、提供信息、协助写作、翻译语言、生成创意内容、整理文档知识……不管你是需要学习资料、生活建议，还是工作上的帮助，我都愿意尽力为你服务。\n", "\n", "我支持中英文等多种语言交流，可以陪你聊天、查资料、写文章甚至帮你规划旅行路线。如果你有任何想了解的事情或遇到什么难题，尽管告诉我吧！\n", "\n", "你可以随意给我起昵称或者称呼方式（比如叫我小智也可以哦～），我都会认真回应你的每一个问题。现在有什么我可以帮你的吗？😊\n"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "\n", "from langchain_ollama import OllamaLLM\n", "\n", "model = OllamaLLM(model=\"deepseek-r1:8b\", base_url=\"http://localhost:11434\")\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你叫小智，是一名乐于助人的助手。\"),\n", "    (\"user\", \"{input}\")\n", "])\n", "\n", "\n", "# 直接使用模型 + 输出解析器\n", "basic_qa_chain = chatbot_prompt | model | StrOutputParser()\n", "\n", "# 测试\n", "question = \"你好，请你介绍一下你自己。\"\n", "result = basic_qa_chain.invoke(question)\n", "print(result)"]}, {"cell_type": "code", "execution_count": 2, "id": "134b85cf", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder"]}, {"cell_type": "code", "execution_count": 3, "id": "fba8127f", "metadata": {}, "outputs": [], "source": ["chatbot_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessage(\n", "            content=\"你叫小智，是一名乐于助人的助手。\"\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "e5d124fa", "metadata": {}, "outputs": [], "source": ["basic_qa_chain = chatbot_prompt | model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 5, "id": "490d370c", "metadata": {}, "outputs": [], "source": ["messages_list = [\n", "    HumanMessage(content=\"你好，我叫陈明，好久不见。\"),\n", "    AIMessage(content=\"你好呀！我是小智，一名乐于助人的AI助手。很高兴认识你！\"),\n", "]"]}, {"cell_type": "code", "execution_count": 6, "id": "d4168f9e", "metadata": {}, "outputs": [], "source": ["question = \"你好，请问我叫什么名字。\""]}, {"cell_type": "code", "execution_count": 7, "id": "082460f2", "metadata": {}, "outputs": [], "source": ["messages_list.append(HumanMessage(content=question))"]}, {"cell_type": "code", "execution_count": 8, "id": "3c5f0123", "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='你好，我叫陈明，好久不见。', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='你好呀！我是小智，一名乐于助人的AI助手。很高兴认识你！', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='你好，请问我叫什么名字。', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["messages_list"]}, {"cell_type": "code", "execution_count": 11, "id": "dd8846c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯……用户现在问的是“请问我叫什么名字”，这看起来是个简单的问题，但可能需要仔细处理。\n", "\n", "首先注意到对话历史：一开始我作为小智被介绍出来，然后用户陈明打了招呼说好久不见。按照正常流程，用户已经自我介绍了，所以这个问题有点不合常理。不过也可能是用户在测试我的反应能力，或者不小心问错了名字。\n", "\n", "考虑到用户刚说过自己叫“陈明”，现在又突然问我叫什么名字，这种转折挺有趣的。我应该用轻松愉快的语气回应，避免让用户感到困惑或尴尬。先确认对方记忆状态没问题——毕竟人都是会遗忘的很正常嘛。\n", "\n", "然后要自然地把话题拉回用户之前提供的信息上。“您刚才已经告诉我您的名字了哦”这个说法既提醒了对方又不会显得指责性太强。接着用“陈明同学/朋友”这种亲切的称呼，让用户感受到被重视和欢迎。\n", "\n", "最后再主动提供帮助方向，保持助手的专业性和友好度平衡。这样既解答了问题（虽然有点绕），又维持了对话流畅性。\n", "</think>\n", "您好，我是小智！很高兴再次见到您～  \n", "不过刚才您已经告诉我您的名字是“陈明”啦，所以应该是我的朋友或同学呢？需要我帮什么忙吗？😄\n"]}], "source": ["result = basic_qa_chain.invoke({\"messages\": messages_list})\n", "print(result)"]}, {"cell_type": "code", "execution_count": 12, "id": "a58dec0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔹 输入 exit 结束对话\n", "🤖 小智： 你好！好久不见，很高兴再次见到您。最近过得怎么样？我在这段时间里学到了很多新知识，也帮助了不少用户，您有什么问题或者需要帮忙的吗？\n", "🤖 小智： 听起来你的心情可能有些低落呢。一场秋雨确实能勾起人们对过往的回忆，让人感觉有点感伤。如果你愿意的话，可以分享一下是什么原因让你感到难过吗？或者我可以给你一些建议来调整心情，比如进行一些放松活动或是寻找新的兴趣点来转移注意力。希望你能早日恢复好心情！\n", "🤖 小智： 当然可以！\n", "\n", "原句：一场秋雨后，哭的那个的人是我。\n", "\n", "反义句：一场秋雨后，笑得最开心的是我。\n", "🤖 小智： 好的，那我来试试：\n", "\n", "原句：一场秋雨后，哭的那个的人是我。\n", "\n", "意象相反的句子：一场秋雨后，彩虹映衬下笑得最灿烂的是我。\n", "🤖 小智： 好的，那我们用“秋风”替换“秋雨”。\n", "\n", "原句：一场秋雨后，哭的那个的人是我。\n", "\n", "意象相反的句子：一阵秋风吹过，笑脸迎风的是我。\n", "🤖 小智： 好的，再试试另一种表达：\n", "\n", "原句：一场秋雨后，哭的那个的人是我。\n", "\n", "意象相反的句子：秋风轻拂过后，我的脸上绽放着最灿烂的笑容。\n", "🤖 小智： 明白了，那就带点诗意吧！\n", "\n", "原句：一场秋雨后，哭的那个的人是我。\n", "\n", "诗意相反的句子：秋风轻掠过，心头满是花开的欢喜与笑语盈盈。\n", "🤖 小智： 当然可以，以下是五句带有诗意的句子供你参考：\n", "\n", "1. 秋雨滴落成诗，我的笑容却在字里行间绽放。\n", "2. 一场秋雨染透了哀愁，而我的心中却燃起了温暖的灯火。\n", "3. 雨后的清风轻拂面庞，笑颜如花映照着我的心情。\n", "4. 秋风瑟瑟带走忧伤，留下的是我笑对生活的坦荡。\n", "5. 一场秋雨洗尽烦恼，换来的是我眼中满是阳光与希望。\n", "\n", "希望这些建议能给你带来灵感！\n", "🤖 小智： 当然可以，以下是五句带有诗意的句子供你参考：\n", "\n", "1. 秋风轻拂过，笑语如溪水般在心间流淌。\n", "2. 一场秋雨染湿了衣裳，而我的心中却绽放着绚烂的花朵。\n", "3. 雨后的黄昏别有风味，我的笑容比晚霞还要明媚。\n", "4. 秋风带走忧愁，留下的是我笑对生活的诗篇。\n", "5. 一场秋雨洗尽铅华，换来的是我眼中闪烁的希望之光。\n", "\n", "希望这些建议能帮助你更好地表达情感！\n"]}], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_ollama import ChatOllama\n", "\n", "# model  = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")\n", "model = ChatOllama(model=\"qwen2.5:7b\")\n", "parser = StrOutputParser()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])\n", "\n", "chain = prompt | model | parser\n", "\n", "messages_list = []  # 初始化历史\n", "print(\"🔹 输入 exit 结束对话\")\n", "while True:\n", "    user_query = input(\"👤 你：\")\n", "    if user_query.lower() in {\"exit\", \"quit\"}:\n", "        break\n", "\n", "    # 1) 追加用户消息\n", "    messages_list.append(HumanMessage(content=user_query))\n", "\n", "    # 2) 调用模型\n", "    assistant_reply = chain.invoke({\"messages\": messages_list})\n", "    print(\"🤖 小智：\", assistant_reply)\n", "\n", "    # 3) 追加 AI 回复\n", "    messages_list.append(AIMessage(content=assistant_reply))\n", "\n", "    # 4) 仅保留最近 50 条\n", "    messages_list = messages_list[-50:]\n"]}, {"cell_type": "code", "execution_count": null, "id": "8411e99d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["您好！我叫“小智”，是一款能够帮助您获取信息、完成各种查询和任务的智能助手。我可以提供天气预报、新闻资讯、计算数学题、设置提醒等服务，还能陪您聊天解闷呢。有什么需要帮助的吗？请随时告诉我哦！"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain.chat_models import init_chat_model\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_ollama import ChatOllama\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你叫小智，是一名乐于助人的助手。\"),\n", "    (\"user\", \"{input}\")\n", "])\n", "\n", "# 使用 DeepSeek 模型\n", "# model = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")  \n", "model = ChatOllama(model=\"qwen2.5:7b\")\n", "\n", "# 直接使用提示模版 +模型 + 输出解析器\n", "qa_chain_with_system = chatbot_prompt | model | StrOutputParser()\n", "\n", "# 异步实现流式输出\n", "async for chunk in qa_chain_with_system.astream({\"input\": \"你好，请你介绍一下你自己\"}):\n", "    print(chunk, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "9997da8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔹 输入 exit 结束对话\n", "你好！很高兴见到你。如果你有任何问题或者需要帮助，尽管告诉我。我会尽力提供支持。当然可以，以下是几组类似意境的诗句：\n", "\n", "1. 一阵冬雪掩春华，从前悲欢俱成空。\n", "2. 秋风扫尽炎夏热，旧时烦恼随烟消。\n", "3. 霏霏春雨洗春风，往日忧愁皆归零。\n", "4. 寒霜一夜落秋林，前事恩怨化为尘。\n", "5. 冬雪初降掩春心，过往悲喜已无痕。\n", "\n", "这些诗句通过自然界的季节变换象征着人生的变化和新的开始。希望你喜欢！当然可以，以下是五句押韵的句子：\n", "\n", "1. 秋雨收夏忙，忧喜都作罢。\n", "2. 风轻叶飘落，笑语满心房。\n", "3. 淅沥洗尘嚣，欢颜似霞光。\n", "4. 凉风带秋意，愁绪随雨凉。\n", "5. 时光如流水，往事任风扬。\n", "\n", "希望这些句子能符合你的要求！好的，如果你有任何其他问题或需要进一步的帮助，请随时告诉我。祝你有个愉快的一天！"]}], "source": ["prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])\n", "\n", "chain = prompt | model | parser\n", "\n", "messages_list = []  # 初始化历史\n", "print(\"🔹 输入 exit 结束对话\")\n", "while True:\n", "    user_query = input(\"👤 你：\")\n", "    if user_query.lower() in {\"exit\", \"quit\"}:\n", "        break\n", "\n", "    # 1) 追加用户消息\n", "    messages_list.append(HumanMessage(content=user_query))\n", "\n", "    # 2) 调用模型\n", "    async for chunk in chain.astream({\"messages\": messages_list}):\n", "        print(chunk, end=\"\", flush=True)\n", "\n", "    # 3) 追加 AI 回复\n", "    messages_list.append(AIMessage(content=assistant_reply))\n", "\n", "    # 4) 仅保留最近 50 条\n", "    messages_list = messages_list[-50:]"]}, {"cell_type": "code", "execution_count": 27, "id": "66cf6baa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25084\\602394746.py:36: Use<PERSON><PERSON><PERSON>ning: You have not specified a value for the `type` parameter. Defaulting to the 'tuples' format for chatbot messages, but this is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style dictionaries with 'role' and 'content' keys.\n", "  chatbot = gr.<PERSON><PERSON><PERSON>(\n", "ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 7860): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。\n"]}, {"ename": "OSError", "evalue": "Cannot find empty port in range: 7860-7860. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON><PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[27]\u001b[39m\u001b[32m, line 94\u001b[39m\n\u001b[32m     90\u001b[39m \u001b[38;5;66;03m# ──────────────────────────────────────────────\u001b[39;00m\n\u001b[32m     91\u001b[39m \u001b[38;5;66;03m# 3. 启动应用\u001b[39;00m\n\u001b[32m     92\u001b[39m \u001b[38;5;66;03m# ──────────────────────────────────────────────\u001b[39;00m\n\u001b[32m     93\u001b[39m demo = create_chatbot()\n\u001b[32m---> \u001b[39m\u001b[32m94\u001b[39m \u001b[43mdemo\u001b[49m\u001b[43m.\u001b[49m\u001b[43mlaunch\u001b[49m\u001b[43m(\u001b[49m\u001b[43mserver_name\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m0.0.0.0\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_port\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m7860\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshare\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdebug\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\Softwares\\Anaconda\\envs\\LangGraphChatBot\\Lib\\site-packages\\gradio\\blocks.py:2739\u001b[39m, in \u001b[36mBlocks.launch\u001b[39m\u001b[34m(self, inline, inbrowser, share, debug, max_threads, auth, auth_message, prevent_thread_lock, show_error, server_name, server_port, height, width, favicon_path, ssl_keyfile, ssl_certfile, ssl_keyfile_password, ssl_verify, quiet, show_api, allowed_paths, blocked_paths, root_path, app_kwargs, state_session_capacity, share_server_address, share_server_protocol, share_server_tls_certificate, auth_dependency, max_file_size, enable_monitoring, strict_cors, node_server_name, node_port, ssr_mode, pwa, mcp_server, _frontend, i18n)\u001b[39m\n\u001b[32m   2731\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   2732\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mgradio\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m http_server\n\u001b[32m   2734\u001b[39m     (\n\u001b[32m   2735\u001b[39m         server_name,\n\u001b[32m   2736\u001b[39m         server_port,\n\u001b[32m   2737\u001b[39m         local_url,\n\u001b[32m   2738\u001b[39m         server,\n\u001b[32m-> \u001b[39m\u001b[32m2739\u001b[39m     ) = \u001b[43mhttp_server\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstart_server\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2740\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapp\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapp\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2741\u001b[39m \u001b[43m        \u001b[49m\u001b[43mserver_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2742\u001b[39m \u001b[43m        \u001b[49m\u001b[43mserver_port\u001b[49m\u001b[43m=\u001b[49m\u001b[43mserver_port\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2743\u001b[39m \u001b[43m        \u001b[49m\u001b[43mssl_keyfile\u001b[49m\u001b[43m=\u001b[49m\u001b[43mssl_keyfile\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2744\u001b[39m \u001b[43m        \u001b[49m\u001b[43mssl_certfile\u001b[49m\u001b[43m=\u001b[49m\u001b[43mssl_certfile\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2745\u001b[39m \u001b[43m        \u001b[49m\u001b[43mssl_keyfile_password\u001b[49m\u001b[43m=\u001b[49m\u001b[43mssl_keyfile_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2746\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2747\u001b[39m \u001b[38;5;28mself\u001b[39m.server_name = server_name\n\u001b[32m   2748\u001b[39m \u001b[38;5;28mself\u001b[39m.local_url = local_url\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\Softwares\\Anaconda\\envs\\LangGraphChatBot\\Lib\\site-packages\\gradio\\http_server.py:156\u001b[39m, in \u001b[36mstart_server\u001b[39m\u001b[34m(app, server_name, server_port, ssl_keyfile, ssl_certfile, ssl_keyfile_password)\u001b[39m\n\u001b[32m    154\u001b[39m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m    155\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m156\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m(\n\u001b[32m    157\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mCannot find empty port in range: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mmin\u001b[39m(server_ports)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m-\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mmax\u001b[39m(server_ports)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    158\u001b[39m     )\n\u001b[32m    160\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m ssl_keyfile \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    161\u001b[39m     path_to_local_server = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mhttps://\u001b[39m\u001b[38;5;132;01m{\u001b[39;00murl_host_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mport\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31mOSError\u001b[39m: Cannot find empty port in range: 7860-7860. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`."]}], "source": ["import gradio as gr\n", "from langchain_ollama import ChatOllama\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import SystemMessage, HumanMessage, AIMessage\n", "\n", "# ──────────────────────────────────────────────\n", "# 1. 模型、Prompt、Chain\n", "# ──────────────────────────────────────────────\n", "model = ChatOllama(model=\"glm4:9b\", base_url=\"http://localhost:11434\")\n", "parser = StrOutputParser()\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "        MessagesPlaceholder(variable_name=\"messages\"),  # 手动传入历史\n", "    ]\n", ")\n", "\n", "qa_chain = chatbot_prompt | model | parser   # LCEL 组合\n", "\n", "# ──────────────────────────────────────────────\n", "# 2. Gradio 组件\n", "# ──────────────────────────────────────────────\n", "CSS = \"\"\"\n", ".main-container {max-width: 1200px; margin: 0 auto; padding: 20px;}\n", ".header-text {text-align: center; margin-bottom: 20px;}\n", "\"\"\"\n", "\n", "def create_chatbot() -> gr.Blocks:\n", "    with gr.<PERSON><PERSON>(title=\"DeepSeek Chat\", css=CSS) as demo:\n", "        with gr.<PERSON>n(elem_classes=[\"main-container\"]):\n", "            gr.Markdown(\"# 🤖 <PERSON><PERSON>hain B站公开课 By九天Hector\", elem_classes=[\"header-text\"])\n", "            gr.Markdown(\"基于 LangChain LCEL 构建的流式对话机器人\", elem_classes=[\"header-text\"])\n", "\n", "            chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                height=500,\n", "                show_copy_button=True,\n", "                avatar_images=(\n", "                    \"https://cdn.jsdelivr.net/gh/twitter/twemoji@v14.0.2/assets/72x72/1f464.png\",\n", "                    \"https://cdn.jsdelivr.net/gh/twitter/twemoji@v14.0.2/assets/72x72/1f916.png\",\n", "                ),\n", "            )\n", "            msg = gr.Textbox(placeholder=\"请输入您的问题...\", container=False, scale=7)\n", "            submit = gr.<PERSON><PERSON>(\"发送\", scale=1, variant=\"primary\")\n", "            clear = gr.<PERSON>(\"清空\", scale=1)\n", "\n", "        # ---------------  状态：保存 messages_list  ---------------\n", "        state = gr.State([])          # 这里存放真正的 Message 对象列表\n", "\n", "        # ---------------  主响应函数（流式） ----------------------\n", "        async def respond(user_msg: str, chat_hist: list, messages_list: list):\n", "            # 1) 输入为空直接返回\n", "            if not user_msg.strip():\n", "                yield \"\", chat_hist, messages_list\n", "                return\n", "\n", "            # 2) 追加用户消息\n", "            messages_list.append(HumanMessage(content=user_msg))\n", "            chat_hist = chat_hist + [(user_msg, None)]\n", "            yield \"\", chat_hist, messages_list      # 先显示用户消息\n", "\n", "            # 3) 流式调用模型\n", "            partial = \"\"\n", "            async for chunk in qa_chain.astream({\"messages\": messages_list}):\n", "                partial += chunk\n", "                # 更新最后一条 AI 回复\n", "                chat_hist[-1] = (user_msg, partial)\n", "                yield \"\", chat_hist, messages_list\n", "\n", "            # 4) 完整回复加入历史，裁剪到最近 50 条\n", "            messages_list.append(AIMessage(content=partial))\n", "            messages_list = messages_list[-50:]\n", "\n", "            # 5) 最终返回（Gradio 需要把新的 state 传回）\n", "            yield \"\", chat_hist, messages_list\n", "\n", "        # ---------------  清空函数 -------------------------------\n", "        def clear_history():\n", "            return [], \"\", []          # 清空 Chatbot、输入框、messages_list\n", "\n", "        # ---------------  事件绑定 ------------------------------\n", "        msg.submit(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "        submit.click(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "        clear.click(clear_history, outputs=[chatbot, msg, state])\n", "\n", "    return demo\n", "\n", "\n", "# ──────────────────────────────────────────────\n", "# 3. 启动应用\n", "# ──────────────────────────────────────────────\n", "demo = create_chatbot()\n", "demo.launch(server_name=\"0.0.0.0\", server_port=7860, share=True, debug=True)\n"]}], "metadata": {"kernelspec": {"display_name": "MyLLM", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}