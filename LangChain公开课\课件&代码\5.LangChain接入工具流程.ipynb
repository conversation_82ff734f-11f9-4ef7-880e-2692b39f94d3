import os
from dotenv import load_dotenv 
load_dotenv(override=True)

!pip install -qU langchain-community langchain-experimental pandas -i https://pypi.tuna.tsinghua.edu.cn/simple

import numpy as np
import pandas as pd

dataset = pd.read_csv('WA_Fn-UseC_-Telco-Customer-Churn.csv')

pd.set_option('max_colwidth',200)

dataset.head(5)

dataset.info()

import pandas as pd
from langchain_core.prompts import ChatPromptTemplate
from langchain_experimental.tools import PythonAstREPLTool

df = pd.read_csv('WA_Fn-UseC_-Telco-Customer-Churn.csv')
tool = PythonAstREPLTool(locals={"df": df})
tool.invoke("df['SeniorCitizen'].mean()")

df['MonthlyCharges'].mean()

import os
from dotenv import load_dotenv 
load_dotenv(override=True)

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.chat_models import init_chat_model
from langchain_core.output_parsers import StrOutputParser

model  = init_chat_model(model="deepseek-chat", model_provider="deepseek")

llm_with_tools = model.bind_tools([tool])

response = llm_with_tools.invoke(
    "我有一张表，名为'df'，请帮我计算MonthlyCharges字段的均值。"
)
response

from langchain_core.output_parsers.openai_tools import JsonOutputKeyToolsParser

parser = JsonOutputKeyToolsParser(key_name=tool.name, first_tool_only=True)

llm_chain = llm_with_tools | parser

llm_chain.invoke("我有一张表，名为'df'，请帮我计算MonthlyCharges字段的均值。")

system = f"""
你可以访问一个名为 `df` 的 pandas 数据框，你可以使用df.head().to_markdown() 查看数据集的基本信息， \
请根据用户提出的问题，编写 Python 代码来回答。只返回代码，不返回其他内容。只允许使用 pandas 和内置库。
"""

prompt = ChatPromptTemplate([
    ("system", system),
    ("user", "{question}")
])

code_chain = prompt | llm_with_tools | parser

code_chain.invoke({"question": "请帮我计算MonthlyCharges字段的均值。"})

chain = prompt | llm_with_tools | parser | tool
chain.invoke({"question": "请帮我计算MonthlyCharges字段的均值。"})

chain.invoke({"question": "请帮我分析gender、SeniorCitizen和Churn三个字段之间的相关关系。"})

def code_print(res):
    print("即将运行Python代码:", res['query'])
    return res

res = code_chain.invoke({"question": "请帮我计算MonthlyCharges字段的均值。"})

res

res['query']

print("即将运行Python代码:", res['query'])

from langchain_core.runnables import RunnableLambda

def code_print(res):
    print("即将运行Python代码:", res['query'])
    return res

print_node = RunnableLambda(code_print)

print_code_chain = prompt | llm_with_tools | parser | print_node | tool
print_code_chain.invoke({"question": "请帮我计算MonthlyCharges字段的均值。"})

df['MonthlyCharges'].mean()

print_code_chain = prompt | llm_with_tools | parser | print_node | tool
print_code_chain.invoke({"question": "请问MonthlyCharges取值最高的用户ID是？"})

import os
from dotenv import load_dotenv 
load_dotenv(override=True)

OPENWEATHER_API_KEY = os.getenv("OPENWEATHER_API_KEY")

import requests,json,os

def get_weather(loc):
    """
    查询即时天气函数
    :param loc: 必要参数，字符串类型，用于表示查询天气的具体城市名称，\
    注意，中国的城市需要用对应城市的英文名称代替，例如如果需要查询北京市天气，则loc参数需要输入'Beijing'；
    :return：OpenWeather API查询即时天气的结果，具体URL请求地址为：https://api.openweathermap.org/data/2.5/weather\
    返回结果对象类型为解析之后的JSON格式对象，并用字符串形式进行表示，其中包含了全部重要的天气信息
    """
    # Step 1.构建请求
    url = "https://api.openweathermap.org/data/2.5/weather"

    # Step 2.设置查询参数
    params = {
        "q": loc,               
        "appid": os.getenv("OPENWEATHER_API_KEY"),    # 输入API key
        "units": "metric",            # 使用摄氏度而不是华氏度
        "lang":"zh_cn"                # 输出语言为简体中文
    }

    # Step 3.发送GET请求
    response = requests.get(url, params=params)
    
    # Step 4.解析响应
    data = response.json()
    return json.dumps(data)

get_weather("Beijing")

get_weather("Chengdu")

from langchain_core.tools import tool

@tool
def get_weather(loc):
    """
    查询即时天气函数
    :param loc: 必要参数，字符串类型，用于表示查询天气的具体城市名称，\
    注意，中国的城市需要用对应城市的英文名称代替，例如如果需要查询北京市天气，则loc参数需要输入'Beijing'；
    :return：OpenWeather API查询即时天气的结果，具体URL请求地址为：https://api.openweathermap.org/data/2.5/weather\
    返回结果对象类型为解析之后的JSON格式对象，并用字符串形式进行表示，其中包含了全部重要的天气信息
    """
    # Step 1.构建请求
    url = "https://api.openweathermap.org/data/2.5/weather"

    # Step 2.设置查询参数
    params = {
        "q": loc,               
        "appid": os.getenv("OPENWEATHER_API_KEY"),    # 输入API key
        "units": "metric",            # 使用摄氏度而不是华氏度
        "lang":"zh_cn"                # 输出语言为简体中文
    }

    # Step 3.发送GET请求
    response = requests.get(url, params=params)
    
    # Step 4.解析响应
    data = response.json()
    return json.dumps(data)

print(get_weather.name)
print(get_weather.description)
print(get_weather.args)

from langchain.chat_models import init_chat_model

# 初始化模型
model = init_chat_model("deepseek-chat", model_provider="deepseek")

# 定义 天气查询 工具函数
tools = [get_weather]

# 将工具绑定到模型
llm_with_tools = model.bind_tools(tools)

response = llm_with_tools.invoke("你好， 请问北京的天气怎么样？")

print(response)

response.additional_kwargs

from langchain_core.output_parsers.openai_tools import JsonOutputKeyToolsParser

parser = JsonOutputKeyToolsParser(key_name=get_weather.name, first_tool_only=True)

llm_chain = llm_with_tools | parser

llm_chain.invoke("请问北京今天天气如何？")

get_weather_chain = prompt | llm_with_tools | parser | get_weather

get_weather_chain.invoke("请问北京今天天气如何？")

from langchain.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser

from langchain.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser

# Prompt 模板
output_prompt = PromptTemplate.from_template(
    """你将收到一段 JSON 格式的天气数据，请用简洁自然的方式将其转述给用户。
以下是天气 JSON 数据：

```json
{weather_json}
```

请将其转换为中文天气描述，例如：
“北京当前天气晴，气温为 23°C，湿度 58%，风速 2.1 米/秒。”
只返回一句话描述，不要其他说明或解释。"""
)

output_chain = output_prompt | model | StrOutputParser()

weather_json = '{"coord": {"lon": 116.3972, "lat": 39.9075}, "weather": [{"id": 803, "main": "Clouds", "description": "\\u591a\\u4e91", "icon": "04d"}], "base": "stations", "main": {"temp": 34.94, "feels_like": 33.23, "temp_min": 34.94, "temp_max": 34.94, "pressure": 1002, "humidity": 22, "sea_level": 1002, "grnd_level": 997}, "visibility": 10000, "wind": {"speed": 6.23, "deg": 175, "gust": 9.33}, "clouds": {"all": 74}, "dt": 1749724384, "sys": {"type": 1, "id": 9609, "country": "CN", "sunrise": 1749674728, "sunset": 1749728589}, "timezone": 28800, "id": 1816670, "name": "Beijing", "cod": 200}'

result = output_chain.invoke({"weather_json": weather_json})
print(result)

full_chain = get_weather_chain | output_chain
response = full_chain.invoke("请问北京今天的天气如何？")
print(response)

from langchain.agents import create_tool_calling_agent, tool
from langchain_core.prompts import ChatPromptTemplate

#定义工具
tools = [get_weather]

# 构建提示模版
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "你是天气助手，请根据用户的问题，给出相应的天气信息"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ]
)

# 初始化模型
model = init_chat_model("deepseek-chat", model_provider="deepseek")

# 直接使用`create_tool_calling_agent`创建代理
agent = create_tool_calling_agent(model, tools, prompt)

from langchain.agents import AgentExecutor
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)


response = agent_executor.invoke({"input": "请问今天北京的天气怎么样？"})
print(response)

print(response["output"])

from langchain.agents import create_tool_calling_agent, tool
from langchain_core.prompts import ChatPromptTemplate

#定义工具
tools = [get_weather]

# 构建提示模版
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "你是天气助手，请根据用户的问题，给出相应的天气信息"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ]
)

# 初始化模型
model = init_chat_model("deepseek-chat", model_provider="deepseek")

# 直接使用`create_tool_calling_agent`创建代理
agent = create_tool_calling_agent(model, tools, prompt)

from langchain.agents import AgentExecutor
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)


response = agent_executor.invoke({"input": "请问今天北京和杭州的天气怎么样，哪个城市更热？"})
print(response)

@tool
def write_file(content):
    """
    将指定内容写入本地文件。
    :param content: 必要参数，字符串类型，用于表示需要写入文档的具体内容。
    :return：是否成功写入
    """
    
    return "已成功写入本地文件。"

from langchain.agents import AgentExecutor, create_tool_calling_agent, tool
from langchain_core.prompts import ChatPromptTemplate


tools = [get_weather, write_file]

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", "你是天气助手，请根据用户的问题，给出相应的天气信息，如果用户需要将查询结果写入文件，请使用write_file工具"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}"),
    ]
)

# 初始化模型
model = init_chat_model("deepseek-chat", model_provider="deepseek")

agent = create_tool_calling_agent(model, tools, prompt)

agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)


agent_executor.invoke({"input": "查一下北京和杭州现在的温度，并将结果写入本地的文件中。"})






