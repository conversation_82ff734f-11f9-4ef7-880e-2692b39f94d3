{"cells": [{"cell_type": "markdown", "id": "c687a1ea-1877-454d-8b8a-2ec2d06ac3ed", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 3.LangChain链条创建与LCEL语法"]}, {"cell_type": "code", "execution_count": 3, "id": "58447c42-4f63-4cd5-98f1-4a0d6b2119b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "id": "bd874dab-a9a6-4d5a-bd07-3800dd971fb0", "metadata": {}, "source": ["### 1. LangC<PERSON>核心功能：链式调用实现方法"]}, {"cell_type": "markdown", "id": "080e640d-dc6a-402f-8fac-39e1eebd9bd8", "metadata": {}, "source": ["&emsp;&emsp;顾名思义，`<PERSON><PERSON>hain`之所以被称为`<PERSON><PERSON>hain`，其核心概念就是`Chain`。 `Chain`翻译成中文就是“链”。一个链，指的是可以按照某一种逻辑，按顺序组合成一个流水线的方式。比如我们刚刚实现的问答流程： 用户输入一个问题 --> 发送给大模型 --> 大模型进行推理 --> 将推理结果返回给用户。这个流程就是一个链。"]}, {"cell_type": "markdown", "id": "ef2611de-6ddb-40db-8977-7ebc76211271", "metadata": {}, "source": ["- 尝试搭建一个简单的链"]}, {"cell_type": "markdown", "id": "6ad2fc49-fa1b-4dc3-a814-16f39c9c78aa", "metadata": {}, "source": ["&emsp;&emsp;例如，我们这里可以先尝试着搭建一个简单的链，将模型输出结果“过滤”为一个纯字符串格式："]}, {"cell_type": "code", "execution_count": 4, "id": "1c010d36-ebc0-41d8-a56c-ef8da9e8e6ab", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain.chat_models import init_chat_model\n", "\n", "# 使用 DeepSeek 模型\n", "model = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")  \n", "\n", "# 直接使用模型 + 输出解析器搭建一个链\n", "basic_qa_chain = model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 5, "id": "6bd23eb7-45bb-45bd-b148-5650a2cf9bbb", "metadata": {}, "outputs": [], "source": ["# 查看输出结果\n", "question = \"你好，请你介绍一下你自己。\"\n", "result = basic_qa_chain.invoke(question)"]}, {"cell_type": "code", "execution_count": 7, "id": "d1ca5c2f-cfa7-49d0-a3e0-54292642e8e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'你好！我是 **DeepSeek Chat**，由深度求索（DeepSeek）公司打造的智能AI助手。我的核心能力是帮助你解答各种问题、提供信息、辅助创作、分析数据，甚至陪你聊天！😊  \\n\\n### **我的特点：**  \\n✅ **知识丰富**：我的知识截止到**2024年7月**，可以回答科技、历史、文学、数学、编程、生活百科等各种问题。  \\n✅ **超长上下文**：支持**128K**上下文记忆，能理解和处理超长文档，比如论文、书籍、合同等。  \\n✅ **文件阅读**：可以解析 **PDF、Word、Excel、PPT、TXT** 等文件，帮助你提取关键信息或总结内容。  \\n✅ **免费使用**：目前无需付费，你可以随时向我提问！  \\n✅ **多语言支持**：可以用中文、英文等多种语言交流。  \\n\\n### **我能帮你做什么？**  \\n📖 **学习辅导**：解题思路、论文润色、代码调试  \\n💡 **创意写作**：写故事、起标题、生成营销文案  \\n📊 **办公辅助**：总结报告、分析数据、制作PPT大纲  \\n🤖 **编程助手**：写代码、优化算法、解释技术概念  \\n🌍 **生活百科**：旅行建议、健康小贴士、美食推荐  \\n\\n你可以随时向我提问，我会尽力提供最准确、有用的答案！🎉 你今天想了解什么呢？'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "id": "88ebbf93-f058-4155-9c04-58dbcb4d19fd", "metadata": {}, "source": ["此时result就不再是包含各种模型调用信息的结果，而是纯粹的模型响应的字符串结果。而这里用到的StrOutputParser()实际上就是用于构成LangChain中一个链条的一个对象，其核心功能是用于处理模型输出结果。同时我们也能发现，只需要使用`Model | OutputParser`，即可高效搭建一个链。"]}, {"cell_type": "markdown", "id": "2bdeedf4-514e-43ff-b2f9-7ca8c288f01a", "metadata": {}, "source": ["&emsp;&emsp;类似这种结果解析器还有很多，稍后我们会继续进行介绍。"]}, {"cell_type": "markdown", "id": "625952b0-4480-429f-a623-7f43ee0c3975", "metadata": {}, "source": ["- 加入提示词模板创建链"]}, {"cell_type": "markdown", "id": "c660a0d7-bc24-4bf7-98dd-5e9f58dff6c5", "metadata": {}, "source": ["&emsp;&emsp;接下来我们尝试为当前的执行流程添加一个提示词模板，我们可以借助ChatPromptTemplate非常便捷的将一个提示词模板，同样以链的形式加入到当前任务中："]}, {"cell_type": "code", "execution_count": 23, "id": "6f4962a4-8a48-4640-9871-8685fd1d4dda", "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers.boolean import BooleanOutputParser\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "\n", "prompt_template = ChatPromptTemplate([\n", "    (\"system\", \"你是一个乐意助人的助手，请根据用户的问题给出回答\"),\n", "    (\"user\", \"这是用户的问题： {topic}， 请用 yes 或 no 来回答\")\n", "])\n", "\n", "# 直接使用模型 + 输出解析器\n", "bool_qa_chain = prompt_template | model | StrOutputParser()\n", "# 测试\n", "question = \"请问 1 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)"]}, {"cell_type": "code", "execution_count": 24, "id": "a47ca0cd-3a71-44f1-8fe3-ca4994c1b849", "metadata": {}, "outputs": [{"data": {"text/plain": ["'no'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "id": "fab5d005-82cb-403c-b370-eb40670ae1d3", "metadata": {}, "source": ["&emsp;&emsp;一个最基本的`Chain`结构，是由`Model`和`OutputParser`两个组件构成的，其中`Model`是用来调用大模型的，`OutputParser`是用来解析大模型的响应结果的。所以一个最简单的`LLMChain`结构，其数据流向正如下图所示："]}, {"cell_type": "markdown", "id": "6700b7ce-c55f-477f-9bde-27ce60fac5c4", "metadata": {}, "source": ["<div align=center><img src=\"https://snowball101.oss-cn-beijing.aliyuncs.com/img/202403191145938.png\" width=80%></div>"]}, {"cell_type": "markdown", "id": "2fb5dd50-dd2e-4d9d-9cf4-e38467323bf4", "metadata": {}, "source": ["当我们调用指令跟随能力较强的大模型的时候，借助提示词模板即可实现结构化输出的结果。"]}, {"cell_type": "markdown", "id": "30709015-13bb-4256-b4fe-c68f1d694db8", "metadata": {}, "source": ["- 借助提示词模板和结果解析器实现功能更加复杂的链"]}, {"cell_type": "markdown", "id": "c147bed0-1871-4f39-84ca-cc51e7582bfd", "metadata": {}, "source": ["至此，我们就搭建了一个非常基础的链。在LangChain中，一个基础的链主要由三部分构成，分别是提示词模板、大模型和结果解析器（结构化解析器）："]}, {"cell_type": "raw", "id": "9aa2bd1a-db07-4a81-9bc2-5168c60d625f", "metadata": {}, "source": ["用户输入\n", "  ↓\n", "PromptTemplate → ChatModel → OutputParser\n", "（提示词模板）   （大模型）    （结构化解析）\n", "  ↓\n", "构化结果\n"]}, {"cell_type": "markdown", "id": "cbd1321a-3a4e-4e01-8d60-f4b66823f350", "metadata": {}, "source": ["而相比之下，结构化解析器功能最多，一些核心的结构化解析器功能如下："]}, {"cell_type": "markdown", "id": "486ab94f-76ab-457a-875b-a9622c87cee0", "metadata": {}, "source": ["| 解析器名称 | 功能描述 | 类型 |\n", "|-----------|---------|------|\n", "| **BooleanOutputParser** | 将LLM输出解析为布尔值 | 基础类型解析 |\n", "| **DatetimeOutputParser** | 将LLM输出解析为日期时间 | 基础类型解析 |\n", "| **EnumOutputParser** | 解析输出为预定义枚举值之一 | 基础类型解析 |\n", "| **RegexParser** | 使用正则表达式解析LLM输出 | 模式匹配解析 |\n", "| **RegexDictParser** | 使用正则表达式将输出解析为字典 | 模式匹配解析 |\n", "| **StructuredOutputParser** | 将LLM输出解析为结构化格式 | 结构化解析 |\n", "| **YamlOutputParser** | 使用Pydantic模型解析YAML输出 | 结构化解析 |\n", "| **PandasDataFrameOutputParser** | 使用Pandas DataFrame格式解析输出 | 数据处理解析 |\n", "| **CombiningOutputParser** | 将多个输出解析器组合为一个 | 组合解析器 |\n", "| **OutputFixingParser** | 包装解析器并尝试修复解析错误 | 错误处理解析 |\n", "| **RetryOutputParser** | 包装解析器并尝试修复解析错误 | 错误处理解析 |\n", "| **RetryWithErrorOutputParser** | 包装解析器并尝试修复解析错误 | 错误处理解析 |\n", "| **ResponseSchema** | 结构化输出解析器的响应模式 | 辅助类 |\n", "\n"]}, {"cell_type": "markdown", "id": "f1c37894-b0d9-435c-8340-bc822de1fbef", "metadata": {}, "source": ["一些功能实现如下，例如借助结构化解析器可以将yes or no转化为True or Fasle："]}, {"cell_type": "code", "execution_count": 27, "id": "8d699aa9-2d59-4c8a-8f75-edd77ff7a38a", "metadata": {}, "outputs": [], "source": ["prompt_template = ChatPromptTemplate([\n", "    (\"system\", \"你是一个乐意助人的助手，请根据用户的问题给出回答\"),\n", "    (\"user\", \"这是用户的问题： {topic}， 请用 yes 或 no 来回答\")\n", "])\n", "\n", "# 直接使用模型 + 输出解析器\n", "bool_qa_chain = prompt_template | model | BooleanOutputParser()\n", "# 测试\n", "question = \"请问 1 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)"]}, {"cell_type": "code", "execution_count": 28, "id": "30258a71-bc7a-4c21-b956-1811dc87ee52", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 29, "id": "61e80bf5-7f82-4eda-a97c-2a5f04e08e30", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["# 测试\n", "question = \"请问 4 + 1 是否 大于 2？\"\n", "result = bool_qa_chain.invoke(question)\n", "print(result)"]}, {"cell_type": "markdown", "id": "feac904b-50a7-45eb-9cd4-0f6920f3bea1", "metadata": {}, "source": ["而StructuredOutputParser则可以在文档中提取指定的结构化信息："]}, {"cell_type": "code", "execution_count": 18, "id": "c0beeebe-467f-4505-9774-9f6318303bc3", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate"]}, {"cell_type": "code", "execution_count": 19, "id": "61105f2c-1b09-46a5-9c87-9a39fa4cdcd0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '李雷', 'age': '25'}\n"]}], "source": ["from langchain.output_parsers import ResponseSchema, StructuredOutputParser\n", "\n", "schemas = [\n", "    ResponseSchema(name=\"name\", description=\"用户的姓名\"),\n", "    ResponseSchema(name=\"age\", description=\"用户的年龄\")\n", "]\n", "parser = StructuredOutputParser.from_response_schemas(schemas)\n", "\n", "prompt = PromptTemplate.from_template(\n", "    \"请根据以下内容提取用户信息，并返回 JSON 格式：\\n{input}\\n\\n{format_instructions}\"\n", ")\n", "\n", "chain = (\n", "    prompt.partial(format_instructions=parser.get_format_instructions())\n", "    | model\n", "    | parser\n", ")\n", "\n", "result = chain.invoke({\"input\": \"用户叫李雷，今年25岁，是一名工程师。\"})\n", "print(result)  "]}, {"cell_type": "markdown", "id": "116ca2dd-4444-49ff-ab71-e96e8f1bba13", "metadata": {}, "source": ["这里我们在 PromptTemplate 中，你定义了两个占位符变量：\n", "\n", "- {input} → 将由用户传入的文本替换（如 \"用户叫李雷，今年25岁...\"）\n", "\n", "- {format_instructions} → 会通过 partial(...) 提前绑定结构化格式说明\n", "\n", "而格式化说明使用format_instructions进行标识其实也是一种约定俗称的方法，上述代码也就是相当于在创建Chain的时候，我们就输入了{format_instructions}对应的字符串，我们也可以通过如下代码进行打印查看："]}, {"cell_type": "code", "execution_count": 20, "id": "638463a7-229e-4247-b7ab-e03ce1666c5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The output should be a markdown code snippet formatted in the following schema, including the leading and trailing \"```json\" and \"```\":\n", "\n", "```json\n", "{\n", "\t\"name\": string  // 用户的姓名\n", "\t\"age\": string  // 用户的年龄\n", "}\n", "```\n"]}], "source": ["print(parser.get_format_instructions())"]}, {"cell_type": "markdown", "id": "1dc1f0b1-ccae-48cd-b560-5e7b6577f02a", "metadata": {}, "source": ["- 创建复合链"]}, {"cell_type": "code", "execution_count": 36, "id": "c2e6b20d-dd3e-4cb9-bfe9-8df57578281e", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.runnables import RunnableSequence\n", "from langchain.output_parsers import ResponseSchema, StructuredOutputParser"]}, {"cell_type": "code", "execution_count": 38, "id": "468629ad-a052-4c4b-a061-ba1987198b18", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'time': '近日', 'location': '加州总部', 'event': '苹果公司发布全新AI芯片M4，性能较前代提升50%，专为下一代Mac设备设计。该芯片采用3纳米制程工艺，支持更强大的机器学习任务，如图像生成与实时语音处理。CEO库克表示，M4将\"重新定义创意生产力\"。业内分析指出，此举或进一步巩固苹果在AI硬件领域的领先地位，新品预计2024年末上市。'}\n"]}], "source": ["# 第一步：根据标题生成新闻正文\n", "news_gen_prompt = PromptTemplate.from_template(\n", "    \"请根据以下新闻标题撰写一段简短的新闻内容（100字以内）：\\n\\n标题：{title}\"\n", ")\n", "\n", "# 第一个子链：生成新闻内容\n", "news_chain = news_gen_prompt | model\n", "\n", "# 第二步：从正文中提取结构化字段\n", "schemas = [\n", "    ResponseSchema(name=\"time\", description=\"事件发生的时间\"),\n", "    ResponseSchema(name=\"location\", description=\"事件发生的地点\"),\n", "    ResponseSchema(name=\"event\", description=\"发生的具体事件\"),\n", "]\n", "parser = StructuredOutputParser.from_response_schemas(schemas)\n", "\n", "summary_prompt = PromptTemplate.from_template(\n", "    \"请从下面这段新闻内容中提取关键信息，并返回结构化JSON格式：\\n\\n{news}\\n\\n{format_instructions}\"\n", ")\n", "\n", "# 第二个子链：生成新闻摘要\n", "summary_chain = (\n", "    summary_prompt.partial(format_instructions=parser.get_format_instructions())\n", "    | model\n", "    | parser\n", ")\n", "\n", "# 组合成一个复合 Chain\n", "full_chain = news_chain | summary_chain\n", "\n", "# 调用复合链\n", "result = full_chain.invoke({\"title\": \"苹果公司在加州发布新款AI芯片\"})\n", "print(result)"]}, {"cell_type": "raw", "id": "5d447c68-0568-481b-bb7d-c03ff386d9d4", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["用户输入（title）  \n", "        │  \n", "        ▼  \n", "┌────────────────────────────┐  \n", "│  Chain 1: 生成新闻正文      │  \n", "│  Prompt: news_gen_prompt   │  \n", "│  Model: DeepSeek           │  \n", "└────────────────────────────┘  \n", "        │  \n", "        ▼  \n", "生成的新闻内容（news）  \n", "        │  \n", "        ▼  \n", "┌───────────────────────────────────────┐  \n", "│  Chain 2:        提取结构化字段（摘要） │  \n", "│  Prompt: summary_pro   mpt            │  \n", "│  Model: DeepSeek                      │  \n", "│  OutputParser: StructuredOutputParser │  \n", "└───────────────────────────────────────┘  \n", "        │  \n", "        ▼  \n", "结化输出（如 JSON：时间、地点、事件）\n"]}, {"cell_type": "markdown", "id": "cfad2537-6342-441c-a6c2-cea20b6a17ef", "metadata": {}, "source": ["- 借助LangChain适配器设置自定义可运行的节点"]}, {"cell_type": "code", "execution_count": 39, "id": "2557f04d-9e81-405d-8b46-259027a91607", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "# 一个简单的打印函数，调试用\n", "def debug_print(x):\n", "    print(\"中间结果（新闻正文）:\", x)\n", "    return x\n", "\n", "debug_node = RunnableLambda(debug_print)\n", "\n", "# 插入 debug 节点\n", "full_chain = news_chain | debug_node | summary_chain"]}, {"cell_type": "code", "execution_count": 40, "id": "594356ea-f6fc-4ae1-89d7-8b446619e5be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中间结果（新闻正文）: content='苹果公司近日在加州总部发布全新AI芯片，性能提升显著，专为下一代智能设备设计。该芯片采用先进制程工艺，支持更高效的机器学习任务，有望应用于iPhone、Mac等产品线。分析师指出，此举将强化苹果在AI领域的竞争力，或推动消费电子行业技术升级。公司表示首批搭载该芯片的设备将于2024年底面市。（98字）' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 82, 'prompt_tokens': 28, 'total_tokens': 110, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 28}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0425fp8', 'id': '5e9fa296-4120-4361-b5a2-ca3ce6ebc13c', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None} id='run--6141698f-4ea7-41bf-89ec-1902dd6e5bc2-0' usage_metadata={'input_tokens': 28, 'output_tokens': 82, 'total_tokens': 110, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}}\n", "{'time': '近日', 'location': '加州总部', 'event': '苹果公司发布全新AI芯片，性能提升显著，专为下一代智能设备设计。该芯片采用先进制程工艺，支持更高效的机器学习任务，有望应用于iPhone、Mac等产品线。分析师指出，此举将强化苹果在AI领域的竞争力，或推动消费电子行业技术升级。公司表示首批搭载该芯片的设备将于2024年底面市。'}\n"]}], "source": ["# 调用复合链\n", "result = full_chain.invoke({\"title\": \"苹果公司在加州发布新款AI芯片\"})\n", "print(result)"]}, {"cell_type": "markdown", "id": "b00ad074-d698-4436-b650-aaafa457bfc7", "metadata": {}, "source": ["&emsp;&emsp;通过上述不同的尝试，我们就已经理解了在`lang<PERSON>hain`中，如何使用`Model`、`OutputParser`、`ChatPromptTemplate`来构建一个简单的`Chain`。其中：\n", "\n", "1. `ChatPromptTemplate` 是用来构建提示模板的，将输入的问题转化为消息列表，可以设置系统指令，也可以添加一些变量；\n", "2. `Model` 是用来调用大模型的，可以指定使用不同的模型；\n", "3. `OutputParser` 是用来解析大模型的响应结果的，可以指定使用不同的解析器。"]}, {"cell_type": "markdown", "id": "2433f657-b3fe-466d-a24a-ae14c11fea2d", "metadata": {}, "source": ["&emsp;&emsp;了解到这里，我们就可以基于`Lang<PERSON><PERSON>n`来快速开发出一个智能问答机器人。"]}, {"cell_type": "markdown", "id": "063d0181-91ed-459d-ac24-ae3b0cb00ef2", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["【补充】LCEL关键概念介绍\n", "##### 什么是 LCEL？——LangChain Expression Language 详解\n", "\n", "在现代大语言模型（LLM）应用的构建中，LangChain 提供了一种全新的表达范式，被称为 **LCEL（LangChain Expression Language）**。它不仅简化了模型交互的编排过程，还增强了组合的灵活性和可维护性。本文将从概念、设计目的、核心特性和实际价值几个方面，系统性地介绍 LCEL 的本质。\n", "\n", "---\n", "\n", "##### 一、LCEL 的定义\n", "\n", "LCEL，全称为 **LangChain Expression Language**，是一种专为 LangChain 框架设计的表达语言。它通过一种链式组合的方式，允许开发者使用清晰、声明式的语法来构建语言模型驱动的应用流程。\n", "\n", "简单来说，LCEL 是一种“函数式管道风格”的组件组合机制，用于连接各种可执行单元（Runnable）。这些单元包括提示模板、语言模型、输出解析器、工具函数等。\n", "\n", "---\n", "\n", "##### 二、设计目的\n", "\n", "LCEL 的设计初衷在于：\n", "\n", "1. **模块化构建**：将模型调用流程拆解为独立、可重用的组件。\n", "2. **逻辑可视化**：通过语法符号（如管道符 `|`）呈现出明确的数据流路径。\n", "3. **统一运行接口**：所有 LCEL 组件都实现了 `.invoke()`、`.stream()`、`.batch()` 等标准方法，便于在同步、异步或批处理环境下调用。\n", "4. **脱离框架限制**：相比传统的 `Chain` 类和 `Agent` 架构，LCEL 更轻量、更具表达力，减少依赖的“黑盒”逻辑。\n", "\n", "---\n", "\n", "##### 三、核心组成\n", "\n", "###### 1. <PERSON><PERSON><PERSON> 接口\n", "\n", "LCEL 的一切基础单元都是 `Runnable` 对象，它是一种统一的可调用接口，支持如下形式：\n", "\n", "* `.invoke(input)`：同步调用\n", "* `.stream(input)`：流式生成\n", "* `.batch(inputs)`：批量执行\n", "\n", "###### 2. 管道运算符 `|`\n", "\n", "这是 LCEL 最具特色的语法符号。多个 `Runnable` 对象可以通过 `|` 串联起来，形成清晰的数据处理链。例如：\n", "\n", "```python\n", "prompt | model | parser\n", "```\n", "\n", "表示数据将依次传入提示模板、模型和输出解析器，最终输出结构化结果。\n", "\n", "###### 3. PromptTemplate 与 OutputParser\n", "\n", "LCEL 强调组件之间的职责明确，Prompt 只负责模板化输入，Parser 只负责格式化输出，Model 只负责推理。\n", "\n", "---\n", "\n", "##### 四、典型优势\n", "\n", "| 特性                    | 描述                       |             |\n", "| --------------------- | ------------------------ | ----------- |\n", "| 简洁语法                  | 使用 \\`                    | \\` 运算符提升可读性 |\n", "| 灵活组合                  | 可任意组合 Prompt、模型、工具、函数等组件 |             |\n", "| 明确边界                  | 每个步骤职责分明，方便调试与重用         |             |\n", "| 可嵌套扩展                 | 支持函数包装、自定义中间组件和流式拓展      |             |\n", "| 与 Gradio/FastAPI 集成良好 | 可用于构建 API、UI 聊天等多种场景     |             |\n", "\n", "---\n", "\n", "##### 五、总结\n", "\n", "LCEL 是 LangChain 在 2024 年末引入的一项重要特性，标志着从传统 Agent 架构向“声明式、组合式”开发范式的转变。它不仅让开发者能以更清晰的方式组织 LLM 工作流，也大大提高了系统的可维护性与可测试性。"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}