# 将文档转换为 Markdown
import os
import pathlib
import pymupdf4llm
import logging


# 配置日志系统
def setup_logger(name: str = "pdf_to_markdown") -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)

    # 添加处理器到 logger
    if not logger.handlers:
            logger.addHandler(console_handler)

    return logger


def pdf_to_markdown(pdf_path: str, output_dir) -> str:
    # 设置日志
    logger = setup_logger()
    basename = os.path.basename(pdf_path).split('.')[0]
    logger.info(f"正在处理{basename}.pdf")
    md_text = pymupdf4llm.to_markdown(pdf_path)
    output_md_path = os.path.join(output_dir, f"{basename}.md")
    pathlib.Path(output_md_path).write_bytes(md_text.encode())
    logger.info(f"{basename}.md已生成")

if __name__ == '__main__':
    pdf_list = [
                r"D:\liuzhihao\MyProject\LLM\example\input\Ceyear\1466系列信号发生器程控手册.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Ceyear\3674、3672、3671、3650、3657系列矢量网络分析仪编程手册.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Ceyear\4082_4052系列信号频谱分析仪程控手册.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\9018-18183.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\AC680XB-Series-Operating-and-Programming-Guide.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\AC6800B Series Basic AC Sources.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\AC6900-Series-Operating-and-Programming-Guide.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\Keysight E8257D program guide.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\Keysight N1911A Power Meters Programming Guide.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\Keysight X-Series Signal user manual.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\Keysight\Keysight 2000X-series Osc.pdf",
                r"D:\liuzhihao\MyProject\LLM\example\input\R&S\SMW200A_UserManual_en_38.pdf"
                ]
    # 设置日志
    logger = setup_logger()
    output_dir = r"D:\liuzhihao\MyProject\Mylangchain\output\02_test"
    for p in pdf_list:
        pdf_to_markdown(p, output_dir)
