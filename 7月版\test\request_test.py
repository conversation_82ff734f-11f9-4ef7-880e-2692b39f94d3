import requests
import json

# ==============================================================================
# 1. 发送 GET 请求
# ==============================================================================
# GET 是最常见的请求类型，用于从服务器获取数据。
print("--- 测试 GET 请求 ---")
try:
    # 附带参数的 GET 请求
    params = {'key1': 'value1', 'key2': 'value2'}
    response_get = requests.get('https://httpbin.org/get', params=params)
    
    # 检查响应状态码
    print(f"GET 请求状态码: {response_get.status_code}")
    
    # 检查是否成功（200 OK）
    if response_get.status_code == 200:
        print("GET 请求成功！")
        # 打印响应内容（JSON 格式）
        print("响应 JSON 内容:")
        print(json.dumps(response_get.json(), indent=2))
        
except requests.exceptions.RequestException as e:
    print(f"GET 请求失败: {e}")
print("-" * 30)


# ==============================================================================
# 2. 发送 POST 请求
# ==============================================================================
# POST 请求通常用于向服务器提交数据，例如创建新资源。
print("--- 测试 POST 请求 ---")
try:
    # 提交表单数据
    data_post = {'name': 'Gemini', 'job': 'AI assistant'}
    response_post = requests.post('https://httpbin.org/post', data=data_post)

    print(f"POST 请求状态码: {response_post.status_code}")
    if response_post.status_code == 200:
        print("POST 请求成功！")
        print("响应 JSON 内容:")
        print(json.dumps(response_post.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"POST 请求失败: {e}")
print("-" * 30)

# ==============================================================================
# 3. 发送带 JSON 数据的 POST 请求
# ==============================================================================
# 使用 json 参数可以更方便地发送 JSON 格式的数据。
print("--- 测试 POST JSON 请求 ---")
try:
    json_data = {'id': 123, 'status': 'active'}
    response_post_json = requests.post('https://httpbin.org/post', json=json_data)
    
    print(f"POST JSON 请求状态码: {response_post_json.status_code}")
    if response_post_json.status_code == 200:
        print("POST JSON 请求成功！")
        print("响应 JSON 内容:")
        print(json.dumps(response_post_json.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"POST JSON 请求失败: {e}")
print("-" * 30)


# ==============================================================================
# 4. 发送 PUT 请求
# ==============================================================================
# PUT 请求通常用于更新服务器上的现有资源。
print("--- 测试 PUT 请求 ---")
try:
    data_put = {'name': 'Updated Gemini'}
    response_put = requests.put('https://httpbin.org/put', data=data_put)

    print(f"PUT 请求状态码: {response_put.status_code}")
    if response_put.status_code == 200:
        print("PUT 请求成功！")
        print("响应 JSON 内容:")
        print(json.dumps(response_put.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"PUT 请求失败: {e}")
print("-" * 30)

# ==============================================================================
# 5. 发送 DELETE 请求
# ==============================================================================
# DELETE 请求用于从服务器上删除资源。
print("--- 测试 DELETE 请求 ---")
try:
    response_delete = requests.delete('https://httpbin.org/delete')
    
    print(f"DELETE 请求状态码: {response_delete.status_code}")
    if response_delete.status_code == 200:
        print("DELETE 请求成功！")
        print("响应 JSON 内容:")
        print(json.dumps(response_delete.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"DELETE 请求失败: {e}")
print("-" * 30)


# ==============================================================================
# 6. 设置请求头（Headers）
# ==============================================================================
# 自定义 Headers 可以用于身份验证、指定数据类型等。
print("--- 测试带自定义 Headers 的请求 ---")
try:
    headers = {
        'User-Agent': 'My Custom User Agent',
        'Accept': 'application/json'
    }
    response_headers = requests.get('https://httpbin.org/headers', headers=headers)
    
    print(f"Headers 请求状态码: {response_headers.status_code}")
    if response_headers.status_code == 200:
        print("Headers 请求成功！")
        print("响应 JSON 内容:")
        print(json.dumps(response_headers.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"Headers 请求失败: {e}")
print("-" * 30)

# ==============================================================================
# 7. 设置请求超时（Timeout）
# ==============================================================================
# timeout 参数可以防止程序因网络问题长时间阻塞。
print("--- 测试请求超时 ---")
try:
    # 设置一个 5 秒的超时
    response_timeout = requests.get('https://httpbin.org/delay/2', timeout=5)
    print(f"超时请求状态码: {response_timeout.status_code}")
    print("请求成功，没有超时。")
    
    # 尝试设置一个短于延迟时间的超时，会引发异常
    # requests.get('https://httpbin.org/delay/5', timeout=2)

except requests.exceptions.Timeout:
    print("请求超时！这正是我们预期的行为。")
except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
print("-" * 30)


# ==============================================================================
# 8. 使用 Session 会话
# ==============================================================================
# Session 对象可以保持跨请求的某些参数（如 Cookies），提高效率。
print("--- 测试 Session 会话 ---")
try:
    with requests.Session() as session:
        # 第一次请求，设置一个 cookie
        response_session_1 = session.get('https://httpbin.org/cookies/set?name=session_cookie')
        print(f"第一次 Session 请求状态码: {response_session_1.status_code}")

        # 第二次请求，会自动带上之前设置的 cookie
        response_session_2 = session.get('https://httpbin.org/cookies')
        print(f"第二次 Session 请求状态码: {response_session_2.status_code}")
        
        if response_session_2.status_code == 200:
            print("Session 请求成功！")
            print("响应 JSON 内容:")
            print(json.dumps(response_session_2.json(), indent=2))

except requests.exceptions.RequestException as e:
    print(f"Session 请求失败: {e}")
print("-" * 30)