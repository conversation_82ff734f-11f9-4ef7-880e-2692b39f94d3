{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-11T07:30:59.872674Z", "start_time": "2025-07-11T07:30:59.166337Z"}}, "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "pdf_emaxple_path = r\"D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\"\n", "loader = PyMuPDFLoader(pdf_emaxple_path)\n", "docs = loader.load()\n", "print(len(docs))\n", "docs[0]\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["276\n"]}, {"data": {"text/plain": ["Document(metadata={'producer': 'Microsoft® Word 2010', 'creator': 'Microsoft® Word 2010', 'creationdate': '2023-07-18T08:26:30+08:00', 'source': 'D:\\\\liuzhihao\\\\FinalCode\\\\input\\\\Ceyear\\\\1466系列信号发生器程控手册.pdf', 'file_path': 'D:\\\\liuzhihao\\\\FinalCode\\\\input\\\\Ceyear\\\\1466系列信号发生器程控手册.pdf', 'total_pages': 276, 'format': 'PDF 1.5', 'title': '', 'author': '高训兵', 'subject': '', 'keywords': '', 'moddate': '2023-07-18T08:27:25+08:00', 'trapped': '', 'modDate': \"D:20230718082725+08'00'\", 'creationDate': \"D:20230718082630+08'00'\", 'page': 0}, page_content='1466 系列 \\n       信号发生器 \\n       程控手册 \\n \\n \\n \\n \\n \\n \\n中电科思仪科技股份有限公司')"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "execution_count": 55}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:15:51.472719Z", "start_time": "2025-07-11T07:15:51.468857Z"}}, "cell_type": "code", "source": "docs[52].page_content", "id": "85fcbededc976101", "outputs": [{"data": {"text/plain": ["'3 程控命令 \\n3.2 通用命令 \\n \\n47 \\n \\n \\n2 \\n4 \\n查询错误 \\n3 \\n8 \\n设备相关错误 \\n4 \\n16 \\n执行错误 \\n5 \\n32 \\n命令错误 \\n6 \\n64 \\n本地键按下 \\n7 \\n128 \\n开机 \\n*IDN? \\n功能描述： 返回仪器标识。 \\n查询格式： *IDN? \\n返回值：   <ID>：\"生产厂家,<仪器型号>,<串号>,<固件版本号>\" 例如：Ceyear \\nTechnologies,1466,2017008,1.0.0 \\n举例：     *IDN?   \\n说明：     仅查询。 \\n*OPC \\n功能描述：该命令用于设置或查询信号发生器标准事件寄存器中的第0位。查询返回值\\n为1表示所有接收的指令已完成。 \\n设置格式：*OPC \\n查询格式：*OPC?  \\n举例：    *OPC? 如果等待的操作完成时，返回1，否则等待。 \\n*RCL <Value> \\n功能描述： 该命令从指定的信号发生器内部寄存器调用仪器状态。 \\n设置格式： *RCL <value> \\n参数说明：  整形数值 \\n范围[ 0，99 ]。 \\n举例：      *RCL 1  \\n说明：      仅设置。 \\n*RST \\n功能描述： 该命令完成信号发生器复位功能，将仪器复位到厂家默认的状态。 \\n设置格式： *RST \\n举例：    *RST 复位信号发生器到厂家默认状态 \\n说明：     仅设置。'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "execution_count": 48}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:28:10.686218Z", "start_time": "2025-07-11T07:28:10.682779Z"}}, "cell_type": "code", "source": ["import pprint\n", "\n", "# for doc in docs:\n", "#     print(doc.page_content)\n", "\n", "pprint.pp(docs[51].page_content)"], "id": "b9777c2fa4f0acb2", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('3 程控命令 \\n'\n", " '3.2 通用命令 \\n'\n", " '46 \\n'\n", " ' \\n'\n", " ' \\n'\n", " ' \\n'\n", " ' \\n'\n", " ' \\n'\n", " ' \\n'\n", " '命令使用： \\n'\n", " '如不特别说明，命令可用于设置或者查询。 \\n'\n", " '若一个命令仅用于设置或查询，或者启动一个事件，命令说明中会单独进行说明。 \\n'\n", " ' \\n'\n", " '*CLS \\n'\n", " '功能描述：清除状态。设置状态字节（STB）、标准事件寄存器（ESR）和问题操作寄 \\n'\n", " '存的事件部分为零。该命令不改变屏蔽和转移寄存器的值，并清除输出缓冲\\n'\n", " '区。 \\n'\n", " ' \\n'\n", " '设置格式：*CLS \\n'\n", " ' \\n'\n", " '举例：    *CLS  清空仪器状态 \\n'\n", " '说明：    仅设置。 \\n'\n", " '*ESE <Value> \\n'\n", " '功能描述：设置或查询标准事件状态使能寄存器。0禁止。1使能。  \\n'\n", " '设置格式： *ESE <value> \\n'\n", " '查询格式： *ESE? \\n'\n", " '参数说明：  \\n'\n", " '<Value> \\n'\n", " '整型数值，各个位的二进制加权和，位映射将表3.1 \\n'\n", " '范围 [ 0，255 ]。 \\n'\n", " '举例:       *ESE 60 使能第4+8+16+32 相应位即第2、3、4、5位。 \\n'\n", " '*ESR? \\n'\n", " '功能描述：读取事件状态寄存器的值，并清0该寄存器。见表3.1 \\n'\n", " '查询格式： *ESR? \\n'\n", " '返回值： \\n'\n", " '整形数值，各个位的二进制加权和，位映射表见表3.1 \\n'\n", " '范围 [ 0，255 ]。 \\n'\n", " '说明：    仅查询。 \\n'\n", " '表3.1 \\n'\n", " '标准事件位映射 \\n'\n", " '位 \\n'\n", " '值 \\n'\n", " '说明 \\n'\n", " '0 \\n'\n", " '1 \\n'\n", " '操作完成 \\n'\n", " '1 \\n'\n", " '2 \\n'\n", " '未用 \\n'\n", " '提 示')\n"]}], "execution_count": 53}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T02:11:44.851980Z", "start_time": "2025-07-11T02:11:44.158490Z"}}, "cell_type": "code", "source": ["pages = []\n", "for doc in loader.lazy_load():\n", "    pages.append(doc)\n", "    if len(pages) >= 10:\n", "        # do some paged operation, e.g.\n", "        # index.upsert(page)\n", "\n", "        pages = []\n", "len(pages)"], "id": "bc63abb1f342cb92", "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T02:32:10.211146Z", "start_time": "2025-07-11T02:32:10.207253Z"}}, "cell_type": "code", "source": ["print(pages[0].page_content)\n", "pprint.pp(pages[0].metadata)"], "id": "6c00656479cb6ced", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["附 录 \n", "附录A SCPI 命令按子系统分类速查表 \n", " \n", "265 \n", " \n", " \n", "371  [:SOURce[1]|2]:RADio:MTONe:ARB:SETup:TABLe:PHASe:I\n", "NITialize(?) \n", "设置多音初始相位 \n", "372  [:SOURce[1]|2]:RADio:MTONe:ARB:SETup:TABLe:PHASe:I\n", "NITialize:SEED \n", "(?) \n", "设置多音音间相位\n", "关系 \n", "373  [:SOURce[1]|2]:RADio:MTONe:ARB:SETup:TABLe:ROW \n", "设置多音中某一行\n", "数据 \n", "374  [:SOURce]:RADio:MTONe:ARB[:STATe](?) \n", "设置多音调制开关 \n", "375  [:SOURce[1]|2]:PWM:BARKer:TYPE(?) \n", "设置脉内调制巴克\n", "码类型 \n", "376  [:SOURce[1]|2]:PWM:BWIDth(?) \n", "设置脉内调制的调\n", "制带宽 \n", "377  [:SOURce[1]|2]:PWM:DIRection(?) \n", "设置脉内调制的调\n", "制方向 \n", "378  [:SOURce[1]|2]:PWM:FSK[:DEViation](?) \n", "设置FSK 调制的调\n", "频偏 \n", "379  [:SOURce[1]|2]:PWM:LENGth(?) \n", "设置调相码的序列\n", "长度 \n", "380  [:SOURce[1]|2]:PWM:SYMBols? \n", "查询调相码的码元\n", "个数 \n", "381  [:SOURce[1]|2]:PWM:PPCM:TYPE(?) \n", "设置调相码类型 \n", "382  [:SOURce[1]|2]:PWM:STATe(?) \n", "设置脉内调制开关 \n", "383  [:SOURce[1]|2]:PWM:TYPE(?) \n", "设置脉内调制类型 \n", "384  :STATus:OPERation:CONDition? \n", "查询操作状态寄存\n", "器中条件寄存器的\n", "值 \n", "385  :STATus:OPERation:ENABle(?) \n", "设置操作状态事件\n", "使能寄存器 \n", "386  :STATus:OPERation:NTRansition(?) \n", "设置操作状态负跳\n", "变过滤器 \n", "387  :STATus:OPERation:PTRansition(?) \n", "设置操作状态正跳\n", "变过滤器 \n", "388  :STATus:OPERation[:EVENt]? \n", "查询操作状态事件\n", "寄存器 \n", "389  :STATus:PRESet \n", "预置状态寄存的值 \n", "390  :STATus:QUEStionable:CONDition? \n", "查询疑问状态寄存\n", "器中条件寄存器的\n", "值 \n", "391  :STATus:QUEStionable:ENABle(?) \n", "设置疑问状态事件\n", "使能寄存器 \n", "392  :STATus:QUEStionable:NTRansition(?) \n", "设置疑问状态负跳\n", "变过滤器\n", "{'producer': 'Microsoft® Word 2010',\n", " 'creator': 'Microsoft® Word 2010',\n", " 'creationdate': '2023-07-18T08:26:30+08:00',\n", " 'source': 'D:\\\\liuzhihao\\\\FinalCode\\\\input\\\\Ceyear\\\\1466系列信号发生器程控手册.pdf',\n", " 'file_path': 'D:\\\\liuzhihao\\\\FinalCode\\\\input\\\\Ceyear\\\\1466系列信号发生器程控手册.pdf',\n", " 'total_pages': 276,\n", " 'format': 'PDF 1.5',\n", " 'title': '',\n", " 'author': '高训兵',\n", " 'subject': '',\n", " 'keywords': '',\n", " 'moddate': '2023-07-18T08:27:25+08:00',\n", " 'trapped': '',\n", " 'modDate': \"D:20230718082725+08'00'\",\n", " 'creationDate': \"D:20230718082630+08'00'\",\n", " 'page': 270}\n"]}], "execution_count": 20}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:40:44.120011Z", "start_time": "2025-07-11T07:39:59.381799Z"}}, "cell_type": "code", "source": ["# 将文档转换为 Markdown\n", "import pymupdf4llm\n", "md_text = pymupdf4llm.to_markdown(pdf_emaxple_path)\n", "\n", "# 以 UTF-8 编码将文本写入文件\n", "import pathlib\n", "pathlib.Path(\"./output.md\").write_bytes(md_text.encode())"], "id": "8539c72c5078bd8f", "outputs": [{"data": {"text/plain": ["538139"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "execution_count": 56}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:41:53.068692Z", "start_time": "2025-07-11T05:41:53.013416Z"}}, "cell_type": "code", "source": ["import os\n", "\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_API_KEY\"] = os.environ.get(\"LANGCHAIN_API_KEY\", \"\")\n", "\n", "from langchain_ollama import ChatOllama\n", "from langchain_ollama import OllamaEmbeddings\n", "import bs4\n", "from langchain import hub\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "chat = ChatOllama(model=\"llama3:8b\")"], "id": "b524b4afccf6a014", "outputs": [], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:43:42.560641Z", "start_time": "2025-07-11T05:43:04.319788Z"}}, "cell_type": "code", "source": ["# Load, chunk and index the contents of the blog.\n", "loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs=dict(\n", "        parse_only=bs4.SoupStrainer(\n", "            class_=(\"post-content\", \"post-title\", \"post-header\")\n", "        )\n", "    ),\n", ")\n", "docs = loader.load()\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "splits = text_splitter.split_documents(docs)\n", "vectorstore = Chroma.from_documents(documents=splits,\n", "                                    embedding=OllamaEmbeddings(model=\"nomic-embed-text:v1.5\"))\n", "\n", "# Retrieve and generate using the relevant snippets of the blog.\n", "retriever = vectorstore.as_retriever()\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | chat\n", "    | StrOutputParser()\n", ")\n", "\n", "rag_chain.invoke(\"What is Task Decomposition?\")"], "id": "97dea9ca3df7a166", "outputs": [{"data": {"text/plain": ["'Task Decomposition is a process that breaks down complex tasks into smaller and simpler steps to facilitate planning and execution. This involves instructing an agent or model to \"think step by step\" and generate a chain of thought, which can be further explored using techniques like Tree of Thoughts. The goal is to create multiple manageable tasks and shed light on the model\\'s thinking process.'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:46:39.224865Z", "start_time": "2025-07-11T05:46:39.215424Z"}}, "cell_type": "code", "source": ["# cleanup\n", "vectorstore.delete_collection()"], "id": "71980e1a92ad76c3", "outputs": [], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:49:08.759326Z", "start_time": "2025-07-11T05:49:07.615363Z"}}, "cell_type": "code", "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "\n", "# Only keep post title, headers, and content from the full HTML.\n", "bs4_strainer = bs4.SoupStrainer(class_=(\"post-title\", \"post-header\", \"post-content\"))\n", "loader = WebBaseLoader(\n", "    web_paths=(\"https://lilianweng.github.io/posts/2023-06-23-agent/\",),\n", "    bs_kwargs={\"parse_only\": bs4_strainer},\n", ")\n", "docs = loader.load()\n", "\n", "len(docs[0].page_content)"], "id": "1d2f0360c6cd0094", "outputs": [{"data": {"text/plain": ["43047"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "execution_count": 8}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:49:16.718854Z", "start_time": "2025-07-11T05:49:16.714865Z"}}, "cell_type": "code", "source": "print(docs[0].page_content[:500])", "id": "e4b3cfe678d2f489", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "      LLM Powered Autonomous Agents\n", "    \n", "Date: June 23, 2023  |  Estimated Reading Time: 31 min  |  Author: <PERSON><PERSON>\n", "\n", "\n", "Building agents with LLM (large language model) as its core controller is a cool concept. Several proof-of-concepts demos, such as <PERSON>GPT, GPT-Engineer and BabyAGI, serve as inspiring examples. The potentiality of LLM extends beyond generating well-written copies, stories, essays and programs; it can be framed as a powerful general problem solver.\n", "Agent System Overview#\n", "In\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:49:47.471087Z", "start_time": "2025-07-11T05:49:47.463895Z"}}, "cell_type": "code", "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=1000, chunk_overlap=200, add_start_index=True\n", ")\n", "all_splits = text_splitter.split_documents(docs)\n", "\n", "len(all_splits)"], "id": "2b0dd6deeb299afa", "outputs": [{"data": {"text/plain": ["63"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:50:01.681125Z", "start_time": "2025-07-11T05:50:01.676139Z"}}, "cell_type": "code", "source": "len(all_splits[0].page_content)", "id": "a626a5651aa6638c", "outputs": [{"data": {"text/plain": ["969"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T05:50:08.014302Z", "start_time": "2025-07-11T05:50:08.010330Z"}}, "cell_type": "code", "source": "all_splits[10].metadata", "id": "32c40c45892c044f", "outputs": [{"data": {"text/plain": ["{'source': 'https://lilianweng.github.io/posts/2023-06-23-agent/',\n", " 'start_index': 8436}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "execution_count": 12}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T06:09:51.186513Z", "start_time": "2025-07-11T06:09:22.487561Z"}}, "cell_type": "code", "source": ["import os\n", "\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_API_KEY\"] = os.environ.get(\"LANGCHAIN_API_KEY\", \"\")\n", "\n", "from langchain_ollama import ChatOllama\n", "from langchain_ollama import OllamaEmbeddings\n", "import bs4\n", "from langchain import hub\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import PyMuPDFLoader\n", "chat = ChatOllama(model=\"llama3:8b\")\n", "# 1. 加载PDF文件\n", "pdf_emaxple_path = r\"D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\"\n", "loader = PyMuPDFLoader(pdf_emaxple_path)\n", "docs = loader.load()\n", "\n", "# 2. 文本分块 (与之前相同)\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "splits = text_splitter.split_documents(docs)\n", "\n", "# 3. 创建向量存储 (与之前相同)\n", "# 确保你的 Ollama 服务的 'nomic-embed-text:v1.5' 模型已运行\n", "vectorstore = Chroma.from_documents(documents=splits,\n", "                                    embedding=OllamaEmbeddings(model=\"nomic-embed-text:v1.5\"))\n", "\n", "# 4. 检索和生成 (与之前相同)\n", "retriever = vectorstore.as_retriever()\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "# 初始化你的聊天模型\n", "# 假设你使用的是 Ollama 的聊天模型，例如 'llama3' 或 'mistral'\n", "llm = Cha<PERSON><PERSON><PERSON><PERSON>(model=\"qwen3:8b\") # 请替换为你实际使用的Ollama聊天模型\n", "\n", "rag_chain = (\n", "    {\"context\": retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "# 示例调用\n", "print(rag_chain.invoke(\"如何使用 [:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger   命令？\"))\n"], "id": "f86a5014aa3ff916", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "好的，我现在要回答用户的问题：“如何使用 [:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger 命令？”。首先，我需要仔细查看提供的上下文内容，找到相关的信息。\n", "\n", "用户提供的上下文多次重复了关于程控命令的部分，特别是关于步进扫描的设置。我需要找到与TRIGger命令相关的部分。在上下文中，看到有一个条目是关于[:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger的，功能描述是当步进扫描中步进触发为触发键时，执行一次触发，每次触发会使扫描频率变化一次。设置格式是[:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger，没有参数。例子是:SWEep:TRIGger触发一次。复位状态是无。\n", "\n", "接下来，我需要将这些信息整理成一个简洁的答案。用户要求使用三句话以内，保持简练。首先，要说明这个命令的作用，即触发步进扫描的单次频率变化。然后，给出命令格式，即直接发送命令而无需参数。最后，可能需要提到触发后频率会变化一次，但根据上下文，可能不需要额外信息，因为用户的问题主要关于如何使用命令。\n", "\n", "需要确保没有使用技术术语错误，并且符合用户提供的上下文。例如，命令的正确格式是[:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger，而例子中的:SWEep:TRIGger可能省略了源部分，但实际使用时可能需要指定源，比如:SOURce1:SWEep:FREQuency:TRIGger。不过根据上下文中的例子，用户可能允许简写形式，但需要确认是否正确。\n", "\n", "最后，检查是否符合用户要求的三句话，保持简洁，不添加额外信息。确保没有超出上下文的信息，比如参数说明等。总结起来，答案应包括命令功能、使用格式和示例。\n", "</think>\n", "\n", "使用 `[:SOURce[1]|2]:SWEep[:FREQuency]:TRIGger` 命令可触发步进扫描的单次频率变化。直接发送该命令即可，无需参数。例如，`:SWEep:TRIGger` 会执行一次触发，使扫描频率按设定步进值变化。\n"]}], "execution_count": 22}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:03:14.601921Z", "start_time": "2025-07-11T07:03:12.518769Z"}}, "cell_type": "code", "source": ["from langchain_community.document_loaders import UnstructuredFileLoader\n", "from langchain_community.document_loaders import UnstructuredMarkdownLoader\n", "from langchain_core.documents import Document\n", "# 需要安装 pip install unstructured 和 pip install \"unstructured[pdf]\"\n", "markdown_path = r\"D:\\liuzhihao\\MyProject\\Mylangchain\\output\\02_test\\1466系列信号发生器程控手册.md\"\n", "loader = UnstructuredFileLoader(markdown_path, mode=\"elements\") # mode=\"elements\" 提取更详细的结构\n", "docs = loader.load()"], "id": "fced717239f2f6d5", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["libmagic is unavailable but assists in filetype detection. Please consider installing libmagic for better results.\n"]}], "execution_count": 35}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:03:58.787354Z", "start_time": "2025-07-11T07:03:58.783466Z"}}, "cell_type": "code", "source": "print(f\"Number of documents: {len(docs)}\\n\")", "id": "9b0fc42c31eae028", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of documents: 3717\n", "\n"]}], "execution_count": 40}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:04:12.554501Z", "start_time": "2025-07-11T07:04:12.551437Z"}}, "cell_type": "code", "source": ["for document in docs[:2]:\n", "    print(f\"{document}\\n\")"], "id": "ec506035115c035e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='1466 系列 信号发生器 程控手册' metadata={'source': 'D:\\\\liuzhihao\\\\MyProject\\\\Mylangchain\\\\output\\\\02_test\\\\1466系列信号发生器程控手册.md', 'category_depth': 0, 'emphasized_text_contents': ['1466 系列', '信号发生器', '程控手册'], 'emphasized_text_tags': ['b', 'b', 'b'], 'languages': ['zho'], 'file_directory': 'D:\\\\liuzhihao\\\\MyProject\\\\Mylangchain\\\\output\\\\02_test', 'filename': '1466系列信号发生器程控手册.md', 'filetype': 'text/markdown', 'last_modified': '2025-07-04T11:16:32', 'category': 'Title', 'element_id': '0344ab15549e5e65e4c19709d5550810'}\n", "\n", "page_content='中电科思仪科技股份有限公司' metadata={'source': 'D:\\\\liuzhihao\\\\MyProject\\\\Mylangchain\\\\output\\\\02_test\\\\1466系列信号发生器程控手册.md', 'category_depth': 5, 'emphasized_text_contents': ['中电科思仪科技股份有限公司'], 'emphasized_text_tags': ['b'], 'languages': ['zho'], 'file_directory': 'D:\\\\liuzhihao\\\\MyProject\\\\Mylangchain\\\\output\\\\02_test', 'filename': '1466系列信号发生器程控手册.md', 'filetype': 'text/markdown', 'last_modified': '2025-07-04T11:16:32', 'parent_id': '0344ab15549e5e65e4c19709d5550810', 'category': 'Title', 'element_id': '88a28f03dc30d29f607ca895e96db7e1'}\n", "\n"]}], "execution_count": 41}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-11T07:04:44.333148Z", "start_time": "2025-07-11T07:04:44.327650Z"}}, "cell_type": "code", "source": "print(set(document.metadata[\"category\"] for document in docs))", "id": "e600709d2f793bfd", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Title', 'ListItem', 'Table', 'NarrativeText', 'UncategorizedText'}\n"]}], "execution_count": 42}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T06:55:46.011132Z", "start_time": "2025-07-16T06:55:42.841215Z"}}, "cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser, CommaSeparatedListOutputParser\n", "from langchain_ollama import OllamaLLM\n", "from langchain.schema.runnable import RunnableLambda\n", "\n", "# 初始化 Ollama 模型\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "\n", "# 定义一个用于所有链的通用输出解析器和格式指令\n", "list_output_parser = CommaSeparatedListOutputParser()\n", "str_output_parser = StrOutputParser()\n", "format_instructions_list = list_output_parser.get_format_instructions()\n", "\n", "# --- 品牌名链 ---\n", "brand_name_prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请为{product}设计一个高端品牌名（简短回答）：\"\n", ")\n", "\n", "# 情况1：直接返回字符串，包装为列表\n", "brand_name_chain = (\n", "    brand_name_prompt\n", "    | llm\n", "    | str_output_parser\n", ")\n", "brand_name_chain_list = brand_name_chain | RunnableLambda(lambda x: [x])\n", "\n", "# 情况2：返回原始LLM输出，提取内容并包装为列表\n", "brand_name_chain_1 = (\n", "    brand_name_prompt\n", "    | llm\n", ")\n", "brand_name_chain_1_processed = (\n", "    brand_name_chain_1\n", "    | RunnableLambda(lambda x: x)\n", "    | RunnableLambda(lambda x: [x])\n", ")\n", "\n", "# 情况3：返回列表，提取第一个元素\n", "brand_name_prompt_2 = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请为{product}设计一个高端品牌名（简短回答）：\",\n", "    partial_variables={\"format_instructions\": format_instructions_list},\n", ")\n", "brand_name_chain_2 = (\n", "    brand_name_prompt_2\n", "    | llm\n", "    | list_output_parser\n", ")\n", "brand_name_chain_2_first = brand_name_chain_2 | RunnableLambda(lambda x: x[0])\n", "\n", "# 测试输出\n", "print(\"情况1（包装为列表）:\", brand_name_chain_list.invoke(\"智能水杯\"))\n", "print(\"情况2（处理原始输出）:\", brand_name_chain_1_processed.invoke(\"智能水杯\"))\n", "print(\"情况3（提取首个元素）:\", brand_name_chain_2_first.invoke(\"智能水杯\"))"], "id": "97fc0c0a4f89f159", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["情况1（包装为列表）: ['悠智源']\n", "情况2（处理原始输出）: ['智饮未来']\n", "情况3（提取首个元素）: AquaIntelli\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T07:47:01.102367Z", "start_time": "2025-07-16T07:46:43.059926Z"}}, "cell_type": "code", "source": ["from langchain_ollama import OllamaLLM, OllamaEmbeddings\n", "from langchain.document_loaders import WebBaseLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.vectorstores import FAISS\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "# 1. 加载文档\n", "loader = WebBaseLoader(web_paths=(\"https://arxiv.org/html/1706.03762v7\",))\n", "data = loader.load()\n", "\n", "# 2. 文档分割\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "splits = text_splitter.split_documents(data)\n", "\n", "# 3. 创建向量存储和检索器\n", "embedding = OllamaEmbeddings(model=\"mxbai-embed-large:latest\")\n", "vectorstore = FAISS.from_documents(splits, embedding)\n", "retriever = vectorstore.as_retriever()\n", "\n", "# 4. 初始化LLM\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "\n", "# 5. 定义提示模板（明确消息类型）\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你是一个问答助手，需要根据提供的上下文回答问题。\"\n", "               \"上下文信息如下：\\n{context}\\n\\n请基于上述上下文回答问题，不要编造信息。\"),\n", "    MessagesPlaceholder(variable_name=\"chat_history\"),  # 要求传入消息列表\n", "    (\"human\", \"{question}\")\n", "])\n", "\n", "# 6. 构建检索-问答链（简化参数传递）\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(doc.page_content for doc in docs)\n", "\n", "# 核心改进：使用更简洁的参数映射，确保chat_history直接传递列表\n", "qa_chain = (\n", "    RunnablePassthrough.assign(\n", "        context=lambda x: (retriever | format_docs).invoke(x[\"question\"])  # 单独处理检索逻辑\n", "    )\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "# 7. 执行问答\n", "chat_history = []  # 初始为空列表（消息对象列表）\n", "\n", "query = \"总结文章的主要观点\"\n", "result = qa_chain.invoke({\n", "    \"question\": query,\n", "    \"chat_history\": chat_history\n", "})\n", "\n", "print(\"回答:\", result)\n", "\n", "# 多轮对话：更新消息历史（必须是HumanMessage和AIMessage组成的列表）\n", "chat_history.extend([\n", "    HumanMessage(content=query),\n", "    AIMessage(content=result)\n", "])\n", "\n", "query2 = \"什么是Transformer？\"\n", "result2 = qa_chain.invoke({\n", "    \"question\": query2,\n", "    \"chat_history\": chat_history  # 此时chat_history是消息对象列表\n", "})\n", "\n", "print(\"回答2:\", result2)"], "id": "4907b4875cf0d2b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["回答: 根据提供的上下文摘要内容，主要涉及了卷积操作、自注意力机制以及位置编码的计算方式。具体而言：\n", "\n", "1. **卷积操作的时间复杂度**：给出的是 \\(O(k \\cdot n \\cdot d^2)\\) 和其他两个常量级的时间复杂度表示。\n", "\n", "2. **自注意力机制的时间复杂度和优化结果**：\n", "   - 常数时间复杂度为 \\(O(1)\\)。\n", "   - 优化后的结果是 \\(O(n/r)\\)，这表明通过某种参数调整或者模型设计可以有效减少计算成本。\n", "\n", "3. **学习率的动态调整公式**：该公式定义了基于步数和预热步骤的学习率调整方式，公式中包含了模型尺寸 \\(d_{\\text{model}}\\)、当前步数 \\(step\\_num\\) 和预热步数 \\(warmup\\_steps\\) 的参数。\n", "\n", "4. **位置编码的计算方法**：通过余弦函数对位置进行编码，公式为 \\(PE_{(pos,2i+1)}=cos(pos/10000^{2i/d_{\\text{model}}})\\)。\n", "\n", "综上所述，这段摘要主要描述了在特定模型或算法中用于处理数据的不同计算复杂度和方法。\n", "回答2: 根据提供的上下文信息，Transformer是一种基于注意力机制的序列转序模型（sequence transduction model），它首次完全摒弃了循环层（recurrent layers）而代之以多头自注意力机制。这种架构在编码器-解码器（encoder-decoder）结构中取代了传统的循环层，并且在翻译任务上表现出色，尤其是在WMT 2014的英语到德国语和英语到法语文本翻译任务上实现了新的性能标准。\n", "\n", "具体来说，Transformer具有以下特点：\n", "- **整体架构**：采用堆叠自注意力机制和点积全连接层，同时分别用于编码器和解码器。\n", "- **性能表现**：与基于循环神经网络（RNN）的序列到序列模型相比，Transformer在仅使用WSJ训练集40K句子的情况下就能超越BerkeleyParser的表现。\n", "- **速度优势**：对于翻译任务，Transformer可以比基于卷积层或循环层的架构显著更快地进行训练。\n", "\n", "这些特点表明Transformer作为一种基于注意力机制的方法，在处理序列数据时具有更高的效率和更好的性能。\n"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": ["### 1. 文档加载 (Load)\n", "这一步与您之前提供的代码相似，使用 WebBaseLoader 从指定URL加载网页内容。"], "id": "b89fe5dd454af2f"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:28:50.555911Z", "start_time": "2025-07-16T08:28:47.662761Z"}}, "cell_type": "code", "source": ["from langchain.document_loaders import WebBaseLoader\n", "\n", "# 定义要加载的URL\n", "url = \"https://arxiv.org/html/1706.03762v7\"\n", "\n", "print(f\"正在从URL加载文档: {url}\")\n", "loader = WebBaseLoader(url)\n", "documents = loader.load()\n", "\n", "if not documents:\n", "    print(\"未能加载文档，请检查URL或网络连接。\")\n", "else:\n", "    print(f\"成功加载 {len(documents)} 个文档片段。\")\n", "    # documents 现在是一个包含 Document 对象的列表\n", "    # 每个 Document 对象有一个 page_content 属性和 metadata 属性"], "id": "c33e443f24d56b5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在从URL加载文档: https://arxiv.org/html/1706.03762v7\n", "成功加载 1 个文档片段。\n"]}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["### 3. 文档分割 (Divide)\n", "将大型文档分割成更小的、可管理的块（chunks），以便于嵌入和检索。RecursiveCharacterTextSplitter 是一个很好的选择，它会尝试保留语义相关的文本。"], "id": "64d67511b81af19d"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:29:47.714690Z", "start_time": "2025-07-16T08:29:47.708705Z"}}, "cell_type": "code", "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "# 初始化文本分割器\n", "# chunk_size: 每个块的最大字符数\n", "# chunk_overlap: 块与块之间的重叠字符数，有助于保留上下文\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=1000,  # 示例值，根据实际内容调整\n", "    chunk_overlap=200 # 示例值，根据实际内容调整\n", ")\n", "\n", "# 分割文档\n", "split_documents = text_splitter.split_documents(documents)\n", "\n", "print(f\"原始文档被分割成 {len(split_documents)} 个文本块。\")\n", "# 打印第一个文本块的内容作为示例\n", "print(\"\\n--- 第一个文本块内容示例 ---\")\n", "print(split_documents[0].page_content[:500] + \"...\")"], "id": "b94ffa50081838fb", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始文档被分割成 79 个文本块。\n", "\n", "--- 第一个文本块内容示例 ---\n", "Attention Is All You Need\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "1 Introduction\n", "2 Background\n", "\n", "3 Model Architecture\n", "\n", "\n", "3.1 Encoder and Decoder Stacks\n", "\n", "Encoder:\n", "Decoder:\n", "\n", "\n", "\n", "3.2 Attention\n", "\n", "3.2.1 Scaled Dot-Product Attention\n", "3.2.2 Multi-Head Attention\n", "3.2.3 Applications of Attention in our Model\n", "\n", "\n", "3.3 Position-wise Feed-Forward Networks\n", "3.4 Embeddings and Softmax\n", "3.5 Positional Encoding\n", "\n", "\n", "4 Why Self-Attention\n", "\n", "5 Training\n", "\n", "5.1 Training Data and Batching\n", "5.2 Hardware and Schedule\n", "5.3 Optimizer\n", "\n", "5.4 Regularization\n", "\n", "Residual Dr...\n"]}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": ["### 3. 文档嵌入 (Embed)\n", "使用嵌入模型（如Ollama上的的 mxbai-embed-large:latest）将每个文本块转换为向量（数字表示）。"], "id": "4e0909c08fa94af2"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:31:53.344741Z", "start_time": "2025-07-16T08:31:53.291436Z"}}, "cell_type": "code", "source": ["from langchain_ollama import OllamaEmbeddings\n", "import os\n", "\n", "# 初始化嵌入模型\n", "embeddings = OllamaEmbeddings(model=\"mxbai-embed-large:latest\")\n", "print(\"Ollama嵌入模型已初始化。\")\n", "\n", "# 嵌入文档块 (这一步通常在向量存储的 from_documents 方法中完成)\n", "# 这一步不会直接在代码中执行，因为它是由向量存储库在内部调用的"], "id": "d7d65817f9952aef", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ollama嵌入模型已初始化。\n"]}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": ["### 4. 基于向量库构建检索-问答链 (Vector Store & Retrieval-QA Chain)\n", "将嵌入后的文档块存储到向量数据库（例如 FAISS），然后基于这个向量库构建一个检索器，并将其与语言模型结合起来形成一个问答链。"], "id": "2b4ed4a000f14960"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:34:02.767079Z", "start_time": "2025-07-16T08:33:59.646229Z"}}, "cell_type": "code", "source": ["from langchain.vectorstores import FAISS\n", "from langchain.chains import RetrievalQA\n", "from langchain_ollama import ChatOllama\n", "\n", "# 确保 embeddings 对象已正确初始化\n", "if \"embeddings\" in locals():\n", "    print(\"\\n--- 正在创建FAISS向量存储 ---\")\n", "    # 从分割后的文档创建FAISS向量存储并进行嵌入\n", "    db = FAISS.from_documents(split_documents, embeddings)\n", "    print(\"FAISS向量存储创建完成。\")\n", "\n", "    # 创建一个检索器\n", "    retriever = db.as_retriever()\n", "    print(\"检索器已创建。\")\n", "\n", "    # 初始化大型语言模型\n", "    # 这里使用 ChatOllama，你可以根据需要选择其他模型\n", "    llm = ChatOllama(model=\"qwen2.5:7b\", temperature=0.0) # temperature=0.0 使回答更确定性\n", "    print(\"ChatOllama 模型已初始化。\")\n", "\n", "    # 构建检索问答链\n", "    # 返回类型设置为 'stuff'，表示所有检索到的文档内容会被填充到LLM的上下文中\n", "    qa_chain = RetrievalQA.from_chain_type(\n", "        llm=llm,\n", "        chain_type=\"stuff\",\n", "        retriever=retriever,\n", "        return_source_documents=True # 返回检索到的源文档\n", "    )\n", "    print(\"检索问答链 (RetrievalQA) 已构建完成。\")\n", "else:\n", "    print(\"跳过向量存储和问答链构建，嵌入模型未初始化。\")"], "id": "c3c8627c004ccf16", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 正在创建FAISS向量存储 ---\n", "FAISS向量存储创建完成。\n", "检索器已创建。\n", "ChatOllama 模型已初始化。\n", "检索问答链 (RetrievalQA) 已构建完成。\n"]}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["### 5. 执行问答\n", "通过构建好的 qa_chain 来提问并获取答案。"], "id": "2a0d4af03d920771"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:35:51.326236Z", "start_time": "2025-07-16T08:35:48.783282Z"}}, "cell_type": "code", "source": ["if \"qa_chain\" in locals():\n", "    print(\"\\n--- 执行问答 ---\")\n", "    question = \"Transformer模型的核心思想是什么？\"\n", "    print(f\"提问: {question}\")\n", "    result = qa_chain.invoke({\"query\": question})\n", "\n", "    print(\"\\n回答:\")\n", "    print(result[\"result\"])\n", "\n", "    print(\"\\n--- 源文档 ---\")\n", "    for i, doc in enumerate(result[\"source_documents\"]):\n", "        print(f\"文档 {i+1} (来源: {doc.metadata.get('source', '未知')}, 页面: {doc.metadata.get('page', 'N/A')}):\")\n", "        print(doc.page_content[:200] + \"...\") # 打印部分内容\n", "else:\n", "    print(\"跳过问答执行，因为问答链未构建。\")"], "id": "1f549eac3e4b9784", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 执行问答 ---\n", "提问: Transformer模型的核心思想是什么？\n", "\n", "回答:\n", "Transformer模型的核心思想是使用堆叠的自注意力机制和点积完全连接层，分别用于编码器和解码器，从而构建一个无需依赖于序列对齐循环神经网络（RNNs）或卷积的端到端转换模型。具体来说，Transformer首次在输入和输出表示的计算中完全依赖自注意力机制，而不是使用序列对齐的递归神经网络或卷积。这种设计使得模型能够并行处理输入数据，并且在机器翻译等任务上取得了很好的效果。\n", "\n", "--- 源文档 ---\n", "文档 1 (来源: https://arxiv.org/html/1706.03762v7, 页面: N/A):\n", "Mo<PERSON> [32]\n", "\n", "26.03\n", "40.56\n", "\n", "2.0â‹…1019â‹…2.0superscript10192.0\\cdot 10^{19}2.0 â‹… 10 start_POSTSUPERSCRIPT 19 end_POSTSUPERSCRIPT\n", "1.2â‹…1020â‹…1.2superscript10201.2\\cdot 10^{20}1.2 â‹… 10 start_POSTSUPERS...\n", "文档 2 (来源: https://arxiv.org/html/1706.03762v7, 页面: N/A):\n", "0.2\n", "\n", "\n", "4.95\n", "25.5\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "0.0\n", "\n", "4.67\n", "25.3\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "0.2\n", "\n", "5.47\n", "25.7\n", "\n", "\n", "\n", "\n", "\n", "(E)\n", "\n", "positional embedding instead of sinusoids\n", "\n", "4.92\n", "25.7\n", "\n", "\n", "\n", "\n", "\n", "big\n", "6\n", "1024\n", "4096\n", "16\n", "\n", "\n", "0.3\n", "\n", "300K\n", "4.33\n", "26.4\n", "213\n", "\n", "\n", "\n", "\n", "\n", "To evaluate the ...\n", "文档 3 (来源: https://arxiv.org/html/1706.03762v7, 页面: N/A):\n", "Self-attention, sometimes called intra-attention is an attention mechanism relating different positions of a single sequence in order to compute a representation of the sequence. Self-attention has be...\n", "文档 4 (来源: https://arxiv.org/html/1706.03762v7, 页面: N/A):\n", "The Transformer follows this overall architecture using stacked self-attention and point-wise, fully connected layers for both the encoder and decoder, shown in the left and right halves of FigureÂ 1,...\n"]}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["### 6. 多轮对话功能\n", "为了支持多轮对话，你需要使用 LangChain 的 ConversationalRetrievalChain 和一个内存模块来存储对话历史。"], "id": "b0d0c4a84d29f279"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T08:48:24.856078Z", "start_time": "2025-07-16T08:48:14.783188Z"}}, "cell_type": "code", "source": ["from langchain.chains import ConversationalRetrievalChain\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain_core.messages import HumanMessage, AIMessage # Ensure this import is present\n", "\n", "if \"llm\" in locals() and \"retriever\" in locals():\n", "    print(\"\\n--- 正在构建多轮对话链 ---\")\n", "    # 初始化对话内存\n", "    # 核心修改在这里：指定 output_key\n", "    memory = ConversationBufferMemory(\n", "        memory_key=\"chat_history\", # 必须是 \"chat_history\"\n", "        return_messages=True,      # 返回消息对象\n", "        output_key=\"answer\"        # 明确告诉内存哪个键是AI的回答\n", "    )\n", "\n", "    # 构建多轮对话检索链\n", "    conversation_chain = ConversationalRetrievalChain.from_llm(\n", "        llm=llm,\n", "        retriever=retriever,\n", "        memory=memory,\n", "        return_source_documents=True # 同样可以返回源文档\n", "    )\n", "    print(\"多轮对话链 (ConversationalRetrievalChain) 已构建完成。\")\n", "\n", "    print(\"\\n--- 执行多轮对话示例 ---\")\n", "\n", "    # 注意：为了 LangChain 0.1.x+ 和更好的兼容性，\n", "    # chat_history 应该存储 HumanMessage 和 AIMessage 对象，而不是元组。\n", "    # 这一部分也已在下面修正。\n", "\n", "    # 第一轮对话\n", "    query1 = \"请问这篇论文的主要贡献是什么？\"\n", "    print(f\"\\n用户: {query1}\")\n", "    chat_history = [] # 初始化对话历史\n", "    response1 = conversation_chain.invoke({\"question\": query1, \"chat_history\": chat_history})\n", "    print(f\"AI: {response1['answer']}\")\n", "    # 更新对话历史为消息对象\n", "    chat_history.append(HumanMessage(content=query1))\n", "    chat_history.append(AIMessage(content=response1[\"answer\"]))\n", "\n", "\n", "    # 第二轮对话，利用上一轮的上下文\n", "    query2 = \"它解决了传统循环神经网络的什么问题？\"\n", "    print(f\"\\n用户: {query2}\")\n", "    response2 = conversation_chain.invoke({\"question\": query2, \"chat_history\": chat_history})\n", "    print(f\"AI: {response2['answer']}\")\n", "    # 继续更新对话历史\n", "    chat_history.append(HumanMessage(content=query2))\n", "    chat_history.append(AIMessage(content=response2[\"answer\"]))\n", "\n", "    # 可以继续进行更多轮对话...\n", "\n", "    print(\"\\n多轮对话示例完成。\")\n", "else:\n", "    print(\"跳过多轮对话链构建和执行，因为LLM或检索器未初始化。\")"], "id": "762b891ce7fd2267", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 正在构建多轮对话链 ---\n", "多轮对话链 (ConversationalRetrievalChain) 已构建完成。\n", "\n", "--- 执行多轮对话示例 ---\n", "\n", "用户: 请问这篇论文的主要贡献是什么？\n", "AI: 提供的摘要信息并没有直接提到论文的主要贡献是什么，而是描述了一些公式和时间复杂度表示。这些内容可能与模型的某些实现细节或计算效率有关，但没有明确指出主要贡献。因此基于给定的信息，我无法准确回答关于论文主要贡献的问题。\n", "\n", "用户: 它解决了传统循环神经网络的什么问题？\n", "AI: 注意力机制模型，如Transformer，解决了一些传统循环神经网络（RNN）的问题。主要解决了以下几个方面：\n", "\n", "1. **并行化处理**：传统的RNN是顺序处理序列数据的，这意味着在处理下一个时间步之前必须完成前一个时间步的计算。而Transformer通过自注意力机制可以在所有时间步骤之间并行进行信息交换。\n", "\n", "2. **长依赖问题**：传统RNN容易出现梯度消失或爆炸的问题，特别是在处理长序列时，这限制了模型捕捉远距离依赖的能力。Transformer通过多头自注意力机制能够更有效地建模长期依赖关系。\n", "\n", "3. **计算效率**：虽然自注意力机制在某些情况下可能比传统的递归结构需要更多的计算资源，但现代硬件（如GPU）的优化使得这种差距可以被有效管理，并且整体上提高了训练和推理的速度。\n", "\n", "4. **模型复杂度**：传统RNN随着序列长度增加而变得越来越复杂，这可能导致过拟合。Transformer通过分层设计减少了这种风险。\n", "\n", "综上所述，注意力机制为处理序列数据提供了一种更高效、并行化的方法，并且能够更好地捕捉长距离依赖关系。\n", "\n", "多轮对话示例完成。\n"]}], "execution_count": 19}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}