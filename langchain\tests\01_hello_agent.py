import os

# 设置API密钥
import os
from dotenv import load_dotenv 
load_dotenv(override=True)

SERPAPI_API_KEY = os.getenv("SERPAPI_API_KEY")
# print(f"SERPAPI_API_KEY: {SERPAPI_API_KEY}")
from langchain_community.agent_toolkits.load_tools import load_tools
from langchain.agents import initialize_agent
from langchain.agents import AgentType
from langchain_ollama import ChatOllama

chat = ChatOllama(model="deepseek-r1:8b", temperature=0.1)

tools = load_tools(["serpapi", "llm-math"], llm=chat)

agent = initialize_agent(
    tools,
    chat,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
)

agent.invoke(input="三天后成都天气怎么样？")