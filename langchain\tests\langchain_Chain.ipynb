{"cells": [{"cell_type": "markdown", "id": "447aacf95795c980", "metadata": {}, "source": ["### 非流式输出"]}, {"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T03:37:01.410023Z", "start_time": "2025-07-14T03:35:40.714694Z"}, "collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，为公司新产品洗衣机取名，我将从多个维度进行思考，并给出一些备选方案，并附上命名思路的详细解释。\n", "\n", "**一、命名思路与原则**\n", "\n", "在开始列举备选方案之前，我们先明确一些命名思路和原则，这将有助于我们筛选出最符合产品定位和品牌形象的名字：\n", "\n", "1.  **产品定位：**\n", "    *   **核心功能：** 清洗、除菌、柔顺、节能、护衣\n", "    *   **目标用户：** 家庭主妇、年轻家庭、注重健康和品质生活的人群\n", "    *   **产品特点：** （需了解产品的具体优势，例如：是否具有创新技术、特别的材质、独特的设计等）\n", "2.  **命名风格：**\n", "    *   **简洁易记：** 名字要避免过于复杂，方便用户记忆和传播。\n", "    *   **积极正面：** 传递积极、干净、舒适的感觉。\n", "    *   **与品牌契合：** 名字要与公司品牌形象相符，提升品牌认知度。\n", "    *   **可延展性：** 名字要具有一定的延展性，方便后续产品线的拓展。\n", "3.  **语言选择：**\n", "    *   **中文：** 贴近用户，易于理解，更具亲和力。\n", "    *   **英文：** 提升国际化形象，但需确保易于发音和记忆。\n", "    *   **中英文结合：** 兼顾国内和国际市场。\n", "\n", "**二、备选方案及命名思路**\n", "\n", "以下是我根据不同侧重点提出的备选方案，并附有详细的命名思路解释：\n", "\n", "**1. 突出核心功能（清洁、护衣）：**\n", "\n", "*   **净澈:**  直接点明洗衣机的核心功能——净澈的清洗。简洁明了，易于理解。\n", "*   **衣净:**  “衣”字突出对象是衣物，“净”字强调清洁，整体简洁，容易记忆。\n", "*   **柔洗:**  突出洗衣机对衣物的柔和保护，适合注重衣物品质的用户。\n", "*   **臻护:**  “臻”字表示极致，“护”字表示保护，强调对衣物的精心呵护。\n", "*   **洁衣坊:**  “洁”字突出清洁，“衣”字点明对象，“坊”字有专业、工坊的感觉，给人一种信赖感。\n", "*   **衣语:**  富有诗意，寓意洗衣机能“倾听”衣物，提供更贴心的护理。\n", "\n", "**2. 突出科技感和创新性：**\n", "\n", "*   **洗愈:**  结合“洗”与“治愈”的含义，寓意洗衣机不仅清洗衣物，还能带来心灵的放松和愉悦。\n", "*   **智洗:**  “智”字突出智能，表明洗衣机具备智能化功能。\n", "*   **云洗:**  暗示洗衣机与云端技术连接，提供智能化的服务和体验。\n", "*   **芯净:**  “芯”代表核心技术，“净”代表清洁，突出洗衣机采用先进的核心技术。\n", "*   **AI洗:** 直接体现人工智能洗衣机的功能，科技感十足。\n", "\n", "**3. 突出健康和除菌：**\n", "\n", "*   **净护:** 结合清洁和保护的含义，突出洗衣机的除菌和护衣功能。\n", "*   **洁净卫:** “洁净”强调清洁，“卫”字则暗示健康、卫生，适合注重健康的家庭。\n", "*   **卫洗:** 突出洗衣机具有卫生、清洁的功能。\n", "*   **抑菌洗:**  直接点明洗衣机具有抑菌功能，更具说服力。\n", "\n", "**4. 中英文结合方案：**\n", "\n", "*   **CareWash:** \"Care\" 意为呵护、关爱，\"Wash\" 意为洗涤，整体简洁，易于发音和记忆。\n", "*   **PureWash:** “Pure” 意为纯净，\"Wash\" 意为洗涤，强调洗衣机带来的纯净体验。\n", "*   **CleanLife:** “Clean” 意为清洁，“Life” 意为生活，寓意洗衣机带来的洁净生活。\n", "*   **Z Care:** “Z”可以理解为公司名称的缩写，或代表“zero” 零污染，零伤害。\n", "\n", "**5. 考虑品牌关联性：**\n", "\n", "*   **（公司名称）Care:**  直接将公司名称与 \"Care\" 结合，提升品牌认知度。例如，如果公司名称为“XX家电”，则可以命名为“XX Care”。\n", "*   **（公司名称）净:**  例如，“XX净”，简单直接，突出公司与清洁相关的产品。\n", "\n", "**三、推荐方案及筛选建议**\n", "\n", "综合考虑以上因素，我个人推荐以下几个方案：\n", "\n", "*   **净澈：** 简洁明了，易于传播，突出核心功能。\n", "*   **柔洗：** 适合注重衣物品质的用户，突出洗衣机的护衣功能。\n", "*   **CareWash:**  中英文结合，易于记忆，更具国际化形象。\n", "*   **(公司名称)Care:**  直接关联公司品牌，提升品牌认知度。\n", "\n", "**筛选建议：**\n", "\n", "1.  **内部投票：**  将备选方案提交给公司内部相关人员进行投票，听取不同意见。\n", "2.  **用户调研：**  选取部分备选方案，进行小范围的用户调研，了解用户对不同名字的偏好和反馈。\n", "3.  **商标查询：**  在确定最终方案之前，务必进行商标查询，确保没有侵权风险。\n", "4.  **域名查询：** 如果有计划建立网站，也需要查询相应的域名是否可用。\n", "\n", "**最后，请您提供更多关于产品特点和公司品牌形象的信息，以便我能够更精准地为您推荐最合适的洗衣机名称。**  例如：\n", "\n", "*   洗衣机有哪些特别的创新技术？\n", "*   产品的主要卖点是什么？\n", "*   公司的品牌定位和核心价值是什么？\n", "*   您希望目标用户对洗衣机产生什么样的感觉？\n"]}], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "llm = OllamaLLM(model=\"gemma3:12b\")\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请你详细思考，给公司里一个好的产品{product}，取名字。\"\n", ")\n", "chain = prompt | llm\n", "print(chain.invoke({\"product\": \"洗衣机\"}))"]}, {"cell_type": "markdown", "id": "d2296d2684a49f60", "metadata": {}, "source": ["### 流式输出"]}, {"cell_type": "code", "execution_count": 2, "id": "94e40e7d06fcd0f4", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T08:22:18.355729Z", "start_time": "2025-07-14T08:21:03.263423Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，为了给公司洗衣机产品取一个好的名字，我将从多个角度进行详细的思考，并提供多个方案，希望能给你提供灵感。\n", "\n", "**一、命名思路和原则**\n", "\n", "在开始列举名字之前，我们先明确一下命名的原则和需要考虑的因素：\n", "\n", "*   **目标用户**: 你的洗衣机主要面向哪些用户？（例如：年轻家庭、注重健康、追求时尚、注重节能、注重大容量等）\n", "*   **产品特点**: 洗衣机有哪些独特的功能和优势？（例如：智能、快速、节能、健康、静音、大容量、创新技术等）\n", "*   **品牌调性**:  公司/品牌的整体形象是什么样的？(例如：高端大气、亲民实用、科技创新、时尚简约等)\n", "*   **可传播性**: 名字要易于记忆、发音，方便口头传播和在线搜索。\n", "*   **独特性**:  避免与其他品牌或产品重名，最好能在商标注册上有所保障。\n", "*   **文化适应性**:  如果产品要出口到其他国家，需要考虑名字在当地文化中的含义，避免产生负面联想。\n", "\n", "**二、命名方案**\n", "\n", "我将根据不同的侧重点，给出多个命名方案，每个方案都附带解释，你可以根据公司的实际情况进行选择和调整：\n", "\n", "**1. 强调智能和科技感**\n", "\n", "*   **智洁 (<PERSON><PERSON><PERSON>):**  “智”代表智能，“洁”代表清洁，简洁明了，易于记忆。\n", "*   **灵洗 (<PERSON><PERSON><PERSON>):**  “灵”代表灵活、智能，“洗”代表洗涤，传达洗衣机智能化的感觉。\n", "*   **净界 (<PERSON><PERSON><PERSON>):**  “净”代表洁净，“界”代表领域，寓意洗衣机带来洁净的新领域。\n", "*   **艾克 (<PERSON><PERSON>):**  (结合了“AI”和“清洁”的谐音，偏向于国际化命名) 给人一种科技感和创新感。\n", "*   **星河洗 (<PERSON><PERSON><PERSON>):**  “星河”寓意着广阔、深邃，象征着洗衣机技术的先进。\n", "\n", "**2. 强调健康和呵护**\n", "\n", "*   **净舒 (<PERSON><PERSON><PERSON>):**  “净”代表洁净，“舒”代表舒适，寓意洗衣机带来洁净和舒适的生活。\n", "*   **呵护洗 (<PERSON><PERSON><PERSON>):**  直接表达洗衣机对衣物的呵护，温馨亲切。\n", "*   **纤净 (<PERSON><PERSON>):**  “纤”代表对衣物的细致呵护，“净”代表洁净。\n", "*   **安洗 (Ān <PERSON>):**  “安”代表安全、安心，“洗”代表洗涤，突出健康、安心的理念。\n", "*   **臻洁 (<PERSON><PERSON><PERSON><PERSON>):**  “臻”代表极致、完美，“洁”代表洁净，体现对洁净的极致追求。\n", "\n", "**3. 强调节能和环保**\n", "\n", "*   **省能洗 (<PERSON><PERSON><PERSON><PERSON>):**  直接点明洗衣机的节能特点。\n", "*   **绿净 (Lǜ Jìng):**  “绿”代表环保，“净”代表洁净，寓意环保和清洁。\n", "*   **易洁 (<PERSON><PERSON>):**  “易”代表节能省心，“洁”代表洁净，突出洗衣机的便捷性。\n", "*   **惜洁 (<PERSON><PERSON>):**  “惜”代表珍惜资源，体现环保理念。\n", "\n", "**4. 强调大容量和实用性**\n", "\n", "*   **容洗 (<PERSON><PERSON><PERSON>):**  “容”代表容量，“洗”代表洗涤，直接表达大容量的特点。\n", "*   **优洗 (<PERSON><PERSON><PERSON>):**  “优”代表优越、卓越，“洗”代表洗涤，突出洗衣机的实用性。\n", "*   **家净 (<PERSON><PERSON>):**  “家”代表家庭，“净”代表洁净，寓意为家庭带来洁净。\n", "\n", "**5.  更具创意的名字 (可能需要进一步调研市场接受度)**\n", "\n", "*   **织梦 (<PERSON><PERSON><PERSON>):**  “织”代表纺织品，“梦”代表美好，寓意洗衣机守护衣物的美好。\n", "*   **云洁 (<PERSON><PERSON>):**  “云”代表智能、科技感，“洁”代表洁净，给人一种轻盈、现代的感觉。\n", "*   **水印 (<PERSON><PERSON>):**  “水”代表洗衣机的主要介质，“印”代表留下美好的记忆。\n", "*   **初洁 (<PERSON><PERSON>):** “初”代表最初的洁净，寓意洗衣机能使衣物恢复最初的美好状态。\n", "\n", "**三、命名建议和注意事项**\n", "\n", "1.  **进行市场调研**: 在最终确定名字之前，最好进行小范围的市场调研，了解目标用户对不同名字的接受度和偏好。\n", "2.  **商标注册**: 确定名字后，务必进行商标注册，以保护公司的知识产权。\n", "3.  **域名注册**: 如果要建立洗衣机产品的官方网站，最好注册与名字相关的域名。\n", "4.  **口头传播测试**:  让不同年龄段、不同文化背景的人尝试念出名字，看看是否容易发音、易于记忆。\n", "5.  **与品牌故事结合**:  好的名字不仅要符合产品特点，还要与公司的品牌故事相结合，增强品牌内涵。\n", "6.  **组合命名**: 可以考虑将几个词语组合起来，创造出更独特的名字，例如“智净呵护”、“绿净家”等。\n", "\n", "**为了更好地帮助你，请提供更多关于洗衣机产品的信息，例如：**\n", "\n", "*   洗衣机的主要卖点是什么？\n", "*   目标用户是谁？\n", "*   公司/品牌的整体形象是什么样的？\n", "*   你更倾向于哪种风格的命名？（例如：科技感、健康感、实用性等）\n", "\n", "我将根据你的反馈，进一步优化命名方案。"]}], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "llm = OllamaLLM(model=\"gemma3:12b\")\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请你详细思考，给公司里一个好的产品{product}，取名字。\",\n", ")\n", "chain = prompt | llm\n", "# print(chain.invoke(input={\"product\": \"洗衣机\"}))\n", "for chunk in chain.stream(input={\"product\": \"洗衣机\"}):\n", "    print(chunk, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "a7e9c36d787ad91d", "metadata": {}, "source": ["### 自定义参数\n", "[OllamaLLM — 🦜🔗 LangChain documentation](https://python.langchain.com/api_reference/ollama/llms/langchain_ollama.llms.OllamaLLM.html#langchain_ollama.llms.OllamaLLM.format)"]}, {"cell_type": "code", "execution_count": 47, "id": "e63a68347b8461d4", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T09:20:26.880603Z", "start_time": "2025-07-14T09:20:24.149885Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"names\": [\n", "    \"净界\",\n", "    \"臻洗\",\n", "    \"织悦\",\n", "    \"清源\",\n", "    \"臻彩\"\n", "  ]\n", "}"]}], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "llm = OllamaLLM(model=\"gemma3:12b\",\n", "                seed=42,\n", "                mirostat_tau=0.5,\n", "                mirostat_eta=0.1,\n", "                mirostat=1,\n", "                format=\"json\",)\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请你简短回答，给公司里一个好的产品{product}，准备5个名字。\",\n", ")\n", "chain = prompt | llm\n", "# print(chain.invoke(input={\"product\": \"洗衣机\"}))\n", "for chunk in chain.stream(input={\"product\": \"洗衣机\"}):\n", "    print(chunk, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "c6e8e1d7565aec39", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>（基础链）"]}, {"cell_type": "code", "execution_count": 53, "id": "4c85f4870ce8c6c9", "metadata": {"ExecuteTime": {"end_time": "2025-07-14T09:36:45.310221Z", "start_time": "2025-07-14T09:36:43.609408Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. **Chronosync**\n", "2. **Aether Watch**\n", "3. **Verve Wear**\n", "\n"]}], "source": ["from langchain_ollama import OllamaLLM\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain.chains import LLMChain\n", "\n", "llm = OllamaLLM(model=\"gemma3:12b\")\n", "prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请为{product}设计一个品牌名，并给出3个备选方案。（简短回答）\"\n", ")\n", "\n", "# 显式创建LL<PERSON>hai<PERSON>（等价于你之前的 prompt | llm）\n", "chain = prompt | llm\n", "# result = chain.invoke(input=\"智能手表\")\n", "result = chain.invoke({\"product\": \"智能手表\"})\n", "print(result)"]}, {"cell_type": "markdown", "id": "223a390f2414341c", "metadata": {}, "source": ["### Sequential<PERSON><PERSON><PERSON>（顺序链）"]}, {"cell_type": "code", "execution_count": 1, "id": "e6ab391b572dc902", "metadata": {"ExecuteTime": {"end_time": "2025-07-16T06:23:10.327600Z", "start_time": "2025-07-16T06:23:06.480627Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 顺序组合结果 ---\n", "品牌名: 铂金时光手表（Platinum Time Watch）\n", "广告语: 经典时间，铂金守护。\n", "描述: 铂金时光手表是一款融合时尚与科技的智能手表，提供精准时间显示与多功能健康管理。\n"]}], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser, CommaSeparatedListOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_ollama import OllamaLLM\n", "\n", "# 初始化 Ollama 模型\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "\n", "# 定义一个用于所有链的通用输出解析器和格式指令\n", "list_output_parser = CommaSeparatedListOutputParser()\n", "str_output_parser = StrOutputParser() # 为简洁广告语使用字符串解析器\n", "format_instructions_list = list_output_parser.get_format_instructions()\n", "\n", "# --- 品牌名链 ---\n", "brand_name_prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"请为{product}设计一个高端品牌名（简短回答）：\"\n", ")\n", "brand_name_chain = (\n", "    brand_name_prompt\n", "    | llm\n", "    | str_output_parser # 品牌名通常是一个，所以用 StrOutputParser\n", ")\n", "\n", "# --- 广告语链 ---\n", "slogan_prompt = PromptTemplate(\n", "    input_variables=[\"brand_name\"],\n", "    template=\"为品牌'{brand_name}'设计一句简洁的广告语（不要额外输出）：\"\n", ")\n", "slogan_chain = (\n", "    slogan_prompt\n", "    | llm\n", "    | str_output_parser # 广告语一般是单个字符串，所以用 StrOutputParser\n", ")\n", "\n", "# --- 产品描述链 ---\n", "description_prompt = PromptTemplate(\n", "    input_variables=[\"brand_name\", \"product\"],\n", "    template=\"用50字以内描述'{brand_name}'品牌的{product}（不需要额外输出）：\"\n", ")\n", "description_chain = (\n", "    description_prompt\n", "    | llm\n", "    | str_output_parser # 产品描述也是单个字符串\n", ")\n", "\n", "# --- 组合成顺序链 (RunnablePassthrough 帮助传递原始输入) ---\n", "overall_chain_sequential = (\n", "    RunnablePassthrough.assign(\n", "        brand_name=brand_name_chain # 生成品牌名，并将其添加到中间结果中\n", "    ) | {\n", "        \"brand_name\": lambda x: x[\"brand_name\"], # 从中间结果中取出品牌名\n", "        \"slogan\": lambda x: slogan_chain.invoke({\"brand_name\": x[\"brand_name\"]}), # 使用品牌名生成广告语\n", "        \"description\": lambda x: description_chain.invoke({\"brand_name\": x[\"brand_name\"], \"product\": x[\"product\"]}) # 使用品牌名和原始产品名生成描述\n", "    }\n", ")\n", "\n", "# 执行链\n", "result_sequential = overall_chain_sequential.invoke({\"product\": \"智能手表\"})\n", "\n", "print(\"\\n--- 顺序组合结果 ---\")\n", "print(f\"品牌名: {result_sequential['brand_name']}\")\n", "print(f\"广告语: {result_sequential['slogan']}\")\n", "print(f\"描述: {result_sequential['description']}\")"]}, {"cell_type": "markdown", "id": "502112f0ff667a4a", "metadata": {}, "source": ["### 路由链 (Router Chain)\n", "路由链允许你根据输入动态选择要执行的子链。这在需要根据用户查询的不同意图来执行不同逻辑时非常有用。\n", "\n", "__基础知识:__ \\\n", "路由链的核心思想是有一个“路由器”LLM，它会分析输入并决定将输入传递给哪个“目标”链。每个目标链都处理特定类型的查询。"]}, {"cell_type": "code", "execution_count": 1, "id": "8b760ebd5a957790", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T03:23:45.170559Z", "start_time": "2025-07-21T03:23:18.110665Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 路由链示例 ---\n", "\n", "--- 命名请求 ---\n", "路由结果:\n", " 1. \"灵锐视界\"\n", "理由：这个名字融合了“灵”和“锐”，代表这款手机智能且灵敏，能够提供敏锐的视觉体验；“视界”则寓意用户可以通过它看到更广阔的世界。\n", "\n", "2. \"未来瞬点\"\n", "理由：“未来”体现了这是一款面向未来的智能手机，预示着科技的进步与创新；“瞬点”意为一瞬即得，突出其快速响应和高效处理的特点，表达出这款手机能迅速抓住每一个机会的精神。\n", "\n", "3. \"智光极境\"\n", "理由：以“智”展现其强大的智能性能，“光”则象征着信息与智慧的传播，二者结合寓意这款手机能够照亮用户探索未知世界的道路。而“极境”，意味着它的功能和体验达到极致境界。\n", "\n", "--- 描述请求 ---\n", "路由结果:\n", " 畅享未来，驾驶零排放新体验！高效能电池，超长续航；智能科技，安全无忧；静谧平顺，重塑出行新风尚。\n", "\n", "--- 通用请求 ---\n", "路由结果:\n", " 北京有许多著名的标志性建筑，其中包括：\n", "\n", "1. **故宫（紫禁城）** - 世界上现存规模最大、保存最完整的木质结构古建筑群。\n", "2. **天安门广场** - 全世界最大的城市中心广场之一，是国家庆典和重要政治活动的举办地。\n", "3. **长城（尤其是八达岭段）** - 作为中国古代防御工程的重要组成部分，被列入世界文化遗产。\n", "4. **鸟巢（国家体育场）** - 2008年北京奥运会主体育场。\n", "5. **水立方（国家游泳中心）** - 同样也是2008年北京奥运会的比赛场馆之一。\n", "6. **颐和园** - 综合性皇家园林，集传统造园艺术之大成，是中国保存最完整的一座皇家行宫御苑。\n", "7. **天坛公园** - 以祈年殿等古建筑群为主体，主要用于明清两代皇帝祭祀上天、祈求五谷丰登的场所。\n", "8. **北京人民大会堂** - 国家级会议和接待的重要场所。\n", "\n", "这些只是其中的一部分，还有许多其他具有代表性的建筑和地标在等待探索。\n"]}], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough, RunnableBranch, RunnableLambda\n", "from langchain_ollama import OllamaLLM\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# 初始化 Ollama 模型\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "\n", "# 定义输出解析器\n", "str_output_parser = StrOutputParser()\n", "\n", "# --- 目标链定义 ---\n", "\n", "# 产品命名链\n", "name_prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"你是一个产品命名专家。请为'{product}'提供3个创意品牌名，并简要说明理由。要求每个名字一行。\",\n", ")\n", "name_chain = name_prompt | llm | str_output_parser\n", "\n", "# 产品描述链\n", "description_prompt = PromptTemplate(\n", "    input_variables=[\"product\"],\n", "    template=\"你是一个营销专家。请为'{product}'撰写一个50字以内的营销描述。确保吸引人并突出核心卖点。\",\n", ")\n", "description_chain = description_prompt | llm | str_output_parser\n", "\n", "# 通用问题处理链\n", "general_prompt = PromptTemplate(\n", "    input_variables=[\"input\"],\n", "    template=\"你是一个通用助手。请简要回答关于'{input}'的问题。\",\n", ")\n", "general_chain = general_prompt | llm | str_output_parser\n", "\n", "# --- 路由逻辑 ---\n", "\n", "# 路由器提示\n", "router_prompt = PromptTemplate(\n", "    input_variables=[\"input\"],\n", "    template=\"\"\"根据用户输入，将请求路由到正确的专家链。\n", "如果输入与产品命名相关，请回复'name'。\n", "如果输入与产品描述相关，请回复'description'。\n", "否则，请回复'general'。\n", "\n", "用户输入: {input}\n", "路由目标:\"\"\",\n", ")\n", "\n", "# 路由器链\n", "router_chain = router_prompt | llm | str_output_parser\n", "\n", "# 定义分支逻辑\n", "branch = RunnableBranch(\n", "    (lambda x: \"name\" in x[\"route\"].lower(), lambda x: name_chain.invoke({\"product\": x[\"input\"]})),\n", "    (lambda x: \"description\" in x[\"route\"].lower(), lambda x: description_chain.invoke({\"product\": x[\"input\"]})),\n", "    lambda x: general_chain.invoke({\"input\": x[\"input\"]}),\n", ")\n", "\n", "# 组合路由链和分支\n", "full_router_chain = {\n", "    \"input\": RunnablePassthrough(),  # 保留原始输入\n", "    \"route\": router_chain           # 计算路由目标\n", "} | branch\n", "\n", "# 运行示例\n", "print(\"--- 路由链示例 ---\")\n", "\n", "print(\"\\n--- 命名请求 ---\")\n", "result_name = full_router_chain.invoke(\"请给我一款新型智能手机取名字。\")\n", "print(f\"路由结果:\\n {result_name}\")\n", "\n", "print(\"\\n--- 描述请求 ---\")\n", "result_description = full_router_chain.invoke(\"描述一下最新的电动汽车。\")\n", "print(f\"路由结果:\\n {result_description}\")\n", "\n", "print(\"\\n--- 通用请求 ---\")\n", "result_general = full_router_chain.invoke(\"北京有哪些标志性建筑？\")\n", "print(f\"路由结果:\\n {result_general}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6a8ffe1c3c8acfbc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f5ac0513e2c7a4bb", "metadata": {}, "source": ["2025年4月-2025年8月 成都华兴汇明科技有限公司 OCR算法实习生\n", "\n", "**核心职责：**参与ICD报表信息、SCPI命令信息提取全流程，使用PaddleOCR，EasyOCR，SuryaOCR，TesseractOCR用于ICD报表信息提取，使用Langchain框架基于Ollama平台结合RAGFlow本地开发SCPI命令提取工具。\n", "\n", "这是我的第二份实习经历，我现在写了一部分了，我需要你根据我提供的描述来完善我的实习经历。\n", "4-5月，我主要开发ICD报表信息提取工具，前期探索了EasyOCR，SuryaOCR，TesseractOCR,最后选择使用PaddlePaddle框架和PaddleOCR开发，使用通用版面解析产线（包含版面区域检测模块、表格结构识别模块、公式识别模块、印章文本检测模块、文本检测模块、文本识别模块、文本行方向分类模块、文档图像方向分类模块、文本图像矫正模块）结合PaddleLabel标注的数据集，训练模型（版面区域检测模块、表格结构识别模块、文本图像矫正模块），并使用基于新模型的通用版面解析产线进行ICD报表信息提取，最后通过docker部署在目标Linux服务器上作为工具调用，ICD报表信息提准确率达95%。\n", "6-8月，我主要开发SCPI命令提取工具，使用Langchain框架基于Ollama平台结合RAGFlow开发SCPI命令提取工具，，Gradio作为前端框架，FastAPI链接前端和后端。通过Langchain的LCEL链式组合不同模块，使用deepseek-r1推理模型提取厂商基础信息和SCPI\n", "命令目录，使用Qwen3模型作为提取LLM的基础模型，通过将PDF文件里的非结构化数据转换为Markdown的格式存储在向量库中，使用RAGflow进行SCPI命令信息的检索增强生成，准确率达85%。使用Qwen2.5VL多模态模型提取所有的SCPI命令及其描述信息并输出标准JSON模板的文件。"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}