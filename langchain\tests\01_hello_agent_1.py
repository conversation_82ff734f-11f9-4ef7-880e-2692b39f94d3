import os
# 从 langchain_core 导入 RunnableConfig
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage

# 从 langchain_community 导入 OllamaEmbeddings 和 ChatOllama
from langchain_ollama import ChatOllama
from langchain_community.tools.tavily_search import TavilySearchResults # SerpAPI 的替代或更现代的搜索工具
from langchain_community.utilities import SerpAPIWrapper # 如果你仍想用SerpAPI，这是其集成方式

# 从 langchain 导入用于创建 Agent 的核心组件
from langchain.agents import create_react_agent, AgentExecutor
from langchain import hub # 引入 hub 用于加载预定义的提示

# 设置 API 密钥
# 注意：直接硬编码 API 密钥不是最佳实践。在生产环境中，应使用环境变量。
# 你的 SerpAPI 密钥
os.environ["SERPAPI_API_KEY"] = "41032fba97f866bdd6680cea364568e0338c0f5832057081d07ccae427f7ef49"

# 实例化聊天模型
# model="deepseek-r1:8b" 确保你在 Ollama 中已经 pull 了这个模型
chat_model = ChatOllama(model="deepseek-r1:8b", temperature=0.6)

# 加载工具
# LangChain 新版本更推荐直接实例化工具，而不是通过 load_tools
# SerpAPI 工具通常用于搜索。llm-math 用于数学计算。
# 这里我们直接实例化 SerpAPIWrapper 作为工具，并给它一个描述
# 注意：load_tools 虽然仍可用，但更推荐直接实例化具体的工具类
# 如果你希望使用 Tavily Search，可以这样实例化：
# search_tool = TavilySearchResults(k=3) # k表示返回多少个搜索结果
# 或者继续使用 SerpAPI：
search_tool = SerpAPIWrapper(serpapi_api_key=os.environ["SERPAPI_API_KEY"])
search_tool.name = "Google Search" # 给工具一个描述性的名字
search_tool.description = "useful for when you need to answer questions about current events or factual questions"

# 对于 llm-math，它在 LangChain 新版本中可能需要一些调整，
# 通常直接在 Agent 中进行简单的数学推理比调用外部工具更高效。
# 但如果你的数学计算非常复杂且需要特定格式，可以考虑。
# 暂时我们只包含搜索工具，让 Agent 专注于信息检索。

tools = [search_tool] # Agent 将会使用这个工具列表

# 从 LangChain Hub 加载 ReAct 代理的提示模板
# 这是新版本中推荐的方式，提供了结构化的、经过优化的提示。
prompt = hub.pull("hwchase17/react")

# 创建 ReAct Agent
# create_react_agent 接受工具列表、LLM 和提示。
# 它返回一个 Runnable 对象，这个对象就是 Agent 的核心逻辑。
agent = create_react_agent(chat_model, tools, prompt)

# 创建 AgentExecutor
# AgentExecutor 负责实际执行 Agent 的步骤，包括工具调用、思考过程等。
# verbose=True 会打印 Agent 的思考过程，这在调试时非常有用。
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True, handle_parsing_errors=True)

# 调用 Agent
# input 是传递给 Agent 的用户查询
# invoke 方法是 Runnable 接口的一部分
# 你也可以使用 agent_executor.stream() 来获取流式输出
response = agent_executor.invoke({"input": "今天是2025年7月7日，三天后成都天气怎么样？"})

print("\n--- Agent 最终响应 ---")
print(response["output"])