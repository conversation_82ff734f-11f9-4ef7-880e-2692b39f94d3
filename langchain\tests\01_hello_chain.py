from langchain.prompts.chat import (
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)
from langchain_ollama import OllamaLLM

# 初始化模型
chat = OllamaLLM(model="deepseek-r1:8b", temperature=0.5)

# 构建提示模板
system_template = "You are a helpful assistant that translates {input_language} to {output_language}."
system_message_prompt = SystemMessagePromptTemplate.from_template(system_template)

human_template = "{text}"
human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)

chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])

# 使用新的Runnable接口替代LLMChain
chain = chat_prompt | chat

# 使用invoke替代run方法
result = chain.invoke({
    "input_language": "English",
    "output_language": "Chinese",
    "text": "I love programming."
})

print(result)