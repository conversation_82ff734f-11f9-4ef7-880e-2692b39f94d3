{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <center>Deepseek企业级Agent项目开发实战</center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <center>Part 2. Ollama REST API - api/generate 接口详解 </center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;Ollama 服务启动后会提供一系列原生 ` REST API` 端点。通过这些`Endpoints`可以在代码环境下与`ollama`启动的大模型进行交互、管理模型和获取相关信息。其中两个`endpoint` 是最重要的，分别是：\n", "  - <font color=\"red\">**POST /api/generate**</font>\n", "  - <font color=\"red\">**POST /api/chat**</font>\n", "\n", "&emsp;&emsp;其他端点情况：\n", "  - POST /api/create   \n", "  - POST /api/tags\n", "  - POST /api/show\n", "  - POST /api/copy\n", "  - DELETE /api/delete\n", "  - POST /api/pull\n", "  - POST /api/push\n", "  - POST /api/embed\n", "  - GET /api/ps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. /api/generate 接口参数概览\n", "\n", "&emsp;&emsp;该接口使用提供的模型为给定提示生成响应。这是一个流式端点，因此会有一系列响应。最终响应对象将包括统计信息和请求中的其他数据。其中比较重要的参数我做了标红处理，大家重点理解。\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>常规参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名      | 类型      | 描述                                                         |\n", "| ----------- | --------- | ------------------------------------------------------------ |\n", "| <font color=\"red\">**model**</font>   | *(必需)*  | 模型名称，必须遵循 `model:tag` 格式，如果不提供，则将默认为 `latest`。 |\n", "| <font color=\"red\">**prompt**</font>  | *(必需)*  | 用于生成响应的提示。                                         |\n", "| **suffix**  | *(可选)*  | 模型响应后的文本。                                         |\n", "| **images**  | *(可选)*  | base64 编码图像的列表（适用于多模态模型，如 llava）。      |\n", "\n", "</div>\n", "\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4> 高级参数 (可选)</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名      | 类型      | 描述                                                         |\n", "| ----------- | --------- | ------------------------------------------------------------ |\n", "| <font color=\"red\">**format**</font>  | *(可选)*  | 返回响应的格式。格式可以是 `json` 或 JSON 模式。<font color=\"red\">最主要的问题是避免产生大量空格</font>         |\n", "| <font color=\"red\">**options**</font> | *(可选)*  | 文档中列出的其他模型参数，例如 `temperature`。              |\n", "| <font color=\"red\">**system**</font>  | *(可选)*  | 系统消息，用于覆盖 Modelfile 中定义的内容。                 |\n", "| **template**| *(可选)*  | 要使用的提示模板，覆盖 Modelfile 中定义的内容。             |\n", "| <font color=\"red\">**stream**</font>  | *(可选)*  | 如果为 `false`，响应将作为单个响应对象返回，而不是对象流。 |\n", "| **raw**     | *(可选)*  | 如果为 `true`，则不会对提示应用格式。                       |\n", "| <font color=\"red\">**keep_alive**</font> | *(可选)* | 控制模型在请求后保持加载的时间（默认：5分钟）。             |\n", "| **context** | *(可选)*  | *(已弃用)* 从先前请求返回的上下文参数，用于保持简短的对话记忆。 |\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;其中，Options参数详细解释如下，同样我对重点参数做了标红处理，大家重点理解。\n", "\n", "| 参数名 | 描述 | 值类型 | 示例用法 |\n", "| --------------- | ------------------------------------------------------------ | ------ | ---------------------- |\n", "| mirostat | 启用 Mirostat 采样以控制困惑度。（默认：0，0 = 禁用，1 = Mirostat，2 = Mirostat 2.0） | int | mirostat 0 |\n", "| mirostat_eta| 影响算法对生成文本反馈的响应速度。较低的学习率会导致调整较慢，而较高的学习率会使算法更具响应性。（默认：0.1） | float | mirostat_eta 0.1 |\n", "| mirostat_tau| 控制输出的连贯性和多样性之间的平衡。较低的值会导致更集中和连贯的文本。（默认：5.0） | float | mirostat_tau 5.0 |\n", "| <font color=\"red\">num_ctx</font> | 设置用于生成下一个标记的上下文窗口大小。（默认：2048）, 影响的是模型可以一次记住的最大 token 数量。 | int | num_ctx 4096|\n", "| repeat_last_n| 设置模型回溯的范围以防止重复。（默认：64，0 = 禁用，-1 = num_ctx） | int | repeat_last_n 64 |\n", "| repeat_penalty| 设置惩罚重复的强度。较高的值（例如 1.5）会更强烈地惩罚重复，而较低的值（例如 0.9）会更宽松。（默认：1.1） | float | repeat_penalty 1.1 |\n", "| <font color=\"red\">temperature</font> | 模型的温度。增加温度会使模型的回答更具创造性。（默认：0.8） | float | temperature 0.7 |\n", "| seed | 设置用于生成的随机数种子。将其设置为特定数字将使模型对相同提示生成相同的文本。（默认：0） | int | seed 42 |\n", "| <font color=\"red\">stop</font> | 设置使用的停止序列。当遇到此模式时，LLM 将停止生成文本并返回。可以通过在 modelfile 中指定多个单独的停止参数来设置多个停止模式。 | string | stop \"AI assistant:\" |\n", "| <font color=\"red\">num_predict</font> | 生成文本时要预测的最大标记数。（默认：-1，无限生成）,影响模型最大可以生成的 token 数量。 | int | num_predict 42 |\n", "| top_k | 降低生成无意义文本的概率。较高的值（例如 100）会给出更多样化的答案，而较低的值（例如 10）会更保守。（默认：40） | int | top_k 40 |\n", "| top_p | 与 top-k 一起工作。较高的值（例如 0.95）会导致更具多样性的文本，而较低的值（例如 0.5）会生成更集中和保守的文本。（默认：0.9） | float | top_p 0.9 |\n", "| min_p | top_p 的替代方案，旨在确保质量和多样性之间的平衡。参数 p 表示考虑标记的最小概率，相对于最可能标记的概率。例如，p=0.05 时，最可能的标记概率为 0.9，值小于 0.045 的 logits 会被过滤掉。（默认：0.0） | float | min_p 0.05 |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;对于`endpoints`来说，如果使用代码调用，常规的调用方式是通`requests`库进行调用。如下所示："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:34:22.0279148Z\",\n", "  \"response\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的人，想快速了解AI的基本概念。\\n\\n这个query很明确，就是希望得到简洁但全面的基础知识概述。不需要太深入的技术细节，重点在于清晰定义和关键应用领域。考虑到“简短”这个要求，回复应该控制在10句话以内，用通俗易懂的语言解释核心内容。\\n\\n用户可能刚接触这个主题，或者需要准备一个简单的科普材料。ta的情绪应该是平和且好奇的，想获得基础认知而非专业讨论。所以回复要避免术语堆砌，但也不能过于简化到失去准确性。\\n\\n我应该先定义什么是AI，然后说明它的发展历史、关键能力领域和技术原理（特别是机器学习），最后列举几个代表性应用。这样既能满足“简短”的要求，又能保证信息的完整性。\\n\\n要注意平衡专业性和可读性。比如提到神经网络时用简单比喻，说深度学习时强调它是机器学习的一个分支而不是独立概念。还要避免过于学术化的表述，但也不能太随意。\\n\\n用户可能后续会追问更具体的问题，所以在结尾可以留个钩子，表示愿意提供更多信息支持ta的深入探索。\\n</think>\\n好的，请看这段关于人工智能的简短介绍：\\n\\n**人工智能（AI）** 是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的机器或系统。其核心目标是研发出能模拟理解、学习、推理、感知和自然语言处理能力的人工智能。\\n\\n简单的说，AI 让电脑能像人一样思考和行动。这涉及到让机器从数据中学习模式，并做出决策（如机器学习），或者直接处理感官信息进行识别（如计算机视觉）。AI 的发展经历了从规则基础到统计学习再到深度学习等多个阶段，后者模仿人脑的结构来实现更复杂的学习。\\n\\n目前 AI 已经在很多领域产生影响，包括：\\n\\n*   **语音助手**：像 Siri 和小爱同学这样的应用。\\n*   **推荐系统**：如 Netflix 或淘宝为你推荐内容。\\n*   **自动驾驶汽车**：使用感知和决策算法导航。\\n*   **医疗诊断**：辅助医生分析医学图像或数据。\\n\\n尽管 AI 还在不断发展中，并非所有方面都能完全替代人类，但它正以前所未有的速度改变着我们的生活、工作乃至思维方式。\",\n", "  \"done\": true,\n", "  \"done_reason\": \"stop\",\n", "  \"context\": [\n", "    151669,\n", "    14880,\n", "    43959,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    151670,\n", "    151667,\n", "    198,\n", "    106287,\n", "    3837,\n", "    20002,\n", "    103945,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    104544,\n", "    2565,\n", "    87267,\n", "    104104,\n", "    99720,\n", "    100631,\n", "    32664,\n", "    99602,\n", "    103198,\n", "    100623,\n", "    3837,\n", "    99172,\n", "    101098,\n", "    99794,\n", "    15469,\n", "    105166,\n", "    101290,\n", "    3407,\n", "    99487,\n", "    1631,\n", "    99165,\n", "    100692,\n", "    3837,\n", "    99486,\n", "    99880,\n", "    101051,\n", "    110485,\n", "    77288,\n", "    100011,\n", "    105549,\n", "    100032,\n", "    113608,\n", "    1773,\n", "    104689,\n", "    99222,\n", "    100403,\n", "    105535,\n", "    104449,\n", "    3837,\n", "    99887,\n", "    101321,\n", "    104542,\n", "    91282,\n", "    33108,\n", "    99936,\n", "    99892,\n", "    100650,\n", "    1773,\n", "    106350,\n", "    2073,\n", "    98237,\n", "    99534,\n", "    854,\n", "    99487,\n", "    101882,\n", "    3837,\n", "    104787,\n", "    99730,\n", "    100359,\n", "    18493,\n", "    16,\n", "    15,\n", "    100908,\n", "    108341,\n", "    3837,\n", "    11622,\n", "    116336,\n", "    86744,\n", "    100272,\n", "    109824,\n", "    104136,\n", "    100185,\n", "    43815,\n", "    3407,\n", "    20002,\n", "    87267,\n", "    99900,\n", "    102108,\n", "    99487,\n", "    100220,\n", "    3837,\n", "    100631,\n", "    85106,\n", "    101077,\n", "    46944,\n", "    105172,\n", "    106094,\n", "    100643,\n", "    1773,\n", "    2565,\n", "    108876,\n", "    104583,\n", "    49111,\n", "    33108,\n", "    100136,\n", "    102962,\n", "    9370,\n", "    3837,\n", "    99172,\n", "    100350,\n", "    99896,\n", "    102875,\n", "    109093,\n", "    99878,\n", "    104075,\n", "    1773,\n", "    99999,\n", "    104787,\n", "    30534,\n", "    101153,\n", "    116925,\n", "    100537,\n", "    111034,\n", "    3837,\n", "    77288,\n", "    105827,\n", "    102767,\n", "    110487,\n", "    26939,\n", "    104899,\n", "    111076,\n", "    3407,\n", "    35946,\n", "    99730,\n", "    60726,\n", "    91282,\n", "    106582,\n", "    15469,\n", "    3837,\n", "    101889,\n", "    66394,\n", "    99652,\n", "    103949,\n", "    100022,\n", "    5373,\n", "    99936,\n", "    99788,\n", "    100650,\n", "    108800,\n", "    105318,\n", "    9909,\n", "    104050,\n", "    102182,\n", "    100134,\n", "    48272,\n", "    100161,\n", "    118569,\n", "    100204,\n", "    110249,\n", "    99892,\n", "    1773,\n", "    99654,\n", "    111628,\n", "    101929,\n", "    2073,\n", "    98237,\n", "    99534,\n", "    97907,\n", "    101882,\n", "    3837,\n", "    107713,\n", "    101907,\n", "    27369,\n", "    9370,\n", "    111293,\n", "    3407,\n", "    105213,\n", "    102243,\n", "    99878,\n", "    105178,\n", "    30440,\n", "    57553,\n", "    33071,\n", "    1773,\n", "    101912,\n", "    104496,\n", "    102398,\n", "    71356,\n", "    13343,\n", "    11622,\n", "    100405,\n", "    113182,\n", "    3837,\n", "    36587,\n", "    102217,\n", "    100134,\n", "    13343,\n", "    104046,\n", "    105226,\n", "    102182,\n", "    100134,\n", "    104111,\n", "    103799,\n", "    104610,\n", "    102024,\n", "    101290,\n", "    1773,\n", "    104019,\n", "    101153,\n", "    102767,\n", "    104380,\n", "    105302,\n", "    112926,\n", "    3837,\n", "    77288,\n", "    105827,\n", "    99222,\n", "    105874,\n", "    3407,\n", "    20002,\n", "    87267,\n", "    105463,\n", "    36993,\n", "    117976,\n", "    33126,\n", "    100398,\n", "    103936,\n", "    3837,\n", "    106325,\n", "    114903,\n", "    73670,\n", "    99337,\n", "    18947,\n", "    103316,\n", "    44729,\n", "    3837,\n", "    51463,\n", "    102167,\n", "    99553,\n", "    110074,\n", "    100143,\n", "    2565,\n", "    9370,\n", "    100403,\n", "    101964,\n", "    8997,\n", "    151668,\n", "    198,\n", "    99692,\n", "    37945,\n", "    50930,\n", "    107083,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    48443,\n", "    334,\n", "    104455,\n", "    9909,\n", "    15469,\n", "    7552,\n", "    334,\n", "    54851,\n", "    104564,\n", "    99891,\n", "    104111,\n", "    103799,\n", "    3837,\n", "    104717,\n", "    50377,\n", "    100006,\n", "    75117,\n", "    102119,\n", "    85106,\n", "    103971,\n", "    100168,\n", "    108530,\n", "    9370,\n", "    102182,\n", "    57191,\n", "    72448,\n", "    1773,\n", "    41146,\n", "    100185,\n", "    100160,\n", "    20412,\n", "    100048,\n", "    20221,\n", "    26232,\n", "    105717,\n", "    101128,\n", "    5373,\n", "    100134,\n", "    5373,\n", "    113272,\n", "    5373,\n", "    108272,\n", "    33108,\n", "    99795,\n", "    102064,\n", "    54542,\n", "    99788,\n", "    100623,\n", "    48692,\n", "    100168,\n", "    3407,\n", "    105172,\n", "    36587,\n", "    3837,\n", "    15469,\n", "    33424,\n", "    102,\n", "    104145,\n", "    26232,\n", "    65101,\n", "    17340,\n", "    101891,\n", "    104107,\n", "    33108,\n", "    100675,\n", "    1773,\n", "    43288,\n", "    109228,\n", "    99258,\n", "    102182,\n", "    45181,\n", "    20074,\n", "    15946,\n", "    100134,\n", "    100144,\n", "    90395,\n", "    104086,\n", "    102041,\n", "    9909,\n", "    29524,\n", "    102182,\n", "    100134,\n", "    48272,\n", "    100631,\n", "    101041,\n", "    54542,\n", "    118119,\n", "    27369,\n", "    71817,\n", "    102450,\n", "    9909,\n", "    29524,\n", "    104564,\n", "    104916,\n", "    74276,\n", "    15469,\n", "    43589,\n", "    99185,\n", "    106014,\n", "    45181,\n", "    104190,\n", "    99896,\n", "    26939,\n", "    100787,\n", "    100134,\n", "    107947,\n", "    102217,\n", "    100134,\n", "    108211,\n", "    100385,\n", "    3837,\n", "    107184,\n", "    108391,\n", "    17340,\n", "    99931,\n", "    9370,\n", "    100166,\n", "    36407,\n", "    101884,\n", "    33126,\n", "    102181,\n", "    105595,\n", "    3407,\n", "    100004,\n", "    15235,\n", "    83002,\n", "    110,\n", "    53393,\n", "    18493,\n", "    99555,\n", "    100650,\n", "    100394,\n", "    99564,\n", "    3837,\n", "    100630,\n", "    48443,\n", "    9,\n", "    256,\n", "    3070,\n", "    105761,\n", "    110498,\n", "    334,\n", "    5122,\n", "    65101,\n", "    74974,\n", "    58143,\n", "    30709,\n", "    99242,\n", "    101181,\n", "    101893,\n", "    99892,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    101914,\n", "    72448,\n", "    334,\n", "    5122,\n", "    29524,\n", "    22642,\n", "    92313,\n", "    105052,\n", "    106184,\n", "    101914,\n", "    43815,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    109044,\n", "    100343,\n", "    334,\n", "    5122,\n", "    37029,\n", "    108272,\n", "    33108,\n", "    102041,\n", "    107018,\n", "    105544,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    100182,\n", "    105262,\n", "    334,\n", "    5122,\n", "    104650,\n", "    103998,\n", "    101042,\n", "    104316,\n", "    107553,\n", "    57191,\n", "    20074,\n", "    3407,\n", "    104138,\n", "    15235,\n", "    32181,\n", "    246,\n", "    18493,\n", "    99607,\n", "    107320,\n", "    90395,\n", "    65676,\n", "    55338,\n", "    99522,\n", "    104297,\n", "    100372,\n", "    105598,\n", "    103971,\n", "    3837,\n", "    111386,\n", "    36556,\n", "    103982,\n", "    31838,\n", "    38342,\n", "    99996,\n", "    101149,\n", "    101933,\n", "    99164,\n", "    103952,\n", "    99424,\n", "    5373,\n", "    99257,\n", "    105326,\n", "    116903,\n", "    1773\n", "  ],\n", "  \"total_duration\": 12060392900,\n", "  \"load_duration\": 2062790800,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 126183700,\n", "  \"eval_count\": 491,\n", "  \"eval_duration\": 9870914900\n", "}\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",  # 这里需要根据实际情况进行修改\n", "    \"stream\": False,       # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "}\n", "\n", "# 调用生成接口\n", "response_generate = requests.post(generate_url, json=generate_payload)\n", "if response_generate.status_code == 200:\n", "    generate_response = response_generate.json()\n", "    print(\"生成响应:\", json.dumps(generate_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_generate.status_code, response_generate.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;返回的响应中包含以下参数，其对应的描述如下："]}, {"cell_type": "markdown", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>响应参数</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数名                  | 描述                                                         |\n", "| ----------------------- | ------------------------------------------------------------ |\n", "| **total_duration**      | 单次响应花费的总时间                                          |\n", "| **load_duration**       | 加载模型花费的时间                                   |\n", "| **prompt_eval_count**   | 提示中的token数                                               |\n", "| **prompt_eval_duration**| 评估提示所花费的时间（以纳秒为单位）                                 |\n", "| **eval_count**          | 响应中的token数                                               |\n", "| **eval_duration**       | 生成响应的时间（以纳秒为单位）                              |\n", "| **context**             | 在此响应中使用的对话的编码，可以在下一个请求中发送以保持对话记忆 |\n", "| **response**            | 空响应是流的，如果未流式传输，则将包含完整的响应             |\n", "\n", "</div>\n", "\n", "&emsp;&emsp;返回的响应体中重点关注以下几个参数："]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. response 参数格式化解析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;`response` 字段指的是模型生成的实际输出内容。对于 `DeepSeek-R1` 模型来说，`response` 字段中包含<think> 标签和正常文本，<think> 标签用于表示模型的思考过程或内部推理，而正常的文本则是模型生成的实际输出内容。注意：非推理类模型的返回结果中没有<think></think>标识。\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<think>\\n嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的人，想快速了解AI的基本概念。\\n\\n这个query很明确，就是希望得到简洁但全面的基础知识概述。不需要太深入的技术细节，重点在于清晰定义和关键应用领域。考虑到“简短”这个要求，回复应该控制在10句话以内，用通俗易懂的语言解释核心内容。\\n\\n用户可能刚接触这个主题，或者需要准备一个简单的科普材料。ta的情绪应该是平和且好奇的，想获得基础认知而非专业讨论。所以回复要避免术语堆砌，但也不能过于简化到失去准确性。\\n\\n我应该先定义什么是AI，然后说明它的发展历史、关键能力领域和技术原理（特别是机器学习），最后列举几个代表性应用。这样既能满足“简短”的要求，又能保证信息的完整性。\\n\\n要注意平衡专业性和可读性。比如提到神经网络时用简单比喻，说深度学习时强调它是机器学习的一个分支而不是独立概念。还要避免过于学术化的表述，但也不能太随意。\\n\\n用户可能后续会追问更具体的问题，所以在结尾可以留个钩子，表示愿意提供更多信息支持ta的深入探索。\\n</think>\\n好的，请看这段关于人工智能的简短介绍：\\n\\n**人工智能（AI）** 是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的机器或系统。其核心目标是研发出能模拟理解、学习、推理、感知和自然语言处理能力的人工智能。\\n\\n简单的说，AI 让电脑能像人一样思考和行动。这涉及到让机器从数据中学习模式，并做出决策（如机器学习），或者直接处理感官信息进行识别（如计算机视觉）。AI 的发展经历了从规则基础到统计学习再到深度学习等多个阶段，后者模仿人脑的结构来实现更复杂的学习。\\n\\n目前 AI 已经在很多领域产生影响，包括：\\n\\n*   **语音助手**：像 Siri 和小爱同学这样的应用。\\n*   **推荐系统**：如 Netflix 或淘宝为你推荐内容。\\n*   **自动驾驶汽车**：使用感知和决策算法导航。\\n*   **医疗诊断**：辅助医生分析医学图像或数据。\\n\\n尽管 AI 还在不断发展中，并非所有方面都能完全替代人类，但它正以前所未有的速度改变着我们的生活、工作乃至思维方式。'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_response[\"response\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;可以通过简单的字符串操作来分离 <think> 标签中的思考内容和正常的文本内容，代码如下："]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["思考内容:\n", " 嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的人，想快速了解AI的基本概念。\n", "\n", "这个query很明确，就是希望得到简洁但全面的基础知识概述。不需要太深入的技术细节，重点在于清晰定义和关键应用领域。考虑到“简短”这个要求，回复应该控制在10句话以内，用通俗易懂的语言解释核心内容。\n", "\n", "用户可能刚接触这个主题，或者需要准备一个简单的科普材料。ta的情绪应该是平和且好奇的，想获得基础认知而非专业讨论。所以回复要避免术语堆砌，但也不能过于简化到失去准确性。\n", "\n", "我应该先定义什么是AI，然后说明它的发展历史、关键能力领域和技术原理（特别是机器学习），最后列举几个代表性应用。这样既能满足“简短”的要求，又能保证信息的完整性。\n", "\n", "要注意平衡专业性和可读性。比如提到神经网络时用简单比喻，说深度学习时强调它是机器学习的一个分支而不是独立概念。还要避免过于学术化的表述，但也不能太随意。\n", "\n", "用户可能后续会追问更具体的问题，所以在结尾可以留个钩子，表示愿意提供更多信息支持ta的深入探索。\n", "\n", "正常内容:\n", " 好的，请看这段关于人工智能的简短介绍：\n", "\n", "**人工智能（AI）** 是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的机器或系统。其核心目标是研发出能模拟理解、学习、推理、感知和自然语言处理能力的人工智能。\n", "\n", "简单的说，AI 让电脑能像人一样思考和行动。这涉及到让机器从数据中学习模式，并做出决策（如机器学习），或者直接处理感官信息进行识别（如计算机视觉）。AI 的发展经历了从规则基础到统计学习再到深度学习等多个阶段，后者模仿人脑的结构来实现更复杂的学习。\n", "\n", "目前 AI 已经在很多领域产生影响，包括：\n", "\n", "*   **语音助手**：像 Siri 和小爱同学这样的应用。\n", "*   **推荐系统**：如 Netflix 或淘宝为你推荐内容。\n", "*   **自动驾驶汽车**：使用感知和决策算法导航。\n", "*   **医疗诊断**：辅助医生分析医学图像或数据。\n", "\n", "尽管 AI 还在不断发展中，并非所有方面都能完全替代人类，但它正以前所未有的速度改变着我们的生活、工作乃至思维方式。\n"]}], "source": ["# 提取 <think> 标签中的内容\n", "think_start = generate_response[\"response\"].find(\"<think>\")\n", "think_end = generate_response[\"response\"].find(\"</think>\")\n", "\n", "if think_start != -1 and think_end != -1:\n", "    think_content = generate_response[\"response\"][think_start + len(\"<think>\"):think_end].strip()\n", "else:\n", "    think_content = \"No think content found.\"\n", "\n", "# 提取正常的文本内容\n", "normal_content = generate_response[\"response\"][think_end + len(\"</think>\"):].strip()\n", "\n", "# 打印结果\n", "print(\"思考内容:\\n\", think_content)\n", "print(\"\\n正常内容:\\n\", normal_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;当然也可以用相同的方式提取返回的响应中所有参数的值："]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: deepseek-r1:8b\n", "Created At: 2025-08-14T06:34:22.0279148Z\n", "Response: <think>\n", "嗯，用户想要一个关于人工智能的简短介绍。看起来ta可能是个学生或者对科技感兴趣的人，想快速了解AI的基本概念。\n", "\n", "这个query很明确，就是希望得到简洁但全面的基础知识概述。不需要太深入的技术细节，重点在于清晰定义和关键应用领域。考虑到“简短”这个要求，回复应该控制在10句话以内，用通俗易懂的语言解释核心内容。\n", "\n", "用户可能刚接触这个主题，或者需要准备一个简单的科普材料。ta的情绪应该是平和且好奇的，想获得基础认知而非专业讨论。所以回复要避免术语堆砌，但也不能过于简化到失去准确性。\n", "\n", "我应该先定义什么是AI，然后说明它的发展历史、关键能力领域和技术原理（特别是机器学习），最后列举几个代表性应用。这样既能满足“简短”的要求，又能保证信息的完整性。\n", "\n", "要注意平衡专业性和可读性。比如提到神经网络时用简单比喻，说深度学习时强调它是机器学习的一个分支而不是独立概念。还要避免过于学术化的表述，但也不能太随意。\n", "\n", "用户可能后续会追问更具体的问题，所以在结尾可以留个钩子，表示愿意提供更多信息支持ta的深入探索。\n", "</think>\n", "好的，请看这段关于人工智能的简短介绍：\n", "\n", "**人工智能（AI）** 是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的机器或系统。其核心目标是研发出能模拟理解、学习、推理、感知和自然语言处理能力的人工智能。\n", "\n", "简单的说，AI 让电脑能像人一样思考和行动。这涉及到让机器从数据中学习模式，并做出决策（如机器学习），或者直接处理感官信息进行识别（如计算机视觉）。AI 的发展经历了从规则基础到统计学习再到深度学习等多个阶段，后者模仿人脑的结构来实现更复杂的学习。\n", "\n", "目前 AI 已经在很多领域产生影响，包括：\n", "\n", "*   **语音助手**：像 Siri 和小爱同学这样的应用。\n", "*   **推荐系统**：如 Netflix 或淘宝为你推荐内容。\n", "*   **自动驾驶汽车**：使用感知和决策算法导航。\n", "*   **医疗诊断**：辅助医生分析医学图像或数据。\n", "\n", "尽管 AI 还在不断发展中，并非所有方面都能完全替代人类，但它正以前所未有的速度改变着我们的生活、工作乃至思维方式。\n", "Done: True\n", "Done Reason: stop\n", "Context: [151669, 14880, 43959, 46944, 101888, 104455, 9370, 98237, 99534, 100157, 1773, 151670, 151667, 198, 106287, 3837, 20002, 103945, 46944, 101888, 104455, 9370, 98237, 99534, 100157, 1773, 104544, 2565, 87267, 104104, 99720, 100631, 32664, 99602, 103198, 100623, 3837, 99172, 101098, 99794, 15469, 105166, 101290, 3407, 99487, 1631, 99165, 100692, 3837, 99486, 99880, 101051, 110485, 77288, 100011, 105549, 100032, 113608, 1773, 104689, 99222, 100403, 105535, 104449, 3837, 99887, 101321, 104542, 91282, 33108, 99936, 99892, 100650, 1773, 106350, 2073, 98237, 99534, 854, 99487, 101882, 3837, 104787, 99730, 100359, 18493, 16, 15, 100908, 108341, 3837, 11622, 116336, 86744, 100272, 109824, 104136, 100185, 43815, 3407, 20002, 87267, 99900, 102108, 99487, 100220, 3837, 100631, 85106, 101077, 46944, 105172, 106094, 100643, 1773, 2565, 108876, 104583, 49111, 33108, 100136, 102962, 9370, 3837, 99172, 100350, 99896, 102875, 109093, 99878, 104075, 1773, 99999, 104787, 30534, 101153, 116925, 100537, 111034, 3837, 77288, 105827, 102767, 110487, 26939, 104899, 111076, 3407, 35946, 99730, 60726, 91282, 106582, 15469, 3837, 101889, 66394, 99652, 103949, 100022, 5373, 99936, 99788, 100650, 108800, 105318, 9909, 104050, 102182, 100134, 48272, 100161, 118569, 100204, 110249, 99892, 1773, 99654, 111628, 101929, 2073, 98237, 99534, 97907, 101882, 3837, 107713, 101907, 27369, 9370, 111293, 3407, 105213, 102243, 99878, 105178, 30440, 57553, 33071, 1773, 101912, 104496, 102398, 71356, 13343, 11622, 100405, 113182, 3837, 36587, 102217, 100134, 13343, 104046, 105226, 102182, 100134, 104111, 103799, 104610, 102024, 101290, 1773, 104019, 101153, 102767, 104380, 105302, 112926, 3837, 77288, 105827, 99222, 105874, 3407, 20002, 87267, 105463, 36993, 117976, 33126, 100398, 103936, 3837, 106325, 114903, 73670, 99337, 18947, 103316, 44729, 3837, 51463, 102167, 99553, 110074, 100143, 2565, 9370, 100403, 101964, 8997, 151668, 198, 99692, 37945, 50930, 107083, 101888, 104455, 9370, 98237, 99534, 100157, 48443, 334, 104455, 9909, 15469, 7552, 334, 54851, 104564, 99891, 104111, 103799, 3837, 104717, 50377, 100006, 75117, 102119, 85106, 103971, 100168, 108530, 9370, 102182, 57191, 72448, 1773, 41146, 100185, 100160, 20412, 100048, 20221, 26232, 105717, 101128, 5373, 100134, 5373, 113272, 5373, 108272, 33108, 99795, 102064, 54542, 99788, 100623, 48692, 100168, 3407, 105172, 36587, 3837, 15469, 33424, 102, 104145, 26232, 65101, 17340, 101891, 104107, 33108, 100675, 1773, 43288, 109228, 99258, 102182, 45181, 20074, 15946, 100134, 100144, 90395, 104086, 102041, 9909, 29524, 102182, 100134, 48272, 100631, 101041, 54542, 118119, 27369, 71817, 102450, 9909, 29524, 104564, 104916, 74276, 15469, 43589, 99185, 106014, 45181, 104190, 99896, 26939, 100787, 100134, 107947, 102217, 100134, 108211, 100385, 3837, 107184, 108391, 17340, 99931, 9370, 100166, 36407, 101884, 33126, 102181, 105595, 3407, 100004, 15235, 83002, 110, 53393, 18493, 99555, 100650, 100394, 99564, 3837, 100630, 48443, 9, 256, 3070, 105761, 110498, 334, 5122, 65101, 74974, 58143, 30709, 99242, 101181, 101893, 99892, 8997, 9, 256, 3070, 101914, 72448, 334, 5122, 29524, 22642, 92313, 105052, 106184, 101914, 43815, 8997, 9, 256, 3070, 109044, 100343, 334, 5122, 37029, 108272, 33108, 102041, 107018, 105544, 8997, 9, 256, 3070, 100182, 105262, 334, 5122, 104650, 103998, 101042, 104316, 107553, 57191, 20074, 3407, 104138, 15235, 32181, 246, 18493, 99607, 107320, 90395, 65676, 55338, 99522, 104297, 100372, 105598, 103971, 3837, 111386, 36556, 103982, 31838, 38342, 99996, 101149, 101933, 99164, 103952, 99424, 5373, 99257, 105326, 116903, 1773]\n", "Total Duration: 12060392900\n", "Load Duration: 2062790800\n", "Prompt Eval Count: 12\n", "Prompt Eval Duration: 126183700\n", "Eval Count: 491\n", "Eval Duration: 9870914900\n"]}], "source": ["# 打印每个参数的值\n", "print(\"Model:\", generate_response[\"model\"])\n", "print(\"Created At:\", generate_response[\"created_at\"])\n", "print(\"Response:\", generate_response[\"response\"])\n", "print(\"Done:\", generate_response[\"done\"])\n", "print(\"Done Reason:\", generate_response[\"done_reason\"])\n", "print(\"Context:\", generate_response[\"context\"])\n", "print(\"Total Duration:\", generate_response[\"total_duration\"])\n", "print(\"Load Duration:\", generate_response[\"load_duration\"])\n", "print(\"Prompt Eval Count:\", generate_response[\"prompt_eval_count\"])\n", "print(\"Prompt Eval Duration:\", generate_response[\"prompt_eval_duration\"])\n", "print(\"Eval Count:\", generate_response[\"eval_count\"])\n", "print(\"Eval Duration:\", generate_response[\"eval_duration\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;`<PERSON>lla<PERSON>` 返回的响应中，采用的时间单位均以纳秒返回。纳秒（nanosecond）和秒（second）之间的关系是：<font color=\"red\">1 秒 = 10⁹ 纳秒</font>\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["单次调用总花费时间: 12.0603929\n", "加载模型花费时间: 2.0627908\n", "评估提示所花费的时间: 0.1261837\n", "生成响应的时间: 9.8709149\n"]}], "source": ["# 将纳秒转换为秒\n", "total_duration_s = generate_response[\"total_duration\"] / 1_000_000_000\n", "load_duration_s = generate_response[\"load_duration\"] / 1_000_000_000\n", "prompt_eval_duration_s = generate_response[\"prompt_eval_duration\"] / 1_000_000_000\n", "eval_duration_s = generate_response[\"eval_duration\"] / 1_000_000_000\n", "\n", "# 打印转换后的秒值\n", "print(\"单次调用总花费时间:\", total_duration_s)\n", "print(\"加载模型花费时间:\", load_duration_s)\n", "print(\"评估提示所花费的时间:\", prompt_eval_duration_s)\n", "print(\"生成响应的时间:\", eval_duration_s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. num_ctx / num_predict 输入输出控制"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;`num_ctx` 和 `num_predict`参数都是需要放置在 `options` 参数中的，其中：\n", "\n", "- `num_ctx`该参数指的是大模型在一次对话中能够\"看到\"和\"记住\"的最大上下文长度，默认配置 2048，相当于一次只能向模型输入 2k `token`，超过 2k 模型就无法记住。当 `prompt` 特别长时往往会出现问题。并且现在开源模型往往支持长上下文，默认配置会严重限制本地模型能力。\n", "\n", "- `num_predict` 参数指的是模型响应返回的最大 token 数据量。\n", "\n", "&emsp;&emsp;我们可以这样测试："]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:43:38.3660155Z\",\n", "  \"response\": \"<think>\\n嗯，用户想要一个关于人工智能的\",\n", "  \"done\": true,\n", "  \"done_reason\": \"length\",\n", "  \"context\": [\n", "    151669,\n", "    14880,\n", "    43959,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    151670,\n", "    151667,\n", "    198,\n", "    106287,\n", "    3837,\n", "    20002,\n", "    103945,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370\n", "  ],\n", "  \"total_duration\": 2412666600,\n", "  \"load_duration\": 2065941700,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 138705600,\n", "  \"eval_count\": 10,\n", "  \"eval_duration\": 207512800\n", "}\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"    # 这里需要根据实际情况进行修改\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",   # 这里需要根据实际情况进行修改\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",  # 这里需要根据实际情况进行修改\n", "    \"stream\": False,       # 默认使用的是True，如果设置为False，则返回的是一个完整的响应，而不是一个流式响应\n", "    \"options\": {\n", "        # \"num_ctx\": 7,  慎用，可能会导致Ollama服务不稳定，建议选择 1024 及以上\n", "        \"num_predict\": 10\n", "    }\n", "}\n", "\n", "# 调用生成接口\n", "response_generate = requests.post(generate_url, json=generate_payload)\n", "if response_generate.status_code == 200:\n", "    generate_response = response_generate.json()\n", "    print(\"生成响应:\", json.dumps(generate_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_generate.status_code, response_generate.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. 流式输出功能"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;接下来看流式输出输出，其参数和如上代码保持一致，只需要在 `response_generate` 中添加 `stream=True`，最后再通过流式的方式进行响应结果处理即可。代码如下所示：\n", "\n", "> 这里有一个使用`DeepSeek-R1`的小技巧，将温度即`temperature`设置在0.5-0.7（建议0.6）的范围内，可以有效防止无尽的重复或不连贯的输出。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户想要一个关于人工智能的简短介绍。这个需求看起来很简单直接，但背后可能隐藏着不同的意图。\n", "\n", "用户可能是学生或刚接触AI的人，想快速了解核心概念；也可能是专业人士需要确认基础定义；甚至可能是老师备课时想找概括性内容。从“简短”这个要求来看，ta更倾向于获得清晰简洁的概述而非深度分析。\n", "\n", "考虑到AI话题的专业性和普及度差异很大，我决定采用中等偏易懂的语言风格，在80字左右给出一个结构化回答：先定义AI是什么，再说明它能做什么，最后区分强弱人工智能的概念。这样既保证了信息密度又不会太技术化。\n", "\n", "需要特别注意避免几个常见误区：不能把算法能力简单说成“智能”，要强调是模拟；要明确人类学习和机器学习的本质区别；最后必须提醒这是个持续发展的领域而非完美实现。\n", "</think>\n", "好的，这是一个关于人工智能的简短介绍：\n", "\n", "**人工智能（AI）简介**\n", "\n", "人工智能是一种利用计算机来模仿人的智能的技术科学。它旨在创造能够执行通常需要人类智能的任务的系统，例如：\n", "*   **学习：** 从数据中获取知识和模式。\n", "*   **推理：** 像人一样进行逻辑思考。\n", "*   **问题解决：** 找到复杂问题的解决方案。\n", "*   **感知：** 理解视觉、听觉等感官信息。\n", "\n", "核心方法包括机器学习（让计算机从经验中自动学习）和深度学习（使用多层神经网络模拟人脑）。AI的目标是开发能像人类一样思考、解决问题甚至具有创造力的“强人工智能”，但目前大多应用集中在执行特定任务上，称为“弱人工智能”或“狭义人工智能”。\n", "\n", "完整响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:44:39.3531409Z\",\n", "  \"response\": \"\",\n", "  \"done\": true,\n", "  \"done_reason\": \"stop\",\n", "  \"context\": [\n", "    151669,\n", "    14880,\n", "    43959,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    151670,\n", "    151667,\n", "    198,\n", "    106287,\n", "    3837,\n", "    20002,\n", "    103945,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    99487,\n", "    100354,\n", "    104544,\n", "    106882,\n", "    101041,\n", "    3837,\n", "    77288,\n", "    101423,\n", "    87267,\n", "    71694,\n", "    99164,\n", "    101970,\n", "    111450,\n", "    3407,\n", "    20002,\n", "    104560,\n", "    99720,\n", "    57191,\n", "    99900,\n", "    102108,\n", "    15469,\n", "    100623,\n", "    3837,\n", "    99172,\n", "    101098,\n", "    99794,\n", "    100185,\n", "    101290,\n", "    24968,\n", "    74763,\n", "    104560,\n", "    114516,\n", "    85106,\n", "    81167,\n", "    99896,\n", "    91282,\n", "    24968,\n", "    100636,\n", "    104560,\n", "    101049,\n", "    56278,\n", "    99504,\n", "    13343,\n", "    116405,\n", "    109193,\n", "    33071,\n", "    43815,\n", "    1773,\n", "    45181,\n", "    2073,\n", "    98237,\n", "    99534,\n", "    854,\n", "    99487,\n", "    101882,\n", "    100192,\n", "    3837,\n", "    2565,\n", "    33126,\n", "    114587,\n", "    100350,\n", "    104542,\n", "    110485,\n", "    9370,\n", "    113608,\n", "    109093,\n", "    102217,\n", "    101042,\n", "    3407,\n", "    106350,\n", "    15469,\n", "    105167,\n", "    106289,\n", "    105178,\n", "    105258,\n", "    26381,\n", "    102478,\n", "    101235,\n", "    3837,\n", "    35946,\n", "    103930,\n", "    101910,\n", "    15946,\n", "    49567,\n", "    99835,\n", "    86744,\n", "    100272,\n", "    109824,\n", "    104040,\n", "    96050,\n", "    23,\n", "    15,\n", "    18600,\n", "    101081,\n", "    107485,\n", "    46944,\n", "    100166,\n", "    32108,\n", "    102104,\n", "    5122,\n", "    60726,\n", "    91282,\n", "    15469,\n", "    102021,\n", "    3837,\n", "    87256,\n", "    66394,\n", "    99652,\n", "    26232,\n", "    106428,\n", "    3837,\n", "    100161,\n", "    108568,\n", "    99193,\n", "    99932,\n", "    104455,\n", "    107402,\n", "    1773,\n", "    99654,\n", "    99929,\n", "    101907,\n", "    34187,\n", "    27369,\n", "    106651,\n", "    99518,\n", "    99670,\n", "    99222,\n", "    99361,\n", "    32108,\n", "    3407,\n", "    85106,\n", "    100654,\n", "    60533,\n", "    101153,\n", "    100204,\n", "    101536,\n", "    112172,\n", "    5122,\n", "    53153,\n", "    99360,\n", "    107018,\n", "    99788,\n", "    100405,\n", "    36587,\n", "    12857,\n", "    2073,\n", "    100168,\n", "    33590,\n", "    30534,\n", "    104046,\n", "    20412,\n", "    105717,\n", "    24968,\n", "    30534,\n", "    100692,\n", "    103971,\n", "    100134,\n", "    33108,\n", "    102182,\n", "    100134,\n", "    110907,\n", "    102665,\n", "    24968,\n", "    100161,\n", "    100645,\n", "    104211,\n", "    100346,\n", "    18947,\n", "    100652,\n", "    104174,\n", "    100650,\n", "    109093,\n", "    104448,\n", "    101884,\n", "    8997,\n", "    151668,\n", "    198,\n", "    99692,\n", "    3837,\n", "    105464,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    48443,\n", "    334,\n", "    104455,\n", "    9909,\n", "    15469,\n", "    7552,\n", "    102335,\n", "    56177,\n", "    104455,\n", "    101158,\n", "    100152,\n", "    104564,\n", "    36407,\n", "    108391,\n", "    103947,\n", "    100168,\n", "    105535,\n", "    99891,\n", "    1773,\n", "    99652,\n", "    106166,\n", "    100211,\n", "    100006,\n", "    75117,\n", "    102119,\n", "    85106,\n", "    103971,\n", "    100168,\n", "    108530,\n", "    9370,\n", "    72448,\n", "    3837,\n", "    77557,\n", "    28311,\n", "    9,\n", "    256,\n", "    3070,\n", "    100134,\n", "    5122,\n", "    334,\n", "    220,\n", "    45181,\n", "    20074,\n", "    15946,\n", "    45912,\n", "    100032,\n", "    33108,\n", "    100144,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    113272,\n", "    5122,\n", "    334,\n", "    4891,\n", "    225,\n", "    237,\n", "    17340,\n", "    101891,\n", "    71817,\n", "    104913,\n", "    104107,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    86119,\n", "    100638,\n", "    5122,\n", "    334,\n", "    46750,\n", "    122,\n", "    26939,\n", "    102181,\n", "    86119,\n", "    9370,\n", "    104520,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    108272,\n", "    5122,\n", "    334,\n", "    10236,\n", "    238,\n", "    228,\n", "    49238,\n", "    104916,\n", "    5373,\n", "    49187,\n", "    99353,\n", "    49567,\n", "    118119,\n", "    27369,\n", "    3407,\n", "    100185,\n", "    39907,\n", "    100630,\n", "    102182,\n", "    100134,\n", "    9909,\n", "    99258,\n", "    104564,\n", "    45181,\n", "    100034,\n", "    15946,\n", "    100756,\n", "    100134,\n", "    7552,\n", "    33108,\n", "    102217,\n", "    100134,\n", "    9909,\n", "    37029,\n", "    42140,\n", "    99371,\n", "    102398,\n", "    71356,\n", "    105717,\n", "    17340,\n", "    99931,\n", "    74276,\n", "    15469,\n", "    104820,\n", "    20412,\n", "    100013,\n", "    26232,\n", "    65101,\n", "    103971,\n", "    101891,\n", "    104107,\n", "    5373,\n", "    107124,\n", "    100636,\n", "    100629,\n", "    113065,\n", "    9370,\n", "    2073,\n", "    99193,\n", "    104455,\n", "    33590,\n", "    77288,\n", "    100004,\n", "    100492,\n", "    99892,\n", "    110678,\n", "    75117,\n", "    105149,\n", "    88802,\n", "    17447,\n", "    3837,\n", "    102424,\n", "    2073,\n", "    99932,\n", "    104455,\n", "    854,\n", "    57191,\n", "    2073,\n", "    103465,\n", "    64559,\n", "    104455,\n", "    55807\n", "  ],\n", "  \"total_duration\": 7583291100,\n", "  \"load_duration\": 32551900,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 1542600,\n", "  \"eval_count\": 365,\n", "  \"eval_duration\": 7547692700\n", "}\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",\n", "    \"options\": {\n", "        \"temperature\": 0.6, \n", "    }\n", "}\n", "\n", "# 调用生成接口\n", "response_generate = requests.post(generate_url, json=generate_payload, stream=True)  # 在这里添加stream=True\n", "if response_generate.status_code == 200:\n", "    # 处理流式响应\n", "    for line in response_generate.iter_lines(): \n", "        if line:\n", "            try:\n", "                # 解码并解析每一行的 JSON\n", "                response_json = json.loads(line.decode('utf-8'))\n", "                if 'response' in response_json:\n", "                    print(response_json['response'], end='', flush=True)\n", "\n", "                # 检查 response_json 字典中是否存在键 'done'，并且其值是否为 True。如果这个条件成立，表示生成的响应已经完成。\n", "                if response_json.get('done', False):\n", "                    print('\\n\\n完整响应:', json.dumps(response_json, ensure_ascii=False, indent=2))\n", "            except json.JSONDecodeError as e:\n", "                print(f\"JSON 解析错误: {e}\")\n", "else:\n", "    print(\"生成请求失败:\", response_generate.status_code, response_generate.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5. <PERSON><PERSON><PERSON> 模型生命周期管理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;默认情况下，<font color=\"red\">**通过`Ollama run`启动一个模型后，会将其在VRAM(显存)中保存5分钟**</font>。主要作用是为了做性能优化，通过保持模型在显存中，可以避免频繁的加载和卸载操作，从而提高响应速度，特别是在连续请求的情况下。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- <font color=\"red\">**keep_alive**</font>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;我们可以通过`ollama stop` 命令立即卸载某个模型。而在生成请求中，一种高效的方式是通过`keep_alive`参数来控制模型在请求完成后保持加载在内存中的时间。其可传入的参数规则如下：\n", "\n", "<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>keep_alive 参数类型</font></p>\n", "<div class=\"center\">\n", "\n", "| 参数类型               | 示例         | 描述                                       |\n", "|------------------------|--------------|--------------------------------------------|\n", "| 持续时间字符串         | \"10m\" 或 \"24h\" | 表示保持模型在内存中的时间，单位可以是分钟（m）或小时（h）。 |\n", "| 以秒为单位的数字       | 3600         | 表示保持模型在内存中的时间，单位为秒。   |\n", "| 任何负数               | -1 或 \"-1m\"  | 表示保持模型在内存中，负数值将使模型持续加载。 |\n", "| '0'                    | 0            | 表示在生成响应后立即卸载模型。             |\n", "\n", "</div>\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成响应: {\n", "  \"model\": \"deepseek-r1:8b\",\n", "  \"created_at\": \"2025-08-14T06:47:56.7536266Z\",\n", "  \"response\": \"<think>\\n嗯，用户想要一个关于人工智能的简短介绍。\\n\\n首先需要考虑用户的背景和需求。这可能是一个普通的学生、职场新人或对技术感兴趣的一般读者，他们想了解AI的基本概念、发展现状以及未来潜力。\\n他们不需要深入的技术细节，而是希望获得清晰易懂且全面概述的核心要素。\\n\\n接下来要梳理AI的主要内容。需要涵盖定义、历史、核心领域和应用等关键点。\\n根据我的知识储备，应该从人工智能的广义和狭义两个层面来介绍其定义，再分三个阶段简述发展史：50年代的基础理论时期、21世纪的技术爆发期以及当前的发展趋势。\\n\\n在介绍核心领域时，要选择最相关的部分。可以包括机器学习、深度学习、自然语言处理等主要技术，并强调它们之间的关系。\\n然后讨论AI的应用场景，这些应当覆盖不同行业和日常生活，如医疗诊断、金融风控、智能制造、自动驾驶等领域。\\n\\n最后需要提到挑战与未来展望，以保持内容平衡。要提及伦理问题、偏见、隐私风险以及就业影响，并说明尽管存在问题但仍充满潜力的发展方向。\\n\\n还要注意语言风格，使其简洁流畅且通俗易懂。\\n可以用一些生动的比喻来解释复杂概念，比如将机器学习比作\\\"通过经验成长的孩子\\\"。\\n同时确保内容客观中立，避免过度宣传或消极偏见。\\n\\n整个回复结构要清晰：定义-历史-核心领域-应用案例-挑战展望。这能帮助用户逐步理解AI全貌，并激发他们进一步探索的兴趣。\\n</think>\\n好的，这是一个关于人工智能的简短介绍：\\n\\n**什么是人工智能？**\\n\\n人工智能（Artificial Intelligence, 简称 AI）是指由人制造出来的智能系统或技术，这些系统能够模拟人类的学习、推理、感知和决策能力。其目标是创建能够在复杂环境中执行通常需要人类智能的任务的机器。\\n\\n**发展与现状：**\\n\\nAI 的研究始于20世纪50年代，并经历了起伏（所谓的“AI之冬”）。近年来，随着大数据、强大的计算能力和算法（尤其是深度学习）的发展，AI 迎来了一个飞速发展的新阶段。现在我们看到的是 AI 技术的全面爆发期。\\n\\n**核心领域：**\\n\\n* **机器学习 (Machine Learning)：** 让计算机从数据中学习模式，并做出预测或决策，而无需显式编程所有规则。\\n*   **深度学习 (Deep Learning)：** 机器学习的一个子集，模仿人脑结构（人工神经网络），特别擅长处理图像、语音和文本等复杂数据。\\n*   **自然语言处理 (Natural Language Processing, NLP)：** 让计算机理解、解释和生成人类语言。\\n\\n**应用与潜力：**\\n\\nAI 已经渗透到我们生活的方方面面。它被用于：\\n\\n* 提供个性化推荐（如购物、视频）\\n* 自动驾驶汽车\\n* 医疗诊断辅助\\n* 金融欺诈检测\\n* 智能客服机器人\\n* 创造更智能的搜索引擎和翻译工具\\n\\n**挑战与展望：**\\n\\n尽管 AI 取得了巨大进展，但也面临着伦理问题（如偏见、隐私）、技术瓶颈以及如何确保公平性和透明度等挑战。未来，AI 有望继续推动各行各业的变革，并解决一些人类面临的复杂难题。\\n\\n总而言之，人工智能是一门致力于创造能像人一样思考和行动的智能体的技术科学，它正在以前所未有的速度发展并深刻地改变着我们的世界。\",\n", "  \"done\": true,\n", "  \"done_reason\": \"stop\",\n", "  \"context\": [\n", "    151669,\n", "    14880,\n", "    43959,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    1773,\n", "    151670,\n", "    151667,\n", "    198,\n", "    106287,\n", "    3837,\n", "    20002,\n", "    103945,\n", "    46944,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    3407,\n", "    101140,\n", "    85106,\n", "    101118,\n", "    107494,\n", "    102193,\n", "    33108,\n", "    100354,\n", "    1773,\n", "    43288,\n", "    87267,\n", "    101909,\n", "    100714,\n", "    105135,\n", "    5373,\n", "    107546,\n", "    102990,\n", "    57191,\n", "    32664,\n", "    99361,\n", "    103198,\n", "    99774,\n", "    99791,\n", "    104785,\n", "    3837,\n", "    99650,\n", "    99172,\n", "    99794,\n", "    15469,\n", "    105166,\n", "    101290,\n", "    5373,\n", "    99185,\n", "    105044,\n", "    101034,\n", "    100353,\n", "    102575,\n", "    8997,\n", "    99650,\n", "    104689,\n", "    100403,\n", "    105535,\n", "    104449,\n", "    3837,\n", "    103955,\n", "    99880,\n", "    100350,\n", "    104542,\n", "    86744,\n", "    100272,\n", "    100136,\n", "    100011,\n", "    113608,\n", "    104867,\n", "    105017,\n", "    3407,\n", "    104326,\n", "    30534,\n", "    106490,\n", "    15469,\n", "    104396,\n", "    43815,\n", "    1773,\n", "    85106,\n", "    102994,\n", "    91282,\n", "    5373,\n", "    100022,\n", "    5373,\n", "    100185,\n", "    100650,\n", "    33108,\n", "    99892,\n", "    49567,\n", "    99936,\n", "    27442,\n", "    8997,\n", "    100345,\n", "    97611,\n", "    100032,\n", "    105422,\n", "    3837,\n", "    99730,\n", "    45181,\n", "    104455,\n", "    9370,\n", "    80942,\n", "    64559,\n", "    33108,\n", "    103465,\n", "    64559,\n", "    100369,\n", "    104873,\n", "    36407,\n", "    100157,\n", "    41146,\n", "    91282,\n", "    3837,\n", "    87256,\n", "    17177,\n", "    101124,\n", "    100385,\n", "    98237,\n", "    41932,\n", "    99185,\n", "    99497,\n", "    5122,\n", "    20,\n", "    15,\n", "    104227,\n", "    105549,\n", "    101911,\n", "    100728,\n", "    5373,\n", "    17,\n", "    16,\n", "    101186,\n", "    105535,\n", "    104716,\n", "    22704,\n", "    101034,\n", "    67949,\n", "    103949,\n", "    101226,\n", "    3407,\n", "    18493,\n", "    100157,\n", "    100185,\n", "    100650,\n", "    13343,\n", "    3837,\n", "    30534,\n", "    50404,\n", "    31235,\n", "    105470,\n", "    99659,\n", "    1773,\n", "    73670,\n", "    100630,\n", "    102182,\n", "    100134,\n", "    5373,\n", "    102217,\n", "    100134,\n", "    5373,\n", "    99795,\n", "    102064,\n", "    54542,\n", "    49567,\n", "    99558,\n", "    99361,\n", "    90395,\n", "    104046,\n", "    104017,\n", "    104186,\n", "    100145,\n", "    8997,\n", "    101889,\n", "    104075,\n", "    15469,\n", "    106736,\n", "    102122,\n", "    3837,\n", "    100001,\n", "    104024,\n", "    100795,\n", "    99604,\n", "    99717,\n", "    33108,\n", "    106480,\n", "    3837,\n", "    29524,\n", "    100182,\n", "    105262,\n", "    5373,\n", "    100015,\n", "    114248,\n", "    5373,\n", "    108638,\n", "    5373,\n", "    109044,\n", "    106483,\n", "    3407,\n", "    100161,\n", "    85106,\n", "    104496,\n", "    104036,\n", "    57218,\n", "    100353,\n", "    109789,\n", "    3837,\n", "    23031,\n", "    100662,\n", "    43815,\n", "    102243,\n", "    1773,\n", "    30534,\n", "    109361,\n", "    112811,\n", "    86119,\n", "    5373,\n", "    99835,\n", "    88970,\n", "    5373,\n", "    107120,\n", "    101052,\n", "    101034,\n", "    101967,\n", "    99564,\n", "    90395,\n", "    66394,\n", "    104138,\n", "    115077,\n", "    115873,\n", "    102216,\n", "    102575,\n", "    103949,\n", "    100696,\n", "    3407,\n", "    104019,\n", "    60533,\n", "    102064,\n", "    104040,\n", "    3837,\n", "    102989,\n", "    110485,\n", "    110205,\n", "    100136,\n", "    116336,\n", "    86744,\n", "    100272,\n", "    8997,\n", "    106782,\n", "    101883,\n", "    106267,\n", "    9370,\n", "    113182,\n", "    36407,\n", "    104136,\n", "    102181,\n", "    101290,\n", "    3837,\n", "    101912,\n", "    44063,\n", "    102182,\n", "    100134,\n", "    56006,\n", "    19403,\n", "    1,\n", "    67338,\n", "    100034,\n", "    100423,\n", "    104545,\n", "    1,\n", "    8997,\n", "    91572,\n", "    103944,\n", "    43815,\n", "    105310,\n", "    15946,\n", "    79095,\n", "    3837,\n", "    101153,\n", "    105831,\n", "    100375,\n", "    57191,\n", "    114217,\n", "    99835,\n", "    88970,\n", "    3407,\n", "    101908,\n", "    104787,\n", "    100166,\n", "    30534,\n", "    104542,\n", "    5122,\n", "    91282,\n", "    12,\n", "    100022,\n", "    12,\n", "    100185,\n", "    100650,\n", "    12,\n", "    99892,\n", "    102206,\n", "    12,\n", "    104036,\n", "    109789,\n", "    1773,\n", "    43288,\n", "    26232,\n", "    100364,\n", "    20002,\n", "    104137,\n", "    101128,\n", "    15469,\n", "    35987,\n", "    99995,\n", "    90395,\n", "    104970,\n", "    99650,\n", "    100642,\n", "    101964,\n", "    110257,\n", "    8997,\n", "    151668,\n", "    198,\n", "    99692,\n", "    3837,\n", "    105464,\n", "    101888,\n", "    104455,\n", "    9370,\n", "    98237,\n", "    99534,\n", "    100157,\n", "    48443,\n", "    334,\n", "    106582,\n", "    104455,\n", "    11319,\n", "    56177,\n", "    104455,\n", "    9909,\n", "    9286,\n", "    16488,\n", "    21392,\n", "    11,\n", "    10236,\n", "    106,\n", "    222,\n", "    24641,\n", "    15235,\n", "    7552,\n", "    104442,\n", "    67071,\n", "    17340,\n", "    100184,\n", "    104355,\n", "    100168,\n", "    72448,\n", "    57191,\n", "    99361,\n", "    3837,\n", "    100001,\n", "    72448,\n", "    100006,\n", "    105717,\n", "    103971,\n", "    105595,\n", "    5373,\n", "    113272,\n", "    5373,\n", "    108272,\n", "    33108,\n", "    102041,\n", "    99788,\n", "    1773,\n", "    41146,\n", "    100160,\n", "    20412,\n", "    50377,\n", "    109608,\n", "    102181,\n", "    109130,\n", "    75117,\n", "    102119,\n", "    85106,\n", "    103971,\n", "    100168,\n", "    108530,\n", "    9370,\n", "    102182,\n", "    3407,\n", "    334,\n", "    99185,\n", "    57218,\n", "    105044,\n", "    5122,\n", "    56177,\n", "    15469,\n", "    43589,\n", "    99556,\n", "    114984,\n", "    17,\n", "    15,\n", "    101186,\n", "    20,\n", "    15,\n", "    104227,\n", "    90395,\n", "    106014,\n", "    111375,\n", "    9909,\n", "    105381,\n", "    2073,\n", "    15469,\n", "    53930,\n", "    99949,\n", "    854,\n", "    74276,\n", "    104255,\n", "    3837,\n", "    101067,\n", "    104315,\n", "    5373,\n", "    104795,\n", "    100768,\n", "    106712,\n", "    107018,\n", "    9909,\n", "    104033,\n", "    102217,\n", "    100134,\n", "    7552,\n", "    103949,\n", "    3837,\n", "    15469,\n", "    32181,\n", "    236,\n", "    101161,\n", "    46944,\n", "    99723,\n", "    94299,\n", "    104174,\n", "    16628,\n", "    100385,\n", "    1773,\n", "    99601,\n", "    97639,\n", "    101038,\n", "    100146,\n", "    15235,\n", "    96155,\n", "    222,\n", "    99216,\n", "    9370,\n", "    100011,\n", "    104716,\n", "    22704,\n", "    3407,\n", "    334,\n", "    100185,\n", "    100650,\n", "    5122,\n", "    56177,\n", "    9,\n", "    3070,\n", "    102182,\n", "    100134,\n", "    320,\n", "    21605,\n", "    20909,\n", "    8,\n", "    5122,\n", "    334,\n", "    33424,\n", "    102,\n", "    104564,\n", "    45181,\n", "    20074,\n", "    15946,\n", "    100134,\n", "    100144,\n", "    90395,\n", "    104086,\n", "    104538,\n", "    57191,\n", "    102041,\n", "    3837,\n", "    68536,\n", "    106431,\n", "    99580,\n", "    28330,\n", "    110569,\n", "    55338,\n", "    104190,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    102217,\n", "    100134,\n", "    320,\n", "    33464,\n", "    20909,\n", "    8,\n", "    5122,\n", "    334,\n", "    220,\n", "    102182,\n", "    100134,\n", "    104111,\n", "    44729,\n", "    42067,\n", "    3837,\n", "    108391,\n", "    17340,\n", "    99931,\n", "    100166,\n", "    9909,\n", "    102249,\n", "    102398,\n", "    71356,\n", "    48272,\n", "    100654,\n", "    107618,\n", "    54542,\n", "    107553,\n", "    5373,\n", "    105761,\n", "    33108,\n", "    108704,\n", "    49567,\n", "    102181,\n", "    20074,\n", "    8997,\n", "    9,\n", "    256,\n", "    3070,\n", "    99795,\n", "    102064,\n", "    54542,\n", "    320,\n", "    54281,\n", "    11434,\n", "    28125,\n", "    11,\n", "    451,\n", "    12567,\n", "    8,\n", "    5122,\n", "    334,\n", "    33424,\n", "    102,\n", "    104564,\n", "    101128,\n", "    5373,\n", "    104136,\n", "    33108,\n", "    43959,\n", "    103971,\n", "    102064,\n", "    3407,\n", "    334,\n", "    99892,\n", "    57218,\n", "    102575,\n", "    5122,\n", "    56177,\n", "    15469,\n", "    83002,\n", "    110,\n", "    53393,\n", "    106694,\n", "    26939,\n", "    97639,\n", "    105301,\n", "    116721,\n", "    1773,\n", "    99652,\n", "    99250,\n", "    100751,\n", "    48443,\n", "    9,\n", "    93685,\n", "    83744,\n", "    106412,\n", "    101914,\n", "    9909,\n", "    29524,\n", "    102297,\n", "    5373,\n", "    87140,\n", "    23083,\n", "    9,\n", "    61991,\n", "    27733,\n", "    100507,\n", "    100343,\n", "    198,\n", "    9,\n", "    94305,\n", "    119,\n", "    99527,\n", "    105262,\n", "    104650,\n", "    198,\n", "    9,\n", "    220,\n", "    100015,\n", "    112894,\n", "    101978,\n", "    198,\n", "    9,\n", "    6567,\n", "    247,\n", "    118,\n", "    26232,\n", "    105041,\n", "    104354,\n", "    198,\n", "    9,\n", "    19468,\n", "    249,\n", "    66078,\n", "    33126,\n", "    100168,\n", "    9370,\n", "    109678,\n", "    33108,\n", "    105395,\n", "    102011,\n", "    271,\n", "    334,\n", "    104036,\n", "    57218,\n", "    109789,\n", "    5122,\n", "    56177,\n", "    104138,\n", "    15235,\n", "    26853,\n", "    244,\n", "    107307,\n", "    102334,\n", "    102442,\n", "    3837,\n", "    106884,\n", "    109187,\n", "    112811,\n", "    86119,\n", "    9909,\n", "    29524,\n", "    99835,\n", "    88970,\n", "    5373,\n", "    107120,\n", "    64359,\n", "    99361,\n", "    109212,\n", "    101034,\n", "    100007,\n", "    103944,\n", "    102511,\n", "    105178,\n", "    105279,\n", "    26381,\n", "    49567,\n", "    104036,\n", "    1773,\n", "    100353,\n", "    3837,\n", "    15469,\n", "    220,\n", "    105295,\n", "    100640,\n", "    101890,\n", "    115181,\n", "    9370,\n", "    105316,\n", "    90395,\n", "    100638,\n", "    101883,\n", "    103971,\n", "    106791,\n", "    102181,\n", "    104768,\n", "    3407,\n", "    117607,\n", "    3837,\n", "    104455,\n", "    99639,\n", "    64689,\n", "    104717,\n", "    100211,\n", "    26232,\n", "    65101,\n", "    17340,\n", "    101891,\n", "    104107,\n", "    33108,\n", "    100675,\n", "    9370,\n", "    100168,\n", "    31914,\n", "    105535,\n", "    99891,\n", "    3837,\n", "    99652,\n", "    96555,\n", "    103982,\n", "    31838,\n", "    38342,\n", "    99996,\n", "    101149,\n", "    99185,\n", "    62926,\n", "    101295,\n", "    29490,\n", "    101933,\n", "    99164,\n", "    103952,\n", "    99489,\n", "    1773\n", "  ],\n", "  \"total_duration\": 14783261000,\n", "  \"load_duration\": 35118100,\n", "  \"prompt_eval_count\": 12,\n", "  \"prompt_eval_duration\": 3750400,\n", "  \"eval_count\": 720,\n", "  \"eval_duration\": 14743814900\n", "}\n", "Tokens per second: 48.83403684076365\n"]}], "source": ["import requests # type: ignore\n", "import json\n", "\n", "# 设置 API 端点\n", "generate_url = \"http://192.168.0.130:11434/api/generate\"\n", "\n", "# 示例数据\n", "generate_payload = {\n", "    \"model\": \"deepseek-r1:8b\",\n", "    \"prompt\": \"请生成一个关于人工智能的简短介绍。\",\n", "    \"stream\": <PERSON><PERSON><PERSON>,\n", "    \"keep_alive\": \"10m\",   # 设置模型在请求后保持加载的时间\n", "    \"options\": {\n", "        \"temperature\": 0.6,\n", "    }\n", "}\n", "\n", "# 调用生成接口\n", "response_generate = requests.post(generate_url, json=generate_payload)\n", "if response_generate.status_code == 200:\n", "    generate_response = response_generate.json()\n", "    print(\"生成响应:\", json.dumps(generate_response, ensure_ascii=False, indent=2))\n", "else:\n", "    print(\"生成请求失败:\", response_generate.status_code, response_generate.text)\n", "\n", "\n", "\n", "if generate_response[\"eval_duration\"] != 0:\n", "    tokens_per_second = generate_response[\"eval_count\"] / generate_response[\"eval_duration\"] * 10**9\n", "    print(f\"Tokens per second: {tokens_per_second}\")\n", "else:\n", "    print(\"eval_duration is zero, cannot calculate tokens per second.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;此时就可以在服务器控制台查看到，`deepseek-r1:32b`模型将可以在显存中保持10分钟。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div align=center><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202502131907776.png\" width=100%></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;`keep_alive` 在工程化的项目中，往往需要根据请求的频率来设置，如果请求不频繁，可以使用默认值或较短的时间，以便在不使用时释放内存。而如果应用程序需要频繁调用模型，可以设置较长的 `keep_alive` 时间，以减少加载时间。很关键，非常影响服务器的性能和应用程序的用户体验。大家一定要注意。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;接下来我们进入下一个课件中了解 `/api/chat` 接口。\n"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}