{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <center>Deepseek企业级Agent项目开发实战</center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <center>Part 4. Ollama REST API - OpenAI  Compatibility</center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;本节内容我们来看一下 `OpenAI Compatibility`。 `OpenAI` 的 `API` 接口是大模型应用开发中最常用、且集成度最高的 `API` 接口规范，其兼容接口主要包括：\n", "\n", "- `chat/completions`\n", "- `completions`\n", "- `models`\n", "- `embeddings`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;我们上两节课程内容中介绍的`/api/generate` 和 `/api/chat` 接口，其实就是 `Ollama` 兼容 `OpenAI` 的 `REST API` 接口的底层实现。其中:\n", "\n", "- `/api/generate` 接口对应 `OpenAI` 的 `completions` 接口；\n", "- `/api/chat` 接口对应 `OpenAI` 的 `chat/completions` 接口；\n", "\n", "&emsp;&emsp;因此我们现在再来看`ollama` 中的`OpenAI compatibility` 的 `API` 接口调用，就非常容易理解了。\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- **非流式调用**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户问了一个关于人工智能的定义问题。看起来是个非常基础的概念性提问，可能是第一次接触AI想了解本质，或者准备写科普文章需要简明解释。\n", "\n", "用户的用词很简洁直接，“什么是”这种开放式问题往往来自知识储备有限但求知欲强的人。ta可能希望获得一个清晰、全面又不晦涩的入门级概述，最好能包含技术背景和核心概念的区别。考虑到“请介绍一下”的措辞，回答应该保持客观中立，避免太口语化。\n", "\n", "这个领域涉及面很广啊……需要从五个维度来拆解：历史发展、基本运作原理、分类架构、典型应用和技术争议。用户可能不知道术语体系存在，在举例时要区分强人工智能和弱AI的微妙区别——否则容易传播误解。\n", "\n", "先说明人类认知过程作为对比基准挺合适，这样AI实现方式的不同就能更清晰地呈现出来。但要注意控制技术用语的比例，像“神经网络”这种词不能光扔结论，得简单带出原理才能达到知识输送的效果。\n", "\n", "啊突然想到要补充学习资源建议！考虑到用户可能接下来会追问具体方向，在结尾可以留个钩子提供延伸帮助。不过第一次回答还是要把核心内容讲透——毕竟连ChatGPT自己都知道很多新手需要基础解析呢。\n", "</think>\n", "你好呀！😊 很高兴为你介绍人工智能（Artificial Intelligence，简称AI）。\n", "\n", "**人工智能是什么？**\n", "\n", "简单来说，人工智能就是让计算机或机器系统模仿人类的智能行为，并执行通常被认为只有人才能完成的任务。比如：理解语言、识别图像、解决问题、学习等。严格一点说，它是一个复杂的跨学科领域，涉及数学、工程学和认知科学等，目的是创造能够以类似于人的方式感知、思考并相互互动的机器。\n", "\n", "### 人工智能的核心目标有三个：\n", "\n", "1. **自动化**：让机器代替人执行特定任务（如数据分析）。\n", "2. **增强能力**：帮助人类更高效地完成某些工作（比如医疗诊断辅助或语言翻译）。\n", "3. **通用型智能**：研发出一种能够像人类一样处理各种问题的AI系统，这也是目前研究的重点和难点之一。\n", "\n", "---\n", "\n", "### 智能有哪些表现方式？\n", "\n", "人工智能可以分为许多不同的技术类型：\n", "\n", "1. **机器学习（ML）**  \n", "   这是 AI 的核心组成部分。通过数据训练让机器不断进步，无需人类明确编程每一细节。比如你教一个程序从海量图片中识别“猫”，它在不给明确定义的情况下自动学会这个任务。\n", "\n", "2. **深度学习（Deep Learning）**  \n", "   属于机器学习的子集，利用多层神经网络模仿人脑结构处理复杂任务：图像识别、语音分析等等——你现在正在跟我聊天的就是基于类似模型训练实现的！\n", "\n", "3. **自然语言处理（NLP）**\n", "   这让 AI 能理解并回应人类的语言。我就是 NLP 的一个成果，可以听懂和回答你对我的“说话”。\n", "\n", "4. **专家系统**  \n", "   早期人工智能的一种形式——在特定领域内模拟权威专家进行决策。\n", "\n", "5. **机器人技术（Robotics）**\n", "   结合机械臂、运动控制等让机器具有实际的行动力。\n", "\n", "---\n", "\n", "### AI 分为哪几类？\n", "\n", "按能力和功能来看，通常分为以下三类：\n", "\n", "1. **弱人工智能（Narrow AI）**  \n", "   只能完成特定任务的人工智能。如 Sir<PERSON>、<PERSON><PERSON>、语音识别软件或图像分析系统等都属于这一类。\n", "\n", "2. **强人工智能（Artificial General Intelligence, AGI）**\n", "   理论上的目标：能够像人类一样胜任广泛认知任务的 AI，目前还没完全实现。\n", "\n", "3. **超级人工智能（Superintelligence）**\n", "   推测这种AI智力远超于人类，甚至拥有自我超越、创新的能力。这是非常遥远或理论上可能出现的新阶段了～\n", "\n", "---\n", "\n", "### 举个栗子🌰：\n", "\n", "- 如果你在用导航App找路，它通过**算法**优化路线。\n", "- 如果你用了推荐系统（比如视频网站的“为你推荐”），背后是 **机器学习** 模式在判断你的喜好。\n", "- 当AI可以自己写作、画画或写代码时，这算是较高级的深度学习应用。\n", "\n", "希望这个介绍能帮你对 AI 有个基本了解！如果你感兴趣，我可以再深入讲解它的历史发展、主要技术路线或者未来应用场景～✨\n", "\n", "要不要了解更多细节呢？\n"]}], "source": ["from openai import OpenAI # type: ignore\n", "\n", "client = OpenAI(\n", "    base_url='http://*************:11434/v1/',     # 这里修改成可访问的 IP\n", "    api_key='ollama',   # 这里随便写，但是api_key字段一定要有\n", ")\n", "\n", "chat_completion = client.chat.completions.create(\n", "    model='deepseek-r1:8b',\n", "    messages=[\n", "        {\n", "            'role': 'user',\n", "            'content': '你好，请你介绍一下什么是人工智能？',\n", "        }\n", "    ],\n", ")\n", "\n", "print(chat_completion.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- **流式调用**\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户问了一个基础但宏大的问题——“什么是人工智能？”这可能是第一次接触AI概念的朋友发出的提问。\n", "\n", "从语气看，ta用了简洁直接的口语化表达（例如用“你”而不是“您”，感叹号比句号少），应该希望获得通俗易懂但又不失准确性的科普。可能是个对科技感兴趣的普通人，或者学生需要完成作业，甚至可能是家长想了解孩子学的知识范畴。\n", "\n", "这类问题虽然表面简单，其实包含几个层次：首先需要定义AI的边界（是工具还是科学？），然后解释核心方法论（机器学习/深度学习的区别），最后要说明实际意义而不仅是技术描述。如果直接说“人工智障”就太轻率了，毕竟用户可能是带着真诚求知欲来的。\n", "\n", "我记得自己2017年开始系统接触AI知识，当时能接受到的公共科普还相对简朴……现在应该用更形象的比喻吧？比如把人类和机器学习的区别比作“照镜子vs烧制瓷器”。\n", "\n", "需要避免专业术语堆砌，但也不能过度简化。用户问的是“介绍”，说明想要了解框架而非细节；但如果完全回避数学概念又显得不真诚。折中方案是先建立直观认知（智能能力展示），再带出技术支撑（机器学习-深度学习体系），最后提及其应用价值——这样既完整又不会太沉重。\n", "\n", "啊，突然想到应该区分强AI/弱AI吗？不过对新手来说这个概念可能过早了……还是放在扩展阅读里比较合适。重点应该是让ta理解“现在用到的人工智能”是怎么回事。\n", "</think>\n", "你好呀！人工智能（Artificial Intelligence），简称**AI**，是研究、开发用于模拟人类智能的理论、方法和技术的一门科学，并且制作具有类似人类智能的能感知或认知能力的软件和机器系统。通俗地说，人工智能的目的就是创造出可以像人一样思考、学习甚至理解事物并做出智能决策的计算机程序。\n", "\n", "### 人工智能的核心特点包括：\n", "1. **数据分析能力强**\n", "2. **自主学习与改进**\n", "3. **模拟人类思维**\n", "\n", "人工智能的发展大致经历了以下几个阶段：\n", "\n", "#### 1.符号主义派（Symbolic AI）\n", "这是早期的人工智能，主要依赖可读、逻辑性的“符号”来模拟推理过程。典型的代表有专家系统、定理证明等。\n", "\n", "#### 2.连接主义派（Connectionism）\n", "起始于神经网络的研究，认为人类的思维是受到大脑生物结构启发而产生的，可以通过模仿人脑的神经元连接机制来进行学习和识别。\n", "\n", "#### 3.机器学习**\n", "这是人工智能的一个重要分支。传统的AI更像是一个“编程解题”的过程，而机器学习更强调训练模型来认识模式、发现规律，并自主作出预测或决策。\n", "\n", "#### 4.深度学习**\n", "是近几年最为热门的领域，基于多层神经网络（称为深度神经网络），能够有效地分析和处理复杂的图像、语音等数据。它被广泛应用于图像识别、自然语言生成等任务中。\n", "\n", "### AI 的主要类型：\n", "\n", "1. **弱人工智能**（Narrow AI）：专注于单一功能或某些特定任务的人工智能，比如会说情感话语的ChatGPT、会识人脸的软件系统。\n", "2. **强/通用人工智能**（AGI）：能够像人类一样完成各种复杂的工作，并进行自主推理和创新的艺术与科学还在探索之中。\n", "\n", "### AI 的主要应用领域：\n", "\n", "- 语音识别与合成\n", "- 图像处理及理解\n", "- 自然语言处理（包括机器翻译、语义理解问答等）\n", "- 智能推荐系统\n", "- 医学影像辅助分析\n", "- 自动驾驶技术\n", "\n", "这些进步让AI逐渐渗透到我们生活的方方面面，从智能手机里的语音助手如Siri和小爱同学，到互联网内容推荐，再到工业自动化生产线以及医疗健康领域的应用。\n", "\n", "如果你对人工智能的某个具体方面感兴趣（比如机器学习、大数据分析等等），我们可以进一步深入探讨。不知道你是否想了解其中一个方向呢？"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    base_url='http://*************:11434/v1/',\n", "    api_key='ollama',\n", ")\n", "\n", "messages = [\n", "    {\n", "        'role': 'user',\n", "        'content': '你好，请你介绍一下什么是人工智能？',\n", "    }\n", "]\n", "\n", "try:\n", "    # 调用聊天接口\n", "    stream = client.chat.completions.create(\n", "        model='deepseek-r1:8b',\n", "        messages=messages,\n", "        stream=True\n", "    )\n", "    \n", "    # 处理流式响应\n", "    for chunk in stream:\n", "        if chunk.choices[0].delta.content is not None:\n", "            print(chunk.choices[0].delta.content, end='', flush=True)\n", "            \n", "except Exception as e:\n", "    print(f\"发生错误: {str(e)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["&emsp;&emsp;在`OpenAI Compatibility` 规范下，目前`Ollama` 支持的模型参数如下：\n", "\n", "<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>支持的功能</font></p>\n", "<div class=\"center\">\n", "\n", "| 功能                     | 描述               |\n", "|--------------------------|--------------------|\n", "| 聊天完成                 | Chat completions    |\n", "| 流媒体                   | Streaming           |\n", "| JSON模式                 | JSON mode           |\n", "| 可再现的输出             | Reproducible outputs |\n", "| 视觉                     | Vision              |\n", "| 工具                     | Tools               |\n", "\n", "</div>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>支持的请求字段</font></p>\n", "<div class=\"center\">\n", "\n", "| 请求字段                | 描述                     |\n", "|-------------------------|--------------------------|\n", "| model                   | 模型                     |\n", "| messages                | 消息                     |\n", "| frequency_penalty       | 频率惩罚                 |\n", "| presence_penalty        | 存在惩罚                 |\n", "| response_format         | 响应格式                 |\n", "| seed                    | 种子                     |\n", "| stop                    | 停止                     |\n", "| stream                  | 流式输出                 |\n", "| stream_options          | 流式选项                 |\n", "| include_usage           | 包含使用情况             |\n", "| temperature             | 温度                     |\n", "| top_p                  | Top-p 采样              |\n", "| max_tokens              | 最大令牌数               |\n", "| tools                   | 工具                     |\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "嗯，用户问的是“什么是人工智能”，这是一个非常基础但重要的问题。可能ta刚接触这个概念，想了解最核心的定义和应用。\n", "\n", "用户用的是礼貌的问候语开头，语气比较正式友好，没有特别紧急或情绪化的表达，应该是个普通的学习者或好奇的技术爱好者。这种情况下需要给出清晰易懂又全面的回答。\n", "\n", "人工智能确实是个宽泛的概念，从1956年达特茅斯会议提出到现在已经发展了半个多世纪。回答时要兼顾历史背景和现代应用，但避免过于学术化。用户可能更想知道人工智做什么，而不是背后的数学原理。\n", "\n", "先简单定义AI，然后分几个层次展开：通用人工智能、机器学习、深度学习这些概念都要提到，因为它们构成了AI的基础分类。再补充些具体应用领域帮助理解，最后加上发展里程碑和挑战会让回答更有说服力。\n", "\n", "特别要注意的是，不能把AI说得像科幻电影那样神奇，要客观描述它的能力和局限性。用户如果被误导以为AI无所不能可能会失望，但完全否定又会失去兴趣。平衡点在于强调当前技术的边界同时展示其潜力。\n", "\n", "啊对了，还要举些接地气的例子，比如淘宝推荐、手机人脸识别这些日常应用，这样抽象的概念就变得具体可感了。最后留个开放性问题，看用户是否想深入某个方向。\n", "</think>\n", "你好！人工智能（Artificial Intelligence），简称AI，是计算机科学的一个分支，它致力于研究和开发能够模拟人类智能的系统或技术，使机器能够执行通常需要人类智慧的任务。\n", "\n", "按照目前的发展阶段来看，人工智能可以分为以下几个层次：\n", "\n", "1. **弱人工智能（Narrow AI）**：专门设计用来完成特定任务的人工智能系统。它们在某些方面可能接近甚至超越人类的能力，但无法像通用人工智能那样胜任广泛多样的任务。例如：\n", "   - 图像识别\n", "   - 语音识别\n", "   - 自然语言处理\n", "\n", "2. **强/通用人工智能（AGI）**：可以执行任何智力任务的人工智能系统，就像人类一样拥有广泛的知识和适应性能力。\n", "\n", "3. **机器学习（Machine Learning）**：是实现人工智能的一种方法。它让计算机能够在没有明确编程的情况下从数据中学习并进行预测或决策。\n", "\n", "4. **深度学习（Deep Learning）**：使用多层神经网络对复杂模式进行学习，目前被认为是实现强AI的重要途径之一。\n", "\n", "### AI的应用领域\n", "\n", "- **自然语言处理 (NLP)** ：如语音助手、聊天机器人等。\n", "- **计算机视觉** ：图像识别、视频分析等。\n", "- **专家系统** ：模拟特定领域的专业知识和判断力。\n", "- **自动驾驶汽车**\n", "- **游戏人工智能**\n", "\n", "### 发展里程碑\n", "\n", "- 1956年，达特茅斯会议首次提出“人工智能”概念\n", "- 近年来，随着大数据、云计算的兴起以及深度学习模型的发展，AI进入了快速发展期。\n", "\n", "尽管人工智能技术已经取得了显著进展，但目前的人工智能系统仍存在局限性，并不具备人类在各种复杂环境中灵活应对的能力。然而，在许多特定领域中，它们已经开始发挥重要作用。\n", "\n", "请问你对某个具体类型的人工智能感兴趣吗？我可以进一步为你介绍相关的细节和应用案例。"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    base_url='http://*************:11434/v1/',\n", "    api_key='ollama',\n", ")\n", "\n", "messages = [\n", "    {\n", "        'role': 'user',\n", "        'content': '你好，请你介绍一下什么是人工智能？',\n", "    }\n", "]\n", "\n", "try:\n", "    # 调用聊天接口\n", "    stream = client.chat.completions.create(\n", "        model='deepseek-r1:8b',\n", "        messages=messages,\n", "        stream=True,\n", "        temperature=0.7,\n", "        max_tokens=1024,\n", "    )\n", "    \n", "    # 处理流式响应\n", "    for chunk in stream:\n", "        if chunk.choices[0].delta.content is not None:\n", "            print(chunk.choices[0].delta.content, end='', flush=True)\n", "            \n", "except Exception as e:\n", "    print(f\"发生错误: {str(e)}\")"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}