from operator import itemgetter
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableParallel, RunnablePassthrough, RunnableMap, RunnableLambda  # 完整导入
from langchain_ollama import ChatOllama

# RunnablePassthrough：传递并扩展上下文
# RunnableLambda：自定义处理逻辑
# RunnableMap：并行执行多个独立链

# --- 1. 初始化模型和解析器 ---
llm = ChatOllama(model="qwen2.5:7b", temperature=0.7)  # 适当随机性，适合生成任务
output_parser = StrOutputParser()


# --- 2. 定义提示模板（明确变量和输出格式） ---
prompt1 = ChatPromptTemplate.from_template(
    "生成一个随机颜色（仅返回颜色名称，如红色、蓝色，不要任何解释或标点）。"
)
prompt2 = ChatPromptTemplate.from_template(
    "什么水果是{color}颜色的？（仅返回一个水果名称，不要解释或标点）"
)
prompt3 = ChatPromptTemplate.from_template(
    "水果{fruit}主要产自哪个国家？（仅返回国家名称，不要解释或标点）"
)
prompt4 = ChatPromptTemplate.from_template(
    "严格按照格式“{color}颜色的水果{fruit}来自{country}。”输出，不添加任何额外内容，只返回这句话。"  # 核心约束
)



# --- 3. 定义链条（按依赖顺序执行） ---
# 步骤1：生成颜色（独立链）
chain_color = prompt1 | llm | output_parser

# 步骤2：用颜色生成水果（依赖color）
chain_fruit = itemgetter("color") | prompt2 | llm | output_parser

# 步骤3：用水果生成国家（依赖fruit）
chain_country = itemgetter("fruit") | prompt3 | llm | output_parser

# 最终链：按顺序传递变量并组合结果
chain_final = (
    # 第一步：生成color并包装为字典 {'color': 'xxx'}
    RunnablePassthrough.assign(color=chain_color)
    # 打印color生成结果（调试用）
    | RunnableLambda(lambda x: (print(f"生成颜色: {x['color']}"), x)[1])
    # 第二步：基于color生成fruit，保留color
    | RunnablePassthrough.assign(fruit=chain_fruit)
    # 打印fruit生成结果（调试用）
    # | RunnableLambda(lambda x: (print(f"生成水果: {x['fruit']}"), x)[1])
    # 第三步：基于fruit生成country，保留color和fruit
    | RunnablePassthrough.assign(country=chain_country)
    # 打印country生成结果（调试用）
    # | RunnableLambda(lambda x: (print(f"生成国家: {x['country']}"), x)[1])
    # 组合所有变量生成最终句子
    | prompt4
    | llm
    | output_parser
)


# --- 4. 测试执行（含非流式和流式输出） ---
if __name__ == "__main__":
    try:
        # print("=== 非流式输出 ===")
        # response = chain_final.invoke({})
        # print(f"\n最终结果: {response}\n")

        print("=== 流式输出 ===")
        print("最终结果: ", end="\n", flush=True)
        for chunk in chain_final.stream({}):
            print(chunk, end="", flush=True)
    except Exception as e:
        print(f"\n执行错误: {e}")