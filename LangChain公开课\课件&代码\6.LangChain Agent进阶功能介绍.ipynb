{"cells": [{"cell_type": "markdown", "id": "0b2ea1f9-824e-4cb3-b506-111a33a8bd3d", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## <font face=\"仿宋\">课程说明："]}, {"cell_type": "markdown", "id": "1949f90f-92dd-4854-a72b-136ce29f0c99", "metadata": {}, "source": ["- 体验课内容节选自[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/3u765N)完整版付费课程"]}, {"cell_type": "markdown", "id": "f60f5608-e1bd-45f2-8b2c-8e7fefaf01d6", "metadata": {}, "source": ["&emsp;&emsp;体验课时间有限，若想深度学习大模型技术，欢迎大家报名由我主讲的[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/3u765N)"]}, {"cell_type": "markdown", "id": "541f3cb5-3695-4a7f-af94-569494f07545", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/b9ccfe4155bb650c83c4a061f3da131.jpg\" alt=\"d0c81dfe43a1becced8c07db33c3a787_\" style=\"zoom:15%;\" />"]}, {"cell_type": "markdown", "id": "6079faab-dbdb-4bfb-91cc-06259f7d474a", "metadata": {}, "source": ["**[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/3u765N)为【100+小时】体系大课，总共20大模块精讲精析，零基础直达大模型企业级应用！**"]}, {"cell_type": "markdown", "id": "a8bf8807-67d6-4a77-9a14-70dddeab5624", "metadata": {}, "source": ["<center><img src=\"https://wechatapppro-1252524126.cdn.xiaoeknow.com/appZe9inzwc2314/image/b_u_5ea8e780054d6_Fop5bmXf/6aueuzm7qbtmje.png?imageView2/2/q/80|imageMogr2/ignore-error/1\" alt=\"img\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "e8c67e39-7c6a-4969-909c-bb3d1173db60", "metadata": {}, "source": ["部分项目成果演示"]}, {"cell_type": "code", "execution_count": 6, "id": "5bf4facd-dfc6-43cf-8732-3f659d9b2d63", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "markdown", "id": "838f8536-8284-4260-8772-5d73f3205bab", "metadata": {}, "source": ["- **MateGen项目演示**"]}, {"cell_type": "code", "execution_count": 3, "id": "e45d91aa-ae74-4284-836d-65fc52dddf26", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/4.MateGen%20Pro%20%E9%A1%B9%E7%9B%AE%E5%8A%9F%E8%83%BD%E6%BC%94%E7%A4%BA.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/4.MateGen%20Pro%20%E9%A1%B9%E7%9B%AE%E5%8A%9F%E8%83%BD%E6%BC%94%E7%A4%BA.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "b74d5d9e-3b20-4452-b5f5-38b5b3d4bf3e", "metadata": {}, "source": ["- **智能客服项目演示**"]}, {"cell_type": "code", "execution_count": 4, "id": "dabf0bcf-ed41-43ad-83de-1c240e738caf", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E6%BC%94%E7%A4%BA.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E6%BC%94%E7%A4%BA.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "47d267a9-e7dd-432e-a7e9-f0eb483c6290", "metadata": {}, "source": ["- **Dify项目演示**"]}, {"cell_type": "code", "execution_count": 6, "id": "89781901-17b5-4e42-aede-79f24c66f26a", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "f33929df-308d-4bf7-8c15-2a04bca1ee59", "metadata": {}, "source": ["- **LangChain&LangGraph搭建Multi-Agnet**"]}, {"cell_type": "code", "execution_count": 7, "id": "6e6a491e-9930-45b8-b2f7-ba8603068b0d", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "3a138911-5e66-405b-a369-5549906bf0d6", "metadata": {}, "source": ["此外，若是对大模型底层原理感兴趣，也欢迎报名由我和菜菜老师共同主讲的[《2025大模型原理与实战课程》(夏季班)](https://ix9mq.xetslk.com/s/49L2eN)"]}, {"cell_type": "markdown", "id": "85fe48dc-e546-4a15-90b6-1e4015cfe9a2", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/da5d51c998df07d747cd223c1ed25f7.jpg\" alt=\"da5d51c998df07d747cd223c1ed25f7\" style=\"zoom:20%;\" />"]}, {"cell_type": "markdown", "id": "6bf32e9d-731d-4140-952e-23fa1265ba56", "metadata": {}, "source": ["**两门大模型课程夏季班目前上新特惠+618年中钜惠双惠叠加，合购还有更多优惠哦~<span style=\"color:red;\">详细信息扫码添加助教，回复“大模型”，即可领取课程大纲&查看课程详情👇</span>**"]}, {"cell_type": "markdown", "id": "3c52b7fa-17ff-42b9-9c96-d3403a9a775f", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101749045.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "340312a4-27eb-4110-8718-25d69043c74f", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "e465f34b-61de-43ef-97bb-e6a2f9ca3a96", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 6.<PERSON><PERSON><PERSON>n Agent进阶功能介绍"]}, {"cell_type": "code", "execution_count": 1, "id": "06eb5610-de56-4828-974c-7767e7f69cec", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "id": "7f8eadb2-8577-4126-94aa-fccca7335889", "metadata": {}, "source": ["### 1. 借助LangChain Agent+内置工具快速搭建智能体"]}, {"cell_type": "markdown", "id": "c6bf5735-b1bf-4901-b41c-005014d79829", "metadata": {}, "source": ["&emsp;&emsp;既然LangChain Agent能更加灵活调用外部工具，LangChain Agent+LangChain内置工具也能更加快速的完成复杂Agent开发。"]}, {"cell_type": "markdown", "id": "311d62d8-1105-4b46-8e22-155267826803", "metadata": {}, "source": ["- 第三方工具：https://python.langchain.com/docs/integrations/tools/"]}, {"cell_type": "markdown", "id": "032a70f8-7eb5-4d48-a225-acfd90049eb2", "metadata": {}, "source": ["- <PERSON><PERSON><PERSON><PERSON>工具：https://python.langchain.com/docs/integrations/tools/tavily_search/"]}, {"cell_type": "code", "execution_count": 100, "id": "f1d73ef2-dfe7-4ab4-ba0c-c9afce30a76a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!pip install -qU langchain-tavily -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "markdown", "id": "1b872898-5d8f-459b-b459-5d9afea3518f", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121907492.png\" alt=\"image-20250612190738471\" style=\"zoom:50%;\" />"]}, {"cell_type": "code", "execution_count": 101, "id": "9f979ba1-0f64-4645-bce2-39989d82bc21", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "a83f7034-1364-402a-a8ae-4c44d028a3d3", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults"]}, {"cell_type": "code", "execution_count": 3, "id": "8c4fcc58-ca75-4b44-94f0-f67ca6340540", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26100\\2306380504.py:1: LangChainDeprecationWarning: The class `TavilySearchResults` was deprecated in LangChain 0.3.25 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-tavily package and should be used instead. To use it run `pip install -U :class:`~langchain-tavily` and import as `from :class:`~langchain_tavily import TavilySearch``.\n", "  search = TavilySearchResults(max_results=2)\n"]}], "source": ["search = TavilySearchResults(max_results=2)"]}, {"cell_type": "code", "execution_count": 4, "id": "13a9e51a-71b0-411c-aeee-20a9dc1f4573", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'title': 'Apple 全球开发者大会定于北京时间6 月10 日回归',\n", "  'url': 'https://www.apple.com.cn/newsroom/2025/03/apples-worldwide-developers-conference-returns-the-week-of-june-9/',\n", "  'content': '加利福尼亚州，库比提诺 Apple 今日宣布将于北京时间 2025 年 6 月 10 日至 14 日在线举行年度全球开发者大会（WWDC）。开发者与学生们将有机会于大会开幕当天参与在 Apple Park 举办的线下特别活动。\\n\\nWWDC25 将聚焦 Apple 软件的最新发展，向所有开发者免费开放。Apple 长期致力于向开发者提供支持。作为这项承诺的一部分，大会将为他们提供机会与 Apple 专家交流，深入了解新工具、新框架和新功能。\\n\\n“我们无比期待与我们的全球开发者社区再次共度精彩非凡的 WWDC。”Apple 全球开发者关系副总裁 Susan Prescott 表示，“我们迫不及待地希望分享最新工具与技术，赋能开发者，帮助他们持续开拓创新。” [...] *   新闻文章\\n    \\n    2025 年 3 月 25 日\\n    \\n    新闻稿\\n    \\n    Apple 全球开发者大会定于北京时间 6 月 10 日回归\\n    \\n    WWDC25 将全程在线举行，供所有开发者免费参加\\n    \\n    加利福尼亚州，库比提诺 Apple 今日宣布将于北京时间 2025 年 6 月 10 日至 14 日在线举行年度全球开发者大会（WWDC）。开发者与学生们将有机会于大会开幕当天参与在 Apple Park 举办的线下特别活动。\\n    \\n    WWDC25 将聚焦 Apple 软件的最新发展，向所有开发者免费开放。Apple 长期致力于向开发者提供支持。作为这项承诺的一部分，大会将为他们提供机会与 Apple 专家交流，深入了解新工具、新框架和新功能。\\n    \\n    “我们无比期待与我们的全球开发者社区再次共度精彩非凡的 WWDC。”Apple 全球开发者关系副总裁 Susan Prescott 表示，“我们迫不及待地希望分享最新工具与技术，赋能开发者，帮助他们持续开拓创新。” [...] 新闻稿 2025 年 3 月 25 日\\n\\nApple 全球开发者大会定于北京时间 6 月 10 日回归\\n==============================\\n\\nWWDC25 将全程在线举行，供所有开发者免费参加\\n\\n  ![Image 2: 2025 全球开发者大会的文字海报。](https://www.apple.com.cn/newsroom/images/2025/03/apples-worldwide-developers-conference-returns-the-week-of-june-9/article/Apple-WWDC25-event-branding_big.jpg.large.jpg)\\n\\n[](https://www.apple.com.cn/newsroom/images/2025/03/apples-worldwide-developers-conference-returns-the-week-of-june-9/article/Apple-WWDC25-event-branding.zip)',\n", "  'score': 0.9047265},\n", " {'title': 'WWDC25 - Apple 开发者',\n", "  'url': 'https://developer.apple.com/cn/wwdc25/',\n", "  'content': '*   [概览](https://developer.apple.com/cn/wwdc25/)\\n*   [特别活动](https://developer.apple.com/cn/wwdc25/special-event/)\\n\\n\\xa0\\n\\n  ![Image 2](https://developer.apple.com/wwdc25/images/p1/wwdc25-sf-motion-still-first-large-p1_2x.jpg)  ![Image 3](https://developer.apple.com/wwdc25/images/p1/wwdc25-sf-motion-still-last-large-p1_2x.jpg)\\n\\n技术和创意活动交织的一周精彩盛宴\\n================\\n\\n**太平洋时间 2025 年 6 月 9 日至 13 日** [...] 亲眼见证 Apple 最新工具、框架和功能的发布。通过 Apple\\xa0工程师和设计师主持的视频讲座，了解如何提升 App 和游戏的体验。在实验室中与 Apple\\xa0专家互动交流，并与全球开发者社区建立联系。以上活动均免费在线举行。\\n\\n[添加到日历](https://developer.apple.com/wwdc25/WWDC2025.ics)\\n\\n##### 体验 WWDC25 的无限精彩\\n\\n访问 Apple\\xa0Developer App、Apple\\xa0开发者网站以及 Apple 开发者微信公众号，参与我们的特别活动。\\n\\n[下载 Apple Developer App](https://apps.apple.com/cn/app/apple-developer/id640199958)\\n\\n[关注 Apple 开发者微信公众号](https://mp.weixin.qq.com/s/slhQ1RBYUpJ71gwEUlwSTg)\\n\\n##### WWDC25 将在 Apple\\xa0Park 拉开帷幕\\n\\n申请亲临现场，与我们一起庆祝太平洋时间 6 月 9 日 (周一) 的 WWDC25 开幕日。',\n", "  'score': 0.854509}]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["search.invoke(\"苹果2025WWDC发布会\")"]}, {"cell_type": "code", "execution_count": 6, "id": "510390c8-3e73-40b8-955e-66e24972de6e", "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentExecutor, create_tool_calling_agent, tool\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain.chat_models import init_chat_model\n", "\n", "tools = [search]\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"你是一名助人为乐的助手，并且可以调用工具进行网络搜索，获取实时信息。\"),\n", "        (\"human\", \"{input}\"),\n", "        (\"placeholder\", \"{agent_scratchpad}\"),\n", "    ]\n", ")\n", "\n", "# 初始化模型\n", "model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "\n", "agent = create_tool_calling_agent(model, tools, prompt)"]}, {"cell_type": "code", "execution_count": 7, "id": "8f90a88e-32ef-4ce9-a3c5-1b750880612b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m\n", "Invoking: `tavily_search_results_json` with `{'query': '苹果2025WWDC发布会时间'}`\n", "\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m[{'title': '苹果WWDC 2025开发者大会 - 新浪财经', 'url': 'https://finance.sina.com.cn/tech/roll/2025-06-09/doc-ineznafq9993908.shtml', 'content': '苹果公司正式宣布，2025 年全球开发者大会（WWDC 2025）将于太平洋时间6 月9 日至13 日（北京时间6 月10 日至14 日）举行。本届大会将继续以线上形式为主，', 'score': 0.93898576}, {'title': '以下是苹果WWDC 2025 活动在您当地时区的开始时间 - cnBeta.COM', 'url': 'https://www.cnbeta.com.tw/articles/tech/1505364.htm', 'content': '苹果的WWDC 2025 大会即将到来，正式发布iOS 26、iPadOS 26、macOS 26 及其他更新只是时间问题。大会将于6 月9 日星期一拉开帷幕，并将在Apple Park', 'score': 0.91435975}]\u001b[0m\u001b[32;1m\u001b[1;3m苹果2025年全球开发者大会（WWDC 2025）将于太平洋时间6月9日至13日（北京时间6月10日至14日）举行。大会将以线上形式为主。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '请问苹果2025WWDC发布会召开的时间是？',\n", " 'output': '苹果2025年全球开发者大会（WWDC 2025）将于太平洋时间6月9日至13日（北京时间6月10日至14日）举行。大会将以线上形式为主。'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n", "\n", "agent_executor.invoke({\"input\": \"请问苹果2025WWDC发布会召开的时间是？\"})"]}, {"cell_type": "markdown", "id": "93c16ade-6e30-47ae-a832-f6618c6388da", "metadata": {}, "source": ["### 2. 实战：多智能体协作实现浏览器自动化"]}, {"cell_type": "markdown", "id": "2cf27abe-998d-4987-94fb-58d078531344", "metadata": {}, "source": ["&emsp;&emsp;正如上述我们使用的`create_tool_calling_agent`方法，它其实在`lang<PERSON><PERSON>n`中是一个通用的用来构建工具代理的方法，除此以外，`lang<PERSON>hain`还封装了非常多种不同的`Agent`实现形式，大家可以在这个链接中查看到所有`<PERSON><PERSON>hain`中已经集成的`Agent`实现形式："]}, {"cell_type": "markdown", "id": "bf50c283-e6e6-414d-a128-72f2b6ea2c44", "metadata": {}, "source": ["<style>\n", ".center \n", "{\n", "  width: auto;\n", "  display: table;\n", "  margin-left: auto;\n", "  margin-right: auto;\n", "}\n", "</style>\n", "\n", "<p align=\"center\"><font face=\"黑体\" size=4>推荐的Agent创建函数</font></p>\n", "<div class=\"center\">\n", "\n", "\n", "| 函数名 | 功能描述 | 适用场景 |\n", "|--------|---------|----------|\n", "| **create_tool_calling_agent** | 创建使用工具的Agent | 通用工具调用 |\n", "| **create_openai_tools_agent** | 创建OpenAI工具Agent | OpenAI模型专用 |\n", "| **create_openai_functions_agent** | 创建OpenAI函数Agent | OpenAI函数调用 |\n", "| **create_react_agent** | 创建ReAct推理Agent | 推理+行动模式 |\n", "| **create_structured_chat_agent** | 创建结构化聊天Agent | 多输入工具支持 |\n", "| **create_conversational_retrieval_agent** | 创建对话检索Agent | 检索增强对话 |\n", "| **create_json_chat_agent** | 创建JSON聊天Agent | JSON格式交互 |\n", "| **create_xml_agent** | 创建XML格式Agent | XML逻辑格式 |\n", "| **create_self_ask_with_search_agent** | 创建自问自答搜索Agent | 自主搜索推理 |"]}, {"cell_type": "markdown", "id": "1b78e4b5-cc3a-42a4-8d70-80eb74da1f7c", "metadata": {}, "source": ["&emsp;&emsp;其中比较通用场景的就是我们刚刚使用的`create_tool_calling_agent`，而对于一些符合`OpenAI API RESTFUL API`的模型，则同样可以使用`create_openai_tools_agent`，另外像`create_react_agent`可以用于一些推理任务，`create_conversational_retrieval_agent`则可以用于一些对话系统，具体还是需要根据实际需求来选择。"]}, {"cell_type": "markdown", "id": "9fbd059a-8487-4442-8b4c-e942cb8e4f1c", "metadata": {}, "source": ["&emsp;&emsp;目前来说，在大模型应用开发领域有非常多的需求场景，其中一个比较热门的就是浏览器自动化，通过自动化提取网页内容，然后进行分析，最后生成报告。这样的流程提升效率和收集信息的有效途径。因此接下来，我们就尝试使用尝试使用`create_openai_tools_agent`来实际开发一个浏览器自动化代理。"]}, {"cell_type": "markdown", "id": "c640827a-ea15-44aa-996e-6780ff429022", "metadata": {}, "source": ["&emsp;&emsp;首先，执行浏览器自动化代理需要安装一系列的第三方依赖包，如下所示："]}, {"cell_type": "code", "execution_count": 2, "id": "d212ba43-5891-42bf-ab9a-1affe324e67e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: playwright in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (1.52.0)\n", "Requirement already satisfied: lxml in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (5.4.0)\n", "Requirement already satisfied: langchain_community in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (0.3.24)\n", "Requirement already satisfied: beautifulsoup4 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (4.13.4)\n", "Requirement already satisfied: pyee<14,>=13 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from playwright) (13.0.0)\n", "Requirement already satisfied: greenlet<4.0.0,>=3.1.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from playwright) (3.2.3)\n", "Requirement already satisfied: typing-extensions in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pyee<14,>=13->playwright) (4.14.0)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.59 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (0.3.64)\n", "Requirement already satisfied: langchain<1.0.0,>=0.3.25 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (0.3.25)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (2.0.41)\n", "Requirement already satisfied: requests<3,>=2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (3.12.11)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (9.1.2)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (2.9.1)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (0.3.45)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (0.4.0)\n", "Requirement already satisfied: numpy>=1.26.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain_community) (2.3.0)\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.6.2)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.4.4)\n", "Requirement already satisfied: propcache>=0.2.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.20.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.8 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain<1.0.0,>=0.3.25->langchain_community) (0.3.8)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain<1.0.0,>=0.3.25->langchain_community) (2.11.5)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.59->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langchain-core<1.0.0,>=0.3.59->langchain_community) (24.2)\n", "Requirement already satisfied: jsonpointer>=1.9 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.59->langchain_community) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (3.10.18)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.23.0)\n", "Requirement already satisfied: anyio in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (4.9.0)\n", "Requirement already satisfied: certifi in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.0.9)\n", "Requirement already satisfied: idna in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (3.10)\n", "Requirement already satisfied: h11>=0.16 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.25->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.25->langchain_community) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.25->langchain_community) (0.4.1)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.1.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from requests<3,>=2->langchain_community) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from requests<3,>=2->langchain_community) (2.4.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.1.0)\n", "Requirement already satisfied: soupsieve>1.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from beautifulsoup4) (2.7)\n", "Requirement already satisfied: sniffio>=1.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.3.1)\n"]}], "source": ["! pip install playwright lxml langchain_community beautifulsoup4 reportlab"]}, {"cell_type": "markdown", "id": "6b6965c3-14f1-4208-a2b5-1e9a00e22c00", "metadata": {}, "source": ["&emsp;&emsp;此外，还需要安装 `Playwright` 浏览器，需要在当前虚拟环境中执行如下命令："]}, {"cell_type": "code", "execution_count": 1, "id": "8950f818-043d-4652-8948-92846aee5e66", "metadata": {}, "outputs": [], "source": ["! playwright install"]}, {"cell_type": "markdown", "id": "92c06774-9884-4d52-bc14-ec294d1af23e", "metadata": {}, "source": ["&emsp;&emsp;这个安装过程它会下载并安装 Playwright 支持的浏览器内核（注意：这里不是用我们本机已有的浏览器），包括`Chromium`（类似 Chrome）、`Firefox`、`WebKit`（类似 Safari），并将这些浏览器下载到本地的 `.cache/ms-playwright` 目录或项目的 `~/.playwright` 目录中，以便 Playwright 使用稳定一致的运行环境。"]}, {"cell_type": "markdown", "id": "53469c55-7172-4bd6-957a-f61a7fa19e71", "metadata": {}, "source": ["&emsp;&emsp;这个案例的核心代码首先是需要用代理工具初始化同步 `Playwright` 浏览器：\n", "\n", "```python\n", "    sync_browser = create_sync_playwright_browser()\n", "    toolkit = PlayWrightBrowserToolkit.from_browser(sync_browser=sync_browser)\n", "    tools = toolkit.get_tools()\n", "```"]}, {"cell_type": "markdown", "id": "a0b3bab4-4d01-4730-918c-ee5d3cc15ca8", "metadata": {}, "source": ["&emsp;&emsp;然后再通过`create_openai_tools_agent`接收初始化的大模型和`Playwright`工具构建共同构建`OpenAI Tools` 代理，最后通过`AgentExecutor`执行代理。\n", "\n", "```python\n", "    # 通过 LangChain 创建 OpenAI 工具代理\n", "    agent = create_openai_tools_agent(model, tools, prompt)\n", "\n", "    # 通过 AgentExecutor 执行代理\n", "    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n", "```"]}, {"cell_type": "markdown", "id": "5421bcb5-0e6b-431b-a300-4170509cac0a", "metadata": {}, "source": ["&emsp;&emsp;完整的代码因为`langChian`的模块化封装非常简洁，如下所示：\n", "\n", "```python\n", "    from langchain_community.agent_toolkits import PlayWrightBrowserToolkit\n", "    from langchain_community.tools.playwright.utils import create_sync_playwright_browser\n", "    from langchain import hub\n", "    from langchain.agents import AgentExecutor, create_openai_tools_agent\n", "    from langchain.chat_models import init_chat_model\n", "    import os\n", "    from dotenv import load_dotenv \n", "    load_dotenv(override=True)\n", "\n", "\n", "    DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "    # print(DeepSeek_API_KEY)  # 可以通过打印查看\n", "\n", "    # 初始化 Playwright 浏览器：\n", "    sync_browser = create_sync_playwright_browser()\n", "    toolkit = PlayWrightBrowserToolkit.from_browser(sync_browser=sync_browser)\n", "    tools = toolkit.get_tools()\n", "\n", "    # 通过 <PERSON><PERSON><PERSON><PERSON> 拉取提示词模版\n", "    prompt = hub.pull(\"hwchase17/openai-tools-agent\")\n", "\n", "    # # 初始化模型\n", "    model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "\n", "    # 通过 LangChain 创建 OpenAI 工具代理\n", "    agent = create_openai_tools_agent(model, tools, prompt)\n", "\n", "    # 通过 AgentExecutor 执行代理\n", "    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\n", "\n", "\n", "    if __name__ == \"__main__\":\n", "        # 定义任务\n", "        command = {\n", "            \"input\": \"访问这个网站 https://github.com/fufankeji/MateGen/blob/main/README_zh.md 并帮我总结一下这个网站的内容\"\n", "        }\n", "\n", "        # 执行任务\n", "        response = agent_executor.invoke(command)\n", "        print(response)\n", "```"]}, {"cell_type": "markdown", "id": "126f2603-ae72-43c7-8982-8fc6e03c8e6c", "metadata": {}, "source": ["&emsp;&emsp;但需要注意的是：`Playwright` 工具的初始化过程需要同步执行，在`Jupyter Notebook`中无法直接使用，需要将代码保存为`Python`文件运行。这里完整的代码脚本为`auto_playwright.py`，已经上传到了百度网盘中，大家可以扫码进行领取。"]}, {"cell_type": "markdown", "id": "8e098a8a-6dcb-43f4-8c67-a2b4391082b3", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102037762.png\" alt=\"image-20250610203719731\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "d58f3b57-89a6-4f1b-a775-ab1e3c97944f", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "3795b900-6752-438c-807e-e6812c82074a", "metadata": {}, "source": ["- `hwchase17/openai-tools-agent`提示词模板介绍"]}, {"cell_type": "markdown", "id": "e3c2ba67-116d-4a75-8231-21916bb17e9a", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121930570.png\" alt=\"image-20250612193010467\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "6b57c4e9-998a-448b-a560-dcdb7ee5fe98", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121931622.png\" alt=\"image-20250612193104516\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "2deebef2-8c02-41a4-af80-41e01dbde326", "metadata": {}, "source": ["&emsp;&emsp;运行效果如下所示："]}, {"cell_type": "code", "execution_count": 104, "id": "a144384f-832e-442c-a50e-c14b009efc6f", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2025-06-12%2019-36-48.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2025-06-12%2019-36-48.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "118ec2c8-bab9-4c5a-8949-ea0f152b16a0", "metadata": {}, "source": ["&emsp;&emsp;更进一步地，我们还可以将`Playwright Agent`封装成工具函数，并结合`<PERSON><PERSON><PERSON><PERSON>`的`LCEL`串行链，实现一个更加复杂的浏览器自动化代理。这里定义的工具如下所示：\n", "\n", "```python\n", "\n", "    # 1. 创建网站总结工具\n", "    @tool\n", "    def summarize_website(url: str) -> str:\n", "        \"\"\"访问指定网站并返回内容总结\"\"\"\n", "        try:\n", "            # 创建浏览器实例\n", "            sync_browser = create_sync_playwright_browser()\n", "            toolkit = PlayWrightBrowserToolkit.from_browser(sync_browser=sync_browser)\n", "            tools = toolkit.get_tools()\n", "            \n", "            # 初始化模型和Agent\n", "            model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "            prompt = hub.pull(\"hwchase17/openai-tools-agent\")\n", "            agent = create_openai_tools_agent(model, tools, prompt)\n", "            agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=False)\n", "            \n", "            # 执行总结任务\n", "            command = {\n", "                \"input\": f\"访问这个网站 {url} 并帮我详细总结一下这个网站的内容，包括主要功能、特点和使用方法\"\n", "            }\n", "            \n", "            result = agent_executor.invoke(command)\n", "            return result.get(\"output\", \"无法获取网站内容总结\")\n", "            \n", "        except Exception as e:\n", "            return f\"网站访问失败: {str(e)}\"\n", "\n", "    # 2. 创建PDF生成工具\n", "    @tool  \n", "    def generate_pdf(content: str) -> str:\n", "        \"\"\"将文本内容生成为PDF文件\"\"\"\n", "        try:\n", "            # 生成文件名（带时间戳）\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filename = f\"website_summary_{timestamp}.pdf\"\n", "            \n", "            # 创建PDF文档\n", "            doc = SimpleDocTemplate(filename, pagesize=A4)\n", "            styles = getSampleStyleSheet()\n", "            \n", "            # 注册中文字体（如果系统有的话）\n", "            try:\n", "                # Windows 系统字体路径\n", "                font_paths = [\n", "                    \"C:/Windows/Fonts/simhei.ttf\",  # 黑体\n", "                    \"C:/Windows/Fonts/simsun.ttc\",  # 宋体\n", "                    \"C:/Windows/Fonts/msyh.ttc\",    # 微软雅黑\n", "                ]\n", "                \n", "                chinese_font_registered = False\n", "                for font_path in font_paths:\n", "                    if os.path.exists(font_path):\n", "                        try:\n", "                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))\n", "                            chinese_font_registered = True\n", "                            print(f\"✅ 成功注册中文字体: {font_path}\")\n", "                            break\n", "                        except:\n", "                            continue\n", "                            \n", "                if not chinese_font_registered:\n", "                    print(\"⚠️ 未找到中文字体，使用默认字体\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"⚠️ 字体注册失败: {e}\")\n", "            \n", "            # 自定义样式 - 支持中文\n", "            title_style = ParagraphStyle(\n", "                'CustomTitle',\n", "                parent=styles['Heading1'],\n", "                fontSize=18,\n", "                alignment=TA_CENTER,\n", "                spaceAfter=30,\n", "                fontName='ChineseFont' if 'chinese_font_registered' in locals() and chinese_font_registered else 'Helvetica-Bold'\n", "            )\n", "            \n", "            content_style = ParagraphStyle(\n", "                'CustomContent',\n", "                parent=styles['Normal'],\n", "                fontSize=11,\n", "                alignment=TA_JUSTIFY,\n", "                leftIndent=20,\n", "                rightIndent=20,\n", "                spaceAfter=12,\n", "                fontName='ChineseFont' if 'chinese_font_registered' in locals() and chinese_font_registered else 'Helvetica'\n", "            )\n", "            \n", "            # 构建PDF内容\n", "            story = []\n", "            \n", "            # 标题\n", "            story.append(Paragraph(\"网站内容总结报告\", title_style))\n", "            story.append(<PERSON><PERSON>(1, 20))\n", "            \n", "            # 生成时间\n", "            time_text = f\"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\"\n", "            story.append(Paragraph(time_text, styles['Normal']))\n", "            story.append(<PERSON><PERSON>(1, 20))\n", "            \n", "            # 分隔线\n", "            story.append(Paragraph(\"=\" * 50, styles['Normal']))\n", "            story.append(<PERSON><PERSON>(1, 15))\n", "            \n", "            # 主要内容 - 改进中文处理\n", "            if content:\n", "                # 清理和处理内容\n", "                content = content.replace('\\r\\n', '\\n').replace('\\r', '\\n')\n", "                paragraphs = content.split('\\n')\n", "                \n", "                for para in paragraphs:\n", "                    if para.strip():\n", "                        # 处理特殊字符，确保PDF可以正确显示\n", "                        clean_para = para.strip()\n", "                        # 转换HTML实体\n", "                        clean_para = clean_para.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')\n", "                        \n", "                        try:\n", "                            story.append(Paragraph(clean_para, content_style))\n", "                            story.append(<PERSON><PERSON>(1, 8))\n", "                        except Exception as para_error:\n", "                            # 如果段落有问题，尝试用默认字体\n", "                            try:\n", "                                fallback_style = ParagraphStyle(\n", "                                    'Fallback',\n", "                                    parent=styles['Normal'],\n", "                                    fontSize=10,\n", "                                    leftIndent=20,\n", "                                    rightIndent=20,\n", "                                    spaceAfter=10\n", "                                )\n", "                                story.append(Paragraph(clean_para, fallback_style))\n", "                                story.append(<PERSON><PERSON>(1, 8))\n", "                            except:\n", "                                # 如果还是有问题，记录错误但继续\n", "                                print(f\"⚠️ 段落处理失败: {clean_para[:50]}...\")\n", "                                continue\n", "            else:\n", "                story.append(Paragraph(\"暂无内容\", content_style))\n", "            \n", "            # 页脚信息\n", "            story.append(<PERSON><PERSON>(1, 30))\n", "            story.append(Paragraph(\"=\" * 50, styles['Normal']))\n", "            story.append(Paragraph(\"本报告由 Playwright PDF Agent 自动生成\", styles['Italic']))\n", "            \n", "            # 生成PDF\n", "            doc.build(story)\n", "            \n", "            # 获取绝对路径\n", "            abs_path = os.path.abspath(filename)\n", "            print(f\"📄 PDF文件生成完成: {abs_path}\")\n", "            return f\"PDF文件已成功生成: {abs_path}\"\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"PDF生成失败: {str(e)}\"\n", "            print(error_msg)\n", "            return error_msg\n", "```"]}, {"cell_type": "markdown", "id": "2c347580-cf57-48fd-a91f-0b57014e3907", "metadata": {}, "source": ["&emsp;&emsp;然后我们可以自定义不同的链路，比如简单的串行链由`Playwright Agent`和 `generate_pdf Agent`组成，即先爬取网页的内容，然后将网页中的内容写入到本地的`PDF`文件中。\n", "\n", "```python\n", "    # 方法1：简单串行链\n", "    simple_chain = summarize_website | generate_pdf\n", "```"]}, {"cell_type": "markdown", "id": "54c20578-2b01-4d5e-b213-ed2aa24093be", "metadata": {}, "source": ["&emsp;&emsp;除此以外，我们还可以再定一个摘要工具，在使用`Playwright`工具访问网页后，根据爬取到的网页内容先使用大模型进行摘要总结，再调用`generate_pdf`工具将总结内容写入到本地的`PDF`文件中。代码如下所示：\n", "\n", "```python\n", "    optimization_prompt = ChatPromptTemplate.from_template(\n", "    \"\"\"请优化以下网站总结内容，使其更适合PDF报告格式：\n", "\n", "    原始总结：\n", "    {summary}\n", "\n", "    请重新组织内容，包括：\n", "    1. 清晰的标题和结构\n", "    2. 要点总结\n", "    3. 详细说明\n", "    4. 使用要求等\n", "\n", "    优化后的内容：\"\"\"\n", "    )\n", "\n", "    model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "\n", "    # 带优化的串行链：网站总结 → LLM优化 → PDF生成\n", "    optimized_chain = (\n", "        summarize_website \n", "        | (lambda summary: {\"summary\": summary})\n", "        | optimization_prompt \n", "        | model \n", "        | StrOutputParser() \n", "        | generate_pdf\n", "    )\n", "```"]}, {"cell_type": "markdown", "id": "5dcc32ce-d461-4f4b-ba91-9fc24d309c3c", "metadata": {}, "source": ["&emsp;&emsp;完整的代码如下所示：\n", "\n", "```python\n", "    from langchain_community.agent_toolkits import PlayWrightBrowserToolkit\n", "    from langchain_community.tools.playwright.utils import create_sync_playwright_browser\n", "    from langchain import hub\n", "    from langchain.agents import AgentExecutor, create_openai_tools_agent\n", "    from langchain.chat_models import init_chat_model\n", "    from langchain_core.tools import tool\n", "    from langchain_core.prompts import ChatPromptTemplate\n", "    from langchain_core.output_parsers import StrOutputParser\n", "    from reportlab.lib.pagesizes import letter, A4\n", "    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer\n", "    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle\n", "    from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER\n", "    from reportlab.pdfbase import pdfmetrics\n", "    from reportlab.pdfbase.ttfonts import TTFont\n", "    import os\n", "    from datetime import datetime\n", "    import os\n", "    from dotenv import load_dotenv \n", "    load_dotenv(override=True)\n", "\n", "\n", "    DeepSeek_API_KEY = os.getenv(\"DEEPSEEK_API_KEY\")\n", "\n", "    # 1. 创建网站总结工具\n", "    @tool\n", "    def summarize_website(url: str) -> str:\n", "        \"\"\"访问指定网站并返回内容总结\"\"\"\n", "        try:\n", "            # 创建浏览器实例\n", "            sync_browser = create_sync_playwright_browser()\n", "            toolkit = PlayWrightBrowserToolkit.from_browser(sync_browser=sync_browser)\n", "            tools = toolkit.get_tools()\n", "            \n", "            # 初始化模型和Agent\n", "            model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "            prompt = hub.pull(\"hwchase17/openai-tools-agent\")\n", "            agent = create_openai_tools_agent(model, tools, prompt)\n", "            agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=False)\n", "            \n", "            # 执行总结任务\n", "            command = {\n", "                \"input\": f\"访问这个网站 {url} 并帮我详细总结一下这个网站的内容，包括主要功能、特点和使用方法\"\n", "            }\n", "            \n", "            result = agent_executor.invoke(command)\n", "            return result.get(\"output\", \"无法获取网站内容总结\")\n", "            \n", "        except Exception as e:\n", "            return f\"网站访问失败: {str(e)}\"\n", "\n", "    # 2. 创建PDF生成工具\n", "    @tool  \n", "    def generate_pdf(content: str) -> str:\n", "        \"\"\"将文本内容生成为PDF文件\"\"\"\n", "        try:\n", "            # 生成文件名（带时间戳）\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filename = f\"website_summary_{timestamp}.pdf\"\n", "            \n", "            # 创建PDF文档\n", "            doc = SimpleDocTemplate(filename, pagesize=A4)\n", "            styles = getSampleStyleSheet()\n", "            \n", "            # 注册中文字体（如果系统有的话）\n", "            try:\n", "                # Windows 系统字体路径\n", "                font_paths = [\n", "                    \"C:/Windows/Fonts/simhei.ttf\",  # 黑体\n", "                    \"C:/Windows/Fonts/simsun.ttc\",  # 宋体\n", "                    \"C:/Windows/Fonts/msyh.ttc\",    # 微软雅黑\n", "                ]\n", "                \n", "                chinese_font_registered = False\n", "                for font_path in font_paths:\n", "                    if os.path.exists(font_path):\n", "                        try:\n", "                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))\n", "                            chinese_font_registered = True\n", "                            print(f\"✅ 成功注册中文字体: {font_path}\")\n", "                            break\n", "                        except:\n", "                            continue\n", "                            \n", "                if not chinese_font_registered:\n", "                    print(\"⚠️ 未找到中文字体，使用默认字体\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"⚠️ 字体注册失败: {e}\")\n", "            \n", "            # 自定义样式 - 支持中文\n", "            title_style = ParagraphStyle(\n", "                'CustomTitle',\n", "                parent=styles['Heading1'],\n", "                fontSize=18,\n", "                alignment=TA_CENTER,\n", "                spaceAfter=30,\n", "                fontName='ChineseFont' if 'chinese_font_registered' in locals() and chinese_font_registered else 'Helvetica-Bold'\n", "            )\n", "            \n", "            content_style = ParagraphStyle(\n", "                'CustomContent',\n", "                parent=styles['Normal'],\n", "                fontSize=11,\n", "                alignment=TA_JUSTIFY,\n", "                leftIndent=20,\n", "                rightIndent=20,\n", "                spaceAfter=12,\n", "                fontName='ChineseFont' if 'chinese_font_registered' in locals() and chinese_font_registered else 'Helvetica'\n", "            )\n", "            \n", "            # 构建PDF内容\n", "            story = []\n", "            \n", "            # 标题\n", "            story.append(Paragraph(\"网站内容总结报告\", title_style))\n", "            story.append(<PERSON><PERSON>(1, 20))\n", "            \n", "            # 生成时间\n", "            time_text = f\"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\"\n", "            story.append(Paragraph(time_text, styles['Normal']))\n", "            story.append(<PERSON><PERSON>(1, 20))\n", "            \n", "            # 分隔线\n", "            story.append(Paragraph(\"=\" * 50, styles['Normal']))\n", "            story.append(<PERSON><PERSON>(1, 15))\n", "            \n", "            # 主要内容 - 改进中文处理\n", "            if content:\n", "                # 清理和处理内容\n", "                content = content.replace('\\r\\n', '\\n').replace('\\r', '\\n')\n", "                paragraphs = content.split('\\n')\n", "                \n", "                for para in paragraphs:\n", "                    if para.strip():\n", "                        # 处理特殊字符，确保PDF可以正确显示\n", "                        clean_para = para.strip()\n", "                        # 转换HTML实体\n", "                        clean_para = clean_para.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')\n", "                        \n", "                        try:\n", "                            story.append(Paragraph(clean_para, content_style))\n", "                            story.append(<PERSON><PERSON>(1, 8))\n", "                        except Exception as para_error:\n", "                            # 如果段落有问题，尝试用默认字体\n", "                            try:\n", "                                fallback_style = ParagraphStyle(\n", "                                    'Fallback',\n", "                                    parent=styles['Normal'],\n", "                                    fontSize=10,\n", "                                    leftIndent=20,\n", "                                    rightIndent=20,\n", "                                    spaceAfter=10\n", "                                )\n", "                                story.append(Paragraph(clean_para, fallback_style))\n", "                                story.append(<PERSON><PERSON>(1, 8))\n", "                            except:\n", "                                # 如果还是有问题，记录错误但继续\n", "                                print(f\"⚠️ 段落处理失败: {clean_para[:50]}...\")\n", "                                continue\n", "            else:\n", "                story.append(Paragraph(\"暂无内容\", content_style))\n", "            \n", "            # 页脚信息\n", "            story.append(<PERSON><PERSON>(1, 30))\n", "            story.append(Paragraph(\"=\" * 50, styles['Normal']))\n", "            story.append(Paragraph(\"本报告由 Playwright PDF Agent 自动生成\", styles['Italic']))\n", "            \n", "            # 生成PDF\n", "            doc.build(story)\n", "            \n", "            # 获取绝对路径\n", "            abs_path = os.path.abspath(filename)\n", "            print(f\"📄 PDF文件生成完成: {abs_path}\")\n", "            return f\"PDF文件已成功生成: {abs_path}\"\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"PDF生成失败: {str(e)}\"\n", "            print(error_msg)\n", "            return error_msg\n", "\n", "\n", "    # 3. 创建串行链\n", "    print(\"=== 创建串行链：网站总结 → PDF生成 ===\")\n", "\n", "    # 方法1：简单串行链\n", "    simple_chain = summarize_website | generate_pdf\n", "\n", "    # 方法2：带LLM优化的串行链\n", "    optimization_prompt = ChatPromptTemplate.from_template(\n", "        \"\"\"请优化以下网站总结内容，使其更适合PDF报告格式：\n", "\n", "    原始总结：\n", "    {summary}\n", "\n", "    请重新组织内容，包括：\n", "    1. 清晰的标题和结构\n", "    2. 要点总结\n", "    3. 详细说明\n", "    4. 使用要求等\n", "\n", "    优化后的内容：\"\"\"\n", "    )\n", "\n", "    model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "\n", "    # 带优化的串行链：网站总结 → LLM优化 → PDF生成\n", "    optimized_chain = (\n", "        summarize_website \n", "        | (lambda summary: {\"summary\": summary})\n", "        | optimization_prompt \n", "        | model \n", "        | StrOutputParser() \n", "        | generate_pdf\n", "    )\n", "\n", "    # 4. 测试函数\n", "    def test_simple_chain(url: str):\n", "        \"\"\"测试简单串行链\"\"\"\n", "        print(f\"\\n🔄 开始处理URL: {url}\")\n", "        print(\"📝 步骤1: 网站总结...\")\n", "        print(\"📄 步骤2: 生成PDF...\")\n", "        \n", "        result = simple_chain.invoke(url)\n", "        print(f\"✅ 完成: {result}\")\n", "        return result\n", "\n", "    def test_optimized_chain(url: str):\n", "        \"\"\"测试优化串行链\"\"\"\n", "        print(f\"\\n🔄 开始处理URL (优化版): {url}\")\n", "        print(\"📝 步骤1: 网站总结...\")\n", "        print(\"🎨 步骤2: 内容优化...\")\n", "        print(\"📄 步骤3: 生成PDF...\")\n", "        \n", "        result = optimized_chain.invoke(url)\n", "        print(f\"✅ 完成: {result}\")\n", "        return result\n", "\n", "    # 5. 创建交互式函数\n", "    def create_website_pdf_report(url: str, use_optimization: bool = True):\n", "        \"\"\"创建网站PDF报告的主函数\"\"\"\n", "        print(\"=\" * 60)\n", "        print(\"🤖 网站内容PDF生成器\")\n", "        print(\"=\" * 60)\n", "        \n", "        try:\n", "            if use_optimization:\n", "                result = test_optimized_chain(url)\n", "            else:\n", "                result = test_simple_chain(url)\n", "                \n", "            print(\"\\n\" + \"=\" * 60)\n", "            print(\"🎉 任务完成！\")\n", "            print(\"=\" * 60)\n", "            return result\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"❌ 处理失败: {str(e)}\"\n", "            print(error_msg)\n", "            return error_msg\n", "\n", "    # 6. 主程序入口\n", "    if __name__ == \"__main__\":\n", "        # 测试URL\n", "        test_url = \"https://github.com/fufankeji/MateGen/blob/main/README_zh.md\"\n", "        \n", "        print(\"选择处理方式:\")\n", "        print(\"1. 简单串行链（直接总结 → PDF）\")\n", "        print(\"2. 优化串行链（总结 → 优化 → PDF）\")\n", "        \n", "        choice = input(\"请选择 (1/2): \").strip()\n", "        \n", "        if choice == \"1\":\n", "            create_website_pdf_report(test_url, use_optimization=False)\n", "        elif choice == \"2\":\n", "            create_website_pdf_report(test_url, use_optimization=True)\n", "        else:\n", "            print(\"使用默认优化模式...\")\n", "            create_website_pdf_report(test_url, use_optimization=True) \n", "```"]}, {"cell_type": "markdown", "id": "a37db154-58f1-4d3c-8b2d-d34dfe34505d", "metadata": {}, "source": ["&emsp;&emsp;上述完整的代码我们已经上传到百度网盘中`playwright_pdf_agent.py`文件中，大家可以扫描下方的二维码免费领取。"]}, {"cell_type": "markdown", "id": "03e02ca4-60c3-4d48-8faf-b69b0c934a47", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102037364.png\" alt=\"image-20250610203728333\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "3c2e816c-9f1b-4f46-ada4-d718955661a5", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "8f6f77ed-7560-473a-a8f5-936da0b71a9a", "metadata": {}, "source": ["&emsp;&emsp;运行效果如下所示："]}, {"cell_type": "code", "execution_count": 5, "id": "45de5d98-aa80-4938-b2e5-98b309abe84e", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%B5%8F%E8%A7%88%E5%99%A8%E5%A4%9A%E6%99%BA%E8%83%BD%E4%BD%93%E5%8D%8F%E4%BD%9C.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%B5%8F%E8%A7%88%E5%99%A8%E5%A4%9A%E6%99%BA%E8%83%BD%E4%BD%93%E5%8D%8F%E4%BD%9C.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "5aab6ac0-ef43-4de0-956b-1deb93a8ac66", "metadata": {}, "source": ["<center>\r\n", "\r\n", "<img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506122039682.png\" alt=\"image-20250612203905602\" style=\"zoom:50%;\" />"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}