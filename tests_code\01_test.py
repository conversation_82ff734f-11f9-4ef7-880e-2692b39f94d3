from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage
from langchain_core.output_parsers import StrOutputParser
import base64
from PIL import Image, ImageEnhance
import io

# 初始化Ollama多模态模型
llm = ChatOllama(model="qwen2.5vl:7b", temperature=0)


def converter_img(image_path):
    """将本地图片转换为Base64编码字符串（符合Ollama图像格式要求）"""
    try:
        with Image.open(image_path) as img:
            # 在converter_img函数中添加增强步骤
            img = img.convert("RGB")  # 统一为RGB格式，避免透明通道问题
            img.thumbnail((1024, 1024))  # 缩放至合理尺寸，避免过大
            buffer = io.BytesIO()
            img.save(buffer, format="JPEG", quality=90)  # 保存为JPEG格式
            img_bytes = buffer.getvalue()

            # 转换为Base64编码（Ollama要求的格式）
            return base64.b64encode(img_bytes).decode("utf-8")
    except Exception as e:
        print(f"图片处理失败: {e}")
        return None


def get_multimodal_response(image_b64, text_query):
    """构造符合Ollama规范的多模态消息并调用模型"""
    # 关键修复：Ollama要求图像必须用type: "image_url"，且包含image_url键
    message_content = [
        {
            "type": "image_url",  # 固定格式：必须为"image_url"
            "image_url": {
                "url": f"data:image/jpeg;base64,{image_b64}"  # 标准Base64 URL格式
            }
        },
        {
            "type": "text",  # 文本部分格式不变
            "text": text_query
        }
    ]

    # 包装为消息列表（符合BaseMessages要求）
    messages = [HumanMessage(content=message_content)]

    # 调用模型并解析结果
    response = llm.invoke(messages)
    return StrOutputParser().parse(response)


# 使用示例
if __name__ == "__main__":
    image_path = r"D:\uploads\processed\images\3674、3672、3671、3650、3657系列矢量网络分析仪编程手册_page_51.png"
    image_b64 = converter_img(image_path)

    if image_b64:
        try:
            query = (
                "请从图片顶部到底部完整提取所有内容，转换为标准Markdown格式。"
                "必须包含所有标题、文字、表格（包括表格内的每一行每一列），"
                "不遗漏任何细节。公式用$包裹，代码用```标记。"
            )
            response = get_multimodal_response(image_b64, query)
            print("模型输出:\n", response)
            # 仅提取content内容
            print("\n纯文本内容:\n", response.content)
        except Exception as e:
            print(f"模型调用失败: {e}")