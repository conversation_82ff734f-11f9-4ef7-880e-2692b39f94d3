# 1. **代码：**

```python
# 导入gradio库，这是创建机器学习/深度学习模型交互界面的工具
import gradio as gr

# 定义核心功能函数
# 这个函数接收两个参数：name(名字)和intensity(问候强度)
# 问候强度决定了返回结果中感叹号的数量
def greet(name, intensity):
    # 返回拼接的问候语，感叹号的数量由intensity决定
    return "Hello, " + name + "!" * int(intensity)

# 创建Gradio界面
# Interface是Gradio的核心类，用于创建简单的输入-输出界面
demo = gr.Interface(
    # fn参数指定要包装的函数，这里是greet函数
    fn=greet,
    # inputs参数定义输入组件
    # 这里使用列表指定两个输入组件：文本框(text)和滑块(slider)
    # 第一个输入对应name参数，第二个输入对应intensity参数
    inputs=["text", "slider"],
    # outputs参数定义输出组件
    # 这里只有一个输出组件：文本(text)，对应greet函数的返回值
    outputs=["text"],
)

# 启动界面
# share参数决定是否生成可共享的公共链接
# 设置为True后会生成一个可以通过互联网访问的URL
demo.launch(share=True)
```

建议使用实际类而非字符串，就像下面这样，而不是第一个例子中使用的字符串。

当你使用字符串如 `"text"` 或 `"slider"` 时，$Gradio$ 会自动为你创建默认配置的组件。这种方式适合快速原型开发，但 不能自定义组件的属性。

而使用 `gr.Textbox()`、`gr.Slider()` 等类实例化的方式，你可以：

- 设置标签（label）
- 设置默认值（value）
- 控制输入输出区域大小（lines, max_lines）
- 设置最小/最大值、步长等参数（min, max, step）

```python
import gradio as gr

def greet(name, intensity):
    return "Hello, " + name + "!" * intensity

demo = gr.Interface(
    fn=greet,
    inputs=["text", gr.Slider(value=2, minimum=1, maximum=10, step=1)],
    outputs=[gr.Textbox(label="greeting", lines=3)],
)

demo.launch()
```

`gr.Textbox()` 和 `gr.Slider()` 是 Gradio 中两个非常常用的组件类，分别用于创建文本输入框和滑动条。它们都支持丰富的参数来自定义行为和外观。

---

## 📝 `gr.Textbox(**kwargs)` 参数详解

### ✅ 功能：
用于创建文本输入或输出区域（如单行输入框、多行文本框等）。

### 🧩 主要参数：

| 参数名              | 类型            | 默认值 | 说明                                                   |
| ------------------- | --------------- | ------ | ------------------------------------------------------ |
| `label`             | str             | None   | 组件上方显示的标签名称                                 |
| `value` / `default` | str 或 Callable | ""     | 默认显示的文本内容                                     |
| `lines`             | int             | 1      | 显示多少行（高度），适用于多行文本框                   |
| `max_lines`         | int             | 20     | 最大允许的行数（当用户输入换行时）                     |
| `placeholder`       | str             | None   | 输入框为空时显示的占位符文字                           |
| `type`              | str             | "text" | 输入类型："text"（普通文本）、"password"（密码输入框） |
| `readonly`          | bool            | False  | 是否只读，禁止用户修改                                 |
| `multiline`         | bool            | False  | 是否为多行文本框（相当于 `lines > 1`）                 |
| `autoscroll`        | bool            | True   | 输出时是否自动滚动到底部（用于日志显示等）             |

### 💡 示例：

```python
gr.Textbox(label="用户名", placeholder="请输入你的名字", lines=2)
```



## 🎚️ `gr.Slider(**kwargs)` 参数详解

### ✅ 功能：
创建一个可拖动的滑动条，用于选择数值范围内的某个值。

### 🧩 主要参数：

| 参数名              | 类型         | 默认值 | 说明                                       |
| ------------------- | ------------ | ------ | ------------------------------------------ |
| `label`             | str          | None   | 组件上方显示的标签名称                     |
| `value` / `default` | float or int | 0      | 初始值                                     |
| `minimum` / `min`   | float or int | 0      | 滑块最小值                                 |
| `maximum` / `max`   | float or int | 100    | 滑块最大值                                 |
| `step`              | float or int | 1      | 每次滑动的步长（精度）                     |
| `log`               | bool         | False  | 是否使用对数刻度（适合跨数量级变化的场景） |
| `interactive`       | bool         | True   | 是否允许用户交互（设为 False 表示只读）    |
| `show_label`        | bool         | True   | 是否显示 label 标签                        |
| `scale`             | int          | None   | 在布局中所占比例（与其它组件配合使用）     |

### 💡 示例：

```python
gr.Slider(label="音量控制", minimum=0, maximum=100, step=5, value=50)
```


---

## 🧱 通用参数（Gradio 大多数组件都支持）

以下是一些 Gradio 组件共有的参数：

| 参数名         | 类型             | 默认值 | 说明                               |
| -------------- | ---------------- | ------ | ---------------------------------- |
| `label`        | str              | None   | 组件标签                           |
| `visible`      | bool             | True   | 是否可见                           |
| `elem_id`      | str              | None   | 自定义 HTML 元素 ID，便于 CSS 定制 |
| `elem_classes` | List[str] or str | None   | 添加自定义 CSS 类名                |
| `container`    | bool             | True   | 是否在独立容器中渲染该组件         |
| `scale`        | int              | None   | 布局中的占比权重                   |
| `min_width`    | int              | 160    | 最小宽度（像素）                   |

---

## ✅ 总结对比表

| 参数      | gr.Textbox | gr.Slider | 说明                        |
| --------- | ---------- | --------- | --------------------------- |
| `label`   | ✅          | ✅         | 设置标签                    |
| `value`   | ✅          | ✅         | 设置默认值                  |
| `minimum` | ❌          | ✅         | 设置最小值                  |
| `maximum` | ❌          | ✅         | 设置最大值                  |
| `step`    | ❌          | ✅         | 设置滑动步长                |
| `lines`   | ✅          | ❌         | 设置显示行数                |
| `type`    | ✅          | ❌         | 设置输入类型（如 password） |

---

如果你有特定的需求（比如：设置密码框、限制输入长度、添加提示信息、自定义样式等），我可以帮你写出具体的代码示例 😊





# **2. 补充说明：**

1. **运行方式：**

- 使用 python 01_hello_gradio.py 运行时，修改代码后需要重新启动才能看到变化
- 使用 gradio 01_hello_gradio.py 运行时，修改代码后刷新浏览器即可看到变化

2. **运行后会启动本地服务器，并提供两个访问地址：**

- 本地地址（如 http://127.0.0.1:7860）只能在本机访问
- 公共地址（如 https://xxx.gradio.app）可以通过互联网访问

3. **浏览器打开地址后，可以看到包含两个输入控件（文本框和滑块）和一个输出区域的界面：**

- 在文本框中输入名字
- 拖动滑块选择问候强度
- 点击"Submit"按钮后会显示问候语，感叹号的数量由滑块位置决定
- 界面的 Flag Button（标记按钮）:
  - 当用户点击 Flag 按钮时，Gradio 会将当前的输入和输出结果记录到本地日志文件（通常是 flagged.csv）。
  - 这个功能主要用于收集用户反馈、测试模型表现或构建训练数据集。

4. **fn 可以多种形式的输入：**

| 输入类型          | 示例                      | 对应 Gradio 组件                  |
| ----------------- | ------------------------- | --------------------------------- |
| 文本字符串        | "hello"                   | `gr.Text()` 或 `gr.Textbox()`     |
| 数字              | 123, 3.14                 | `gr.Number()`、`gr.Slider()`      |
| 图像文件          | .jpg, .png                | `gr.Image()`                      |
| 音频文件          | .wav, .mp3                | `gr.Audio()`                      |
| 视频文件          | .mp4, .avi                | `gr.Video()`                      |
| 文件              | .pdf, .docx, .py, .txt 等 | `gr.File()`                       |
| 表格数据          | CSV、Pandas DataFrame     | `gr.Dataframe()`、`gr.Matrix()`   |
| Markdown 文档内容 | 字符串形式的 Markdown     | `gr.Markdown()` 或 `gr.Textbox()` |





