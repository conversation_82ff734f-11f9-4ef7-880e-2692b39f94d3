# **AC6900 Series Three-Phase AC Power** **Sources**

## AC6903H, AC6906H, AC6912H, AC6918H - 5 kHz AC6903L, AC6906L, AC6912L, AC6918L - 550 Hz

OPERATING AND PROGRAMMING GUIDE


2 Keysight AC6900 Operating and Programming Guide


Notices 5


**Copyright Notice** **5**
**Manual Part Number** **5**

**Edition** **5**

**Published by** **5**
**Warranty** **5**
**Technology Licenses** **5**
**U.S. Government Rights** **6**


Safety and Regulatory Information 7


**General Information** **7**

**Safety Warnings** **7**
**Product Grounding** **7**
**General Warnings** **8**
**Environmental Warnings** **8**
**Shock Hazard** **9**

**Moving Warnings** **10**
**Installation Warnings** **10**
**Equipment Cautions** **11**
**General Cautions** **11**

**Installation Cautions** **11**

**Operational Cautions** **11**
**Servicing Cautions** **11**
**Safety and Regulatory Symbols** **12**


1 Getting Started 15


**Welcome** **16**

**Introduction to the Instrument** **18**

**Specifications** **26**
**Supplemental Characteristics** **28**


2 Installation 37


**Before Installation or Use** **38**

**Outline Diagrams** **40**
**Rack and Floor Mounting Options** **44**
**Installing a GPIB Board Accessory** **46**
**Power Cord Connections** **47**

**Switchboard and Circuit Breaker Requirements** **53**
**Interface Connections** **55**

**Quick Start Lesson** **57**
**Output Power Connections** **62**
**Analog and Digital Connections** **71**
**Parallel Connections** **74**


3 User Information 81


**Front Panel Menu Reference** **82**

**Programming the Output** **87**
**Setting Protection and Limits** **93**
**Turning the Output On and Off** **98**


Keysight AC6900 Operating and Programming Guide 3


**Displaying Measurements** **103**
**Using Advanced Functions** **109**
**Using Output Controls** **119**
**Using System Utilities** **126**
**Remote Interface Configuration** **136**
**Digital IO Configuration** **145**


4 SCPI Programming 149


**Introduction to SCPI** **150**

**Command Quick Reference** **156**
**ABORt Subsystem** **171**
**CURRent Subsytem** **173**
**DIGital Subsystem** **175**
**DISPlay Subsystem** **177**
**FETCh and MEASure Subsystems** **179**
**FREQuency Subsystem** **183**
**FUNCtion Subsystem** **186**
**HCOPy Subsystem** **187**
**IEEE-488 Common Commands** **188**

**INITiate Subsystem** **194**
**INSTrument Subsystem** **195**
**LXI Subsystem** **196**
**MEMory Subsystem** **197**
**OUTPut Subsystem** **198**
**PROGram Subsystem** **206**
**SENSe Subsytem** **214**
**SIMulation Subsystem** **217**
**STATus Subsystem** **221**
**SYSTem Subsystem** **234**
**TRIGger Subsystem** **243**
**VOLTage Subsystem** **246**
**WAVE Subsystem** **258**
**Trigger System Overview** **261**
**Status System Overview** **266**
**Default Settings** **274**
**Measurement Details** **280**

**Error and Limit Checking** **282**


5 Maintenance 285


**Servicing the Instrument** **286**
**Cleaning the Instrument** **289**
**Removing and Installing an SD Card** **291**
**Error Messages** **293**
**Hardware Error Codes** **300**


Index 303


4 Keysight AC6900 Operating and Programming Guide


Notices

### **Notices**

#### **Copyright Notice**


© Keysight Technologies, Inc. 2021- 2024

#### **Manual Part Number**


AC6900-90001

#### **Edition**


Edition 5, September 2024
Updated September 2024

#### **Published by**


Keysight Technologies, Inc.
Bayan Lepas Free Industrial Zone
11900 Bayan Lepas, Penang
Malaysia

#### **Warranty**


THE MATERIAL CONTAINED IN THIS DOCUMENT IS PROVIDED "AS IS," AND IS SUBJECT TO BEING

CHANGED, WITHOUT NOTICE, IN FUTURE EDITIONS. FURTHER, TO THE MAXIMUM EXTENT

PERMITTED BY APPLICABLE LAW, KEYSIGHT DISCLAIMS ALL WARRANTIES, EITHER EXPRESS OR

IMPLIED WITH REGARD TO THIS MANUAL AND ANY INFORMATION CONTAINED HEREIN,

INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS

FOR A PARTICULAR PURPOSE. KEYSIGHT SHALL NOT BE LIABLE FOR ERRORS OR FOR INCIDENTAL

OR CONSEQUENTIAL DAMAGES IN CONNECTION WITH THE FURNISHING, USE, OR PERFORMANCE

OF THIS DOCUMENT OR ANY INFORMATION CONTAINED HEREIN. SHOULD KEYSIGHT AND THE

USER HAVE A SEPARATE WRITTEN AGREEMENT WITH WARRANTY TERMS COVERING THE

MATERIAL IN THIS DOCUMENT THAT CONFLICT WITH THESE TERMS, THE WARRANTY TERMS IN

THE SEPARATE AGREEMENT WILL CONTROL.

#### **Technology Licenses**


The hardware and/or software described in this document are furnished under a license and may be
used or copied only in accordance with the terms of such license.


Portions of this software are licensed by third parties including open source terms and conditions. To
the extent such open source licenses require source code be provided, we will provide it at no cost
[upon request. Please contact Keysight support, https://www.keysight.com/find/assist to make such a](https://www.keysight.com/find/assist)

request.


Keysight AC6900 Operating and Programming Guide 5


Notices

#### **U.S. Government Rights**


The Software is “commercial computer software,” as defined by Federal Acquisition Regulation (“FAR”)
2.101. Pursuant to FAR 12.212 and 27.405-3 and Department of Defense FAR Supplement (“DFARS”)
227.7202, the U.S. government acquires commercial computer software under the same terms by
which the software is customarily provided to the public. Accordingly, Keysight provides the Software
to U.S. government customers under its standard commercial license, which is embodied in its End
User License Agreement (EULA), a copy of which can be found at
[http://www.keysight.com/find/sweula. The license set forth in the EULA represents the exclusive](http://www.keysight.com/find/sweula)
authority by which the U.S. government may use, modify, distribute, or disclose the Software. The
EULA and the license set forth therein, does not require or permit, among other things, that Keysight:
(1) Furnish technical information related to commercial computer software or commercial computer
software documentation that is not customarily provided to the public; or (2) Relinquish to, or
otherwise provide, the government rights in excess of these rights customarily provided to the public
to use, modify, reproduce, release, perform, display, or disclose commercial computer software or
commercial computer software documentation. No additional government requirements beyond those
set forth in the EULA shall apply, except to the extent that those terms, rights, or licenses are explicitly
required from all providers of commercial computer software pursuant to the FAR and the DFARS and
are set forth specifically in writing elsewhere in the EULA. Keysight shall be under no obligation to
update, revise or otherwise modify the Software. With respect to any technical data as defined by FAR
2.101, pursuant to FAR 12.211 and 27.404.2 and DFARS 227.7102, the U.S. government acquires no
greater than Limited Rights as defined in FAR 27.401 or DFAR 227.7103-5 (c), as applicable in any

technical data.


6 Keysight AC6900 Operating and Programming Guide


Safety and Regulatory Information

### **Safety and Regulatory Information**

#### **General Information**


The equipment is for industrial use.


Equipment operators are subject to all applicable safety regulations. Along with the warning and safety
notices in this manual, all relevant safety, accident prevention, and environmental regulations must
also be followed. In particular, the operators of the equipment:


l Must be informed of the relevant safety requirements.


l Must have read and understood the operating manual before using the equipment.


l Must use the designated and recommended safety equipment.


The following general safety precautions must be observed during all phases of operation of this
instrument. Failure to comply with these precautions or with specific warnings or instructions
elsewhere in this manual violates safety standards of design, manufacture, and intended use of the
instrument. Keysight Technologies assumes no liability of the customer’s failure to comply with the
requirements.

#### **Safety Warnings**


A WARNING notice denotes a hazard. It calls attention to an operating procedure, practice, or the like
that, if not correctly performed or adhered to, could result in personal injury or death. Do not proceed
beyond a WARNING notice until the indicated conditions are fully understood and met.


Should network communication issues occur, the instrument settings shown in the
Browser Web Control page may not represent the actual state of the instrument. This
may result in unexpected hazardous voltages on the output and sense connections that
could result in personal injury, death, or damage to a device under test. Before touching
the output or sense connections or connecting to a device under test, always verify the

state of the instrument.

#### **Product Grounding**


The instrument is a Class 1 product and is provided with a grounding-type power cord
set. The instrument chassis and cover are connected to the instrument electrical ground
to minimize shock hazard. The ground pin of the cord set plug must be firmly connected
to the electrical ground (safety ground) terminal at the power outlet. Any interruption of
the protective earth (grounding) conductor or disconnection of the protective earth
terminal will cause a potential shock hazard that could result in personal injury or death.


Keysight AC6900 Operating and Programming Guide 7


Safety and Regulatory Information

#### **General Warnings**


Do not use this product in any manner not specified by the manufacturer. The protective
features of this product may be impaired if it is used in a manner not specified in the
operation instructions.


Instruments that appear damaged or defective should be made inoperative and secured
against unintended operation until they can be repaired by qualified service personnel.


The instrument contains an internal fuse, which is not user accessible.


DO NOT REMOVE COVERS. NO OPERATOR SERVICEABLE PARTS INSIDE. REFER

SERVICING TO QUALIFIED SERVICE PERSONNEL.


SECURELY TURN OFF THE CIRCUIT BREAKER ON THE SWITCHBOARD BEFORE

HANDLING THE POWER CORDS. TO AVOID AN ELECTRIC SHOCK, THE PROTECTIVE

CONDUCTOR TERMINAL MUST BE CONNECTED TO AN ELECTRICAL GROUND.


NE PAS RETIRER LES COUVERTURE DE LA BOITE. AUCUN OPÉRATEUR SERVICEABLE
PIÈCES À L'INTÉRIEUR. CONFIER L'ENTRETIEN DE PERSONNEL QUALIFIÉ.


CORRECTEMENT COUPER LE DISJONCTEUR ARMOIRE AVANT DE MANIPULER LES
CORDONS D'ALIMENTATION. POUR ÉVITER TOUT CHOC ÉLECTRIQUE, LE
CONDUCTEUR DE PROTECTION BORNE DOIT ÊTRE RELIÉE À UNE MASSE ÉLECTRIQUE.

#### **Environmental Warnings**


This product is designed for safe indoor use. Use indoors only.


Do not operate the instrument near flammable gases or fumes.


To prevent the possibility of explosion or fire, do not use the product near alcohol, thinner
or other combustible materials, or in an atmosphere containing such vapors.


Do not install the product near a heater, in direct sunlight, or in areas subject to drastic
temperature changes. The operating temperature range is 0 to 40 °C (32 to 104 °F), and
the storage temperature range is -10 to 60 °C (14 to 140 °F).


Do not install the product in high-humidity locations, such as near a boiler, humidifier, or
water supply. The operating humidity range is 20% to 80% relative humidity (no
condensation), and the storage humidity range is 90% relative humidity or less (no
condensation). Condensation may occur even within the operating humidity range. In
such cases, do not use the instrument until the condensation dries up completely.


Do not install the product in a corrosive atmosphere or in environments containing
sulfuric acid mist, etc. This may cause corrosion of various conductors and bad contacts
of connectors inside the instrument leading to malfunction and failure, or in the worst
case, a fire.


Do not install the product in a dusty location. Accumulation of dust can lead to electric

shock or fire.


8 Keysight AC6900 Operating and Programming Guide


Safety and Regulatory Information

#### **Shock Hazard**


Before making any load or sense connections be sure to turn the POWER switch off and
remove the power plug from an outlet or turn off the circuit breaker at the switchboard.


When the power switch is turned off while the output is on, residual voltage still remains
at the output terminals.


Do not touch the output terminal block for at least 20 seconds after the power switch is

tuned off.


When installing the switch between the OUTPUT terminal block and the load, be sure to

turn off the circuit breaker of the switchboard.


Be sure to turn the switch off before connecting the load to the terminal at the load end of

the switch.


Do not touch the switch terminal or the output terminal when the output is on.


Do not remove the instrument covers. There are no customer-serviceable parts inside.
Some circuits are active and have power briefly after the power switch is turned off.


Always turn off the circuit breaker at the switchboard before cleaning.


Always ensure that the power cord ground is connected.


This product is an IEC Safety Class I equipment (equipment with a protective conductor
terminal). Be sure to ground (earth) the unit.

Connect the protective conductor terminal to earth ground.


In DC mode, the Line (P1) terminals are at positive potential and the Neutral terminals are
at negative potential when setting the positive value. The opposite is true when setting
the negative value.


Do not use the terminal block with the terminal cover removed.


Turn off the switchboard circuit breaker before connecting the cord.


This product includes protective earth terminals. To minimize shock hazard, the
instrument must be connected to the AC power mains through a grounded power cable,
with the ground wire firmly connected to an electrical ground (safety ground) at the
power outlet. Any interruption of the protective (grounding) conductor or disconnection
of the protective earth terminal will cause a potential shock hazard that could result in
personal injury.


If a capacitor, battery, or similar device is connected as a load in DC mode, voltage
remains at the section connected to the output terminal block even when the output is off
until the load energy is discharged. The discharge time of the internal capacitor when no
load is connected is approximately 0.1 seconds. To prevent the possibility of electric
shock, do not touch the output terminal block.


Keysight AC6900 Operating and Programming Guide 9


Safety and Regulatory Information

#### **Moving Warnings**


Turn off the power switch before moving the instrument. Moving the unit while the power
is turned on can cause electric shock or instrument damage.


Remove all wiring before moving the instrument. Moving the unit with the cables
connected can cause wires to break or injuries due to the product falling over.


Do not move the instrument by yourself. Be sure to have two or more people move the
instrument. Exercise special care when moving the instrument over a slope or across
steps. Ensure that the instrument is not moved accidentally while it is being operated by
locking the casters with your foot. For model AC6918, lower the stopper to secure the

instrument in its final installation location.


If you are using a lift or bands to move the instrument, be sure to slide the lift or bands

under the bottom of the instrument and then raise the instrument

#### **Installation Warnings**


Have a qualified engineer connect the power cord to the switchboard.


Protective circuits inside the instrument are connected to match the input terminal
polarity. Make sure the colors of the wires connected to the input terminals are correct.


Verify that all safety precautions are taken. Make all connections to the unit before
applying power. Note the instrument's external markings described under "Safety
Symbols."


You cannot use standard rack mounting support rails, as they would block the airflow
needed for cooling.


10 Keysight AC6900 Operating and Programming Guide


Safety and Regulatory Information

#### **Equipment Cautions**


A CAUTION notice denotes a hazard. It calls attention to an operating procedure, practice, or the like
that, if not correctly performed or adhered to, could result in damage to the product or loss of
important data. Do not proceed beyond a CAUTION notice until the indicated conditions are fully

understood and met.

#### **General Cautions**


Do not place objects on the instrument. Placing objects, especially heavy objects, on top
of the product can cause failures.

#### **Installation Cautions**


Do not block the air intake at the front of the instrument or the exhaust at the rear.


Large voltage distortion on the AC power line can lead to malfunction. Do not connect the
instrument to a generator or a similar device.


Do not use the product in a location where strong magnetic or electric fields are nearby or
a location where large amounts of distortion or noise are present on the input power
supply waveform. The product may malfunction.


Do not install the product on an inclined surface or location subject to vibrations. The
product may fall or tip over, causing damage or injuries or both.

#### **Operational Cautions**


You cannot set the voltage limit when the instrument is being controlled by external
analog signals. An excessive external voltage may damage the load.


When the output is turned on, several volts of undershoot or overshoot may appear for a
time period on the order of ten microseconds.

#### **Servicing Cautions**


This instrument has a void seal sticker on various locations on the chassis.

This seal prohibits opening the chassis or making any modifications.


Any attempt to open or modify the instrument will void the warranty and could
compromise its performance or safety. For assistance, please contact Keysight
technical support.


Keysight AC6900 Operating and Programming Guide 11


Safety and Regulatory Information

#### **Safety and Regulatory Symbols**


Direct current


Alternating current


Frame or chassis terminal


On supply


Off supply


Caution, risk of electric shock


Caution, refer to accompanying documents


Earth ground terminal


The CE mark is a registered trademark of the European Community.
CAN IECS/NMB-001(A) indicates compliance with the Canadian Interference- Causing
Equipment Standard.
ISM GRP 1-A indicates that the instrument is an Industrial Scientific and Medical Group
1 Class A product (CISPER 11, Clause 4).


The TUV mark is a registered trademark of the European community. It certifies NRTL

for North America.


The RCM mark is a registered trademark of the Spectrum Management Agency of Australia. This signifies compliance with the Australian EMC Framework regulations under

the terms of the Radio Communications Act of 1992.


The UKCA mark Indicates United Kingdom Conformity Assessed.


Contains one or more of the 6 hazardous substances above the maximum concentration

value (MCV), 40 Year EPUP.


South Korean Class A EMC Declaration

This equipment has been conformity assessed for use in business environments. In a
residential environment this equipment may cause radio interference. This EMC statement applies to the equipment only for use in business environment.


12 Keysight AC6900 Operating and Programming Guide


Safety and Regulatory Information


**Waste Electrical and Electronic Equipment (WEEE) Directive 2012/19/EU**


The following crossed out wheeled bin symbol indicates that separate collection for waste electric and
electronic equipment (WEEE) is required, as obligated by DIRECTIVE 2012/19/EU and other National
legislation.


Do not dispose in domestic household waste. Please refer to
[about.keysight.com/en/companyinfo/environment/takeback.shtml to](http://about.keysight.com/en/companyinfo/environment/takeback.shtml)
understand your trade in options with Keysight in addition to product

takeback instructions.


Keysight AC6900 Operating and Programming Guide 13


Keysight AC6900 Operating and Programming Guide

# 1 Getting Started


**Welcome**


**Introduction to the Instrument**


**Specifications**


**Supplemental Characteristics**


1 Getting Started

#### **Welcome**


This manual includes user, service, and programming information for the Keysight AC6900 Series
Three-Phase AC Power Sources, which can be used stand alone, or mounted in a test rack with the

available bracket options. The front panel display lets you easily access and view setup and
measurement information. This information may also be accessed using programming commands.


Choose from models up to 18 kVA, all with 0 to 320 Vrms, ±452 VDC, and 1 Hz to 5 kHz output
capability. Both LAN/LXI Core and USB interfaces are standard. You can choose to add GPIB or analog
and digital interfaces to add basic transient signals.


**Models**


The AC6900 Series Three-Phase AC Power Sources include eight models:


AC6903L 550 Hz/AC6903H 5 kHz 3 kVA


AC6906L 550 Hz/AC6906H 5 kHz 6 kVA


AC6912L 550 Hz/AC6912H 5 kHz 12 kVA


AC6918L 550 Hz/AC6918H 5 kHz 18 kVA


The "H" or High frequency models are designed to generate output frequencies up to 5000 Hz.


The "L" or Low frequency models are designed to generate output frequencies up to 550 Hz in 3 phase
operation only.


**Options and Accessories**


To order any of the following, please contact you nearest Keysight Sales and Support Office


**Input Options**


Input options must be ordered when purchasing the instrument. Not available for AC6903 units.


**Option** **Description**


200 3 phase AC, 200-230V, 50/60Hz (4-wire L1, L2, L3, and )


400 3 phase AC, 380-480, 50/60Hz (5-wire L1, L2, L3, N, and )


**Interface Accessories**


**Accessory** **Description**


AC69GPBU GPIB interface board


AC69ALGU Analog interface connector - controls the output with external analog signals.


AC69DIGU Digital interface connector - controls the output with external digital signals


AC69MEMC SD Card for AC6900 Series


16 Keysight AC6900 Operating and Programming Guide


1 Getting Started


**Parallel Cables**


Not available for AC6903 units.


**Parallel Cables** **Description**


AC69PAR1 Parallel operation cable - for paralleling units; length = 1 m


AC69SYN1 Synchronization cable - for synchronizing power switches on parallel units; length = 1 m


**Rack Mount Brackets**


**Rack Mount** **Description**


AC69RAC3 Rack mount flange kit for AC6903 units.


AC69RAC6 Bracket for mounting AC6906 units in a standard EIA inch instrument rack.


AC69RAC12 Bracket for mounting AC6912 units in a standard EIA inch instrument rack.


**Base Hold Kit**


**Base Hold Kit** **Description**


AC69RAC18 Base hold angle kit for permanently fastening AC6918 units to the floor


**Power Cords**


Refer to Power Cord Requirements for details.


**Option MEM**


Option MEM provides a micro-SD card, which can be easily removed for sanitization purposes. Option
MEM can only be ordered when the instrument is purchased. Refer to Removing and Installing an SD

Card for details.


Note that instruments with Option MEM will not turn on if the original SD card is not installed in the

unit.


**Documentation, Firmware, and Technical Support**


You can download the latest version of this document and accees the latest firmware from the AC6900

[product page at www.keysight.com/find/AC6900.](http://www.keysight.com/find/AC6800B)


[For technical support, please refer to www.keysight.com/find/assist.](http://www.keysight.com/find/assist)


**Contacting Keysight Technologies**


United States: (800) 829-4444

Europe: 31 20 547 2111
Japan: 0120-421-345


[Use www.keysight.com/find/assist to contact Keysight worldwide.](http://www.keysight.com/find/assist)


Keysight AC6900 Operating and Programming Guide 17


1 Getting Started

#### **Introduction to the Instrument**


**Front Panel at a Glance**


**Front Panel Display at a Glance**


**Rear Panel at a Glance**


**Instrument Ratings**


**Voltage Ranges, Phases, and Output Characteristics**


**Front Panel at a Glance**


The following table lists the main parts of the front panel, from left to right:


18 Keysight AC6900 Operating and Programming Guide


1 Getting Started

#### 1 USB port 2 The display lets you configure and monitor the instrument.

Repeatedly pressing the Meter key selects from 3 meter views:
3 phases + All, as shown
3 phases only, in larger format - top to bottom
1 phase in largest format, 2 phases in smallest format

#### 3 [Meter] selects the metering view. Pressing it repeatedly cycles the

display through three formats as described above.

[System] accesses the Utilities\System settings menu.

[Error] accesses the Utilities\Error menu

[Store&Recall] accesses the Utilities\Store&Recall menu

#### 4 The puqt arrows move around the command menus and

select characters in alphanumeric entry fields. The [Enter] key

makes a selection in a menu and enters the edit mode for numeric

parameters.

#### 5 The Output Preset keys lets you save to or recall instrument states

in memory locations 1 and 2.

#### 6 Data Entry keys let you enter numeric values.

[0] through [9] enter numbers.

[.] enters the decimal point.

[–] toggles between positive and negative numbers.

backspace key to delete characters.


[Exp] enters the letter E to allow you to enter an exponent to the
right of it.

[Enter] enters a value. If you exit a field without pressing [Enter],
the value is ignored.

#### 7 Select Phase selects an output phase (1-3) or selects All output

phases.


Keysight AC6900 Operating and Programming Guide 19


1 Getting Started

#### 8 The RPG quickly lets you scroll though any numeric or alpha
numeric data fields

#### 9 The Status LEDs indicate when an error or a protection event has

occurred.

#### 10 The [On/Off] key turns the selected output phase or all output

phase on or off.

#### 11 The Power switch powers the unit on or off.

Located directly under the display on AC6903 units.

#### 12 The Power Selector switch specifies the instrument's hierarchy in a

parallel configuration. Not available on AC6903 units.


20 Keysight AC6900 Operating and Programming Guide


**Front Panel Display at a Glance**

#### 1 Title bar Left side displays the following information:

F.RESP = fast CV response is set
S.RESP = slow CV response is set
SYNC = frequency synchronization is enabled
SNS = remote sensing is enabled
SS = soft start/stop is enabled

SIM = simulation mode is enabled

SEQ = sequence mode is enabled
OUT.Z = output impedance control is enabled

WAV = a custom waveform is enabled

Follower# = information about the follower is displayed
AIN = the analog input is enabled

#### 2 Phases (1-3) Displays information about the selected phase

Right side displays the following information: status:
OFF = the output is off
CV = the output is in constant voltage mode
~CV = CV with surge suppression OFF
CL = the output is in current limit
IPK = the output is in peak current limit
OV = the output is disabled by the over voltage protection
UV = the output is disabled by the under voltage protection
OC = the output is disabled by the over current protection
OT = the over temperature protection has tripped
OP = the overpower protection has tripped

#### 3 All phases Displays the line to line measurements for all phases

Sets global values for Vac, Vdc (line to neutral), and Hz

#### 4 Soft keys Refer to the Front Panel Menu reference



1 Getting Started


Center displays the following information:

!ERR = an error has occurred

!UNB = the output voltage or phase is unbalanced
Right side displays the following information:


= the LAN is connected


= the LAN is not connected


= the USB is connected


RWL = the remote front panel lock is enabled

RMt = the instrument is set to remote mode


INP = the input protection has tripped
EXT = the external protection has tripped
WDG = watchdog protection - no I/O activity

INH = the remote inhibit is active

Lower left indicates the voltage range setting
L = low range
H = high range
Lower right indicates the output coupling:

AC coupled


DC coupled


AC+DC coupled



Keysight AC6900 Operating and Programming Guide 21


1 Getting Started


**Rear Panel at a Glance**

#### **AC6903 shown**


22 Keysight AC6900 Operating and Programming Guide


1 Getting Started


The following table lists the main parts of the rear panel:

#### 1 Parallel The connectors for parallel operation. Not available on AC6903 units. 2 Slots 1 - 3 The expansion slots for accessory control boards. 3 Analog IN The connector port for analog control. 4 Digital IO The connector port for digital control. 5 LAN The port for remote control. 6 USB The port for remote control. 7 OUTPUT The terminal block connects the output terminals to the load. 8 AC INPUT The barrier block connects the AC mains power cord.


Refer to Output Power Connections and Power Cord connections for installation details.


**Instrument Ratings**


**Rated Output Capacity** **AC6903** **AC6906** **AC6912** **AC6918**


1 phase and 3 phase output 3 kVA 6 kVA 12 kVA 18 kVA


1 phase 3-wire output 2 kVA 4 kVA 8 kVA 12 kVA


**Maximum Output Current**


1 phase output (L/H) 30 A/15 A 60 A/30 A 120 A/60 A 180 A/90 A


1 phase 3-wire and 3 phase output (L/H) 10 A/5 A 20 A/10 A 40 A/20 A 60 A/30 A


**Internal Power Module Ratings**


1 phase output 3 kVA total 6 kVA total 12 kVA total 18 kVA total


1 phase 3-wire output 2 kVA total 4 kVA total 8 kVA total 12 kVA total


3 phase output 1 kVA each phase 2 kVA each phase 4 kVA each phase 6 kVA each phase


During single-phase output and three-phase output, all power modules are used. During single-phase
three-wire output, the phase 3 power module is not used.


**3-phase output** **1-phase, 3-wire output**


Keysight AC6900 Operating and Programming Guide 23


1 Getting Started


**Voltage Ranges, Phases, and Output Characteristics**


**Settings and Limits Ranges**


The instrument has two voltage ranges: 160 V (low) and 320 V (high).


The table below indicates the output voltage setting and limits ranges. Set the limits so that the upper
limit is greater than or equal to the lower limit.


**Voltage range** **Voltage Setting Range** **Voltage Limit Range**


L range (low) AC Mode 0.0 to 161.0 V 0.0 to 322.0 V


DC mode -227.5 to +227.5 V -455.0 to +455.0 V


H range (high) AC Mode 0.0 to 322.0 V 0.0 to 322.0 V


DC mode -455.0 to +455.0 V -455.0 to +455.0 V


**Phase vs Line**


The following illustrates the difference between phase voltage and line voltage.


In three-phase AC output, when the phase difference between the three phases is 120° and the output
voltages are equal, line voltage = √3 × phase voltage.





L range (low) AC Mode 0.0 to 322.0 V 0.0 to 278.8 V


DC mode -455.0 to +455.0 V N/A


H range (high) AC Mode 0.0 to 644.0 V 0.0 to 557.7 V


DC mode -910.0 to +910.0 V N/A


**Output Characteristic**


The following figures illustrate the derating characteristic for both output voltage and output
frequency.


24 Keysight AC6900 Operating and Programming Guide


1 Getting Started


**3-phase AC Derating Characteristic** **DC Derating Characteristic**


**Output Current Ratio vs**

**Output Frequency**


Keysight AC6900 Operating and Programming Guide 25


1 Getting Started

#### **Specifications**


Unless otherwise noted, specifications are warranted where the product operates in an environment
with an ambient temperature of 23 °C ±5 °C, after a 30-minute warm-up period. Specifications apply at
the output terminals, with the sense terminals connected to the output terminals (local sensing).


**Output**











AC voltage (L/H) Rating (L/H) [2] 160 V / 320 V


Setting range (L/H) 0.0 ~ 161.0 V / 0.0 ~ 322.0 V


Resolution 0.1 V


Accuracy (L/H) [3, 4] ±(0.15% + 0.3 V) ( 45 Hz - 500 Hz)
±(1% + 0.3 V) / ±(1% + 0.6 V) (500 Hz - 5 kHz)



Maximum rms
current (L/H) [5]



1 phase 30 A/15 A 60 A/30 A 120 A/60 A 180 A/90 A



1 phase 3 wire,
3 phase



10 A/5 A 20 A/10 A 40 A/20 A 60 A/30 A



Power capacity 1 phase, 3 phase 3 kVA 6 kVA 12 kVA 18 kVA


1 phase 3 wire 2 kVA 4 kVA 8 kVA 12 kVA


Load power factor 0 ~ 1 (leading or lagging)


Frequency Range [6] 1 Hz ~ 5 kHz (5 kHz -3dB, < 40 Hz power derating is required)


Resolution 0.01 Hz (1.00 Hz ~ 99.99 Hz), 0.1 Hz (100.0 Hz ~ 999.9 Hz), 1 Hz (1000 Hz ~ 5000 Hz)


Accuracy ±0.01%, temperature coefficient: ±0.005%/°C


Phase Resolution 0.1° (waveform bank 0 and 1 Hz ~ 500 Hz), 1° (500 Hz ~ 4 kHz), 2° (> 4 kHz)


Accuracy [7] Within 120° ±(0.4° +fo×0.9°) where fo = output frequency (kHz)


**Output DC** **[1]**


DC voltage (L/H) Rating [8] ±226 V / ±452 V


Setting range (L/H) 0 - ±227.5 V / 0 - ±455 V


Resolution 0.1 V


Accuracy ±(0.15% +0.3 V)


Maximum current [9] 30 A/15 A 60 A/30 A 120 A/60 A 180 A/90 A


Power capacity 3 kW 6 kW 12 kW 18 kW


1. When combined with AC and DC output, the peak voltage must be between -455 V to 455 V (H range) or -227.5 V to 227.5 V (L range).


2. The specified guaranteed voltage range is 1 V to 160 V and 2 V to 320 V.


3. At no load, the output response is in Fast or Medium, using the compensation function.


4. At the phase angle of 120° of each phase for Line voltage.


5. When the output voltage is between 100 Vac and 160 Vac or 200 Vac and 320 Vac, the output current is reduced by the output voltage.
When the output frequency is between 1 Hz and 40 Hz, the output current is reduced by the output frequency. The output current is 70 % at 1 Hz.


6. On the L models, the frequency is between 1 Hz to 550.0 Hz for three-phase output.


7. Example in which the angle conversion is at a given frequency: within 120° ± 0.5° at 60 Hz output, within 120° ± 0.8° at 400 Hz output.


8. The guaranteed voltage range is 1.4 Vdc to 226 Vdc, 2.8 Vdc to 452 Vdc.


9. When the output voltage is between 100 Vdc and 226 Vdc (L range) or 200 Vdc and 452 Vdc (H range), the output current is reduced by the output voltage.


26 Keysight AC6900 Operating and Programming Guide


1 Getting Started



**Measurement and Stability**









AC voltage Resolution 0.1 V


Accuracy ±(0.03% of reading + 100 mV) (45 Hz ~ 100 Hz)
±(0.1% of reading + 100 mV) (100 Hz ~ 999.9 Hz)

±(0.5% of reading + 1 V) (1 kHz ~ 5 kHz)


rms current Resolution 0.01 A 0.01 A 0.1 A 0.1 A


Accuracy [1] ±(0.15% of reading + 0.20% of full scale) (45 Hz - 65 Hz)
±(0.5% of reading + 0.5% of full scale) (DC, 40 Hz - 999.9 Hz)

±(1.2% of reading + 1.2% of full scale) (1 kHz - 5 kHz)


Peak current Resolution 0.01 A 0.01 A 0.1 A 0.1 A


Accuracy [2] 4% of full scale


Active power Resolution 1 W 1 W 10 W 10 W


Accuracy (L/H) [1, 3] ±(0.7% +0.7% of fs+0.001% of fs/V) / ±(0.7% +0.7% of fs +0.0005% of fs/V)

(45 Hz ~ 100 Hz)


Apparent power Resolution 1 VA 1 VA 10 VA 10 VA


Accuracy (L/H) [1, 3] ±(0.3% +0.3% of fs +0.001% of fs/V) / ±(0.3% +0.3% of fs +0.0005% of fs/V)

(45 Hz ~ 100 Hz)


Power Factor Resolution 0.01


DC voltage Resolution 0.1 V


Accuracy 0.05% of reading +150 mV


**Output Stability**


Line regulation [4] ±0.1%



Load regulation (L/H) [5, 6] ≤100 Hz

≤500 Hz


≤1 kHz


Total harmonics distortion [6] ≤100 Hz

≤500 Hz


≤5 kHz



0.3%

0.5%

1.5%/kHz



±0.1 V/±0.2 V

±0.3 V/±0.6 V


±1 V/±2 V



±0.1 V/±0.2 V

±0.3 V/±0.6 V


±1 V/±2 V



±0.1 V/±0.2 V

±0.3 V/±0.6 V


±1 V/±2 V



±0.2V/±0.4V

±0.3V/±0.6V


±1V/±2



1. At 10 % to 100 % of maximum rated current, sine wave


2. Pulse height of sine wave


3. At a power factor of 1


4. For input voltage changes within the rated range


5. For output current changes within 0 to 100 % of the rating and when the compensation function is not used.


6. When the output phase voltage is between 80 V and 160 V (L range) or 160 V and 320 V (H range), the load power factor is 1, and the response
is FAST at the output terminal block.


Keysight AC6900 Operating and Programming Guide 27


1 Getting Started

#### **Supplemental Characteristics**


Supplemental characteristics are not warranted but are descriptions of performance determined either
by design or by type testing. Supplemental characteristics are typical unless otherwise noted.











Apparent Power ≤4 kVA ≤7.8 kVA ≤15.6 kVA ≤23.4 kVA


Power Factor (typical) [1] 0.93 0.95 0.95 0.95


Efficiency (typical) [1] 82% 85% 85% 85%


Hold-up time for power interruption [1] 10 ms


Frequency (nominal) 50 Hz ~ 60 Hz


Frequency (variation) 45 Hz ~ 65 Hz



Voltage (nominal) 100 V input:
200 V input:
400 V input:


Voltage (variation) 100 V input:
200 V input:
400 V input:


Phase 100 V input:
200 V input:
400 V input:


Maximum rms input current [2] 100 V input:
200 V input:
400 V input:


Protective conductor current 200 V input: [3]

400 V input:



100-120 Vrms

200-240 Vrms 200 - 230 Vrms

380 - 480 Vrms


85-132 Vrms

170-250 Vrms 170 - 250 Vrms

323 - 519 Vrms


1-phase, 3-wire

3-phase, 4-wire
3-phase, 5-wire



48 A

24 A 27 A

14 A


≤3.5 mA ≤10 mA

≤3.5 mA



53 A

28 A


≤15 mA

≤3.5 mA



80 A

42 A


≤20 mA

≤3.5 mA



1. At output voltage 100 V/200 V, rated output current, sine wave, load power factor 1, output frequency 40 Hz to 1 kHz
2. Current at the minimum voltage (within the allowable variation range)
3. At output voltage 100 V/200 V, rated output current, sine wave, load power factor 1, output frequency 45 Hz to 65 Hz











Maximum peak current [1] 4 times the maximum output current


Inrush current capacity Current at 1.4 times the maximum output current for 0.5 s


Temperature coefficient [2] 100 ppm/°C


Transient response [3] 40 μs (Fast)


Response speed Tr/Tf [4] 40 μs (Fast); 100 μs (Medium); 300 μs (Slow)


Ripple noise [5] 0.25 V rms 0.25 V rms 0.3 V rms 0.4 V rms


Variation by output frequency [6, 7]




 - with voltage correction enabled

 - with voltage correction disabled



±0.3% ( 1Hz ~ 1 kHz); ±10% (1001 Hz ~ 5 kHz)

-3 dB (5 kHz)



1. Repeated output is possible when the crest factor is 4.
2. For changes within the operating temperature range At output phase voltage 100 V/200 V, no load
3. When the output voltage is 100V or 200V, the load power factor is 1, and the output current changes from 0 A to the rated value and from the rated value to 0

A.

4. At 10 % to 90 % of the output voltage
5. 5 Hz to 1 MHz components in DC mode


28 Keysight AC6900 Operating and Programming Guide


1 Getting Started


6. Voltage variation over 40 Hz to 5 kHz in AC mode with 55 Hz as the reference
7. When the output phase voltage is between 80 V and 160 V (L range) or 160 V and 320 V (H range), the load power factor is 1, and the response is Fast.


**Output Impedance**













Low Range Resistance 0 Ωto 667 mΩ 0 Ωto 333 mΩ 0 Ωto 167 mΩ 0 Ωto 111 mΩ


Reactance Fast 15 µH to 667 µH 7 µH to 333 µH 3 µH to 167 µH 2 µH to 111 µH


Medium 27 µH to 667 µH 13 µH to 333 µH 7 µH to 167 µH 4 µH to 111 µH


Slow 80 µH to 667 µH 40 µH to 333 µH 20 µH to 167 µH 13 µH to 111 µH


High Range Resistance 0 Ωto 2 667 mΩ 0 Ωto 1333 mΩ 0 Ωto 667 mΩ 0 Ωto 444 mΩ


Reactance Fast 55 µH to 2667 µH 27 µH to 1333 µH 13 µH to 667 µH 9 µH to 444 µH


Medium 107 µH to 2667 µH 53 µH to 1333 µH 27 µH to 667 µH 18 µH to 444 µH


Slow 320 µH to 2667 µH 160 µH to 1333 µH 80 µH to 667 µH 53 µH to 444 µH


**1 phase 3 wire; and 3 phase**


Low Range Resistance 0 Ωto 2000 mΩ 0 Ωto 1000 mΩ 0 Ωto 500 mΩ 0 Ωto 333 mΩ


Reactance Fast 40 µH to 2000 µH 20 µH to 1000 µH 10 µH to 500 µH 7 µH to 333 µH


Medium 80 µH to 2000 µH 40 µH to 1000 µH 20 µH to 500 µH 13 µH to 333 µH


Slow 240 µH to 2000 µH 120 µH to 1000 µH 60 µH to 500 µH 40 µH to 333 µH


High Range Resistance 0 Ωto 8000 mΩ 0 Ωto 4000 mΩ 0 Ωto 2000 mΩ 0 Ωto 1333 mΩ


Reactance Fast 160 µH to 8000 µH 80 µH to 4000 µH 40 µH to 2000 µH 27 µH to 1333 µH


Medium 320 µH to 8000 µH 160 µH to 4000 µH 80 µH to 2000 µH 53 µH to 1333 µH


Slow 960 µH to 8000 µH 480 µH to 4000 µH 240 µH to 2000 µH 160 µH to 1333 µH


**Output On/Off Phase**


**All Models**


Phase Angle Setting Setting range 0.0 degrees to 360.0 degrees (0.0 degrees = 360.0 degrees)


Resolution 0.1 degrees


Accuracy [1] ±1 degrees (≤1 kHz)


1. Lag due to response speed not included


**Harmonics Measurement**


**All Models**


Frequency range (fundamental) 10 Hz ~ 1 kHz


Upper limit harmonic analysis 5 [th] ~ 50 [th]


Measurement items Rms Voltage and Current / Phase angle THD


Current magnitude accuracy [1] 0.15% of reading + 0.20% of full scale (Fundamental 50 Hz or 60 Hz)

1.2% of reading + 1.2% of full scale (Harmonics 2nd ~ 50th)


1.. At an ambient temperature of 23 °C±5 °C


Keysight AC6900 Operating and Programming Guide 29


1 Getting Started


**Protection and Limits**





Overvoltage (OVP) RMS 14.0 V to 500.5 V 0.1 V


Positive peak +14.0 V to +500.5 V 0.1 V


Negative peak             - 500.5 V to -14.0 V 0.1 V


Undervoltage (UVP) 0.0 V to 500.5 V 0.1 V


Overcurrent (OCP) Trip timer 0 s [1] to 10 s 1 s


Watchdog (WDP) Delay timer 1 s to 3600 s 1 s


**Limit Functions** **Settings Range** **Resolution**


Voltage Limit Upper & Lower AC volt 0.0 V to 322.0 V 0.1 V


Upper & Lower DC volt -455.0 V to 455.0 V 0.1 V



Current Limit Current limit Maximum current limit ×0.1 to


maximum current limit ×1.1


Positive & Negative limit Maximum current limit ×0.1 to


maximum current limit ×4.2



0.01 A (0.35 A to 100.0 A)

0. 1A (100.0 A to 1000 A)


0.01 A (0.35 A to 100.0 A)

0. 1A (100.0 A to 1000 A)



Frequency Limit 1.00 Hz to 5000 Hz 0.01 Hz (1.00 Hz to 99.99 Hz)
0.1 Hz (100.0 Hz to 999.9 Hz)

1 Hz (1000 Hz to 5000 Hz)


1. Response time is minimized by setting it to 0 seconds. Minimum response time 200 ms or less.


**Communication Interface**


**All Models**


Software protocol IEEE Std 488.2-1992


Command Language Complies with SCPI Specification 1999.0


USB Complies with the USB 2.0 specifications; data rate: 480 Mbps (high speed), socket B type, self-powered


Message terminator LF or EOM during reception, LF + EOM during transmission


Device class Complies with the USBTMC-USB488 device class specifications


LAN IEEE802.3, 100Base-TX Ethernet

LXI Rev.1.5 2016

(Extended Functions: VXI-11, HiSLIP), data rate: 100 Mbps (auto negotiation, Full Speed)

AUTO MDIX function

IPv4, RJ45 connector, category 5, straight cable


Communication protocol VXI-11, HiSLIP, SCPI-RAW, SCPI-Telnet


Message terminator VXI-11 and HiSLIP: LF or END during reception, LF + END during transmission


SCPI-RAW: LF


GPIB option Complies with IEEE Std 488.1-1987
SH1, AH1, T8, L4, SR0, RL0, PP0, DC0, DT0, C0, E1
24-pin connector (receptacle)


Message terminator LF or EOI during reception, LF + EOI during transmission


Primary address 1 to 30


30 Keysight AC6900 Operating and Programming Guide


1 Getting Started


**Signal I/O**


**General**


**All Models**


Insulation resistance Primary - terminals signal I/O 500 Vdc, 10 MΩor more


Output - terminals signal I/O 500 Vdc, 10 MΩor more


Input terminals - Output terminals Non-isolated (shared common circuit)


Withstanding voltage Primary - terminals pins 1.5 kVac for 1 minute


Output - terminals pins 1.5 kVac for 1 minute


Isolation voltage ±42 V peak


**Analog Input**


**Analog INPUT (Analog In connector, Ch.A to Ch.C)** **All Models**


Number of channels 3 channels [1]


Terminal DSUB 9-pin


Input resistance 10 kΩ±10% (unbalanced)


Maximum allowable input voltage ±12 V


Control system selection EXTDC Mode Amplifies the input waveform


VPROG Mode Changes the AC voltage, DC voltage, or frequency output using a DC signal


Input voltage range ±10 V


Gain Setting range EXTDC Mode x5.00 to x220.00


VPROG Mode x5.00 to x50.00


Resolution 0.01


Offset adjustment range ±200 (equivalent to ±200 mV through input voltage conversion)


EXTDC mode Output voltage tr/tf [2] Main unit specification +50 μs (typical)


Output distortion Main unit specification +0.2% (0.1% or less, when receiving sine wave) (typical)


Output voltage error ±3.6 V (when gain and offset are at default values)


Output voltage temperature coefficient 200 ppm/°C (typical)


Setting delay time 10 μs (typical)


1. The common lines of the analog input signals, digital input signals, and selectable I/O signals are not isolated.


2. At 10 % to 90 % of the output voltage.


Keysight AC6900 Operating and Programming Guide 31


1 Getting Started


**Digital I/O**


**Input Control (Channels 1, 2, 3, and 4)** **All Models**


Input Internal Circuit pull-up to 5 V by a 1.7 kΩ


Maximum Voltage Rating 10 Vdc


Maximum Low-level Input Voltage 0.8 Vdc


Minimum High-level Input Voltage 4 Vdc


Low-level Current 1.8 mA (typical)



Selectable input
control signals



DISABLE No enabled functions


Output On/Off Output on/off control


Sequence Run/Stop Sequence run/stop control


Protection Clear Protection clear


External Alarm External alarm input


Wiring Control 1P Output method change (single-phase)


Wiring Control 1P3W Output method change (single-phase, three-wire)


Wiring Control 3P Output method change (three-phase)


Volt Range Output range change


Recall Output Preset Slot 1 Preset Slot 1 recall


Recall Output Preset Slot 2 Preset Slot 2 recall


Output Inhibit Output-on inhibit


Programmable Signal Programmable signal input



Fixed output signal Shutdown Shutdown signal input


Logic polarity Selectable


**Ouptut Status (Channels 5, 6, 7, and 8)** **All Models**


Output Internal Circuit Photocoupler open-collector output


Maximum Voltage Rating 30 Vdc


Maximum Current 8 mA


Minimum Low-level Voltage 0.7 V DC (typical)



Selectable output
status signals



DISABLE No enabled functions


I pk Limit Status Peak current limit status


Overload Status Overload status


Busy Status Busy status


Wiring Control 1P Output method status (single-phase)


Wiring Control 1P3W Output method status (single-phase, three-wire)


Wiring Control 3P Output method status (three-phase)


Voltage Range H Status Output voltage H range status


Power ON Status POWER switch on status


SEQ Status Out Sequence/power line abnormality simulation status output


Programmable Signal Programmable signal status



Fixed output signal Alarm status Alarm status


Logic polarity Selectable


32 Keysight AC6900 Operating and Programming Guide


1 Getting Started



**Configurable I/O (Channels 9 and 10)** **All Models**


Input Maximum Voltage Rating 5 Vdc


Maximum Low-level Input Voltage 0.8 Vdc


Minimum High-level Input Voltage 2 Vdc


Output Maximum Low-level Output Voltage 0.55 Vdc


Minimum High-level output Voltage 3.8 Vdc


Minimum Low-level SInk Current 12 mA



Configurable input sig
nals


Configurable output
signals



DISABLE No enabled functions


Sync Clock In Output reference phase input


SEQ Trigger In Sequence trigger input


Output Control Output on/off


Sequence Execution Control Sequence run/stop


Alarm Clear Alarm clear


External Alarm External alarm input


Wiring Control 1P Output method change (single-phase)


Wiring Control 1P3W Output method change (single-phase, three-wire)


Wiring Control 3P Output method change (three-phase)


Volt Range Output range change


Recall Output Preset Slot 1 Preset Slot 1 recall


Recall Output Preset Slot 2 Preset Slot 2 recall


Output Inhibit Output-on inhibit


Programmable Signal Programmable signal input


DISABLE No enabled functions


Standard Clock Out Output reference phase output


SEQ Trigger Out Sequence trigger output


Output ON Status Output on status


I Pk Limit Status Limit status


Overload Status Overload status


Busy Status Busy status


Wiring 1P Status Output method status (single-phase)


Wiring 1P3W Status Output method status (single-phase, three-wire)


Wiring 3P Status Output method status (three-phase)


Volt Range H Status Output voltage H range status


Power ON Status POWER switch on status


SEQ Status Out Sequence/power line abnormality simulation status output


Programmable Signal Programmable signal status



Logic polarity Selectable


Keysight AC6900 Operating and Programming Guide 33


1 Getting Started


**Parallel Operation**


**All Models**


Maximum number of units in parallel operation N(units in parallel) ≤4


Power capacity [1] [Capacity of one unit]×N



Output voltage
stability



Load regulation from 0 to 100% variation within the rating


Variation according to output frequency for variation within the rated range



±0.5 V or less (≤100 Hz) [2]


±1.2% or less (≤1 kHz) [2 3]



Total harmonic distortion 0.5% or less (≤100 Hz) [2]


Output voltage response speed Tr/Tf 100μs (typical w/response Medium) / 300 μs (typical w/response Slow) [4]


Ammeter, Wattmeter Displays the total value of parallel operation

of parallel operation on the primary unit [5]


1. The maximum rating is limited to 48 kVA for single-phase and 96 kVA for single-phase three-wire, regardless of the number of units in parallel.


2. Value at the OUTPUT terminal block of the master unit for output voltage 80 V to 160 V or 160 V to 320 V, load power factor 1


3. Voltage variation over 40 Hz to 5 kHz in AC mode with 55 Hz as the reference


4. At 10 % to 90 % of the output


5. The resolution varies depending on the output capacity during parallel operation


**General**


**General** **All Models**



Insulation

resistance


Withstand

voltage



Primary-to-chassis
Output-to-chassis
Primary-to-output


Primary-to-chassis
Output-to-chassis
Primary-to-output



500 Vdc, 10 MΩor more


1500 Vac, 2150 Vdc, for 1 minute



Isolation to ground 320 Vrms / 452 Vdc


Electromagnetic compatibility (EMC) [1] Complies with requirements of the following directive and standards

EMC Directive 2014/30/EU
EN 61326-1 (Class A [2] ), EN 55011 (Class A [2], Group 1 [3] )
Applicable under the following conditions:
The maximum length of all cabling and wiring connected to the product must be <3 m.
(excluding the cable that connects to the LAN)


Safety Complies with the requirements of the following directive and standards:
Low voltage directive 2014/35/EU [2]

EN 61010-1 (Class I [4], Pollution Degree 2 [5] )



Environmental

conditions



Operating environment Indoor use, overvoltage category II


Operating temperature range 0 - 40°C (32°F - 104°F)


Operating humidity range 20% rh - 80% rh (no condensation)


Storage temperature range -10 - +60°C (14°F - 140°F)


Storage humidity range 90% rh or less (no condensation)


Altitude Up to 2000 m (6561.7 feet)



34 Keysight AC6900 Operating and Programming Guide


1 Getting Started













Net Dimensions (W x H x D in mm) 430 x 129.2 x 667.5 430 x 262 x 562 430 x 389 x 562 430 x 563 x 562



Overall dimensions (+ wheels, + covers)
(W x H x D in mm)


Weight   (200 V input)

(400 V input)



28 kg 47 kg
49 kg



440 x 160 x 720 445 x 345 x 650 445 x 475 x 650 445 x 660 x 665



71 kg
72 kg



104 kg

106 kg



Input terminal M6 M5 M5 M8 (200 V input)
M5 (400 V input)


Output terminal M6 M5 M5 M6


1. Only on models that have the CE marking on the panel.


2. This is a Class A instrument. This product is intended for use in an industrial environment. This product may cause interference if used in residential areas.
Such use must be avoided unless the user takes special measures to reduce electromagnetic emissions to prevent interference to the reception of radio

and television broadcasts.


3. This is a Group 1 instrument. This product does not generate and/or use intentionally radio-frequency energy, in the form of electromagnetic radiation,
inductive and/or capacitive coupling, for the treatment of material or inspection/analysis purpose.


4. This product confirms to Class I. Be sure to ground the protective conductor terminal of this product. If not grounded properly, safety is not guaranteed.


5. Pollution is addition of foreign matter (solid, liquid or gaseous) that may produce a reduction of dielectric strength or surface resistivity.
Pollution Degree 2 assumes that only non-conductive pollution will occur except for an occasional temporary conductivity caused by condensation.


Keysight AC6900 Operating and Programming Guide 35


Keysight AC6900 Operating and Programming Guide

# 2 Installation


**Before Installation or Use**


**Outline Diagrams**


**Rack and Floor Mounting Options**


**Installing an Optional GPIB Board**


**Power Cord Connections**


**Switchboard and Circuit Breakers**


**Interface Connections**


**Quick Start Lesson**


**Output Power Connections**


**Analog and Digital Connections**


**Parallel Connections**


2 Installation

#### **Before Installation or Use**


**Inspect the Unit**


When you receive your instrument, inspect it for obvious shipping damage. If there is damage, notify
the shipping carrier and nearest Keysight Sales and Support Office immediately. Refer to
[www.keysight.com/find/assist. Save all packing and shipping materials in case the unit must be](http://www.keysight.com/find/assist)
returned or moved. Refer to Repackaging for Shipment for further information.


**Check for Items Supplied**


Verify that you received the following items along with your instrument.


Packing list AC6900-90001 1


Digital I/O Connector, D-Subminiature 25-pin plug [1] AC6900-30001 1


Cap bolts [2] - not included with AC6903 units AC6900-40001 8


Cable ties - for bundling sense and load wires AC6900-80001 4


Safety Information booklet 9320-6797 1



Envelope with Certificate of Calibration, China RoHS
Addendum, and South Korea EMC Class A Declaration



9230-0333 1



1 This plug must be installed in the back of the unit to allow the unit to turn on (see item 4 under Rear Panel at a Glance).
2 To be installed in the sides of the unit where the shipping brackets were attached.


Refer to the Packing list for any additional items that may be included with your shipment. If anything
is missing, please contact your nearest Keysight Sales and Support Office.


**Review Safety Information**


This AC source is a Safety Class 1 instrument, which means it has a protective earth terminal. That
terminal must be connected to earth ground through a power source equipped with an earth ground.
Refer to the Safety Notices for general safety information. Before installation or operation, check the
instrument and review this guide for safety warnings and instructions. Safety warnings for specific
procedures are located at appropriate places throughout this guide.


**Location Considerations**


The AC6900 Series instruments are Overvoltage Category II instruments that should only be operated
in a controlled, indoor environment subject to the following restrictions:


Do not operate the instrument near flammable gases or fumes.


38 Keysight AC6900 Operating and Programming Guide


2 Installation


Operating: 0 to 40 °C (32 to 104 °F), 20% to 80% relative humidity, noncondensing


Storage: –10 to 60 °C (14 to 140 °F), 90% or less relative humidity, noncondensing


Altitude: Up to 2000 m


Air Flow: Allow at least 50 cm of free space in front and behind the unit.


Do not block the air intake at the front of the instrument or the exhaust at the rear.


**Moving Considerations for AC6906, AC6912 and AC6918**


When moving or transporting this instrument to the installation location, be sure to observe the
following precautions:


l Raise the stopper on model AC6918. Turn the stopper counterclockwise to raise it. Moving the unit

without raising the stopper may cause injuries if the unit tips over.


l Unlock the four casters under the unit. Be sure to have two or more people move the unit. Exercise

special care when moving the unit over a slope or across steps.


l When using a forklift, slide the forks under the bottom of the unit, check that the unit is stable, and

then raise the unit. This also applies when using a band or harness to move the unit.


l Do not tip the unit on its side or turn it upside down.


After the instrument is positioned in its installation location, ensure that the unit is not moved
accidentally when being operated.


l Lock the four casters under the unit with your foot.


l Lower the stopper on model AC6918. Turn the stopper clockwise to lower it.


Keysight AC6900 Operating and Programming Guide 39


2 Installation

#### **Outline Diagrams**


All dimensions are in millimeters.


40 Keysight AC6900 Operating and Programming Guide


2 Installation


Keysight AC6900 Operating and Programming Guide 41


2 Installation


42 Keysight AC6900 Operating and Programming Guide


2 Installation


Keysight AC6900 Operating and Programming Guide 43


2 Installation

#### **Rack and Floor Mounting Options**


**Rack Mounting - for Models AC6903, AC6906 and AC6912**


Optional Rack mount kits allow the instruments to be installed in a 19-inch EIA rack.


**Heavy Weight**
Do not move or lift the instrument by yourself. Be sure to have two or more people
move the instrument. If you are using a lift or bands to move the instrument, be sure

to slide the lift or bands under the instrument and then raise the instrument.


Standard L-shaped rack rails are required to support the instrument in the rack. The
brackets cannot support the weight of the instruments by themselves.

Do not block the air intake at the front of the instrument or the exhaust at the rear.


KIt Items Description AC69RAC3 Qty. AC69RAC6 Qty. AC69RAC12 Qty.


1 Brackets 2 2 2


2 Flat Head Screws M4x0.7x10 4 8 8


3 Dress Screws with Nylon Washer 4 8 8


4 Clip Nuts for Rack Frame 10-32 0.5-in 4 8 8


1. Remove the feet or

casters from the bottom of

the unit. (AC6903 and
AC6906 shown)


2. Use the supplied flat head screws (M4x10) to attach the brackets to the unit.


AC6903 AC6906 AC6912


44 Keysight AC6900 Operating and Programming Guide


2 Installation



3. Install suitable rack rails to support the unit.
Use the supplied dress screws and clip nuts (10-32), to mount the unit in the rack.


AC6903 AC6906 AC6912


**Floor Mounting - for Model AC6918**


The optional Hold Angle kit allows the instrument to be mounted to the floor.


1 L-shape anchor 4


2 Bolt M10x35 8


3 Spring washer M10 8


4 Washer M10 8


Four anchor bolts (not provided) are also required to attach the instrument to the floor.



1. Mark and drill the locations on the floor where

the anchor bolts will be installed.



2. Install the four L-shape anchors to the right and

left sides of the base of the instrument. Then fix the

instrument with anchor bolts.



Keysight AC6900 Operating and Programming Guide 45


2 Installation

#### **Installing a GPIB Board Accessory**


By using the GPIB interface board accessory, you can control this product through the GPIB.


Refer to Keysight Sales and Support Office for information about ordering the AC69GPBU GPIB board.


Install the interface board in the single SLOT, or in SLOT 3 on the rear panel. Refer to Rear Panel at a

Glance for the location.


To install a board:


1. Check that the power switch is turned off.


2. Touch the grounded metal to discharge your static electricity.


3. For Model AC6903, unscrew the single SLOT cover screws and remove the cover. For all other

Models, unscrew the SLOT 3 cover screws and remove the cover. Keep the cover in case the interface

board is ever removed.


4. Hold the board so that the printed circuit board side is facing up.


5. Slide the board all the way into the slot until the board's connector is fully inserted into the slot con

nector at the back.


6. Use the slot cover screws that you removed in step 3 to secure the board.


46 Keysight AC6900 Operating and Programming Guide


2 Installation

#### **Power Cord Connections**


**Connecting the AC6903**


**Connecting the AC6906 and AC6912**


**Connecting the AC6918**


A power cord is not supplied with this product. This product complies with IEC Overvoltage Category II
(energy-consuming equipment supplied from a fixed installation).


Risk of Electric Shock Turn off the switchboard circuit breaker before connecting the
cord. Do not use the terminal block with the terminal cover removed. Always ensure that
the power cord ground is connected.


Risk of Fire . Have a qualified engineer connect the power cord to the switchboard.


Ensure that all input connections are correct . AC6900 series accept both Wye and Delta
3-phase power input for both the 200 V and 400V models. For the 400 V input models,
the Neutral phase must be connected . Damage to the instrument may result if the

Neutral is not connected.


The AC6900 Series instruments may be hard wired to the utility mains. Refer to
Switchboard and circuit breaker requirements for guidance on branch circuit and circuit
breaker sizing. A switchboard circuit breaker disconnect must be provided.


**Power Cord Requirements**


A power cord is not supplied with this product.


The cable cross-section must be suitable for the maximum input current of the
instrument. The ground cable must have the same cross-section as the phase cable.





AC6903 100/200 V 1P Single core, 3 pcs 14 mm [2] or more 48 A M6


AC6906 200 V 3P Single core, 4 pcs 5.5 mm [2] or more 27 A M5


400 V 3P Single core, 5 pcs 5.5 mm [2] or more 14 A M5


AC6912 200 V 3P Single core, 4 pcs 14 mm [2] or more 53 A M5


400 V 3P Single core, 5 pcs 5.5 mm [2] or more 28 A M5


AC6918 200 V 3P Single core, 4 pcs 22 mm [2] or more 80 A M8


400 V 3P Single core, 5 pcs 8 mm [2] or more 42 A M5


Keysight AC6900 Operating and Programming Guide 47


2 Installation


Prepare a power cord that meets the above specifications. The temperature rating of the power cable
should be higher than 70 degrees Celsius.


The following table gives the tightening torque of the input terminal screws.


M5 2.0 2.4


M6 2.5 3.0


M8 5.5 6.6


**Connecting the AC6903**


1. Check that the AC power supply meets the instrument's nominal input rating, which is either singlephase 100 VAC to 120 VAC, or single-phase 200 VAC to 240 VAC. The frequency is 50 or 60 Hz.


2. Check that the power switch is turned off.


3. Unfasten the two M4 screws from the AC INPUT terminal block cover and remove the cover.


4. Run the poser cable through the opening in the input cover as shown. Connect the power cord
according to the labeling on the INPUT terminal block. Always connect the ground wire first.


5. Fasten the cover that you removed in step 3 with the two screws.


8. Turn off the switchboard's circuit breaker.


7. Connect the power cord according to the indication on the switchboard.


8. Turn on the switchboard's circuit breaker. This completes the connections.


48 Keysight AC6900 Operating and Programming Guide


2 Installation


**Connecting the AC6906 and AC6912**


1. Check that the AC power supply meets the instrument's nominal input rating, which is either 200
VAC 3-phase, or 400 VAC 3-phase. The frequency is 50 or 60 Hz.


2. Check that the power switch is turned off.


3. Unfasten the five M4 screws from the terminal block cover and remove the cover.


4. Connect the power cord according to the labeling on the INPUT terminal block. Always connect the
ground wire first.


200 V input


400 V input


Damage to the instrument may result

if the Neutral is not connected.


5. Turn off the switchboard's circuit breaker.


6. Connect the power cord according to the indication on the switchboard.


7. Loosen the two screws until the INPUT terminal cover can move.


Keysight AC6900 Operating and Programming Guide 49


2 Installation


8. Fasten the terminal cover you removed in step 3 with the three screws.


9. Slide the INPUT terminal cover until it just touches the wires, and then fasten the screws.


10. Connect the switchboard end of the power cord to match the switchboard connections.


11. Turn on the switchboard's circuit breaker. This completes the connections.


**Connecting the AC6918**


1. Check that the AC power supply meets the instrument's nominal input rating, which is either 200
VAC 3-phase, or 400 VAC 3-phase. The frequency is 50 or 60 Hz.


2. Check that the power switch is turned off.


3. Unfasten the five M4 screws from the terminal block cover and remove the cover.


4. Connect the power cord according to the labeling on the INPUT terminal block. Always connect the
ground wire first.


50 Keysight AC6900 Operating and Programming Guide


200 V input


400 V input


5. Turn off the switchboard's circuit breaker.



2 Installation


Damage to the instrument may result

if the Neutral is not connected.



6. Connect the power cord according to the indication on the switchboard.


7a. For the 200 V input model, remove the two M4 screws, change the orientation of the INPUT
terminal cover, and fasten with the screws you just removed. Tighten the screws loosely so that the

INPUT terminal cover can still be moved.


7b. For three-phase 400 V input model, loosen the two M4 screws until the INPUT terminal cover

moves.


8. Fasten the terminal cover you removed in step 3 with the five screws..


9. Slide the INPUT terminal cover until it touches the wires, and then fasten the screws.


Keysight AC6900 Operating and Programming Guide 51


2 Installation


10. Connect the switchboard end of the power cord to match the switchboard connections.


11. Turn on the switchboard's circuit breaker. This completes the connections


52 Keysight AC6900 Operating and Programming Guide


2 Installation

#### **Switchboard and Circuit Breaker Requirements**


Have a qualified engineer connect the power cord to the switchboard.


Turn off the switchboard circuit breaker to disconnect the instrument from the AC line in

an emergency. The breaker must be suitably located and easily reached, and it must be
marked as the disconnecting device for the equipment.


This section is provided for guidance only; consult with local experts to ensure strict compliance with
all local electrical code and safety requirements. These requirements take precedence over any
guidance provided in this section.


l Each phase requires a separate breaker.


l Dedicate the circuit breaker for models AC6903, AC6906, AC6912, and AC6918.


l Keep the switchboard easily accessible at all times.


l Apply a label to the switchboard, clearly identifying the disconnecting device and its associated


model.


l Be sure to connect the wires correctly between the switchboard and the input terminals of the

product as shown:


Single-phase 100/200 V input: L, N and GND.


Three-phase 200 V input: L1, L2, L3, and GND.


Three-phase 400 V input: L1, L2, L3, N, and GND.


The following table provides information about worst case current consumption for all AC6900 Series
models for two nominal AC mains voltages. Entries are obtained by dividing the worst case power
consumption (in VA) by the nominal mains voltage and rounding to the next highest integer value. Line
currents for other nominal voltages may be calculated similarly.


**Maximum Input VA and Current**


**AC6903** **AC6906** **AC6912** **AC6918**


Max Input kVA 4 kVA 7.8 kVA 15.6 kVA 23.4 kVA


Max current @ 100 V line ~48 A N/A N/A N/A


Max current @ 200 V line ~24 A ~27 A ~53 A ~80 A


Max current @ 400 V line N/A ~14 A ~28 A ~42 A


**Breaker Sizes**


Consult with local authorities to ensure full compliance with electrical code and safety requirements
before connecting any AC6900 Series instrument.


Keysight AC6900 Operating and Programming Guide 53


2 Installation


**Line Voltage** **AC6903** **AC6906** **AC6912** **AC6918**


100 V line 75 A N/A N/A N/A


200 V line 40 A 50 A 75 A 125 A


400 V line N/A 30 A 40 A 60 A


Different regions of the world have different sizing requirements for branch circuit conductors and
circuit breakers. In Europe and other regions where IEC standards apply, circuits breakers generally
are rated at 100% utilization, meaning that a device connected to a dedicated branch circuit may draw
a maximum current up to the circuit breaker rating. In the United States, the NEC generally specifies
what is known as the "80% rule" which requires branch circuits to be rated at 1.25 times the maximum
nameplate rating of the connected device.


Standard circuit breaker sizes also vary by region. The table below provides guidance for circuit
breaker sizing for various nominal mains voltages. The guidance for North American mains voltages

includes the 1.25 factor associated with the 80% rule.


**IEC 60898-1 and European Standard EN 60898-1 Standard Sizes**


6, 10, 13, 16, 20, 25, 32, 40, 50, 63, 80, and 100 A


**NEMA Standard Sizes (also commonly used in Japan)**


15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, and 100 A


54 Keysight AC6900 Operating and Programming Guide


2 Installation

#### **Interface Connections**


**GPIB Connection**


**USB Connection**


**LAN Connection**


This section describes how to connect to the instrument's various communication interfaces. For

additional information about configuring and using the communications interfaces, refer to Remote
Interface Configuration.


To begin, please download and install the Keysight IO Libraries Suite from
[www.keysight.com/find/IOlibraries.](http://www.keysight.com/find/IOlibraries)


Refer to Rear Panel at a Glance for the location of the interface connectors.


**GPIB Connection**


1. Connect your computer to your instrument's GPIB interface accessory using a GPIB interface cable.

The GPIB card should be installed in the appropriate accessory slot as shown under Rear Panel at a

Glance.


2. Use the Connection Expert utility of the Keysight IO Libraries Suite to connect to the installed GPIB


interface card.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments.


**USB Connection**


1. Connect the instrument's rear-panel USB device port to a USB port on your computer.


2. With the Connection Expert utility of the Keysight IO Libraries Suite running, the computer will auto
matically recognize the instrument. This may take several seconds. When the instrument is recognized, your computer will display the VISA alias, IDN string, and VISA address. This information is

located in the USB folder.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments.


Keysight AC6900 Operating and Programming Guide 55


2 Installation


**LAN Connection**


1. Connect a LAN cable from the instrument's rear-panel LAN port to the site LAN or your computer. The

instrument's factory default LAN settings automatically obtain an IP address from the network using a
DHCP server (DHCP is on). The DHCP server will register the instrument’s host name with the
dynamic DNS server. The host name and IP address can then be used to communicate with the instrument. If you are using a private LAN, you can leave all LAN settings as they are. The instrument will
automatically choose an IP address using AutoIP if a DHCP server is not present. The instrument
assigns itself an IP address from the block 169.254.nnn. The Lan indicator appears in the lower right
corner of the display when the LAN port has been configured.


2. Use the Connection Expert utility of the Keysight IO Libraries Suite to add the instrument and verify a

connection. To add the instrument, you can request the Connection Expert to discover the instrument. If the instrument cannot be found, add the instrument using its host name or IP address.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments. You can also use your
computer's Web browser to communicate with the instrument. See Using the Web Interface.


56 Keysight AC6900 Operating and Programming Guide


2 Installation

#### **Quick Start Lesson**


**Turn the Unit On and Off**


**Set the Output Voltage**


**Set the Output Current Limit**


**Use Built-in Help System**


**Turn the Unit On and Off**


To turn the instrument on:


1. Check that nothing is connected to any output terminal block on the instrument.


2. Check that the power cord is of the correct type and correctly connected.


3. Check that the supplied AC6900-30001, external Digital I/O Connector (D-Sub 25 Pin Plug) is con
nected to the DIGITAL I/O port on the rear panel (see item 4 under Rear Panel at a Glance).


4. Check that the POWER SELECTOR switch on the front panel is set to "Primary".


5. Flip the POWER switch up to turn the instrument on.


The following screen will appear on the display. It shows information about all three output phases and
a combined view showing the line information of all outputs.


Keysight AC6900 Operating and Programming Guide 57


2 Installation


Pressing the Meter key will sequence through all of the meter views.


It takes about 10 seconds for the instrument to initialize before it is ready for use.


If the instrument does not turn on, verify that the power cord is firmly connected (power-line voltage is
automatically sensed at power-on). Also make sure that the instrument is connected to an energized

power source.


When the POWER switch is turned on for the very first time, the unit starts in the factory default
condition. When the power is turned on the next time, the product starts with the settings that were in

use the last time that the POWER switch was turned off. You can set the condition that the unit will be

in when the power is turned on. Refer to Turning the Ouptut On and Off for more information.


**Set the Output Voltage**


**Method 1**


Select an output to control. Use the Phase keys to select the output phase.


The selected area of the display will change to allow you to edit the active fields in your selection. In
the following example, Phase 3 has been selected.


Use the Navigation keys to select the field you wish to edit. The field can be edited when it appears
highlighted.


58 Keysight AC6900 Operating and Programming Guide


2 Installation


Use the Numeric Entry keys to select a voltage for Phase 3. Press Enter to confirm and set the value.


Note: Use the RPG knob to

increment or decrement

the value that is already in

the field.


**Method 2**


Alternatively, you can also select the [Source Settings] softkey to access the available source settings.
Note that the title bar of the display changes to indicate where you are in the front panel menu.


As previously described, use the Phase keys to select the output phase. In the above example Phase 1

has been selected.


As previously described, use the Navigation keys and the Numeric Entry keys to select a voltage for

Phase 1 Press Enter to confirm and set the value.


Entering a value in the ALL phases fields panel will overwrite the values in the corresponding Phase 1
through Phase 3 fields


**Set the Output Current Limit**


To limit the output current, press the [Protection Settings] softkey to access the protection and limit
settings. Then select the [Limits] softkey.


The current limit operates on the rms value of the output current. The default action taken when the
current limit is exceeded is that the output will shut down due to an Overcurrent Protection (OCP). If
OCP is disabled, the output current will be limited to the maximum current setting and the voltage will


Keysight AC6900 Operating and Programming Guide 59


2 Installation


be decreased to maintain the current at its limit setting. Refer to Setting Protection for more

information.


As previously described, use the Navigation keys and the Numeric Entry keys to select a maximum
current limit value for a specified phase or all phases. Press Enter to confirm and set the value.


Entering a value in the Current ALL phases field will overwrite the values in the corresponding Phase 1
through Phase 3 fields.


**Use the Built-in Help System**


**View Errors**


If a self-test error occurs, a message is displayed on the title bar. For self-test and other error
messages, refer to Error Messages for more information.


Select the Utilities - Error softkeys to display any error messages.


60 Keysight AC6900 Operating and Programming Guide


2 Installation


**View Help**


Select the Utilities - Help softkeys to view built-in help at any time.


Press the Meter key to immediately exit the front panel menu.


Flip the POWER switch down to turn the instrument off.


Keysight AC6900 Operating and Programming Guide 61


2 Installation

#### **Output Power Connections**


**Preparation**


**Connecting the Load Cables (AC6903)**


**Connecting the Load Cables (AC6906, and AC6912)**


**Connecting the Load Cables (AC6918)**


**Connecting the Remote Sense Wires**


**Preparation**


Risk of Electric Shock


Before making any load or sense connections be sure to turn the POWER switch off and

turn off the circuit breaker of the switchboard.


Even with power turned off, residual voltage may remain at the output terminals. Do not
touch the output terminal block for at least 20 seconds after the power is tuned off.


Always attach the OUTPUT terminal cover so that the terminals are not exposed during
operation.


Use load cables with a voltage rating that meets or exceeds the product’s isolation
voltage (±452 V).


Use load cables whose capacity is adequate for the maximum output current and with
sturdy, flame-resistant insulation


When using single-phase output, be sure to connect three cables each for L and N. There
is a risk of touching the unused terminals through the gap of the OUTPUT terminal cover.


A sudden load variation exceeding the rating may cause a large overshoot.


To avoid risk of heat buildup or spark emission, fasten the wires securely using the
recommended torque.


Connecting a voltage source such as a battery to the instrument will result in current
flowing from the voltage source to the instrument and may damage the instrument. If you
need to connect a voltage source to the instrument, you must provide a switch between
the voltage source and the instrument, match the output voltage to the voltage source,
and then connect the voltage source.


62 Keysight AC6900 Operating and Programming Guide


2 Installation


The L and N terminals of the OUTPUT terminal block are isolated from the input power
supply. The polarity does not constitute a problem in terms of safety. The polarity matters in synchronous mode (in which the product is synchronized with the input power
supply) and DC mode, so check the polarity of the load before you connect it to the
product. You can use either L or N to ground the product.


**Wire Requirements**


Use noncombustible load wires rated to carry the maximum rated output current.











0.9 18 0.82 17


1.25 16 1.31 19


2 14 2.08 27


3.5 12 3.31 37


5.5 10 5.26 49


8 8 8.37 61


14 6 13.3 88


22 4 21.15 115


30 2 33.62 139


38 1 42.41 162


50 1/0 53.49 190


60 2/0 67.42 217


80 3/0 85.03 257


100 4/0 107.2 298


The values vary depending conditions such as the wire covering (insulator) and material (allowable
temperature) and whether they are multi-core cables. For cables other than those specified in the
table above, please consult with qualified personnel.


The following table provides the maximum output current that each model can generate.





AC6903 Low range 30 A 10 A


High range 15 A 5 A


AC6906 Low range 60 A 20 A


High range 30 A 10 A


AC6912 Low range 120 A 40 A


High range 60 A 20 A


AC6918 Low range 180 A 60 A


High range 90 A 30 A


Keysight AC6900 Operating and Programming Guide 63


2 Installation


The following table gives the tightening torque of the output terminal screws.


M5 2.0 2.4


M6 2.5 3.0


M8 5.5 6.6


**Output Terminal Covers**


When the product is shipped from the factory, an OUTPUT terminal cover is attached to keep the
OUTPUT terminals from becoming exposed. If you are not using the OUTPUT terminal block, attach the
terminal cover so that the terminals are not exposed.


AC6903


AC6906, and AC6912


AC6918


Connecting the Load Cables (AC6903)


1. Check that the power switch is turned off.


2. Turn off the switchboard’s circuit breaker.


64 Keysight AC6900 Operating and Programming Guide


2 Installation


3. Unfasten the two M3 screws from the OUTPUT terminal block cover and remove the cover.


4. Attach the shorting bar for single-phase output only.
Remove the shorting bar for three-phase output and single-phase 3-wire output.


5. Securely connect the load cables to the OUTPUT terminal block. Connect the power cord according
to the labeling on the INPUT terminal block. Always connect the ground wire first.


6. Fasten the cover that you removed in step 3 with the two screws. This completes the connections.


Connecting the Load Cables (AC6906, and AC6912)


1. Check that the power switch is turned off.


2. Turn off the switchboard’s circuit breaker.


3. Remove the five M4 screws from the terminal block cover, and remove the cover.


Keysight AC6900 Operating and Programming Guide 65


2 Installation


4. Securely connect the load wires to the OUTPUT terminal block. The length of the load cables should

be less than 30 meters. If the load has a ground (GND) terminal, be sure to connect it to the G terminal of the instrument's OUTPUT terminal block. Be sure to use a wire that is greater than or equal

to the diameter of the wires used to connect the load.


5. Attach the OUTPUT terminal cover so that the unused terminals are not exposed.


6. Fasten the terminal cover you removed in step 3 with the five screws (1-phase shown). This com
pletes the connections.


66 Keysight AC6900 Operating and Programming Guide


2 Installation


Connecting the Load Cables (AC6918)


1. Check that the power switch is turned off.


2. Turn off the switchboard’s circuit breaker.


3. Remove the five M4 screws from the terminal block cover, and remove the cover.


4. Securely connect the load wires to the OUTPUT terminal block. The length of the load cables should

be less than 30 meters. If the load has a ground (GND) terminal, be sure to connect it to the G terminal of the instrument's OUTPUT terminal block. Be sure to use a wire that is greater than or equal

to the diameter of the wires used to connect the load.


Keysight AC6900 Operating and Programming Guide 67


2 Installation


5. To use three-phase output or single-phase three-wire output, remove the OUTPUT (N) terminal cover


from the terminal block cover.


6. Remove the two M4 screws, change the orientation of the INPUT terminal cover, and fasten with the

screws you just removed. Tighten the screws just enough so that the OUTPUT terminal cover can still

be moved.


7. Fasten the terminal cover you removed in step 3 with the three screws.


8. Slide he OUTPUT terminal cover until it touches the wires, and then fasten the screws.


9. To use the three-phase output or single-phase three-wire output, attach the OUTPUT (N) terminal

cover so that the unused terminals are not exposed. This completes the connections.


1-phase 3-wire output 3-phase output


68 Keysight AC6900 Operating and Programming Guide


2 Installation


**Connecting the Remote Sense Wires**


Risk of Electric Shock Before making any load or sense connections be sure to turn the

POWER switch off and turn off the circuit breaker of the switchboard. Do not touch the

output terminal block for at least 20 seconds after the power is tuned off.


Remote sensing compensates for voltage drops in long load cables by monitoring the output voltage
directly at the load, The sensing function can compensate up to 1 Vrms for a single load line. Always
select a load wire that is thick enough to prevent the voltage drop in the wire from exceeding the
compensation voltage.


1. Check that the power switch is turned off.


2. Turn off the switchboard’s circuit breaker.


3. Remove the five M4 screws from the terminal block cover, and remove the cover..


4. Insert the wires into the sense connector as shown. Observe the illustrated precautions.


5. Twist the sense wires together. Use the supplied cable ties to bundle the sense wires. Firmly tighten

the cable tie so that the sensing cables do not come loose.


Keysight AC6900 Operating and Programming Guide 69


2 Installation


6. Securely connect the sense wires to the OUTPUT terminal block and the Load terminals as shown in

the following wiring diagrams. Twist or bundles the sense wires. Connect the sense wires as close to
the load as possible.


AC6903


AC6906 AC6912 AC6918


7. Re-attach the terminal cover. For AC6906, and AC6912, run the sensing wires through the gap in the

location shown below. For AC6903 and AC6918, run the sensing wires through the gap in the
OUTPUT terminal block. This completes the remote sensing connections.


70 Keysight AC6900 Operating and Programming Guide


2 Installation

#### **Analog and Digital Connections**


**Analog Connections**


**Digital Connections**


This section describes how to connect to the instrument's analog and digital interfaces. Refer to Rear

Panel at a Glance for the location of the interface connectors.


**Analog Connections**


There are two available analog input functions, which are mutually exclusive. You can apply external
signals to change the AC voltage, DC voltage, or frequency. You can also amplify input signals that are
applied to the connector channels.


The following are the pin assignments for the Analog IN connector. Refer to Keysight Sales and
Support Office for information about ordering the AC69ALGU analog input mating connector.


1 CH A


2 CH B


3 CH C


4, 5 not used


6 CH A common


7 CH B common


8 CH C common


9 not used


When the instrument is being controlled using external analog signals, voltage limits
cannot be set. Applying an excessive external voltage by mistake can damage the load.


Keysight AC6900 Operating and Programming Guide 71


2 Installation


**Digital Connections**


This section describes the wiring considerations of the digital IO signals lines.


The following are the pin assignments for the Digital IN connector. Refer to Keysight Sales and
Support Office for information about ordering the AC69DIGU digital interface connector for making
your wiring connections


1 CH 1 Input 14 CH 1 common


2 CH 2 input 15 CH 2 common


3 CH 3 input 16 CH 3 common


4 CH 4 input 17 CH 4 common


5 not used 18 not used


6 CH 5 output 19 CH 5 common


7 CH 6 output 20 CH 6 common


8 CH 7 output 21 CH 7 common


9 CH 8 output 22 CH 8 common


10 not used 23 not used


11 CH 9 input/output 24 CH 9 common


12 CH 10 input/output 25 CH 10 common


13 not used


**Input Control Signals**


The following illustration show connections for one Input Control channel at pins 1 and 14.


For normal wiring For long distance wiring,
replace switch S with a relay


72 Keysight AC6900 Operating and Programming Guide


2 Installation


The following recommendations are provided for guidance when connecting to the Digital port.


To reduce the influence of noise on the signal, connect a 2-core shielded wire or a twisted-pair wire,
and keep the wire as short as possible. If the wiring is long, it is easy for noise to interfere with the
operation of the digital signals. Use the following recommendation for long distance wiring.


The maximum open-circuit voltage across input pins is approximately 12 V, and the maximum shortcircuit current is approximately 7.1 mA (the internal circuits are pulled up to 12 V with 1.7 kΩ resistors).


l Use external contacts that have a contact rating greater than or equal to 7.1 mA at 12 Vdc.


l High-level input voltage: 11 V to 12 V, or open


l Low-level input voltage: 0 V to 1 V


l You can set the active signal polarity for each channel.


l If you are using a shielded cable, connect the shield to the connector shell.


**Output Status Signals**


The output signal is an open-collector output from a photocoupler (30 Vdc, 8 mA max.). It is isolated
from the inside of the product. You can set the active signal polarity for each channel.


l Maximum voltage: 30 V


l
Maximum current (sink): 8 mA


Keysight AC6900 Operating and Programming Guide 73


2 Installation

#### **Parallel Connections**


**Switch Settings**


**Connecting the Optional Communications Cables**


**Connecting the Optional Synchronization Cables**


**Connecting the Output Cables**


**Connecting the Load**


**Turning the Units On and Checking Operation**


**Disconnecting Parallel Operation**


The parallel information in this section does not apply to AC6903 units.


**Switch Settings**


Risk of Electric Shock

Before making any load or sense connections be sure to turn the POWER switch off and
turn off the circuit breaker of the switchboard. Even with power turned off, residual
voltage may remain at the output terminals. Do not touch the output terminal block for at
least 20 seconds after the power is tuned off.


Always attach the OUTPUT terminal cover so that the terminals are not exposed during
operation.


Use load cables with a thickness (conductor cross-sectional area) equivalent to that of

the load cable for each instrument.


Refer to Keysight Sales and Support Office for information about ordering the optional
AC69PAR1 parallel interface connector for making your wiring connections, and the
AC69SYN1 Synchronization cable - for synchronizing the power switches.


Refer to Rear Panel at a Glance for the location of the parallel connectors.


The power capacity of each phase is limited to 48 kVA, and the maximum current is limited to 480 A.


If the response is set to high-speed (FAST), it is changed to normal speed (MEDIUM) during parallel
operation.


The following table describes the switch settings for up to three secondary units connected to one
primary unit.


74 Keysight AC6900 Operating and Programming Guide


2 Installation


**Primary unit** **Secondary 1** **Secondary 2** **Secondary 3**


Address 0 1 2 3


No. of secondary units 1 1


No. of secondary units 2 2 2


No. of secondary units 3 3 3 3


**Connecting the Optional Communications Cables**


Signals flow from the OUT connector to the IN connector. Make sure the IN and OUT connections are
correct. The connector is always installed in the OUT connector of the primary unit.


or


**Connecting the Optional Synchronization Cables**


Connect the optional power synchronize cables. With these cables installed, you can configure the
paralleled system so that when the primary unit is turned on, all the secondary units also turn on.


Signals flow from the J2 connector to the J1 connector. Make sure the J1 and J2 connections are
correct. The connector is always installed in the J2 connector of the primary unit. Push the power
synchronize cables in until they are locked in place


Keysight AC6900 Operating and Programming Guide 75


2 Installation


or


Route the synchronization cable out through the openings in the sides of the terminal cover. Do not
pinch the synchronization cables by the cover.


Set the front panel switches the match the rear panel synchronization settings. When you operate the
primary unit’s POWER switch, the secondary units' power supplies turn on synchronously.


**Connecting the Output Cables**


Refer to the following figure, and connect the OUTPUT terminal block and relay terminals.


76 Keysight AC6900 Operating and Programming Guide


2 Installation


**Connecting the Load**


Risk of Electric Shock

Before making any load or sense connections be sure to turn the POWER switch off and
turn off the circuit breaker of the switchboard. Do not touch the output terminal block for
at least 20 seconds after the power is tuned off.


Always attach the Output terminal cover so that the terminals are not exposed during
operation..


Do not reverse the L and N polarities.


Use noncombustible cables with a thickness appropriate for the output current to
connect from the relay terminal to the load.


Keysight AC6900 Operating and Programming Guide 77


2 Installation


**Turning the Units On and Checking Operation**


Parallel operation is controlled from the primary unit. You cannot control parallel operation from a
secondary unit.


If turn-on synchronization is enabled, turn on the primary unit’s POWER switch.


If synchronization is disabled, turn on the POWER switches on the primary unit and all secondary units

within 10 seconds.


Check the operation of the unit before connecting the load, or open the load's connection relay if the

load is connected.


l POWER switch on


l Voltage setting

l OUTPUT on/off


l Voltage range switching


If a problem occurs on even one unit, you will not be able to perform parallel operation.


**Disconnecting Parallel Operation**


To end parallel operation, turn off the power, and then remove the connection cables from the
PARALLEL connectors and the power synchronize cables from the J1 and J2 connectors. Hold down
the lock lever of the power synchronize cable, and pull it free of the unit.


Set the rotary switches of the number of secondary units and addresses to zero.


78 Keysight AC6900 Operating and Programming Guide


2 Installation


If the power supplies are synchronized, set the POWER Selector switches to Primary with the POWER

switch turned off.


If the current limit setting on the primary unit exceeds the rated current, the current limit will be
changed to the maximum value for that instrument's setting only.


Keysight AC6900 Operating and Programming Guide 79


Keysight AC6900 Operating and Programming Guide

# 3 User Information


**Front Panel Menu Reference**


**Programming the Output**


**Setting Protection and Limits**


**Turning the Output On and Off**


**Displaying Measurements**


**Using Advanced Functions**


**Using Output Controls**


**Using System Utilities**


**Remote Interface Configuration**


**Digital IO Configuration**


3 User Information

#### **Front Panel Menu Reference**


This is an overview of the front-panel menus.


Press any of the softkeys to access the front-panel menus.


The green down arrows are used to indicate that there is a lower level within that menu. If there is no
more down arrow, it indicates that you are at the lowest possible level. The Green up arrow is used to
move back to the upper level.


On front panel softkeys that act as a toggle, the item highlighted in orange is the active selection.
Press the softkey to make the selection.


      - in this example, the front panel menu is unlocked


Note that the title bar of the front panel display is used to show the current menu level. A backslash
symbol is used to indicate the lower level of the menu path.


In the above title bar example, the Advance settings are located under the Source Settings menu.


Use the Back softkey to back out of the menu levels.


**The Menu Tree**


**Menu Heading** **Description**


Source

Settings 

Mode > 3 Phase Configures the terminal wiring for 3-phase



1 Phase


3-wire



Configures the terminal wiring for 1-phase 3-wire



Range

Hi  Lo


Coupling

ACDC  AC  DC


Surge Sup


On  Off



1 Phase Configures the terminal wiring for 1-phase
In 1 phase, all three terminals are combined


Toggles to select the High or Low output voltage


Toggles to select the output coupling
ACDC combines the AC and the DC output signals


Turns output surge suppression On or Off
When Off, the output status shows ~CV



82 Keysight AC6900 Operating and Programming Guide


3 User Information



**Menu Heading** **Description**



Advance > ON Phase


On  Off


OFF Phase


On  Off


Soft Start


On  Off


Soft Stop


On  Off


Sync

Dis  Line  Ext



Selects the On phase and specifies On phase angle


Selects the Off phase and specifies Off phase angle


Selects Soft Start and specifies the output rise time


Selects Soft Stop and specifies the output fall time


Specifies the synchronization source of the output
Also specifies the delay for the Line phase angle



Measure

Settings 

Protection

Settings 


Voltage > Selects Voltage measurements


Current > Selects Current measurements


ACDC Selects ACDC (rms) measurements


AC Selects AC measurements


DC Selects DC measurements


Peak Selects Peak measurements


Peak Clear Clears all Peak measurements



Advance > Sense > Sense

On  Off


SNS Type

Hard  Soft


SNS Point

Out  Sense


SNS Control


AC  DC



Sense sets measurement averaging and peak hold times

Turns the sense controls On or Off


Selects Hard or Soft sensing functions


Selects the sensing point for soft sensing


Selects the output voltage type for soft sensing



Protection > Protection configures OVP, OCP, UVP, and WDP



UVP

On  Off


OCP

On  Off


WDP

On  Off



UVP turns under voltage protection on or off


OCP turns over current protection on or off


WDP turns watchdog protection on or off



Limit > Frequency > Sets limits for Voltage, Max Current, ±Peak Current
Click Frequency to specify frequency limits



Protection


Clear



Click Protection Clear to clear any protections
If the fault persists, the protection will not clear



Keysight AC6900 Operating and Programming Guide 83


3 User Information


**Menu Heading** **Description**


Advance 

Simulation > Simulation configures power line abnormality simulation



SIM

On  Off


Polarity
Pos  Neg


T1 Type
Time  Angle


T5 Type
Time  Cycle



Turn simulation on or off


Specifies the polarity of the simulation


Specifies the T1 setting in time or phase angle


Specifies the T5 setting in time or cycle



Run SIM Runs the simulation


Sequence > Sequence configures a sequence of output steps


Add Click Add to add a new step to the sequence


Delete Deletes the selected step from the sequence


Clear All Removes all sequence steps



Properties > Continuous


On  Off



Properties configures a sequence loop
Turns continuous loop on or off



Harmonic

Analysis >


Output

Control >



Run SEQ Runs the sequence if the initiation is manual


Performs harmonic analysis on the output



Type

Volt  Curr


Harmonic

All  Odd  Even



Specifies the harmonic type


Specifies the harmonic to display



á, â Scroll up or down the harmonic list


Configures the output controls



Impedance


Control


Output
Response


Custom

Waveform


Analog

Input


Parallel


Output



Impedance


On  Off


Speed

S  M  F


Custom Wav


On  Off


Type

Sine Peak Flat


Analog In


On  Off


CTRL Para

All VAC VDC



Impedance Control specifies resistance and reactance
Turns impedance control on or off


Sets the response speed Slow, Medium, or Fast


Configures a custom waveform

Turns the custom waveform on or off


Specify the waveform type to configure


Configures the analog connector pins
Turns the analog control on or off


Specifies the channel control parameter


Shows the address switch and cable connections



84 Keysight AC6900 Operating and Programming Guide


3 User Information



**Menu Heading** **Description**


Utilities 


Store &

Recall >



Store

Settings >


Recall

Settings >


Power On

Settings >


Output

Presets >


Set to

Default



Dest

Int  Ext



Stores settings to internal memory

or to the USB drive



Store When external, select the options to store



Set to

Power On


Source

Int  Ext



Sets storage settings to Power on state

Not available when Destination + External


Recall settings from internal memory

or from the external USB drive



Recall Recall the settings



Set to

Power On


Preset (1, 2)


Store


Preset (1, 2)


Recall



Set the power on settings to the

Power on values


Quickly saves the present settings of the

unit in Preset location 1 or 2


Quickly recalls the settings of the unit

stored in Preset location 1 or 2


Sets the units to the default settings



I/O Config > USB Status > Displays the USB connect string



LAN Status > LAN

Restart


LAN

Reset



Displays the LAN status

Click LAN Restart to restart the LAN


Resets the LAN to the previous settings


Displays the LAN settings

Turns DHCP on or off


Turns AutoDNS on or off


Turns mDNS on or off



LAN

Settings >



DHCP

On  Off


AutoDNS


On  Off


mDNS

On  Off



Keyboard Available when a text field is selected



Set to

Default


Digital I/O > Input

Control


Output


Status



Input Control

(1, 2, 3)


Output status

(5, 6, 7, 8)



Input Control


On  Off


Polarity
Pos  Neg


Mode

Live  Latch


Output Status


On  Off


Polarity
Pos  Neg



Sets the LAN to the factory defaults


Configures Input Control 1, 2, or 3
Turns input control on or off


Sets polarity for Input Control 1, 2, or 3


Sets the mode for the Output inhibit signal


Configures Output Status 5, 6, 7, or 8
Turns output status on or off


Sets polarity for Output Status 5, 6, 7, or 8



Keysight AC6900 Operating and Programming Guide 85


3 User Information


**Menu Heading** **Description**



Config


IO



Config I/O

(9, 10)



Config IO


On  Off


Direction


In  Out


Polarity
Pos  Neg


Mode

Live  Latch



Configures Config IO 9, or 10
Turns config IO on or off


Specifies the IO direction


Sets polarity for Config IO 9, or 10


Sets the mode for the Output inhibit signal



GPIB Status > Displays and sets the GPIB address
Only available with GPIB option.



Turns the Display on or off


Turns the Screen saver on or off


Selects the Phase naming convention
The default is Phase 1, Phase 2, Phase 3


Turns key clicks on or off


Turns the Sleep Function on or off



System
Settings >



User Options > Display

On  Off


Scr Sav

On  Off


Phase Conv

123 ABC UVW


Key Click


On  Off


Sleep

On  Off



Date & Time > Year Specify the year


Month Specify the month


Day Specify the day


Hour Specify the hour


Minute Specify the minute



Advance > Offset

Adjust



Adjusts the offset for the output and sense

terminals



Power Mgmt á, â Selects internal power modules to disable
in order to limit power


Enable All Enables all selected power modules


Disable All Removes power management for all



About > Firmware


Update >



Update Displays instrument information
Also access the firmware update function



Sanitize Permanently deletes all data stored in
instrument memory


Error > Clear Displays errors and clears error display


Help > á, â Selects the help topic



Lock

Unlock



Locks and unlocks the front panel keys



86 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Programming the Output**


**Programming Mode, Range, Coupling, and Suppression**


**Programming AC Voltage**


**Programming DC Voltage**


**Typical Commands**


You can program the output voltage, frequency, frequency limits, and starting phase angle. Before
programming the output, select the appropriate output mode and voltage range.


**Programming Mode, Range, Coupling, and Suppression**


From the front panel softkeys select Source Settings


**Voltage Range, Coupling, and Suppression**


In the Source Settings menu, start by specifying the voltage range. Toggle the Range softkeybutton to
select either Hi or Lo range. The output must be off to change the range settings. The instrument
checks to ensure that currently active settings are within the new range setting.


Next, specify the coupling. Toggle the Coupling softkey to select one of three couplings: ACDC, AC, or
DC. AC produces AC output, DC produces DC output, and ACDC produces AC superimposed on the
DC output.


Refer to Turning the Output On or Off for information about activating voltage Surge Sup pression, and
additional output programming functions available under the Advance softkey.


**Output Mode**


Then select the output Mode softkey, and select one of three output modes.


Check to make sure that the wiring on the back of your unit matches your mode selection. A
confirmation message will ask you to confirm the wiring. The instrument will then configure the
internal relays to match the wiring selection.


Keysight AC6900 Operating and Programming Guide 87


3 User Information


3-phase


1-phase 3-wire


1-phase


The instrument will then configure the internal relays to match the wiring selection.


**Programming AC Voltage**


**Output Voltage, Frequency, and Phase Angle**


The following display shows how to set the voltage, frequency, and phase. Note that the display


indicates that the High range has been selected for all phases, and the coupling is ACDC


88 Keysight AC6900 Operating and Programming Guide


3 User Information


You can specify individual (or independent) settings for each phase (1, 2, or 3), or specify the same
value for ALL phases.


Depending on the Coupling selection you can specify an AC voltage + DC voltage. You can also specify
the phase angle between phases. Note that phase differences other that 120° between phases will


result in an unbalanced output, which will be indicated in the title bar


Settings in the All sub frame are global and apply to all phases. Within the All settings, you can opt to
set the line-to-line voltage instead of the line-to-neutral voltage. This will overwrite any previous
settings in the individual phase setting. The line to neutral voltage setting field will be unavailable as all
three phases will be set to the same value. This also applies to the DC voltage settings.


You can set the output frequency from 40 to 5000 Hz for AC mode and ACDC mode, regardless of
whether the output is on. The frequency can only be set in the All sub frame, and applies to all phases.


The voltage range and coupling can also be set in the All sub frame, but are unavailable when the
output is enabled as shown in the example above (the CV status indicates that the output is enabled).


**Output Settings in 1 phase 3-wire and 1 phase modes**


Not all settings in the All sub frame can be changed. It depends on the output mode. For example, in
three-phase mode; the line to line DC voltage cannot be set. Hence, N/A is used to indicate this. Below
are examples of the source settings pages for two additional different output modes.


1 phase 3-wire


1 phase


Keysight AC6900 Operating and Programming Guide 89


3 User Information


**Programming DC Voltage**


In DC mode, the Line (P1) terminals are at positive potential and the Neutral terminals are
at negative potential when setting the positive value. The opposite is true when setting
the negative value.


The AC6900 Series can output DC voltage. When the output is turned on, you cannot change the
output mode or voltage range. You must turn the output off first. You can set the voltage regardless of
whether the output is on or off.


**Voltage Range, Coupling, and Suppression**


In the Source Settings menu, start with setting the coupling to DC by toggling the Coupling softkey.


Toggle the Range softkey to select either Hi or Lo range. The output must be off to change the range
settings. The instrument checks to ensure that currently active settings are within the new range
setting.


Refer to Turning the Output On or Off for information about activating voltage Surge Sup pression, and
additional output programming functions available under the Advance softkey.


**Output Mode**


Select the Mode softkey and select 1-Phase .


Note that you can also select 1-phase, 3-wire if you wish to output positive and negative DC voltages.


Check to make sure that the wiring on the back of your unit matches your mode selection. A
confirmation message will ask you to confirm the wiring. The instrument will then configure the
internal relays to match the wiring selection.


1 phase


The instrument will then configure the internal relays to match the wiring selection.


90 Keysight AC6900 Operating and Programming Guide


3 User Information


**DC Voltage**


Specify the DC voltage to be output as shown below. In this example the DC voltage will be
superimposed on the AC voltage.


Note that you can also output just DC voltage by not programming the AC voltage.


It is always a good idea to check the limits to make sure the value that you program is within the
specified limit setting.


Press the [Protection Settings ] softkey to access the protection and limit settings.


Then select the [Limits] softkey.


Keysight AC6900 Operating and Programming Guide 91


3 User Information


**Typical Commands**


To set AC voltage:


SYSTem:CONFigure:WIRing 3 Selects 3-phase mode.


OUTPut:COUPling AC Select AC mode.


VOLTage:RANGe 160 Select the 160 V range.


VOLTage 110 Specify 110 V.


FREQuency 55 Specify 55 Hz.


OUTPut ON Turn the output on.


To set DC voltage:


SYSTem:CONFigure:WIRing 1 Selects 3-phase mode.


OUTPut:COUPling DC Select DC mode.


VOLTage:RANGe 160 Select the 160 V range.


VOLTage:OFFSet 40 Specify 40 V.


OUTPut ON Turn the output on.


To query maximum and minimum limits:


VOLTage? MINimum


VOLTage? MAXimum


FREQuency? MINimum


FREQuency? MAXimum


VOLTage:OFFSet? MINimum


VOLTage:OFFSet? MAXimum


92 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Setting Protection and Limits**


**Setting Protection**


**Setting Limits**


**Typical Commands**


**Setting Protection**


The protection function applies limits when there is a danger of damaging the instrument’s internal
circuitry or to protect the load. When a protection function is activated, an Error is generated, and the
output is turned off.


Press the Protection Settings softkey to access the protection settings.


Then select the Protection softkey.


Enter the Protection Settings in the following display.


**Configure OverVoltage and UnderVoltage Protection**


Overvoltage protection cannot be disabled. For overvoltage protections, enter values for OV, Positive
Peak OV and Negative Peak OV. By default, these are set to their maximum allowable values.


For undervoltage protection, enter an undervoltage protection that turns the output off when the
output goes below the undervoltage value. The default setting is zero. Toggle the UVP softkey to turn
undervoltage protection on or off.


If the measured voltage exceeds the OVP value, or is less than the UVP value, an error occurs, and the
output turns off.


Keysight AC6900 Operating and Programming Guide 93


3 User Information


**Configure OverCurrent Protection**


You can turn overcurrent protection on or off. Toggle the OCP softkey on or off.


When you turn overcurrent protection on, you can also specify the amount of time that must elapse
before the output is turned off when the current limit is exceeded. This is useful when you don't want
the output to be turned off due to issues such as inrush current causing short-term overloads. Timer
values can be specified from 0 to 10 seconds.


The current detection response is not instantaneous. Depending on the current measurement response speed, there may be a delay of approximately 0.1 seconds.


To specify the current limits, refer to Setting Limits.


**Clear Overcurrent Protection**


Press the Protection Settings softkey to access the protection settings.


To clear an overcurrent protection condition, select Protection Clear .


The current output status condition is displayed in the title bar of the output phase to help you ensure
that clearing the protection condition is appropriate and likely to produce expected results. See

Phases 1-3.


**Configure Watchdog protection**


This function turns off the output when SCPI communication is not performed for a length of time that
is equal to or exceeds the watchdog setting. It is disabled by factory default.


The time until the watchdog protection is activated can be set from 1 second to 3600 seconds
(default = 60 s)


You can turn watchdog protection on or off. Toggle the WDP softkey on or off.


**Setting Limits**


The Limits function limits the instrument's output setting to prevent damage to the load caused by
operator error. You should specify limits before applying output to the load, but you can set limits with
the OUTPUT on. Note that the limit value takes precedence over the setting value for voltage and
current. If the current setting value exceeds the limit range when the voltage or frequency limit value is
changed, the setting value (voltage or current) is set to a limit value that is close to the limit setting.


Press the Protection Settings softkey to access the limits settings.


Then select the Limit softkey.


94 Keysight AC6900 Operating and Programming Guide


3 User Information


Enter the Limit Settings in the following display.


**Voltage limits and voltage limit operation**


The instrument lets you to limit the allowable values for the voltage offset. The following table shows
the maximum voltage and current limit settings for all models, which allows the limits to be set to any
value within the given range. The factory default lower limit of DC mode is 0.0 V, so remember to
change the limit if you need to output a negative DC voltage.


**Limit Functions** **Settings Range** **Resolution**


Voltage Limit Upper & Lower AC volt 0.0 V to 322.0 V 0.1 V


Upper & Lower DC volt -455.0 V to 455.0 V 0.1 V



Current Limit Current limit Maximum current limit ×0.1 to


maximum current limit ×1.1


Positive & Negative limit Maximum current limit ×0.1 to


maximum current limit ×4.2


**Current limits and current limit operation**



0.01 A (0.35 A to 100.0 A)

0. 1A (100.0 A to 1000 A)


0.01 A (0.35 A to 100.0 A)

0. 1A (100.0 A to 1000 A)



Limits can be placed on the allowable current that flows through the load. There are maximum current,
positive peak current, and a negative peak current limits. You can set limits according to the load

conditions.


The positive and negative peak current limits instantly limit the peak output currents based on the limit
lettings. The maximum current limit operates on the rms value of the output current.


Under Protection Settings you can set the action to perform (whether to turn off the output, or limit
the current) when the current exceeds the Max current limit. You can also set the amount of time that

must elapse before the output is turned off when the current limit is exceeded.


The current detection response is not instantaneous. Depending on the current measurement response speed, there may be a delay of approximately 0.1 seconds


With overcurrent protection ON :


Keysight AC6900 Operating and Programming Guide 95


3 User Information


If the output current exceeds the current limit for more than the specified time, an error is generated
and the output is turned off


With overcurrent protection OFF :


If the output current exceeds the current limit, the instrument prevents the output current from
exceeding the current limit decreasing the output voltage.


If you select to control the output voltage, the following functions will not be available:


l
Soft start and Soft Stop (set output rise and fall time).


l Run power line abnormality simulations.


l Run sequences.


l Soft sensing and regulation adjustments.


**Frequency limits**


You can set frequency limits regardless of whether the output is on or off.


Select the Frequency softkey.


Set the frequency limit to prevent the instrument from generating a frequency that is greater than the

limit.


The output frequency is limited to 550 Hz on "L" models in 3-phase mode only.


96 Keysight AC6900 Operating and Programming Guide


3 User Information


**Typical Commands**


The following lists some typical protection commands:


VOLTage:PROTection:UPPer 150 Sets upper overvoltage limit


VOLTage:PROTection:LOWer 50 Sets lower overvoltage limit


VOLTage:PROTection:LOWer:STATe ON Enables the lower overvoltage protection


VOLTage:PROTection:PEAK:UPPer 300 Sets positive overvoltage peak protection


VOLTage:PROTection:PEAK:LOWer 300 Sets negative overvoltage peak protection


CURRent:PROTection:STATe 1 Overcurrent protection disables the output


CURRent:PROTection:DELay 5 Delay before before disabling the output


OUTPut:PROTection:WDOG ON Enables watchdog protection


OUTPut:PROTection:WDOG:DELay 600 Sets the watchdog timer


The following lists some typical output limit commands:


VOLTage:LIMit:UPPer MAX Sets upper AC voltage limit


VOLTage:LIMit:LOWer MIN Sets lower AC voltage limit


VOLTage:OFFset:LIMit:UPPer MAX Sets upper DC voltage limit


VOLTage:OFFSet:LIMit:LOWer MIN Sets lower DC voltage limit


CURRent MAX Sets current limit for all phases


CURRent:PEAK:UPPer MAX Sets upper peak current limit for all phases


CURRent:PEAK:LOWer MAX Sets lower peak current limit for all phases


FREQuency:LIMit:UPPer MAX Sets upper frequency limit


FREQuency:LIMit:LOWer MIN Sets lower frequency limit


Keysight AC6900 Operating and Programming Guide 97


3 User Information

#### **Turning the Output On and Off**


**Enable the Output**


**Impedance with Output Off**


**Voltage Surge Suppression**


**Advanced On/Off Functions**


**Typical Commands**


**Enable the Output**


Press On/Off key to enable the output. The On/Off key illuminates. If a load is connected to the output,
the front panel display will indicate that it is drawing current. Otherwise, the current reading will be
zero. The status indicator shows the output’s status.


For a description of the status indicators, refer to Front Panel Display at a Glance.


Press On/Off again to disable the output.


OFF: The output voltage turns off (into a high impedance mode). The output is always off at power-on.


If a protection function trips, the output also turns off. However, if the current limit operation is limit
control and the overload protection function (current limit) trips, the output is not turned off.


Risk of Electric Shock

To prevent the possibility of electric shock, do not touch the output terminal block.


If a capacitor, battery, or similar device is connected as a load in DC mode, voltage
remains at the section connected to the output terminal block even when the output is off
until the load energy is discharged. The discharge time of the internal capacitor when no
load is connected is approximately 0.1 seconds. To prevent the possibility of electric
shock, do not touch the output terminal block.


When the output is turned on, several volts of undershoot or overshoot may appear for a
time period on the order of ten microseconds.


98 Keysight AC6900 Operating and Programming Guide


3 User Information


**Impedance with Output Off**


The AC6900 Series instruments do not disconnect output from the internal circuits mechanically using
switches and relays. Instead, when the output is turned off, the instruments electrically increase output
impedance to limit the output without chattering.


The approximate resistance in the high-impedance condition is shown below, so if the load is a battery
or a similar device, a slight current may flow into the instrument and the load may be discharged.


AC6903 ~55 kΩ ~237 kΩ ~160 kΩ ~643 kΩ


AC6906 ~55 kΩ ~237 kΩ ~160 kΩ ~643 kΩ


AC6912 ~27 kΩ ~122 kΩ ~81 kΩ ~346 kΩ


AC6918 ~18 kΩ ~82 kΩ ~55 kΩ ~237 kΩ


**Voltage Surge Suppression**


In the Source Settings menu, you can activate voltage surge suppression ( Surge Sup softkey) when the
output is turned off.


Refer to Programming the Output for information about programming the output Mode, Range and
Coupling .


With voltage surge suppression ON :


The output is turned off after the output voltage is set to 0 V (low output impedance), after which the
output impedance is changed to high. Initially setting the impedance low prevents large voltage
overshooting and undershooting by the load immediately after the output is turned off. It is
recommended that you use the instrument with the voltage surge suppression set to on (the default).


Note that if the voltage surge suppression is on, an unexpected current may flow through the
instrument immediately after the output is turned off depending on the connected load (power
conditioner, regenerative inverter, battery, etc.). If this is unacceptable, turn voltage surge suppression

off.


With voltage surge suppression OFF :


When the output is turned off, the output of this product is at high impedance (refer to above chart).


**Advanced On/Off Functions**


In the Source Settings menu, select the Advance softkey to enable or disable output phase control,
specify soft start and stop, and enable output frequency synchronization.


Keysight AC6900 Operating and Programming Guide 99


3 User Information


**Output ON & OFF Phase**


Without enabling phase control, the output turn-on will occur at random points in the output

waveform.


You can control both output-on and output-off phases by specifying a turn-on and turn-off phase in
degrees, The default setting is zero degrees. You can specify values from 0 to 360 degrees. To enable
the phase controls, toggle the On Phase and Off Phase softkeys to ON.


With phase control off, the output always turns off at the zero-crossing phase to ensure
the instrument's output capacitor discharges when there is no load.


**Soft Start & Stop**


Soft start lets you gradually increased the output voltage when the output is turned on to control the
load device’s inrush current. Suppressing the inrush current prevents the voltage from dropping and
the output from being turned off as a result of an alarm activation


Soft Stop lets you gradually decreased the output voltage when the output is turned off to control the

inductive kickback that occurs when the current is cut off.


The default rise and fall time setting is 0. 1 seconds. You can specify values from 0.1 to 30.0 seconds.
To enable the soft start and soft stop controls, toggle the Soft Start and Soft Stop softkeys to ON.


100 Keysight AC6900 Operating and Programming Guide


3 User Information


Soft start and stop cannot be set when the product is being controlled with an external analog signal
and the signal source is set to external signal (EXT) only or when the product is being controlled with
an external analog signal and the voltage is being varied using an external DC signal. Soft start and
stop are disabled when the current limit is exceeded.


**Frequency Sync**


The sync function synchronizes the frequency and phase of the instrument's output voltage to the 50
Hz or 60 Hz input line or to an external sync signal. This function operates when the lower frequency
limit is less than 45 Hz and the upper frequency limit is greater than 65 Hz. This is useful in situations
when the display of an external measuring instrument is not stable.


To enable the sync function, toggle the Sync to LINE or EXT.


When LINE is selected, sync function synchronizes the frequency and phase of the output to the 50 Hz
or 60 Hz input line (refer to the following table).


When EXT is selected, sync function synchronizes the frequency and phase of the output to an external
sync input signal (refer to the Digital IO Configuration ).


To finely control the synchronization phase of the input voltage on three-phase input models, set the
Delay phase angle to a value from 0 to 360 degrees.


When turning off frequency synchronization, the output frequency is set to either 50 Hz or

60 Hz.



200 V input In-phase with input
V L1-L2


400 V input In-phase with input
V L1-N


**Typical Commands**





V Ø1-N and V L1-L2
are in phase


In-phase with the input



The following lists additional output turn-on/turn off configuration commands:


OUTPut ON|OFF Turns the output on or off


OUTPut:SSUPpression:STATe ON Enables output surge suppression


OUTPut:PHASe:ON Enables the output-on phase control


OUTPut:PHASe:ON:LEVel 90 Sets the output-on phase


OUTPut:PHASe:OFF Enables the output-off phase control


OUTPut:PHASe:OFF:LEVel 90 Sets the output-off phase


OUTPut:SSTart ON Enables soft start turn-on


Keysight AC6900 Operating and Programming Guide 101


3 User Information


OUTPut:SSTart:TIME 10 Sets the soft start turn-on


OUTPut:SSTart:FALL ON Enables soft start turn-off


OUTPut:SSTart:TIME:FALL 10 Sets the soft start turn-off


FREQuency:SYNChronous ON Enables frequency synchronous



FREQuency:SYNChronous:MODE

LINE



Sets the frequency synchronous source



FREQuency:SYNChronous:PHASe 10 Sets the frequency synchronous phase


102 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Displaying Measurements**


**Measurement Settings**


**Advanced Settings**


**Sense Terminal Settings**


**Typical Commands**


All output measurements are shown on the front panel displays. Pressing the key lets you
sequence through the various measurement displays.


**Meter** All -- >


**Meter** 3 phase -->


**Meter** 1 phase -->


Keysight AC6900 Operating and Programming Guide 103


3 User Information


The default display view is Meter All view. Sequencing through the measurement views provides
additional measurement and setting details about the selected phase.


Pressing the [ Ø1 ], [ Ø2 ] or [ Ø3 ] key can be used to select the phase of interest. Pressing the similar key
again will also causes it to cycle between the metering view with the selected phase activated.


While in the metering view, pressing the [ ALL ] key will always change the metering view back to Meter
All View with the All phase area selected.


The All area of the display shows the measurements that apply to all phases. On the left side, the lineto-line voltage between the indicated phases is shown (phase 1 to 2, 2 to 3, and 1 to 3). The right side
of the display shows the line-to-neutal voltage, provided it is the same ofr all phases. It also displays
the total power in Watts, the apparent power in VA, and the power factor.


**Measurement Settings**


From the front panel softkeys select Measure Settings


The measure settings menu lets you select from the following measurement functions:


Selecting either the Voltage or Current softkeys brings up the following menu, which provides the
same measurement couplings for either measurement.


Selecting ACDC, AC, DC, or Peak applies the selected measurement coupling to the voltage or current

measurements.


**ACDC** The rms value of the output is presented in the metering displays.
**AC** The AC component of the output is presented in the metering displays.
**DC** The average (DC) component of the output is presented in the metering displays.


104 Keysight AC6900 Operating and Programming Guide


3 User Information


**Peak** The peak current is displayed as an absolute value of the maximum instant
aneous current measured.


The following shows the voltage coupling selections. Similar couplings apply to the current function.


**ACDC** voltage **AC** voltage **DC** voltage **Peak** voltage


Note that there is no DC voltage measurement in the above example because the instrument coupling


is set to ACDC . Also note that even if a negative DC voltage is being generated, the peak current is
displayed as a positive value.


If the selection is set to Peak, the peak measurement can be held. This additional setting associated
with the peak measurement coupling, is available under the Advance menu. Selecting the Peak Clear
softkey clears the peak hold measurement.


**Advanced Settings**


From the front panel softkeys select Advance to access the advanced measurement settings.


The advanced settings menu lets you configure measurement Averaging and Peak Hold


Averaging - Enter the number of averaged points in the Voltage and Current fields. The Voltage and
current fields let you choose from 1 to 32 points. Use the Data Entry keypad to enter your values, the
press Enter.


Peak Hold - Enter the peak hold time in the Voltage and Current fields. Values are in seconds, from 0 to
10. If a value greater than ten is entered, the peak hold time will be indefinite. Selecting the Peak Clear
softkey clears the peak hold measurement.


Keysight AC6900 Operating and Programming Guide 105


3 User Information


**Sense Terminal Settings**


Select Sense to access the sense configuration settings.


Note that the sense wiring diagram is also displayed. Refer to Sense Terminal Wiring below.


The Sense softkey must be toggled On to access the sense selections.


Toggle the Sense Type softkey to select either Hard or Soft sensing.


Hard sensing measures the output voltage compensation point directly across the load. It can
compensate the voltage up to approximately 1.5 V for a single load line. Because compensation is
performed in real time, the output voltage can be stabilized at a high speed. However, the output may
become unstable and oscillate depending on the wiring and the load type. If the output becomes
unstable, use soft sensing


Soft sensing is recommended if you are using a load that does not require a fast output voltage
response speed. Soft sensing can compensate the voltage up to ±10 % of the product’s set voltage.
The maximum output voltage during compensation is limited by the rated output voltage. Refer to
Voltage Compensation for more information about using remote sensing.


When Soft sense type is selected, two additional softkeys are displayed:


The SNS Point softkey toggles between Out and Sense.


Out selects the output terminals as the remote sense point when using Soft sensing.


Sense selects the location where the remote sense wires are connected as the remote sense point
when using Soft sensing.


The SNS Control softkey toggles between AC and DC.


Specify whether AC or DC measurements are being made when using Soft sensing.


106 Keysight AC6900 Operating and Programming Guide


3 User Information


**Sense Terminal Wiring**


The Sense display will show one of the following three output terminal settings that you have
configured and confirmed in the output Mode settings menu.


Now you must also check and confirm that the sense wiring on the back of your unit matches your
previous mode selection. A confirmation message will ask you to confirm the sense wiring. The
instrument will then configure the internal sense relays to match one of the following wiring selections
(3-phase, 1-phase 3-wire, or 1-phase) .


3-phase


1-phase 3-wire


1-phase


The instrument will then configure the internal sense relays to match the wiring selection.


Keysight AC6900 Operating and Programming Guide 107


3 User Information


**Typical Commands**


Display commands


DISPlay ON Turns the front-panel display on or off.


DISPlay:METer:COUPling:CURRent AC Selects the current measurement unit.


DISPlay:METer:COUPling:VOLTage AC Selects the voltage measurement unit.


DISPlay:PHASe P123 Selects the phase-naming convention.


DISPlay:VIEW METER_ALL Selects the phases to display.


Measurement commands:


FETCh:ALL? Returns all previous measurements


MEASure:ALL? Returns all new measurements


FETCh < _Measured_Item_ >? Returns all previous measurement items


MEASure < _Measured_Item_ >? Returns all new measurement items


Sense commands


SENSe:AVERage < _value_  - Sets the moving averaging count for both voltage

and current measurements.


SENSe:CURRent[:PEAK]:HOLD:TIMe Sets the peak current hold time


SENSe:VOLTage[:PEAK]:HOLD:TIMe Sets the peak voltage hold time


SENSe:CURRent[:PEAK]:HOLD:CLEar Clears the peak current hold value


SENSe:VOLTaqe[:PEAK]:HOLD:CLEar Clears the peak voltage hold value


108 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Using Advanced Functions**


**Power Line Abnormality Simulations**


**Programming Output Sequences**


**Sequence Examples**


**Harmonic Analysis**


**Output Controls**


**Typical Commands**


Press the [Advnce ] softkey to access the advanced functions


Advance functions include: Simulations, Sequences, Harmonic Analysis, ad Power Management


**Power Line Abnormality Simulations**


You can simulate power supply line errors by stopping the output (to simulate power failures) and
decreasing and increasing the voltage (to simulate voltage dips and pops).


You can use this to test switching power supplies and other electronic devices


A sine wave is generated during the power line abnormality simulations. Even if a special waveform is
set with the waveform bank, a sine wave will be generated as soon as you execute the power line
abnormality simulation. You can set this function regardless of whether the output is on or off.


Select the Simulation softkey to access the Simulation function.


A graphic is displayed that identifies the parameters of the simulated waveform as shown below. The
generated waveform created from the front panel applies to all output phases.


You can create simulations for individual phases as well as from a waveform bank using

SCPI SIMulation commands.


Keysight AC6900 Operating and Programming Guide 109


3 User Information


Toggle the SIM softkey to turn the simulation function ON.


There are a number of parameters that you can set to configure the simulation waveform.


In the above example the following parameters are shown


T1 time Voltage regulation start time in seconds. This is the time or the phase from the waveform's zero
crossing (the point where the signal crosses the zero axis) to the point where the voltage regulation — such as increase or decrease — starts to occur. (0.0 s to 0.9999 s)


T2 time Slope time 1. For Voltage Increase, this indicates how much time is required to raise the voltage
to the increase voltage. For Voltage Drops, this indicates how much time is required to lower the
voltage to the drop voltage. (0.0 s to 99.99 s)


T3 time Voltage regulation time. For Voltage Increase, this indicates the length of time that the voltage
will be kept at the increase voltage. For Voltage Drops, this indicates the length of time that the
voltage will be kept at the drop voltage. (0.1 s to 9.999 s)


T4 time Slope time 2. For Voltage Increase, this indicates how much time is required to lower the voltage
from the increase voltage to the voltage that is in use during the T5 time period. For Voltage
Drops, this indicates how much time is required to raise the voltage from the drop voltage to the
voltage that is in use during the T5 time period. (0.0 s to 99.99 s)


T5 time Return time in seconds (or cycles - see below). This indicates how long as a length of time that
the voltage will be kept at the level that it returns to after the Voltage Increase or Voltage Drop is
completed. (0.0 s to 99.99 s)


T3 voltage Regulated voltage. For Voltage Increase, this is the voltage level that the voltage will be raised to.
For Voltage decrease, this is the voltage level that the voltage will be lowered to. (L range: 0 V to
161.0 V, H range: 0 V to 322.0 V


No. of loop Number of times to repeat the T1 to T5 simulation cycle (1 to 9,998; 9999 = infinity).


In addition to the programming the above parameters you can also specify the voltage regulation start
polarity. This sets the zero crossing (the time at which the voltage becomes zero) that will be the
reference for T1 to either positive zero crossing or negative zero crossing.


110 Keysight AC6900 Operating and Programming Guide


3 User Information


Toggle the Polarity softkey to indicate either Pos itive or Neg ative.


Furthermore, you can also specify the T1 parameter in degrees, and the T5 parameter in cycles, rather
than time, as shown below.


Toggle the T1 Type softkey to indicate Angle . Toggle the T5 Type softkey to indicate Cycle


A1 angle Voltage regulation start angle. This sets the voltage regulation start phase from the waveform's
zero crossing (the point where the signal crosses the zero axis) to the point where voltage regulation — such as increase or decrease — starts to occur. (0 to 359.9 degrees)


N5 cycle Return cycles. This indicates how long as a number of cycles of the present frequency that the
voltage will be kept at the level that it returns to after the Voltage Increase or Voltage Drop is completed. (0 to 999,900)


To start the power line abnormality simulation, press the Run SIM softkey.


Toggle the SIM softkey to OFF to stop the simulation. You can also stop the simulation by turning the
output off.


You cannot run a power line abnormality simulation in the following situations:


- The overcurrent protection (OCP) is turned off.


- The regulation adjustment or soft sensing compensation function is in use.


- Low range is selected and the T3 voltage is set to a value outside of its range.


- The T3 voltage is set to a value outside the voltage limits.


Keysight AC6900 Operating and Programming Guide 111


3 User Information


**Programming Output Sequences**


A sequence is a series of settings—values such as the output voltage, frequency, and time—that are
created and saved in advance, and are then recalled and run at a later time.


Sequences are groups of executable units called steps. When a sequence is executed (run), its steps
are executed in order, starting with the specified starting step. A single execution of a sequence is
completed after the sequence’s specified last step has been executed.


You can use the jump function to skip steps and repeatedly execute all the steps in the sequence
except for those that have been skipped.


The maximum number of steps is 600


Select the Sequence softkey to access the Sequence function.


The sequence steps will be displayed as shown below.


Pressing the Add softkey will add a step, either as a first step, or at the end of an existing list. Pressing
the Delete softkey deletes the active step. Press Clear All to delete all steps.


You will notice that each step in the sequence as shown above includes a number of programmable
parameters. These are described as follows:


Step Sets the step that you want to configure (from 1 to 600)


Freq Sets the step frequency in Hz


ACV Sets the AC voltage of the step.


DCV Sets the DC voltage of the step


Time Sets the time of the sequence execution in hours, minutes, seconds, and milliseconds.
Hours = 0 to 23; Minutes = 0 to 59; Seconds = 0 to 59; Milliseconds = 0.1 to 999.9


Outp ON Turns the output either on (checked) or off (unchecked)


112 Keysight AC6900 Operating and Programming Guide


3 User Information


Stat Out Turns the status signal output either on (checked) or off (unchecked). When the status signal output is
turned on, the signal is output only while the step’s waveform is being output.


Trig In Turns the trigger in signal either on (checked) or off (unchecked). When the trigger input is turned on, the
sequence is paused after the previous step is finished, and the product enters a trigger input wait state. If
a trigger signal (pulse width of at least 10 μs) is received through the DIGITAL I/O terminal, the paused is
released, and the step is executed.


Trig Out Turns the trigger in signal either on (checked) or off (unchecked). When the trigger signal output is turned
on, the signal is output for several tens of microseconds when a step is executed.


Note that you can select a ramp or a step function to change the value a linearly over the
specified time or change the value as a step. Navigate to the icon and then press the [Enter] key to
toggle between the two choices. The following illustrates the difference in a ramp or step change:


Voltage

signal
change


Frequency

signal
change


Press the Properties softkey to program additional global parameters for the sequence steps.


Pressing the Continuous softkey togges the selection between On or Off. When Off, you can specify the
number of sequence repetitions in the No. of loops box. You can also check the Continuous box to have
the sequence repeat continuously. When set to Off, the sequence runs once.


To run the sequence, press Back, then press the Run SEQ softkey.


Keysight AC6900 Operating and Programming Guide 113


3 User Information


**Sequence Examples**


User-Defined


**Step Number** **000** **001** **002** **003** **004** **005** **006** **007** **008** **009** **010** **011**


AC Volt V 5 10 0 10 0 0 5 5 5 0 5 5


AC Step/Ramp


FREQ Hz 50 50 50 50 50 50 40 80 40 40 80 40


FREQ Step/Ramp


Time ON ms 100 100 100 100 100 100 100 100 100 100 100 100


Output ON ON ON ON ON ON ON ON ON ON ON ON


In the above example, steps 002, 005, and 009 are used to return tne voltage to zero before starting
the next step.


Voltage Ramp


AC Volt V 0 10 10 0


AC Step/Ramp


FREQ Hz 50 50 50 50


FREQ Step/Ramp


114 Keysight AC6900 Operating and Programming Guide


3 User Information


Time ON ms 100 100 100 100


Output ON ON ON ON


In the above example, step 002 does not generate a ramp because the voltage setting of step 002 is
the same as the step 001.


Voltage Sweeps


**Step Number** **000** **001** **002** **003** **004** **005** **006** **007** **008**


AC Volt V 0 10 0 10 0 10 0 10 0


AC Step/Ramp


FREQ Hz 50 50 50 50 50 50 50 50 50


FREQ Step/Ramp


Time ON ms 100 100 1 100 1 100 1 100 100


Output ON ON ON ON ON ON ON ON ON


In the above example, small starting steps (steps 000, 002, 004, and 006) are used to set the voltage to
zero so that the subsequent ramps will start at the zero before ramping to their 10 V setting.


Frequency Sweeps


AC Volt V 0 10 10 10 10 10 0


AC Step/Ramp


FREQ Hz 40 80 40 80 40 80 40


Keysight AC6900 Operating and Programming Guide 115


3 User Information


FREQ Step/Ramp


Time ON ms 1 100 1 100 1 100 100


Output ON ON ON ON ON ON ON


In the above example, steps 000, 002, and 004 set the starting frequency for the each of the frequency

ramps.


**Harmonic Analysis**


You can perform harmonic analysis on the output voltage and output current. A simplified
measurement method is used, so this method does not conform to standards such as IEC.


The analyzed harmonic varies depending on the output frequency.







1 Hz to 100 Hz 50th 500.1 Hz to 600 Hz 8th


100.1 Hz to 200 Hz 25th 600.1 Hz to 700 Hz 7th


200.1 Hz to 300 Hz 16th 700.1 Hz to 800 Hz 6th


300.1 Hz to 400 Hz 12th 800.1 Hz to 1000 Hz 5th


400.1 Hz to 500 Hz 10th


Select the Harmonic softkey to access the Harmonic function.


The harmonics list will be displayed as shown below.


THD is the total harmonic distortion (ratio of the harmonic components up to the 50th harmonic
relative to the fundamental component).


Phase degree is the phase difference (±180 deg) from the fundamental voltage waveform.


The measured values of 1st to the 50th harmonic are rms values.


116 Keysight AC6900 Operating and Programming Guide


3 User Information


Pressing the Type softkey toggles the harmonic list to display either voltage or current harmonics. This
is indicated in the Output Type box.


Pressing the Harmonic softkey toggles to display either All harmonics, just the Even harmonics, or just
the Odd harmonics. This is indicated in the Harmonic Type box. The example below shows the Odd
harmonics being displayed.


Because the system is able to show harmonic up to the 50th order and the screen real estate is limited
to display only six harmonics at a time, the á, âsoftkeys are used to scroll through all the harmonic
order. When reaching the last page, pressing the Next Page will go back to the first page.


**Typical Commands**


Simulation


Configure the simulation parameters.


SIMulation:T1:PHASe:STATe OFF Sets T1 using time


SIMulation:T5:CYCLe:STATe OFF Sets T5 using time


SIMulation:T1:TIME 0.5 Sets the voltage regulation starting time


SIMulation:T2:TIME 10 Sets slope time 1


SIMulation:T3:TIME 1 Sets the voltage regulation time


SIMulation:T3:VOLTage 150 Sets the regulated voltage


SIMulation:T4:TIME 10 Sets slope time 2


SIMulation:T5:TIME 10 Sets the return time


SIMulation:REPeat:COUNt 50 Sets the number of repetitions


Execute a power line abnormality simulation


OUTPut ON Turn the output on.


SIMulation:STATe RUN Runs the simulation


Keysight AC6900 Operating and Programming Guide 117


3 User Information


SIMulation:EXECuting? Queries the simulation status. If running, "RUN" and the repetition
number is returned. If stopped, "STOP" is returned.


SIMulation:STATe STOP Send this command to stop the simulation.


Sequences


Use the PROG:EDIT command configure each sequence step.


PROGram:Edit 1,50,OFF,100,OFF,0,OFF,0.5,OFF,ON,OFF,ON,0 Step 1


PROGram:Edit 2,60,ON,200,OFF,0,OFF,1,OFF,OFF,OFF,ON,0 Step 2


PROGram:Edit 3,400,ON,230,OFF,0,OFF,2,ON,OFF,OFF,ON.0 Step 3


Configure the sequence conditions.


PROGram:STEP:STARt 1 Sets the starting step number


PROGram:STEP:END 3 Sets the ending step number


PROGram:LOOP 10 Sets the number of repetitions


Execute a programming sequence.


PROGram:EXEcuting RUN Runs the sequence


PROGram:EXECuting? Queries the sequence status.


PROGram:EXECuting STOP Send this command to stop the sequence


Harmonic Analysis


FETCh:< _Harmonic_Item_ >? Returns 50 previous harmonic measurements


MEASure:< _Harmonic_Item_ >? Returns 50 new harmonic measurements


118 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Using Output Controls**


**Impedance Control**


**Output Response**


**Custom Waveform**


**Analog Input**


**Parallel Output**


**Typical Commands**


Press the [Advance ] softkey to access the advanced functions


Select the Output Control softkey to access the Output controls.


The following output control functions are available


**Impedance Control**


This instrument's output impedance is approximately zero ohms. Commercial power sources have an
impedance of several milliohms to several ohms. By programming an output impedance, you can
simulate the same environment as that provided by commercial power supplies.


The output impedance control can be accessed through the Impedance Control softkey. As shown
below, the resistance and reactance controls are enabled by toggling the Impedance softkey to ON.


Keysight AC6900 Operating and Programming Guide 119


3 User Information


You can enter a value for All phases, or customize values for each phase. The same applies when
programming output reactance values.


Refer to the Output Impedance specification for the allowable resistance and reactance values.


If setting value is outside the specifications range, the OUT.Z indicator is displayed in red. If the values
exceed the upper limit of the specifications, the largest allowable value will be programmed. If the
value is less than the lower limit, the lowest allowable value will be programmed.


Select Back to complete the setup.


An annunciator will be shown on the title bar to indicate that the output impedance control has

been enabled.


**Output Response**


You can set the response speed of the internal amplifier to one of the following three levels according
to the load conditions and how you will use the instrument.


Fast response - This is used for applications that require fast rise and fall speeds of power supplies.
Depending on the load conditions, the output may become unstable or oscillate. Check the output
voltage waveform before you actually perform the tests. If the output is unstable, set the response to

Medium or Slow.


Medium response (the default) - This is used for applications covering a range of frequencies from
commercial power line frequencies to the frequencies used by ship and aircraft power supplies.


Slow response - This is used to provide stable power to a variety of loads. Note that even if a capacitor
that has a large capacitance is connected to the output of the instrument, this setting can be used to
provide stable operations. This setting provides a sufficient response speed to generate commercial
power line frequencies.


The output response control can be accessed through the Output Response softkey. The default
response is set to Medium. When set to the default, there is no annunciator on the main display to
indicate the output response. If you decide to change the response speed to Slow or Fast, the
annunciator will show S.RESP for slow response, and F.RESP for fast response


120 Keysight AC6900 Operating and Programming Guide


3 User Information


Toggle the Speed softtkey to select he desired output response speed, The selection will appear in the
Speed box.


Select Back to complete the setup.


**Custom Waveform**


You can generate both Peak clipped and Flat curve waveforms. The instrument creates custom voltage
waveforms by performing D/A conversion on the waveform data that is stored in its internal waveform

bank.


User-defined waveforms stored in other waveform bank locations (from 1 to 256) can be accessed and

customized using the information entered in the Custom control.


Waveform bank location zero (0) contains a reference sinewave with a positive peak of 32767 points, a
negative peak of 32767, with a cycle of 4096 points. The phase resolution is 0.01 degrees. Waveform
bank location zero cannot be overwritten. In the factory default condition, all waveform bank locations

contain this reference sinewave.


Peak clipped waveform - The peak portion of waveforms is clipped by the specified crest factor value.
The sine waveform is adjusted so that the specified rms value and the rms value of the waveform that is
actually output are the same. For a sine wave, the crest factor is 1.41. In the voltage waveforms of
commercial power lines, the peaks are clipped, so the crest factor is between 1.2 and 1.4.


Flat curve waveform - This is a waveform defined in IEC61000-4-13. The peak of the waveform is
clipped by the specified clip factor. The waveform peak is defined to be 1. The specified rms value and
the rms value of the waveform that is actually output are not the same.


The custom waveform controls can be accessed through the Custom Waveform softkey. As shown
below, a Sine waveform type has been selected, and you can import custom sinewaves from the
waveform bank for each output phase or All output phases.


Keysight AC6900 Operating and Programming Guide 121


3 User Information


As shown below, a Peak waveform type has been selected, and you can import custom sinewaves from
the waveform bank for each output phase or All output phases. You can also change the crest factor of
the setup.


As shown below, a Flat waveform type has been selected, and you can import custom sinewaves from
the waveform bank for each output phase or All output phases. You can also change the clip factor of
the setup.


122 Keysight AC6900 Operating and Programming Guide


3 User Information


If you enter a value that is outside the permissible range for the setting; a message box will appear as

shown below.


Select Back to complete the setup.


A WFM annunciator will be shown on the title bar to indicate that a custom waveform is being
generated.


**Analog Input**


External analog signals can be used to change the AC voltage, DC voltage, or frequency using a DC
signal.


Refer to Analog and Digital Connections for information on wiring and connections.


As shown below, the Analog Input has been enabled. Note that the additional softkey ( CTRL Para ) for
the control parameter will only be available once the Analog Input has been enabled.


This example shows the control parameter is set to ALL. In this setting, CH A is used to control the AC
Voltage of all 3 phases, CH B is used to control the DC Voltage of all 3 phases and lastly, CH C is used
to control the Frequency setting. The control parameters are shown in the settings boxes.


In the following example, the control parameter is set to VAC. In this setting, each of channels is used
to control the corresponding phase AC Voltage as shown in the setting box.


Keysight AC6900 Operating and Programming Guide 123


3 User Information


If the control parameter is set to VDC, each of the channels is used to control the corresponding DC
Voltage as shown in the settings boxes.


**Parallel Output**


The parallel information in this section does not apply to AC6903 units.


Refer to Parallel Connections for more information on wiring and connections between instruments.


The following display only illustrates the connections and switch settings for parallel operation. There
are controls on this front panel display.


**Typical Commands**


Impedance Control


OUTPut:IMPedance ON Turns the impedance control on or off


OUTPut:IMPedance:REACtive < _value_ >[(@chanlist)] Sets the output impedance


OUTPut:IMPedance:REAL < _value_  - [(@chanlist)] Sets the output reactance


124 Keysight AC6900 Operating and Programming Guide


3 User Information


Output Response


VOLTage:RESPonse FAST Programs the output response


Custom Waveform


WAVE:STATe ON Enables or disables the custom waveform


WAVE:DATA:ARBitrary < _bank_number_ >, < _block_  - Sets a waveform with block data at the
specified waveform bank



WAVE:DATA:CLIP < _bank_number_ >, < _pclip_value_ >,

<value>


WAVE:DATA:IECPclip < _bank_number_ >, < _pclip__
_value_ >,< _value_ 

WAVE:DATA:POINt < _bank_number_ >, < _point_ >,

< _data_ 


Sets the crest factor of the peak clipped

waveform


Sets the clip factor of the flat curve

waveform


Programs the arbitrary waveform data
points



WAVE:DATA:SINusoid < _bank_number_  - Resets the waveform to a sinusoid


WAVE:DATA:TYPE? < _bank_number_  - Queries the type of waveform


Analog Input


VOLTage:PROGramming:EXTernal:MODE EXTDC Sets the external analog signal's
programming mode.



VOLTage:PROGramming:EXTernal:EXTDC

:SIGNal:SOURce EXT


VOLTage:PROGramming:EXTernal:EXTDC
:SIGNal:POLarity < _channel_ >, NORM


VOLTage:PROGramming:EXTernal:VPRogram :STATe
< _channel_ >,ON



Sets the external analog input
programming signal source.


Sets the external analog input
programming signal polarity.


Sets the voltage programming output

state for each channel.



VOLTage:PROGramming:EXTernal:VPRogram :MAP ALL Sets the voltage programming output

mapping.


Parallel Output


SYSTem:CONFigure:FORMation:FRAMe? Queries the number of units in parallel.


SYSTem:CONFigure:FORMation:FRAMe:INFO? < _value_  - Queries information about the specified
unit operating in parallel.


Keysight AC6900 Operating and Programming Guide 125


3 User Information

#### **Using System Utilities**


**Store and Recall**


**System Settings**


**Error Messages**


**Help Menu**


**Typical Commands**


Press the Utilities softkey to access the utilities.


Utilities include: Store and Recall, IO Configuration, System Settings, Error, and Help


**Using Store and Recall**


The product has a preset memory, setup memory, and USB storage capability.


Preset memory is internal can be recalled by pressing one of two Output Preset keys.


The following items can be saved to the two preset memory locations (Preset 1 and Preset 2).


l Frequency


l AC voltage


l DC voltage


l Waveform bank number


For single-phase three-wire output and three-phase output, phase voltages are stored. The line
voltages are calculated from the phase voltages


Because you can easily recall saved settings, this feature is useful when you want to switch between
two different settings (sudden voltage change or sudden frequency change).


Setup memory is also internal and can store up to 10 sets of front panel settings and be accessed from
the front panel menu.


USB storage is available from the front panel USB port. The following items can be saved to a USB
memory device.


126 Keysight AC6900 Operating and Programming Guide


3 User Information


l
Front Panel settings (same as in setup memory)


l Power line abnormality simulation settings


l Sequence data


l Waveform bank data


You cannot use a USB memory device that has a capacity greater than 16 GB. You may need to use a
PC to format the USB memory device into FAT32 format. Note that some USB memory devices may
not operate correctly.


**Store Settings**


Select the Store & Recall softkey to access the Store/Recall controls.


Then select the Store Settings softkey


The following store settings are available:


Toggle the Dest ination softkey to select either an Internal or External storage location. When the
destination is Internal, you have the option of selecting from 0 to 9 internal storage locations. Specify

the location in the Store state box.


Press the Store softkey to save the panel settings in the specified location.


You also have the choice to set a specific store state to the Power On state so that every time the unit is
rebooted, it will automatically set all settings according to the settings in the specified store state.
However, this is limited to one of the 10 internal memory states. Press the Set to Power On softkey.


Keysight AC6900 Operating and Programming Guide 127


3 User Information


Specify the store state location that you wish to use as the power on recall state. Then press the Set to
Power On softkey.


When the Dest ination softkey is set to External, you can choose additional storage setting options like
Panel Settings, Simulation Setting, Sequence Data, Waveform Bank or All. Refer to Default Settings
for further information. Whenever you navigate the setting field that has multiple options to choose, a
pop-up message will appear to tell you that the RPG knob can be used to browse through available
options.


Select Back to complete the setup.


**Recall Settings**


Recalling settings is similar to storing settings. First, select the Recall Settings softkey


The following store settings are available:


128 Keysight AC6900 Operating and Programming Guide


3 User Information


When the Source is set to internal, you can only recall settings from the internal storage locations 0 to
9. When the source is set to External, you can also recall Panel Settings, Simulation Setting, Sequence
Data, Waveform Bank or All.


The instrument stores the Front Panel settings shown in the table below at five-second intervals. To
ensure that settings changes are saved before power-off, wait at least five seconds after changing a
setting before turning the instrument off. Otherwise, the last settings may not be stored.


Recalling is not possible in the following situations.


l A power line abnormality simulation is in progress.


l A sequence is running.


l Soft start or soft stop is in progress.


l The settings to be recalled are outside the range of the present limit function or protection function.


l The peak value of the AC+DC waveform is outside the output voltage range.


**Output Preset**


Select the Output Presets softkey to access the preset functions.


The following function keys are available.


Press the Preset 1 Store or Preset 2 Store softkey to store the present instrument state in the selected
preset memory location.


Press the Preset 1 Recall or Preset 2 Recall softkey to recall the instrument state in the selected preset
memory location. These softkeys perform the same function as the front panel Output Preset keys

described earlier in this section.


Keysight AC6900 Operating and Programming Guide 129


3 User Information


**Set to Default**


Press the Set to Default softkey to return the instrument to the factory default front panel settings as
described under Default Settings.


**Using System Settings**


Select the System Settings softkey to access the System Settings controls.


Select the User Options softkey to configure the following user options:


Press the Display softkey to toggle between turning the display On or Off. When the display is turned
off, press any of the softkeys or front panel keys to turn it back on.


Press the Scr Svr (screen saver) softkey to toggle between turning the screen saver on or off.


When the screen saver is turned On, the screen saver function blanks the screen after 30 minutes of
inactivity. The display is turned on again when power is cycled, after an instrument reset (*RST), or
when you press any of the softkeys or front panel keys.


Press the Phase Conv (convention) softkey to toggle between 123, ABC, or UVW


This is meant to change the label or naming convention for the individual phases. Different regions
around the world uses different naming convention for the AC line. The default is set to 123. In the
above example, it is set to ABC. Hence, the three sub-frames in the main display area will show Phase
A, Phase B and Phase C. If you select UVW, it will then change to Phase U, Phase V and Phase W.


Press the Key Click softkey to toggle between turning the key click sound On or Off.


Press the Sleep softkey to toggle between turning sleep mode On or Off.


You can set the instrument so that it enters sleep mode whenever no output is being generated
(because the output is turned off). This turns off the internal power modules, which reduces power
consumption. You cannot turn the output on for approximately 1 minute after sleep mode is exited
until the internal power modules turn on.


Select the Date & Time softkey to configure the date and time.


The following settings are available:


130 Keysight AC6900 Operating and Programming Guide


3 User Information


The text boxes on the right side show the current Date and Time in DD/MM/YYYY and HH:MM:SS
format respectively.


Click on the appropriate softtkey to highlight the text box to change the date or time. Note that the
Second settings cannot be adjusted. Changing the Hour or Minute settings will automatically causes

the Second field to reset to zero 0


Select the Advance softkey to configure the advanced system settings.


Select the Offset Adjust softkey to fine tune the output offset to achieve the zero point.


The following settings are available:


You can provide a voltage offset for both the sense terminals and the output terminals. Enter the offset
voltage for each phase. Note that 1-phase-3 wire only uses Phase 1 and Phase 2.


Select the Power Mgmt (management) softkey to manage the instrument's internal power modules.


Keysight AC6900 Operating and Programming Guide 131


3 User Information


The following settings are available:


These selections let you disable one or more power module within the unit to limit the output power or
disable the defective power module. Use the up and down arrows to navigate to the power module.

Then un-check to disable the selected module. Enable All enables all modules. Disable All disables all

modules.


Select the About softkey to view Information such as model number, serial number, options and

firmware revision.


Press the Firmware Update softkey to updated the firmware through the front panel USB port. A popup message will appear with further instructions.


Note that the firmware for a standard unit cannot be installed on an Option MEM unit, neither can
firmware for an Option MEM unit be installed on a standard unit.


132 Keysight AC6900 Operating and Programming Guide


3 User Information


Select the Sanitize softkey to delete all user data stored in the instrument's internal memory.


The following message will appear:


Then press the Sanitize softkey


Sanitization is per the National Industrial Security Program Operating Manual (NISPOM). Factory data
(instrument firmware, model number, serial number, MAC address and calibration data) is not erased.

After the data is cleared, the instrument is rebooted. This procedure is typically performed only when
you need to remove an instrument from a secure area. The sanitization procedure is not recommended
for use in routine applications because of the possibility of unintended loss of data.


If your unit includes a micro-SD card (Option MEM), you can simply remove the card to sanitize the
instrument. Refer to Removing and Installing an SD Card for details.


**Error Messages**


If a self-test error occurs, a message is displayed on the title bar. For programming errors and other
error messages, refer to Error Messages for more information.


Select the Utilities - Error softkeys to display any error messages.


Keysight AC6900 Operating and Programming Guide 133


3 User Information


For hardware related errors, a pop-up message may appear directly on the display. Refer to Hardware

Error Codes for more information.


**Help Menu**


Select the Utilities - Help softkeys to view built-in help at any time.


Use the up and down arrows to navigate to the selected topic. Then press the Select softkey.


**Typical Commands**


Store and Recall


*SAV <0 to 10> Saves the instrument state to a nonvolatile memory location.


*RCL < _0 to 10_ - Recalls a saved instrument state.


*RST Resets the instrument to factory default values.


MEMory:SAVE[:IMMediate] 1|2 Saves the output settings to memory location 1 or 2.


MEMory:RECall[:IMMediate] 1|2 Recalls the memory saved in location 1 or 2


MEMory:RECall:PREView? 1|2 Displays the memory contents saved in location 1 or 2.


134 Keysight AC6900 Operating and Programming Guide


3 User Information



System Settings


SYSTem:DATE <yyyy>,<mm>,<dd> Sets the local date of the system clock.


SYSTem:TIME <hh>,<mm>,<ss> Sets the local time of the system clock.



SYSTem:CONFigure:ADJust:
VOLTage:FINE < _value_ 

SYSTem:CONFigure:ADJust:
VOLTage:TERMinal:MODE OTERM


SYSTem:CONFigure:FORMation:

PSAVer:MAXimum < _value_ 

SYSTem:CONFigure:FORMation:
PSAVer:MODules E|D {,E|D) . . .


SYSTem:CONFigure:FORMation:

PSAVer:RESet



Sets the voltage offset value.


Applies the voltage offset value to the output terminal.


Sets the maximum VA for the power save function.


Sets the specified power modules to run using the power

save function.


Resets the power save settings



*IDN? Returns the instrument’s identification string.


Error Messages


SYSTem:ERRor[:NEXT]? Reads and clears one error from the error queue.


SYSTem:ERRor[:NEXT]:COUNt? Returns the number of errors in the error queue.


Keysight AC6900 Operating and Programming Guide 135


3 User Information

#### **Remote Interface Configuration**


**USB Status**


**LAN Status**


**LAN Settings**


**GPIB Status**


**Using the Web Interface**


**Using Telnet**


**Using Sockets**


**Using HiSLIP**


**Typical Commands**


This section describes how to configure each remote interface.


This instrument supports remote interface communication over GPIB (optional), USB, and LAN
(default). All three interfaces are "live" at power up, and they may be used simultaneously. To use these
[interfaces,you must first install the latest Keysight IO Libraries Suite from www.keysight.com. Then](http://www.keysight.com/)
connect your instrument to your PC.


**USB Status**


The Keysight IO Libraries are required to control the instrument through the USB inter
face.


Use a standard USB cable to connect the instrument to the computer. There are no configurable USB
parameters, but you can retrieve the USB connect string from the from the front panel display by
pressing


Utilities > .


IO Config 

USB Status


The following information is displayed:


136 Keysight AC6900 Operating and Programming Guide


3 User Information


**LAN Status**


The front-panel annunciator indicates that the LAN port is connected and configured. The
instrument continually monitors the instrument's LAN port and automatically reconfigures it when the

instrument is disconnected and then reconnected to a network.


The front panel annunciator indicates that the USB interface is connected and configured.


To begin configuring the remote interface from the front panel, access the LAN settings by pressing
Utilities > IO Config - LAN Status .


The status will indicate if the LAN is connected (Running), or if it is disconnected (Fault).


LAN connected LAN disconnected


If the LAN is disconnected select LAN Restart to restart the LAN. If you need to reset the LAN select
LAN Reset . If you need to configure the LAN, go back to the previous menu and select LAN Settings .


Keysight AC6900 Operating and Programming Guide 137


3 User Information


**LAN Settings**


From this menu you can configure the LAN. To access any of the three configurable panels, you toggle
the corresponding softkey ( DHCP, AutoDNS, or mDNS ) either ON or Off. The item highlighted in
orange is the active selection.


Select Set to Default to return the LAN settings to their Default values


The following display shows all three configurable panels with editing enabled. Use the front panel
navigation keys or RPG knob to navigate to the numeric field you wish to edit. Then use the front panel
numeric entry keys to enter a value.


Select the Keyboard softkey for alphanumeric entries in the DNS hoostname and mDNS service fields.


In the following example, note that the last character in the text box is being highlighted in orange.
This indicates that this is the selected character. Pressing Delete Char will delete this character only.
To navigate between the next and previous character, press Next Char or Previous Char Pressing Clear

All deletes all characters in the text box.


To input new characters, you will need to navigate through the keyboard selections using the
navigation keys or RPG knob. Note that the currently selected character is 3 and this is indicated by
the orange highlight. Rotating the RPG clockwise, moves the highlight to the right. Rotating the RPG


138 Keysight AC6900 Operating and Programming Guide


3 User Information


counter clockwise; move the highlight to the left. In this way you can navigate to any character on the
keyboard.


When you have highlighted the desired character, either press Enter or press Next Char . This confirms
your selection and enters it into the oftkey if it is the last character that is currently being selected in

the text box.


Select Done when complete.


**IP Configuration Description**


The IP Configuration parameters include:



IP source:

DHCP


IP source:

:Manual



Automatically configures instrument addressing. When selected, the instrument will first try to
obtain an IP address from a DHCP server. If a DHCP server is found, the DHCP server will assign
an IP address, Subnet Mask, and Default Gateway to the instrument. If a DHCP server is unavailable, the instrument will try to obtain an IP address using AutoIP. AutoIP automatically assigns an
IP address, Subnet Mask, and Default Gateway addresses on networks that do not have a DHCP

server.


Manually configures instrument addressing by entering values in the three fields (listed below)
that only appear when DHCP is turned off and Manual appears in the IP source box.



IP Address Specifies the instrument's Internet Protocol (IP) address, which is required for all IP and TCP/IP
communications with the instrument. An IP Address is of the form nnn.nnn.nnn.nnn, where each
nnn is a decimal number from 0 to 255 with no leading zeros (for example, ************).


Subnet Mask Allows the instrument to determine whether a client IP address is on the same local subnet. The

same numbering notation applies as for the IP Address. When a client IP address is on a different
subnet, all packets must be sent to the Default Gateway.


DEF Gateway Specifies the IP Address of the default gateway that allows the instrument to communicate with
systems not on the local subnet, as determined by the subnet mask setting. The same numbering
notation applies as for the IP Address. A value of 0.0.0.0 indicates that no default gateway is

defined.


Dot notation addressing


Keysight AC6900 Operating and Programming Guide 139


3 User Information


Dot-notation addresses ("nnn.nnn.nnn.nnn" where "nnn" is a value from 0 to 255) must be

expressed with care, as most PC web software interprets byte values with leading zeros as octal
(base 8) numbers. For example, "***************" is actually equivalent to decimal "************"
because ".020" is interpreted as "16" expressed in octal, and ".011" as "9". To avoid confusion, use
decimal values from 0 to 255, without leading zeros.


**Auto DNS Configuration Description**


DNS is an internet service that translates domain names into IP addresses. It is also needed for the

instrument to find and display its host name assigned by the network. Normally, DHCP discovers the
DNS address information; you only need to change this if DHCP is unused or not functional.



AutoDNS

ON


AutoDNS

OFF


DNS(1)

address


DNS(2)

address



Automatically configures instrument addressing. When selected, the instrument will first try to
obtain an IP address from a DHCP server as previously described..


Manually configures instrument addressing by entering values in the two fields (listed below) that
only appear when AutoDNS OFF is selected.


Specifies the primary address of the server. Contact your LAN administrator for details.The same
numbering notation applies as for the IP Address.A value of 0.0.0.0 indicates that no default

server is defined.


Specifies the secondary address of the server. Contact your LAN administrator for details.The
same numbering notation applies as for the IP Address.A value of 0.0.0.0 indicates that no default

server is defined.



**DNS Hostname and mDNS Configuration Description**


A host name is the host portion of the domain name, which is translated into an IP address.


Each instrument is shipped with a default host name with the format: K-modelnumber-serialnumber,
where modelnumber is the unit’s 7-character model number (e.g. AC6803B), and serialnumber is the
last five characters of the 10-character serial number located on the label on the top of the unit, for
example 45678.


DNS hostname This field displays the DNS host name.. The name may contain upper and lower case letters, num
bers, and dashes (-).If the field is left blank, no name is registered. The maximum length is 15 char
acters.


mDNS ON With mDNS on, you can enter the DNS hostname into the mDNS service box


mDNS service This field registers the supplied name with the selected naming service. Applies when mDNS ON

is selected. With mDNS off, no name is registered.


140 Keysight AC6900 Operating and Programming Guide


3 User Information


**GPIB Status**


The GPIB interface requires the optional GPIB interface board and uses a standard IEEE-488 cable to
connect to the computer.


Each device on the GPIB (IEEE-488) interface must have a unique whole number address between 0
and 30 (default 5). Your computer’s GPIB interface card address must not conflict with any instrument
on the interface bus. View or change the GPIB address by pressing Utilities > IO Config - GPIB Status .


The following information is displayed:


Use the front panel numeric entry keys to enter a different GPIB address.


**Using the Web Interface**


Your instrument has a built-in Web interface that lets you control it directly from the Web browser on
your computer. With the Web interface, you can access the front panel control functions including the
LAN configuration parameters. Up to six simultaneous connections are allowed. With additional
connections, performance will be reduced.


The built-in Web interface only operates over the LAN. It requires Internet
Explorer 11, Firefox, Chrome, or Microsoft Edge.


The Web interface is enabled when shipped. To launch the Web interface:


1. Open your computer's Web browser.


2. Enter the instrument’s host name or IP address into the browser’s Address field. The following
welcome page will appear.


Keysight AC6900 Operating and Programming Guide 141


3 User Information


3. Click on the Browser Web Control tab in the navigation bar on the left to begin controlling your
instrument. The front panel of the Web interface works much the same way as the actual front panel

of the instrument.


A password is required to control the instrument using the Web interface. The
password is set to "Keysight" from the factory. To set a different password, click
View & Modify Configuration . Refer to the online help for details.


142 Keysight AC6900 Operating and Programming Guide


3 User Information


Should network communication issues occur, the instrument settings shown in the
Browser Web Control page may not represent the actual state of the instrument. This
may result in unexpected hazardous voltages on the output and sense connections that
could result in personal injury, death, or damage to a device under test. Before touching
the output or sense connections or connecting to a device under test, always verify the

state of the instrument.


4. Click the Configure LAN tab to edit the LAN configuration.


5. For additional help about any of the pages, click the Help with this Page tab.


**Using Telnet**


In a DOS command window, enter the command telnet host name 5024 where host name is the

instrument's host name or IP address, and 5024 is the instrument’s telnet port.


You should get a Telnet session box with a title indicating that you are connected to the instrument.
Type the SCPI commands at the prompt.


Keysight AC6900 Operating and Programming Guide 143


3 User Information


**Using Sockets**


The instrument allows any combination of up to six simultaneous data socket,
control socket, and telnet connections to be made.


The instrument uses port 5025 for SCPI socket services. A data socket on this port can be used to send
and receive ASCII/SCPI commands, queries, and query responses. All commands must be terminated
with a newline for the message to be parsed. All query responses will also be terminated with a

newline.


The socket programming interface also allows a control socket connection. The control socket can be
used by a client to send device clear and to receive service requests. Unlike the data socket, which
uses a fixed port number, the port number for a control socket varies and must be obtained by sending
the following SCPI query to the data socket: SYSTem:COMMunicate:TCPip:CONTrol?


After the port number is obtained, a control socket connection can be opened. As with the data socket,
all commands to the control socket must be terminated with a newline, and all query responses

returned on the control socket will be terminated with a newline.


To send a device clear, send the string "DCL" to the control socket. When the power system has
finished performing the device clear it echoes the string "DCL" back to the control socket.


Service requests are enabled for control sockets using the Service Request Enable register. Once
service requests have been enabled, the client program listens on the control connection. When SRQ
goes true the instrument will send the string "SRQ +nn" to the client. The "nn" is the status byte value,
which the client can use to determine the source of the service request.


**Using HiSLIP**


The High-Speed LAN Instrument Protocol (HiSLIP) is a protocol for TCP-based instrument control. It
includes conventional test and measurement protocol capabilities with minimal performance impact.


[For technical details regarding HiSLIP, see www.ivifoundation.org.](http://www.ivifoundation.org/)


**Typical Commands**


SYSTem:COMMunicate:LAN:CONTrol? Returns the TCP port number for SCPI-RAW.


SYSTem:COMMunicate:RLSTate LOCal Configures the remote/local state of the instrument.


SYSTem :COMMunicate:RLSTate? Queries the remote/local state of the instrument.


144 Keysight AC6900 Operating and Programming Guide


3 User Information

#### **Digital IO Configuration**


**Input Control**


**Output Status**


**Configure IO**


**Typical Commands**


External digital signals can be used to perform the following controls.


l Controlling the instrument through external contacts


l Monitoring the operation status


There are four channels (1 to 4) that are controlled using external contacts, four channels (5 to 8) that
monitor the operating status, and two channels (9 to 10) that you can select whether to control using
external contacts or monitor the operating status (Selectable I/O). These channels are accessible
through the Digital I/O connector. Refer to Analog and Digital Connections for information on wiring

and connections.


This section describes how to configure the Digital IO. Access the Digital IO controls by pressing


Advance > .


IO Config 

Digital IO


The following information is displayed:


Keysight AC6900 Operating and Programming Guide 145


3 User Information


This display shows the digital IO pinouts for the three different types of signals. An illustration of the
Digital connector is also shown. Each of these signal types can be further configured by pressing the
Input Control, Output Status or Config. IO softkeys.


**Input Control**


Select an input channel by selection the Input Control 1, Input Control 2, or Input Control 3 softkey.


You can enable or disable each of the channels by toggling the Input Ctrl softkey. Toggle the Polarity
softkey to assign the signal polarity. When configured, the Inhibit Control signal can be latched to stay
on by toggling the Mode softkey.


For Channels 1-3, you can select (or map) to any of the control signals listed as follows. Note that
Channel 4 is reserved for the shutdown signal and is turned on by default and cannot be changed.


There are two additional channels (Channel 9 and Channel 10) that you can configure as Input
Controls or as Output Status signals. These two channels can also be assigned to the following control
signals.


However, you cannot assign a control signal to more than one channel. Therefore, excluding the
Shutdown function (Channel 4), you can only assign five control signals to the maximum available
channels (Channels 1-3 and Channels 9 and 10 if configured as control signals.) Note that the last two
control signals in the list can only be assigned to channels 9 and 10.


When the control signal is true, the selected function is enabled.


Refer to Digital IO for a list of all the selectable Input Control signals.


146 Keysight AC6900 Operating and Programming Guide


3 User Information


**Output Status**


Select an input channel by selection the Output Status 5, Output Status 6, Output Status 7, or Output
Status 8 softkey.


You can enable or disable each of the channels by toggling the Out Status softkey, Toggle the Polarity
softkey to assign the signal polarity


For Channels 5-7, you can select (or map) to any of the status signals listed as follows. Note that
Channel 8 is reserved for the Protection status. As mentioned above, there are two additional channels
(Channel 9 and Channel 10) that you can configure as Output Status signals.


However, in contrast to the Input Control signal, the Output Status signal CAN be assigned to more
than one channel. However you can still only assign five status signals total to the maximum available
channels (Channels 5-7 and Channels 9 and 10 if configured as status signals.) Note that the last two
status signals in the list can only be assigned to channels 9 and 10.


When the status signal is true, the selected status is true.


Refer to Digital IO for a list of all the selectable Output Status signals.


Keysight AC6900 Operating and Programming Guide 147


3 User Information


**Configure IO**


Channels 9 and 10 can be configured to be either Input Control, or Output Status signals.


You can enable or disable each of the channels by toggling the Config IO softkey, Toggle the Direction
softkey to designate the signal as either Input Control or Output Status. Toggle the Polarity softkey to
assign the signal polarity. When configured either way, any of the Input Control or Output Status
signals can be mapped to the two Configurable IO channels. When configured, the Inhibit Control
signal can be latched to stay on by toggling the Mode softkey.


Note that there are two additional Clock In/Out signals and two additional SEQ Trigger IN/Out signals
that can only be assigned to the Configurable IO channels.


Refer to Digital IO for a list of all the Configurable IO signals.


**Typical Commands**


DIGital:INPut:DATA? Queries the state of the digital input port.


DIGital:OUTPut:DATA < _value_ - Sets the state of the digital output port.


DIGital:PIN<1-3,5-7,9,10>:FUNCtion <map_item> Maps the designated digital IO pin to a specific
in/out function signal.


DIGital:PIN<1-3,5-10>:POLarity POSitive|NEGative Sets the polarity of the designated digital IO
pin.


148 Keysight AC6900 Operating and Programming Guide


Keysight AC6900 Operating and Programming Guide

# 4 SCPI Programming


**Introduction to SCPI**

**Command Quick Reference**
**Commands by Subystem:**


**ABORt Subsystem**
**CURRent Subsystem**
**DIGital Subsytem**
**DISPlay Subsystem**
**FETCh/MEASure Subsystem**
**FREQuency Subsystem**
**FUNCtion Subsystem**
**HCOPy Subsystem**
**IEEE-488 Common Commands**

**INITiate Subsystem**
**INSTrument Subsystem**
**LXI Subsystem**
**MEMory Subsystem**
**OUTPut Subsystem**
**PROGram Subsystem**
**SENSe Subsystem**
**SIMulation Subsystem**
**STATus Subsystem**
**SYSTem Subsystem**
**TRIGger Subsystem**
**VOLTage Subsystem**
**WAVE Subsystem**


**Trigger System Overview**
**Status System Overview**
**Default Settings**
**Error and Limit Checking**


4 SCPI Programming

#### **Introduction to SCPI**


Introduction


**Keywords**


**Queries**


**Command Separators and Terminators**


**Syntax Conventions**


**Parameter Types**


**Device Clear**


**Typical Command Processing Times**


In addition to using SCPI commands, you can also control the instrument using
[the IVI driver, available at ivifoundation.org.](http://ivifoundation.org/)


**Introduction**


This manual describes instrument programming using Standard Commands for Programmable
Instruments (SCPI) over LAN, USB, and GPIB (optional). Select the interface type from the front panel,
and be sure to understand the SCPI syntax and functions before you send SCPI commands and
queries. In addition to using SCPI commands, you can also control the instrument using the IVI driver,
[available at ivifoundation.org.](http://ivifoundation.org/)


The SCPI language is an ASCII-based programming language for test and measurement instruments.
SCPI has two types of commands, common and subsystem.


**IEEE-488.2 Common Commands**


The IEEE-488.2 standard defines common commands that perform functions such as reset, self-test,
and status. Common commands always begin with an asterisk ( - ), are three characters long, and may
include parameters. The command keyword is separated from the first parameter by a space.


**Subsystem Commands**


Subsystem commands perform specific instrument functions. They extend one or more levels below
the root in a hierarchical structure. Associated commands are grouped under a common node, thus
forming subsystems. A portion of the OUTPut subsystem is shown below to illustrate the tree system.
Note that the brackets around a keyword, such as in [:STATe], indicate an optional keyword.


:OUTPut


[:STATe] ON|1|OFF|0

:COUPling AC|DC|ACDC

:PROTection

:CLEar


150 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Keywords**


Keywords, also referred to as headers, are instructions recognized by the instrument. Common
commands are also keywords.


OUTPut is a root keyword, STATe, COUPling, and PROTection are second-level keywords, and CLEar
are third-level keywords. Colons ( : ) separate the keyword levels.


The command syntax shows most commands (and some parameters) as a mixture of upper- and
lower-case letters. The upper-case letters indicate the abbreviated spelling for the command. For
shorter program lines, you can send the abbreviated form. For better readability, send the long form.


In the above examples, OUTP and OUTPUT are both acceptable. You can use upper- or lower-case
letters. Therefore, OUTPUT, outp, and OuTp are all acceptable. Other forms, such as OUT, will

generate an error.


**Queries**


Following a keyword with a question mark ( ? ) turns it into a query (Example: VOLTage?,
VOLTage:TRIGgered?). If a query contains parameters, place the question mark after the last keyword,
before the parameters. Insert a space between the question mark and the first parameter.


You can query the value of most parameters. For example, to query the output voltage, send:


VOLTage?


You can also query the minimum or maximum allowable voltage:


VOLTage? MIN

VOLTage? MAX


Wait for all of the results of a query to be returned before sending another command or query.
Otherwise, a _Query Interrupted_ error will occur and the unreturned data will be lost.


**Command Separators and Terminators**


**Separators**


Colons ( : ) separate keyword levels. Blank spaces separate command parameters from their
corresponding keyword. If a command requires more than one parameter, a comma separates
parameters. In the following example, the frequency (100), low frequency limit (90), and high frequency
limit (110) are separated by commas. Note the space after the word FREQuency.


FREQuency 100,90,110


Semicolons ( ; ) separate commands within the same subsystem. This lets you send several subsystem
commands within the same message string. For example, the following command:


FREQuency 100,90,110;MODE FIXed


Keysight AC6900 Operating and Programming Guide 151


4 SCPI Programming


is the same as sending the following commands:


FREQuency 100,90,110

FREQuency:MODE FIXed


You can also combine commands of different subsystems within a message string. In this case, you
must use a colon to return the command parser to the root level in order to access another subsystem.
For example, you could specify the frequency and turn on the output as follows:


FREQuency 100,90,110;:OUTPut ON


The colon after the semicolon returns the command parser to the root.


**Terminators**


A command string must terminate with a new line (<NL>) character. The IEEE-488 EOI (End-OrIdentify) message is interpreted as a <NL> character and can be used to terminate a command string
in place of an <NL>. A carriage return followed by a new line (<CR><NL>) is also accepted. Command
string termination always resets the SCPI command path to the root level.


**Syntax Conventions**


Triangle brackets ( < - ) indicate that a parameter. For example, in the command syntax DISPlay

[:WINDow]:TEXT "< _string_ >", the < _string_ - parameter is inside triangle brackets. These brackets are not
sent with the command string. For example, you could send DISPlay:WINDow:TEXT "Test in progress".


A vertical bar ( | ) separates multiple parameter choices for a given command string. For example,
AC|DC|ACDC in the OUTPut:COUPling command indicates that you can specify AC, DC, or ACDC. The
bar is not sent with the command string.


Square brackets ( [ ] ) indicate that a keyword or parameter is optional. These brackets are not sent with
the command string. If you do not specify a value for an optional parameter, the instrument ignores the
parameter. In the DISPlay[:WINDow]:TEXT example above, the optional [:WINDow] level means that
DISPlay:TEXT is the same as DISPlay:WINDow:TEXT.


**Parameter Types**


The SCPI language defines several data formats to be used in commands and queries.


**Numeric Parameters**


Commands that require numeric parameters will accept all commonly used decimal representations of
numbers including optional signs, decimal points, and scientific notation. If a command accepts only
certain specific values, the instrument will automatically round the input numeric parameters to the
accepted values. The following command requires a numeric parameter for the voltage value:


[SOURce:]CURRent < _value_ >|MINimum|MAXimum


152 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


Note that special values for numeric parameters such as MINimum and MAXimum are also accepted.
Instead of selecting a specific value for the voltage parameter, you can substitute MIN to set the
voltage to its minimum allowable value, or MAX to set it to its maximum allowable value.


You can also optionally include the following engineering unit suffixes with numeric parameters:


Voltage V Reactance H


Current A Hours HR


Wattage W Minutes MIN


Apparent Power VA Seconds S


Reactive Power VAR Percent PCT


Degrees DEG Rersistance OHM


Frequency HZ


You may also put the following prefixes before a unit of measure:


U micro 1/1,000,000


M milli 1/1,000


K kilo 1,000


As with other SCPI keywords, both units and prefixes are case-insensitive. Thus, you could indicate
millivolts as mv, mV, Mv, or MV.


**Discrete Parameters**


Discrete parameters specify settings that have a limited number of values (like IMMediate, EXTernal, or
BUS). Like command keywords, they may have short and long forms. You can mix upper- and lowercase letters. Query responses always return the short form in upper-case letters. The following
command requires a discrete parameter:


DISPlay:VIEW METER_VI|METER_VP|METER VIP


**Boolean Parameters**


Boolean parameters represent a single binary condition that is either true (1 or ON) or false (0 or OFF).
A boolean query always returns 0 or 1. The following command requires a boolean parameter:


DISPlay ON|1|OFF|0


**ASCII String Parameters**


String parameters can contain virtually any ASCII characters. A string must begin and end with
matching single (') or double (") quotation marks. To include the quote delimiter as part of the string,
enter it twice without any characters in between. The following command uses a string parameter:


DISPlay:TEXT "Test in progress . . ."


Keysight AC6900 Operating and Programming Guide 153


4 SCPI Programming


**Arbitrary Block Program or Response Data**


Definite-length block data <Block> allows any type of device-dependent data to be programmed or
returned as a series of 8-bit binary data bytes. This is particularly useful for transferring large quantities

of data or 8-bit extended ASCII codes.


Arbitrary block data that starts with #.


**Phase Designations**


When the parameter is numeric, this product allows you to designate the phases you want to set using
the syntax (@chanlist). The INSTrument command settings do not apply to phase-designated

commands.


Phase 1 is (@1), Phase 2 is (@2), and Phase 3 is (@3).


In the following example, the Phase 2 is set to 130 V.


VOLT 130,(@2)


Multiple phases can be designated at once. To designate Phase 1 (@1) and Phase 3 (@3)


VOLT 130,(@1,3)


To designate all phases (@1 to @3)


VOLT 130,(@1:3)


**Device Clear**


Device Clear is an IEEE-488 low-level bus message that you can use to return the instrument to a
responsive state. Different programming languages and IEEE-488 interface cards provide access to
this capability through their own unique commands. The status registers, the error queue, and all
configuration states are left unchanged when a Device Clear message is received.


Device Clear performs the following actions:


If a measurement is in progress, it is aborted.


The instrument returns to the trigger idle state.


154 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


The instrument's input and output buffers are cleared.


The instrument is prepared to accept a new command string.


The ABORt command is the recommended method to terminate an instrument

operation.


**Typical Command Processing Times**


The table below documents some typical, average command processing times (milliseconds) for
various commands and queries. This can help you determine the impact of some common SCPI

commands on total test time.


The command processing time is the time until the next command is accepted. It does not include
hardware response time.


**Command** **GPIB** **[[1]]** **USB** **Description**


*CLS 5 6 Clear the status data.


*RCL 233 230 Recall the contents of a state storage location.


*RST 233 230 Perform a device reset.


*SAV 13 14 Save the current settings.


FREQuency 15 17 Set the AC output frequency.


MEASure:CURRent:AC? 333 333 Query the AC current.


MEASure:VOLTage:AC? 333 333 Query the AC voltage.


OUTPut OFF 19 23 Turn the output OFF.


OUTPut ON 9 11 Turn the output on.


VOLTage 16 18 Set the AC voltage.


[1]
Using Keysight IO Libraries.


Keysight AC6900 Operating and Programming Guide 155


4 SCPI Programming

#### **Command Quick Reference**


**ABORt Subsystem**


ABORt[:ALL] Aborts all operations.


ABORt:ACQuire Aborts all ACQuire operations.


ABORt:PROGram Aborts all PROGram operations.


ABORt:SIMulation Aborts all SIMulation operations.


ABORt:TRANsient Aborts all TRANsient actions.


**CURRent Subsytem**


**Command/Query** **Description**




[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] < _value_ >|MINimum|MAXimum[,< _lower_limit_ >,< _upper_limit_ >] [,(@chanlist)]

[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum] [,
(@chanlist)]


[SOURce:]CURRent:PEAK:LOWer < _value_ >|MINimum|MAXimum [,(@chanlist)]

[SOURce:]CURRent:PEAK:LOWer? [MINimum|MAXimum] [,(@chanlist)]


[SOURce:]CURRent:PEAK:UPPer < _value_ >|MINimum|MAXimum [,(@chanlist)]

[SOURce:]CURRent:PEAK:UPPer? [MINimum|MAXimum] [,(@chanlist)]


[SOURce:]CURRent:PROTection:STATe ON|1|OFF|0

[SOURce:]CURRent:PROTection:STATe?


[SOURce:]CURRent:PROTection:TRIP:DELay < _value_ 
[SOURce:]CURRent:PROTection:TRIP:DELay?


**DIGital Subsystem**



Sets the immediate AC current limit in amps
(rms). Specifies optional lower and upper peak

limits.


Specifies the lower peak current limit in amps.


Specifies the upper peak current limit in amps.


Enables or disables current protection.


Sets the protection trip delay in seconds.



**Command/Query** **Description**


DIGital:INPut:DATA? Queries the state of the digital input port.



DIGital:OUTPut:DATA < _value_ 
DIGital:OUTPut:DATA?


DIGital:PIN<1-3,5-7,9,10>:FUNCtion <map_item>

DIGital:PIN<1 - 10>:FUNCtion?


DIGital:PIN<1-3,5-10>:POLarity POSitive|NEGative
DIGital:PIN<1-10>:POLarity?



Sets the state of the digital output port.


Maps the designated digital IO pin to a specific in/out
function signal.


Sets the polarity of the designated digital IO pin.



156 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**DISPlay Subsystem**


**Command/Query** **Description**



DISPlay[:WINDow][:STATe] ON|1|OFF|0
DISPlay[:WINDow][:STATe]?


DISPlay[:WINDow]:METer:COUPling AC|DC|ACDC|PEAK,
AC|DC|ACDC|PEAK
DISPlay[:WINDow]:METer:COUPling?


DISPlay[:WINDow]:METer:COUPling:CURRent AC|DC|ACDC|PEAK
DISPlay[:WINDow]:METer:COUPling:CURRent?


DISPlay[:WINDow]:METer:COUPling:VOLTage AC|DC|ACDC|PEAK
DISPlay[:WINDow]:METer:COUPling:VOLTage?


DISPlay[:WINDow]:PHASe P123|ABC|UVW
DISPlay[:WINDow]:PHASe?


DISPlay[:WINDow]:VIEW METER_ALL|METER_3P|METER_1P
DISPlay[:WINDow]:VIEW?


**FETCh and MEASure Subsystems**



Turns the front-panel display on or off.


Selects both the voltage and current measurement

unit.


Selects the current measurement unit.


Selects the voltage measurement unit.


Selects the phase-naming convention.


Selects the phases to display.



**Command/Query** **Description**



FETCh[:SCALar]:ALL? [(@chanlist)]
MEASure[:SCALar]:ALL? [(@chanlist)]


FETCh[:SCALar]:CURRent:AC? [(@chanlist)]
MEASure[:SCALar]:CURRent:AC? [(@chanlist)]


FETCh[:SCALar]:CURRent:ACDC? [(@chanlist)]
MEASure[:SCALar]:CURRent:ACDC? [(@chanlist)]


FETCh[:SCALar]:CURRent:AMPLitude:MAXimum? [(@chanlist)]
MEASure[:SCALar]:CURRent:AMPLitude:MAXimum? [(@chanlist)]


FETCh[:SCALar]:CURRent:AMPLitude:MAXimum:HOLD? [(@chanlist)]
MEASure[:SCALar]:CURRent:AMPLitude:MAXimum:HOLD? [(@chanlist)]


FETCh[:SCALar]:CURRent:CREStfactor? [(@chanlist)]
MEASure[:SCALar]:CURRent:CREStfactor? [(@chanlist)]


FETCh[:SCALar]:CURRent[:DC]? [(@chanlist)]
MEASure[:SCALar]:CURRent[:DC]? [(@chanlist)]


FETCh[:SCALar]:FREQuency? [(@chanlist)]
MEASure[:SCALar]:FREQuency? [(@chanlist)]


FETCh[:SCALar]:POWer[:DC]? [(@chanlist)]
MEASure[:SCALar]:POWer[:DC]? [(@chanlist)]


FETCh[:SCALar]:POWer:AC[:REAL]? [(@chanlist)]
MEASure[:SCALar]:POWer:AC[:REAL]? [(@chanlist)]


FETCh[:SCALar]:POWer:AC:APParent? [(@chanlist)]
MEASure[:SCALar]:POWer:AC:APParent? [(@chanlist)]



Returns all measurements, except TOTal measurements, as a comma-separated list.


AC current (Arms)


Current, AC+DC (Arms)


Instantaneous peak current (A)


Peak AC current held value (A)


Current crest factor


Average DC current (A)


AC output frequency (Hz)


Average DC power (W)


AC active power (W)


AC apparent power (VA)



Keysight AC6900 Operating and Programming Guide 157


4 SCPI Programming


**Command/Query** **Description**



FETCh[:SCALar]:POWer:AC:REACtive? [(@chanlist)]
MEASure[:SCALar]:POWer:AC:REACtive? [(@chanlist)]


FETCh[:SCALar]:POWer:AC:PFACtor? [(@chanlist)]
MEASure[:SCALar]:POWer:AC:PFACtor? [(@chanlist)]


FETCh[:SCALar]:POWer:ACDC[:REAL]? [(@chanlist)]
MEASure[:SCALar]:POWer:ACDC[:REAL]? [(@chanlist)]


FETCh[:SCALar]:POWer:ACDC:APParent? [(@chanlist)]
MEASure[:SCALar]:POWer:ACDC:APParent? [(@chanlist)]


FETCh[:SCALar]:POWer:ACDC:REACtive? [(@chanlist)]
MEASure[:SCALar]:POWer:ACDC:REACtive? [(@chanlist)]


FETCh[:SCALar]:POWer:ACDC:PFACtor? [(@chanlist)]
MEASure[:SCALar]:POWer:ACDC:PFACtor? [(@chanlist)]


FETCh[:SCALar]:VOLTage[:DC]? [(@chanlist)]
MEASure[:SCALar]:VOLTage[:DC]? [(@chanlist)]


FETCh[:SCALar]:VOLTage:AC? [(@chanlist)]
MEASure[:SCALar]:VOLTage:AC? [(@chanlist)]


FETCh[:SCALar]:VOLTage:ACDC? [(@chanlist)]
MEASure[:SCALar]:VOLTage:ACDC? [(@chanlist)]


FETCh[:SCALar]:VOLTage:AMPLitude:MAXimum? [(@chanlist)]
MEASure[:SCALar]:VOLTage:AMPLitude:MAXimum? [(@chanlist)]


FETCh[:SCALar]:VOLTage:AMPLitude:MAXimum:HOLD? [(@chanlist)]
MEASure[:SCALar]:VOLTage:AMPLitude:MAXimum:HOLD? [(@chanlist)]


FETCh[:SCALar]:LTLVoltage:DC? [(@chanlist)]
MEASure[:SCALar]:LTLVoltage:DC? [(@chanlist)]


FETCh[:SCALar]:LTLVoltage:AC? [(@chanlist)]
MEASure[:SCALar]:LTLVoltage:AC? [(@chanlist)]


FETCh[:SCALar]:LTLVoltage:ACDC? [(@chanlist)]
MEASure[:SCALar]:LTLVoltage:ACDC? [(@chanlist)]


FETCh[:SCALar]:LTLVoltage:AMPLitude:MAXimum? [(@chanlist)]
MEASure[:SCALar]:LTLVoltage:AMPLitude:MAXimum? [(@chanlist)]


The following returns the total from all phases


FETCh[:SCALar]:POWer[:DC]:TOTal?
MEASure[:SCALar]:POWer[:DC]:TOTal?


FETCh[:SCALar]:POWer:ACDC[:REAL]:TOTal?
MEASure[:SCALar]:POWer:ACDC[:REAL]:TOTal?


FETCh[:SCALar]:POWer:AC:APParent:TOTal?
MEASure[:SCALar]:POWer:AC:APParent:TOTal?


FETCh[:SCALar]:POWer:AC:REACtive:TOTal?
MEASure[:SCALar]:POWer:AC:REACtive:TOTal?


FETCh[:SCALar]:POWer:AC:PFACtor:TOTal?
MEASure[:SCALar]:POWer:AC:PFACtor:TOTal?



AC reactive power (VAR)


AC power factor


AC active power (W)


AC+DC apparent power (VA)


AC+DC reactive power (VAR)


AC+DC power factor


Average DC voltage (V)


AC voltage (Vrms)


Voltage, AC+DC (Vrms)


Instantaneous peak voltage (V)


Peak AC voltage held value (V)


Line-to-line average DC voltage (V)


Line-to-line AC voltage (Vrms)


Line-to-line voltage AC+DC voltage (Vrms)


Line-to-line peak voltage (V)


Total DC power (W)


Total AC active power (W)


Total AC apparent power (VA)


Total AC reactive power (VAR)


Total AC power factor



158 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**Command/Query** **Description**



FETCh[:SCALar]:POWer:ACDC[:REAL]:TOTal?
MEASure[:SCALar]:POWer:ACDC[:REAL]:TOTal?


FETCh[:SCALar]:POWer:ACDC:APParent:TOTal?
MEASure[:SCALar]:POWer:ACDC:APParent:TOTal?


FETCh[:SCALar]:POWer:ACDC:REACtive:TOTal?
MEASure[:SCALar]:POWer:ACDC:REACtive:TOTal?


FETCh[:SCALar]:POWer:ACDC:PFACtor:TOTal?
MEASure[:SCALar]:POWer:ACDC:PFACtor:TOTal?


The following returns harmonic data


FETCh:CURRent:HARMonic[AMPLitude]? [(@chanlist)]
MEASure:CURRent:HARMonic[AMPLitude]? [(@chanlist)]


FETCh:CURRent:HARMonic:PHASe? [(@chanlist)]
MEASure:CURRent:HARMonic:PHASe? [(@chanlist)]


FETCh:CURRent:HARMonic:THD? [(@chanlist)]
MEASure:CURRent:HARMonic:THD? [(@chanlist)]


FETCh:VOLTage:HARMonic[AMPLitude]? [(@chanlist)]
MEASure:VOLTage:HARMonic[AMPLitude]? [(@chanlist)]


FETCh:VOLTage:HARMonic:PHASe? [(@chanlist)]
MEASure:VOLTage:HARMonic:PHASe? [(@chanlist)]


FETCh:VOLTage:HARMonic:THD? [(@chanlist)]
MEASure:VOLTage:HARMonic:THD? [(@chanlist)]


**FREQuency Subsystem**



Total AC active power (W)


Total AC+DC apparent power (VA)


Total AC+DC reactive power (VAR)


Total AC+DC power factor


Harmonic current amplitude


Harmonic current phase


Total harmonic distortion (THD)


Harmonic voltage amplitude


Harmonic voltage phase


Total harmonic distortion (THD)



**Command/Query** **Description**




[SOURce:]FREQuency[:CW] < _value_ >|MINimum|MAXimum[,< _lower_limit_ >,< _upper__
_limit_ >]

[SOURce:]FREQuency[:CW]? [MINimum|MAXimum]

[SOURce:]FREQuency[:IMMediate] < _value_ >|MINimum|MAXimum[,< _lower__
_limit_ >,< _upper_limit_ >]

[SOURce:]FREQuency[:IMMediate]? [MINimum|MAXimum]


[SOURce:]FREQuency:LIMit:LOWer <value>

[SOURce:]FREQuency:LIMit:LOWer? [MINimum|MAXimum]


[SOURce:]FREQuency:LIMit:UPPer <value>

[SOURce:]FREQuency:LIMit:UPPer? [MINimum|MAXimum]


[SOURce:]FREQuency:TRIGgered < _value_ >|MINimum|MAXimum

[SOURce:]FREQuency:TRIGgered? [MINimum|MAXimum]


[SOURce:]FREQuency:SYNCronous[:STATe] ON|1|OFF|0

[SOURce:]FREQuency:SYNCronous[:STATe]?


[SOURce:]FREQuency:SYNChronous:MODE LINE|EXTernal

[SOURce:]FREQuency:SYNChronous:MODE?


[SOURce:]FREQuency:SYNChronous:PHASe:DELay < _value_ >|MINimum|MAXimum

[SOURce:]FREQuency:SYNChronous:PHASe:DELay? [MINimum|MAXimum]



Sets the immediate AC frequency and optionally the lower and upper frequency soft limits.


Sets the lower AC frequency soft limit.


Sets the upper AC frequency soft limit.


Sets the triggered AC output frequency in

Hertz.


Enables or disables frequency synchronous
operation.


Sets the synchronization signal source.


Sets the synchronization offset angle in
degrees.



Keysight AC6900 Operating and Programming Guide 159


4 SCPI Programming


**FUNCtion Subsystem**


**Command/Query** **Description**




[SOURce:]FUNCtion[:SHAPe]:BANK[:INDex] < _index_ - [,(@chanlist)]

[SOURce:]FUNCtion[:SHAPe]:BANK[:INDex]? [(@chanlist)]


**HCOPy Subsystem**



Selects a waveform shape by bank index.



**Command/Query** **Description**


HCOPy:SDUMp:DATA? Returns the display image in .png format.


**IEEE-488 Subsystem**


**Command/Query** **Description**


*CLS Clear status command.



*ESE < _value_ 
*ESE?



Event status enable command and query.



*ESR? Event status event query.


*IDN? Identification Query. Returns the instrument’s identification string.


*OPC Sets the OPC (operation complete) bit in the standard event register.


*OPC? Returns a 1 to the output buffer when all pending operations complete.


*OPT? Returns a string identifying any installed options.



*PSC 0|1

*PSC?



Enables (1) or disables (0) the clearing of certain enable registers at power on.



*RCL < _0 to 10_ - Recalls a saved instrument state.


*RST Resets the instrument to default values.


*SAV <0 to 10> Saves the instrument state to a nonvolatile memory location.



*SRE < _value_ 
*SRE?



Service request enable command and query.



*STB? Status byte query. Reads the Status Byte Register, which contains the status summary bits and the Output
Queue MAV bit.


*TRG Trigger command. Applies a software trigger (equivalent to IEEE 488.1 Device Trigger) for both TRANsient
and ACQuire trigger groups.


*TST? Self-test. Returns the errors found during the most recent power-on self-test.


160 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**INITiate Subsystem**


**Command/Query** **Description**


INITiate[:IMMediate]:ACQuire Initiates the ACQuire (measurement) operation.


INITiate[:IMMediate]:PROGram Initiates the PROGram operation.


INITiate[:IMMediate]:SIMulation Initiates the SIMulation operation.


INITiate[:IMMediate]:TRANsient Initiates the TRANsient operation.


**INSTrument Subsystem**


**Command/Query** **Description**



INSTrument[:SELect] OUTPut0|OUTPut1|OUTPut2|OUTPut3
INSTrument[:SELect]?


INSTrument:NSELect 0|1|2|3

INSTrument:NSELect?


**LXI Subsystem**



Selects the phase to be controlled by output phase name.


Selects the phase to be controlled by output phase number.



**Command/Query** **Description**



LXI:IDENtify[:STATe] ON|1|OFF|0
LXI:IDENtify[:STATe]?


LXI:MDNS[:STATe] ON|1|OFF|0
LXI:MDNS[:STATe]?


**MEMory Subsystem**



Turns the front-panel LXI identify indicator (blinking "Lan" annunciator) on or off.


Turns the LAN mDNS service on or off.



**Command/Query** **Description**


MEMory:RECall[:IMMediate] 1|2 Recalls the memory saved in location 1 or 2


MEMory:RECall:PREView? 1|2 Displays the memory contents saved in location 1 or 2


MEMory:SAVE[:IMMediate] 1|2 Saves the output settings to memory location 1 or 2


**OUTPut Subsystem**


**Command/Query** **Description**



OUTPut[:STATe] ON|1|OFF|0
OUTPut[:STATe]?


OUTPut:COUPling AC|DC|ACDC
OUTPut:COUPling?


OUTPut:IMPedance[:STATe] ON|1|OFF|0
OUTPut:IMPedance[:STATe]?



Enables or disables the instrument's output.


Sets the output coupling mode.


Enables or disables the output impedance control.



Keysight AC6900 Operating and Programming Guide 161


4 SCPI Programming


**Command/Query** **Description**



OUTPut:IMPedance:REACtive < _value_ >|MINimum|MAXimum [,(@chanlist)]
OUTPut:IMPedance:REACtive? [MINimum|MAXimum] [(@chanlist)]


OUTPut:IMPedance:REAL < _value_ >|MINimum|MAXimum [,(@chanlist)]
OUTPut:IMPedance:REAL? [MINimum|MAXimum] [(@chanlist)]


OUTPut:INHibit:MODE LATChing|LIVE

OUTPut:INHibit:MODE?


OUTPut:PHASe:OFF[:STATe] ON|Free|OFF|Fixed
OUTPut:PHASe:OFF[:STATe]?


OUTPut:PHASe:OFF:LEVel < _value_ 
OUTPut:PHASe:OFF:LEVel?


OUTPut:PHASe:ON[:STATe] ON|Free|OFF|Fixed
OUTPut:PHASe:ON[:STATe]?


OUTPut:PHASe:ON:LEVel < _value_ 
OUTPut:PHASe:ON:LEVel?


OUTPut:PHASe:P1Offset < _value_ >|MINimum|MAXimum
OUTPut:PHASe:P1Offset? [MINimum|MAXimum]


OUTPut:PHASe:P12 < _value_ >|MINimum|MAXimum
OUTPut:PHASe:P12? [MINimum|MAXimum]


OUTPut:PHASe:P13 < _value_ >|MINimum|MAXimum
OUTPut:PHASe:P13? [MINimum|MAXimum]


OUTPut:PON:SAFE ON|1|OFF|0

OUTPut:PON:SAFE?


OUTPut:PON:STATe RST|RCL<0-9>|AUTO

OUTPut:PON:STATe?



Sets the reactive part of the output impedance in Hen
ries.


Sets the resistance part of the output impedance in

ohms.


Sets the operating mode of the remote inhibit digital
pin.


Enables or disables the output-off phase control

when OUTPut OFF is sent.


Sets the output-off phase angle when OUTPut OFF is
sent in degrees.


Enables or disables the output-on phase control

when OUTPut ON is sent.


Sets the output-on phase angle when OUTPut ON is
sent in degrees.


Sets the absolute phase 1 offset angle to the specified
reference in degrees.


Sets the difference between the phase 1 angle and
the phase 2 angle in degrees.


Sets the difference between the phase 1 angle and
the phase 3 angle in degrees.


Enables or disables the power-on output state.


Specifies the output power-on state.



OUTPut:PROTection:CLEar Resets the latched protection.



OUTPut:PROTection:WDOG[:STATe] ON|1|OFF|0
OUTPut:PROTection:WDOG[:STATe]?


OUTPut:PROTection:WDOG:DELay < _value_ >|MINimum|MAXimum
OUTPut:PROTection:WDOG:DELay? [MINimum|MAXimum]


OUTPut:SSTart[:STATe][:RISE] ON|1|OFF|0
OUTPut:SSTart[:STATe][:RISE]?


OUTPut:SSTart[:STATe]:FALL ON|1|OFF|0
OUTPut:SSTart[:STATe]:FALL?


OUTPut:SSTart:TIME[:RISE] < _value_ >|MINimum|MAXimum
OUTPut:SSTart:TIME[:RISE]? [MINimum|MAXimum]


OUTPut:SSTart:TIME:FALL < _value_ >|MINimum|MAXimum
OUTPut:SSTart:TIME:FALL? [MINimum|MAXimum]


OUTPut:SSUPpression:STATe ON|1|OFF|0
OUTPut:SSUPpression:STATe?



Enables or disables the I/O watchdog timer.


Sets the watchdog delay time.


Enables or disables the soft-start rise.


Enables or disables the soft-start fall.


Sets the soft-start rise time in seconds.


Sets the soft-start fall time in seconds.


Enables or disables the voltage surge suppression fea
ture.



162 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**PROGram Subsytem**


**Command/Query** **Description**


PROGram:CLEar Sets all sequence steps to their default values.



PROGram:EDIT < _step_ >,< _freq_ >,< _ramp-bool_ >,< _ac-volt_ >,< _ramp-bool_ >,< _dc-_
_volt_ >,< _ramp-bool_ >,< _step-time_ >,< _output-bool_ >,< _status-bool_ >,< _trigin-_
_bool_ >,< _trigout-bool_ >,< _bank-number_ PROGram:EDIT? < _step_ 

PROGram:EDIT:FUNCtion[:SHAPe]:BANK[:INDex] < _step_ >, < _bank-number_ 
[,(@chanlist)]
PROGram:EDIT:FUNCtion[:SHAPe]:BANK[:INDex]? < _step_ - [,(@chanlist)]


PROGram:EDIT:IMPedance[:STATe] < _step_ >,ON|1|OFF|0
PROGram:EDIT:IMPedance[:STATe]? < _step_ 

PROGram:EDIT:IMPedance:REACtive < _step_ >, < _value_ - [,(@chanlist)]
PROGram:EDIT:IMPedance:REACtive? < _step_ - [,(@chanlist)]


PROGram:EDIT:IMPedance:REAL < _step_ >, < _value_ - [,(@chanlist)]
PROGram:EDIT:IMPedance:REAL? < _step_ - [,(@chanlist)]


PROGram:EDIT:JUMP < _step_ >,< _jump-bool_ >,< _jump-step_ >,< _jump-count_ PROGram:EDIT:JUMP? < _step_ 

PROGram:EDIT:PHASe:P1Offset < _step_ >,< _enable-bool_ >,< _value_ PROGram:EDIT:PHASe:P1Offset? < _step_ 

PROGram:EDIT:PHASe:P12 < _step_ >,< _enable-bool_ >,< _value_ PROGram:EDIT:PHASe:P12? < _step_ 

PROGram:EDIT:PHASe:P13 < _step_ >,< _enable-bool_ >,< _value_ PROGram:EDIT:PHASe:P13? < _step_ 

PROGram:EDIT:PHASe:RAMP < _step_ >, <OFF|LEAD|LAG>,<OFF|LEAD|LAG>,
<OFF|LEAD|LAG>
PROGram:EDIT:PHASe:RAMP? < _step_ 

PROGram:EDIT:PHASe:STARt < _step_ >, < _value_ PROGram:EDIT:PHASe:STARt? < _step_ 

PROGram:EDIT:PHASe:STOP < _step_ >, < _value_ PROGram:EDIT:PHASe:STOP? < _step_ 

PROGram:EDIT:VOLTage < _step_ >, < _value_ - [,(@chanlist)]
PROGram:EDIT:VOLTage? < _step_ 

PROGram:EDIT:VOLTage:OFFSet < _step_ >, < _value_ - [,(@chanlist)]
PROGram:EDIT:VOLTage:OFFSet? < _step_ 

PROGram:EXECuting STOP|RUN|PAUSe < _time_ >,< _loop_ >,< _step_ PROGram:EXECuting?


PROGram:LOOP < _value_ 
PROGram:LOOP?



Programs the sequence step.


Sets the waveform bank number of the sequence

step.


Enables or disables the output impedance of the step.


Sets the reactive component of the output impedance
of the step.


Sets the resistive component of the output impedance of the step.


Configures the jump settings of the sequence step.


Sets the phase 1 offset angle of the sequence step.


Sets the phase 1 and phase 2 angle difference of the

sequence step.


Sets the phase 1 and phase 3 angle difference of the

sequence step.


Sets the phase signal change characteristics of the

sequence step.


Sets the starting phase angle of the sequence step.


Sets the stopping phase angle of the sequence step.


Sets the unbalanced ac voltage of the sequence step.


Sets the unbalanced dc voltage of the sequence step.


Changes the execution state of the sequence pro
gram.


Sets the number of program loops.



PROGram:STEP:ADD < _step_ - Adds a new program step.


PROGram:STEP:CLEar < _step_ - Removes the specified program step.


Keysight AC6900 Operating and Programming Guide 163


4 SCPI Programming


**Command/Query** **Description**



PROGram:STEP:END < _step_ 
PROGram:STEP:END?


PROGram:STEP:STARt < _step_ 
PROGram:STEP:STARt?


**SENSe Subsytem**


**Command/Query** **Description**



Sets the sequence ending step.


Sets the sequence starting step.



SENSe:AVERage < _value_ SENSe:AVERage?


SENSe:CURRent:AVERage < _value_ SENSe:CURRent:AVERage?



Sets the moving averaging count for both voltage and current meas
urements.


Sets the moving averaging count for current measurements.



SENSe:CURRent[:PEAK]:HOLD:CLEar Clears the peak held current measurement.



SENSe:CURRent[:PEAK]:HOLD:TIME < _value_ >|MINimum|MAXimum
SENSe:CURRent[:PEAK]:HOLD:TIME? [MINimum|MAXimum]


SENSe:VOLTage:AVERage < _value_ SENSe:VOLTage:AVERage?



Sets the peak hold time for current measurements in seconds.


Sets the moving averaging count for voltage measurements.



SENSe:VOLTage[:PEAK]:HOLD:CLEar Clears the peak held voltage measurement.



SENSe:VOLTage[:PEAK]:HOLD:TIME < _value_ >|MINimum|MAXimum
SENSe:VOLTage[:PEAK]:HOLD:TIME? [MINimum|MAXimum]


**SIMulation Subsystem**



Sets the peak hold time for voltage measurements in seconds.



**Command/Query** **Description**



SIMulation:EXECuting RUN|STOP, <repeat-count>
SIMulation:EXECuting?


SIMulation:POLarity POSitive|NEGative
SIMulation:POLarity?


SIMulation:REPeat:COUNt < _value_ 
SIMulation:REPeat:COUNt?



Runs or stops the power failure simulation.


Sets the voltage polarity of the power line simulation.


Sets the repeat count of the power line simulation.



SIMulation:STATe ON|1|OFF|0 Enables or disables a power line simulation sequence.



SIMulation:T1:PHASe[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T1:PHASe[:LEVel]? [MINimum|MAXimum]


SIMulation:T1:PHASe:STATe ON|1|OFF|0

SIMulation:T1:PHASe:STATe?


SIMulation:T1:TIME[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T1:TIME[:LEVel]? [MINimum|MAXimum]



Sets the starting phase angle of the simulation in degrees.


Selects whether the simulation start will be set in terms of

time or phase.


Sets the starting time of the simulation.



164 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**Command/Query** **Description**



SIMulation:T2:TIME[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T2:TIME[:LEVel]? [MINimum|MAXimum]


SIMulation:T3:TIME[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T3:TIME[:LEVel]? [MINimum|MAXimum]


SIMulation:T3:VOLTage[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T3:VOLTage[:LEVel]? [MINimum|MAXimum]


SIMulation:T4:TIME[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T4:TIME[:LEVel]? [MINimum|MAXimum]


SIMulation:T5:CYCLe[:LEVel] < _value_ SIMulation:T5:CYCLe[:LEVel]?


SIMulation:T5:CYCLe:STATe ON|1|OFF|0

SIMulation:T5:CYCLe:STATe?


SIMulation:T5:TIME[:LEVel] < _value_ >|MINimum|MAXimum
SIMulation:T5:TIME[:LEVel]? [MINimum|MAXimum]


**STATus Subsystem**



Sets the first slope time of the simulation.


Sets the time of the simulation.


Sets the regulated voltage of the simulation.


Sets the second slope time of the simulation.


Sets the number of return cycles of the simulation.


Selects whether the simulation remaining in the returned
state will be set in terms of time or cycles.


Sets the return time of the simulation.



**Command/Query** **Description**


STATus:PRESet Initializes the transition and enable filters for both

OPERation and QUEStionable register groups.


STATus:OPERation[:EVENt]? Queries the event of the Operation status group.


STATus:OPERation:CONDition? Queries the condition of the Operation status group.



STATus:OPERation:ENABle < _value_ 
STATus:OPERation:ENABle?


STATus:OPERation:NTRansition < _value_ 
STATus:OPERation:NTRansition?

STATus:OPERation:PTRansition < _value_ 
STATus:OPERation:PTRansition?



Sets the value of the enable register of the Operation status group.


Sets and queries the value of negative and positive
transition filters of the Questionable status group.



STATus:OPERation:INSTrument[:EVENt]? Queries the event of the Operation Instrument

status group.


STATus:OPERation:INSTrument:CONDition? Queries the condition of the Operation Instrument

status group.



STATus:OPERation:INSTrument:ENABle < _value_ 
STATus:OPERation:INSTrument:ENABle?


STATus:OPERation:INSTrument:NTRansition < _value_ 
STATus:OPERation:INSTrument:NTRansition?

STATus:OPERation:INSTrument:PTRansition < _value_ 
STATus:OPERation:INSTrument:PTRansition?



Sets the value of the enable register of the Operation Instrument status group.


Sets and queries the value of negative and positive
transition filters of the Operation Instrument status

group.



STATus:OPERation:INSTrument:ISUMmary<1|2|3>[:EVENt]? Queries the event of the Operation Instrument Isum
mary status group.


STATus:OPERation:INSTrument:ISUMmary<1|2|3>:CONDition? Queries the condition of the Operation Instrument
Isummary status group.


Keysight AC6900 Operating and Programming Guide 165


4 SCPI Programming


**Command/Query** **Description**



STATus:OPERation:INSTrument:ISUMmary<1|2|3>:ENABle < _value_ STATus:OPERation:INSTrument:ISUMmary<1|2|3>:ENABle?


STATus:OPERation:INSTrument:ISUMmary<1|2|3>:NTRansition < _value_ STATus:OPERation:INSTrument:ISUMmary<1|2|3>:NTRansition?
STATus:OPERation:INSTrument:ISUMmary<1|2|3>:PTRansition < _value_ STATus:OPERation:INSTrument:ISUMmary<1|2|3>:PTRansition?



Sets the value of the enable register of the Operation Instrument Isummary status group.


Sets and queries the value of negative and positive
transition filters of the Operation Instrument Isum
mary status group.



STATus:QUEStionable[:EVENt]? Queries the event of the Questionable status group.


STATus:QUEStionable:CONDition? Queries the condition of the Questionable status

group.



STATus:QUEStionable:ENABle < _value_ 
STATus:QUEStionable:ENABle?


STATus:QUEStionable:NTRansition < _value_ 
STATus:QUEStionable:NTRansition?

STATus:QUEStionable:PTRansition < _value_ 
STATus:QUEStionable:PTRansition?



Sets the value of the enable register of the Questionable status group.


Sets and queries the value of negative and positive
transition filters of the Questionable status group.



STATus:QUEStionable:INSTrument[:EVENt]? Queries the event of the Questionable Instrument

status group.


STATus:QUEStionable:INSTrument:CONDition? Queries the condition of the Questionable Instru
ment status group.



STATus:QUEStionable:INSTrument:ENABle < _value_ 
STATus:QUEStionable:INSTrument:ENABle?


STATus:QUEStionable:INSTrument:NTRansition < _value_ 
STATus:QUEStionable:INSTrument:NTRansition?

STATus:QUEStionable:INSTrument:PTRansition < _value_ 
STATus:QUEStionable:INSTrument:PTRansition?



Sets the value of the enable register of the Questionable Instrument status group.


Sets and queries the value of negative and positive
transition filters of the Questionable Instrument

status group.



STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>[:EVENt]? Queries the event of the Questionable Instrument
Isummary status group.


STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:CONDition? Queries the condition of the Questionable Instrument Isummary status group.



STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:ENABle < _value_ STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:ENABle?


STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:NTRansition < _value_ STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:NTRansition?
STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:PTRansition < _value_ STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:PTRansition?



Sets the value of the enable register of the Questionable Instrument Isummary status group.


Sets and queries the value of negative and positive
transition filters of the Questionable Instrument

Isummary status group.



166 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**SYSTem Subsystem**


**Command/Query** **Description**


SYSTem:BEEPer[:IMMediate] Issues a single beep.



SYSTem:BEEPer:KCLick ON|1|OFF|0

SYSTem:BEEPer:KCLick?


SYSTem:BEEPer:STATe ON|1|OFF|0

SYSTem:BEEPer:STATe?



Disables or enables the click tone heard when you press
a front-panel key.


Disables or enables the beep heard when an error is gen
erated.



SYSTem:COMMunicate:LAN:CONTrol? Returns the TCP port number for SCPI-RAW.



SYSTem:COMMunicate:RLSTate LOCal|REMote|RWLock

SYSTem :COMMunicate:RLSTate?


SYSTem:CONFigure:ADJust:VOLTage:FINE < _value_ >|MINimum|MAXimum [,(@chanlist)]
SYSTem:CONFigure:ADJust:VOLTage:FINE? [MINimum|MAXimum] [,
(@chanlist)]


SYSTem:CONFigure:ADJust:VOLTage:TERMinal:MODE OUTPut|SENSe
SYSTem:CONFigure:ADJust:VOLTage:TERMinal:MODE?



Configures the remote/local/lockout state of the instru
ment.


Sets the voltage offset value.


Applies the voltage offset value either to the output ter
minal or the sense terminal.



SYSTem:CONFigure:FORMation:FRAMe[:COUNt]? Queries the number of units operating in parallel.


SYSTem:CONFigure:FORMation:FRAMe:INFO? < _value_ - Queries information about the specified unit operating in
parallel.


SYSTem:CONFigure:FORMation:PMODule[:COUNt]? < _value_ - Queries the number of power modules in the specified

frame.


SYSTem:CONFigure:FORMation:PMODule:INFO? < _frame_ >, < _module_ - Queries information about the specified module operating in a frame.



SYSTem:CONFigure:FORMation:PSAVer:MAXimum < _value_ >|MINimum|MAXimum
SYSTem:CONFigure:FORMation:PSAVer:MAXimum? [MINimum|MAXimum]


SYSTem:CONFigure:FORMation:PSAVer:MODules E|D {,E|D) . . .
SYSTem:CONFigure:FORMation:PSAVer:MODules?



Sets the maximum VA for the power save function in

Watts.


Sets the specified power modules to run using the
power save function.



SYSTem:CONFigure:FORMation:PSAVer:RESet Resets the power save settings.



SYSTem:CONFigure:NOUTputs 1|2|3
SYSTem:CONFigure:NOUTputs?
SYSTem:CONFigure:WIRing 1|2|3
SYSTem:CONFigure:WIRing?


SYSTem:DATE <yyyy>,<mm>,<dd>

SYSTem:DATE?


SYSTem:ERRor[:NEXT]?
SYSTem:ERRor[:NEXT]:COUNt?


SYSTem:LOCal

SYSTem:REMote

SYSTem:RWLock


SYSTem:PASSword[:CENable] "< _password_ >"
SYSTem:PASSword[:CENable]?



Specifies the number of output phases. The two commands are equivalent.


Sets the local date of the system clock.


Reads and clears one error from the error queue.
Returns the number of errors in the error queue.


These three commands are equivalent to

SYStem:COMMunicate:RLSTate


Enables password protection by entering the password.



Keysight AC6900 Operating and Programming Guide 167


4 SCPI Programming


**Command/Query** **Description**


SYSTem:PASSword[:CENable]:STATe? Queries if password protection is enabled.


SYSTem:PASSword:CDISable "< _password_ >" Disables password protection.


SYSTem:PASSword:NEW "< _existing_password_ >","< _new_password_ >" Sets a new password.


SYSTem:SLEep:EXECute Immediately activates sleep mode.


SYSTem:SECurity:IMMediate Clears all user memory (including stored states) and

reboots the instrument in the *RST state.



SYSTem:TIME <hh>,<mm>,<ss>

SYSTem:TIME?



Sets the local time of the system clock.



SYSTem:VERSion? Returns the version of SCPI that the instrument uses.


**TRIGger Subsystem**


**Command/Query** **Description**


TRIGger:ACQuire[:IMMediate] Sends a software trigger to the ACQuire subsystem.



TRIGger:ACQuire:SOURce IMMediate|BUS
TRIGger:ACQuire:SOURce?



Sets the trigger source that starts the measurement after INIT:ACQ.



TRIGger:PROGram[:IMMediate] Sends a software trigger to the PROGram subsystem.



TRIGger:PROGram:SOURce IMMediate|BUS
TRIGger:PROGram:SOURce?



Sets the trigger source that starts the program.



TRIGger:SIMulation[:IMMediate] Sends a software trigger to the SIMulation subsystem.



TRIGger:SIMulation:SOURce IMMediate|BUS
TRIGger:SIMulation:SOURce?



Sets the trigger source that starts the simulation.



TRIGger:TRANsient[:IMMediate] Triggers the TRANsient subsystem.



TRIGger:TRANsient:SOURce IMMediate|BUS
TRIGger:TRANsient:SOURce?



Sets the trigger source for changing the output value after INIT:TRAN.



168 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**VOLTage Subsystem**


**Command/Query** **Description**




[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] < _value_ >|MINimum|MAXimum[, < _low_limit_ >,
< _high_limit_ >] [,(@chanlist)]

[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum] [,(@chanlist)]


[SOURce:]VOLTage[:LEVel]:LIMit:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage[:LEVel]:LIMit:LOWer? [MINimum|MAXimum]

[SOURce:]VOLTage[:LEVel]:LIMit:UPPer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage[:LEVel]:LIMit:UPPer? [MINimum|MAXimum]


[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] < _value_ >|MINimum|MAXimum [,(@chanlist)]

[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude]? [MINimum|MAXimum] [,(@chanlist)]


[SOURce:]VOLTage:COMPensate:MODE DISabled|HARD|SOFT

[SOURce:]VOLTage:COMPensate:MODE?


[SOURce:]VOLTage:COMPensate:SOFT:CONTrol[:STATus] AC|DC

[SOURce:]VOLTage:COMPensate:SOFT:CONTrol[:STATus]?


[SOURce:]VOLTage:COMPensate:SOFT:TERMinal OUTPut|SENSe

[SOURce:]VOLTage:COMPensate:SOFT:TERMinal?


[SOURce:]VOLTage:LTLine < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:LTLine? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet[:IMMediate] < _value_ >|MINimum|MAXimum [,< _low_limit_ >, < _high__
_limit_ >] [,(@chanlist)]

[SOURce:]VOLTage:OFFSet[:IMMediate]? [MINimum|MAXimum] [,(@chanlist)]


[SOURce:]VOLTage:OFFSet:LIMit:LOWer?

[SOURce:]VOLTage:OFFSet:LIMit:UPPer?


[SOURce:]VOLTage:OFFSet:TRIGgered < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:OFFSet:TRIGgered? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet:LTLine < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:OFFSet:LTLine? [MINimum|MAXimum]


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:GAIN < _channel_ >,< _value_ 
[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:GAIN? < _channel_ 

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:OFFSet < _channel_ >,< _value_ 
[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:OFFSet? < _channel_ 

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:APERture < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:APERture? [MINimum|MAXimum]


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:POLarity < _channel_ >,
NORMal|INVerted

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:POLarity? < _channel_ 

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:SOURce EXTernal|INT_EXT

[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:SOURce?


[SOURce:]VOLTage:PROGramming:EXTernal:MODE EXTDC|VPRogram|OFF

[SOURce:]VOLTage:PROGramming:EXTernal:MODE?


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:GAIN < _channel_ >,< _value_ 
[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:GAIN? < _channel_ 


Sets the immediate AC voltage
level, and optionally the lower
and upper soft limits.


Sets the the lower and upper
AC voltage limits.


Sets the triggered AC voltage

level.


Sets the compensation mode.


Sets the compensated voltage

mode.


Sets the compensation sensing
point.


Sets the line-to-line AC voltage.


Sets the immediate DC voltage
in VDC, and also optionally sets

the soft limits.


Queries the lower and upper DC
voltage limits.


Sets the triggered DC voltage in

VDC.


Sets the line-to-line DC

voltage.


Sets the voltage gain of the
external analog input EXTDC.


Sets the voltage offset of the
external analog input EXTDC.


Sets the external analog input
signal's aperture time.


Sets the external analog input
programming signal polarity.


Sets the external analog input
programming signal source.


Sets the external analog signal's programming mode.


Sets the voltage gain of external
analog input VPR.



Keysight AC6900 Operating and Programming Guide 169


4 SCPI Programming


**Command/Query** **Description**




[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:OFFSet < _channel_ >,< _value_ 
[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:OFFSet? < _channel_ 

[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:MAP ALL|ACVoltage|DCVoltage

[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:MAP?


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:STATe < _channel_ >, ON|1|OFF|0

[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:STATe? < _channel_ 

[SOURce:]VOLTage:PROGramming:SOURceINTernal|EXTernal

[SOURce:]VOLTage:PROGramming:SOURce?


[SOURce:]VOLTage:PROTection:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:PROTection:LOWer? [MINimum|MAXimum]


[SOURce:]VOLTage:PROTection:LOWer:STATe ON|1|OFF|0

[SOURce:]VOLTage:PROTection:LOWer:STATe?


[SOURce:]VOLTage:PROTection:UPPer < _value_ >|MINimum|MAXimum [,< _neg_limit_ >, < _pos_limit_ >]

[SOURce:]VOLTage:PROTection:UPPer? [MINimum|MAXimum]


[SOURce:]VOLTage:PROTection:PEAK:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:PROTection:PEAK:LOWer? [MINimum|MAXimum]


[SOURce:]VOLTage:PROTection:PEAK:UPPer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:PROTection:PEAK:UPPer? [MINimum|MAXimum]


[SOURce:]VOLTage:RANGe[:UPPer] < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:RANGe[:UPPer]? [MINimum|MAXimum]


[SOURce:]VOLTage:RESPonse SLOW|MEDium|FAST

[SOURce:]VOLTage:RESPonse?


**WAVE Subsystem**


**Command/Query** **Description**



Sets the voltage offset of the
external analog input VPR.


Sets the voltage programming
output mapping.


Sets the voltage programming
output state for each channel.


Sets the voltage programming

source.


Sets the under-voltage pro
tection value.


Enables or disables the under
voltage protection


Sets the over-voltage pro
tection value.


Sets the negative peak OVP

limit.


Sets the positive peak OVP

limit.


Sets the voltage range.


Sets the output response.



WAVE:DATA:ARBitrary < _bank_number_ >, < _block_ 
WAVE:DATA:ARBitrary? < _bank_number_ 

WAVE:DATA:CLIP < _bank_number_ >, < _value_ >|MINimum|MAXimum

WAVE:DATA:CLIP? < _bank_number_ - [MINimum|MAXimum]


WAVE:DATA:IECPclip < _bank_number_ >, < _value_ >|MINimum|MAXimum

WAVE:DATA:IECPclip? < _bank_number_ - [MINimum|MAXimum]


WAVE:DATA:POINt < _bank_number_ >, < _point_ >, < _data_ 
WAVE:DATA:POINt? < _bank_number_ >, < _point_ 


Sets a waveform with block data at the specified wave
form bank.


Sets the crest factor of the peak clipped waveform.


Sets the clip factor of the flat curve waveform.


Programs the arbitrary waveform data points.



WAVE:DATA:SINusoid < _bank_number_ - Resets the waveform to a sinusoid.


WAVE:DATA:TYPE? < _bank_number_ - Queries the type of waveform.


WAVE:STATe ON|1|OFF|0 Enables or disables the custom waveform.


170 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **ABORt Subsystem**


ABORt commands cancel any triggered actions and returns the trigger system back to the Idle state.

ABORt commands are also executed with the *RST command.


**ABORt[:ALL]**


Aborts all operations.


(none) (none)


You cannot specify ta trigger subsystem with this command. It aborts all four trigger operations.


The trigger status at power-on is the same as that after an ABORt.


If an ABORt is sent during a measurement, the measured data is discarded. If an INIT:ACQ has not
been sent, the acquired data is retained.


**ABORt:ACQuire**


Aborts all ACQuire operations.


(none) (none)


This command resets the WTG-meas and MEAS-active bits in the Operation Status registers.


If you send an ABOR command without having sent INIT:ACQ, the measurement data is not discarded.


**ABORt:PROGram**


Aborts all PROGram operations.


(none) (none)


This command stops the triggered function of the sequence operation.


Keysight AC6900 Operating and Programming Guide 171


4 SCPI Programming


**ABORt:SIMulation**


Aborts all SIMulation operations.


(none) (none)


This command stops the triggered function of the power line abnormality simulation.


**ABORt:TRANsient**


Aborts all TRANsient actions.


(none) (none)


This command stops the triggered function for any output changes.


172 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **CURRent Subsytem**


The CURRent subsystem limits the instrument's output current.


**[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] <** _**value**_ **>|MINimum|MAXimum**

**[,<** _**lower_limit**_ **>,<** _**upper_limit**_ **>] [,(@chanlist)]**

**[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum] [,(@chanlist)]**


Sets the immediate AC current limit in amps (rms). Optionally sets the lower and upper limit values. If
no optional limit values are set, the optional limits are set to the maximum current values. The optional
(@chanlist) parameter lets you designate specific output phases.


10 % to 110% of the of the rated current in A MAX|MIN


Current units (UA, MA, A) are allowed.


Values exceeding the instrument's maximum value will be set to the instrument's maximum value.


The *RST default is the instrument's MAXimum value.


**[SOURce:]CURRent:PEAK:LOWer <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**

**[SOURce:]CURRent:PEAK:LOWer? [MINimum|MAXimum] [,(@chanlist)]**

**[SOURce:]CURRent:PEAK:UPPer <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**

**[SOURce:]CURRent:PEAK:UPPer? [MINimum|MAXimum] [,(@chanlist)]**


Sets the negative (lower) and positive (upper peak current limits. Values are in amps. The optional
(@chanlist) parameter lets you designate specific output phases.



10 % to 420% of the of the positive rated current in A
10 % to 420% of the of the negative rated current in A



MAX|MIN



Values exceeding the instrument's maximum value will be set to the instrument's maximum value.


The *RST default is the instrument's MAXimum value.


Keysight AC6900 Operating and Programming Guide 173


4 SCPI Programming


**[SOURce:]CURRent:PROTection:STATe ON|1|OFF|0**

**[SOURce:]CURRent:PROTection:STATe?**


Enables or disables current protection.


ON|1|OFF|0 0 (OFF) or 1 (ON)


ON (TRIP): Turns OUTPUT off and activates the alarm for overload conditions longer than three

seconds.


OFF (LIMIT CONTROL): Decreases output voltage to keep current at or below the limit when an

overload occurs.


To clear an overcurrent condition, remove the condition's cause and send OUTPut:PROTection:CLEar.


If overcurrent protection is enabled and the output goes into current limit, the output is disabled and
the Questionable Condition status register OCP bit is set.


The *RST command sets this parameter to ON.


**[SOURce:]CURRent:PROTection:TRIP:DELay <** _**value**_ **>**

**[SOURce:]CURRent:PROTection:TRIP:DELay?**


Sets the protection trip delay in seconds.


0.0 - 10.0 seconds 3.5


This command sets the time that must elapse before the output is turned off when the current limit is

exceeded.


The *RST command sets this parameter to 10 seconds.


174 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **DIGital Subsystem**


Digital commands program the digital control port on the rear panel of the instrument


**DIGital:INPut:DATA?**


Queries the state of the digital input port.


(none) <0-63>


**DIGital:OUTPut:DATA <** _**value**_ **>**

**DIGital:OUTPut:DATA?**


Sets the state of the digital output port.


<0 - 63> <0-63>


**DIGital:PIN<1-3,5-7,9,10>:FUNCtion <map_item>**
**DIGital:PIN<1 - 10>:FUNCtion?**


Refer to Digital Connections for the designated connector pins for channels 1-10.


Maps the designated digital IO pin to a specific in/out function signal.



1-3,5-7,9,10

<map_item> - see table



<map_item>



The following functions can be assigned (mapped) to the specified channels.


Keysight AC6900 Operating and Programming Guide 175


4 SCPI Programming


DIS: Disable DIS: Disable DIS: Disable


OUTCTL: Output control OUTSTAT: Output ON Status CLKIN: Sync clock input


SEQEXEC: Sequence execution control IPKLIM: Current Pk Limit Status SEQTRIGIN: SEQ trigger input


PROTCLR: Protection clear ILIM: Overload Status CLKOUT: Standard clock output


EXTPROT: External protection BUSY: Busy Status SEQTRIGOUT: SEQ trigger output


WIRCTL_1P: Wiring method control 1P WIRCHK_1P: Wiring Method 1P Status



WIRCTL_1P3W: Wiring method control

1P3W



WIRCHK_1P3W: Wiring Method 1P3W

Status



WIRCTL_3P: Wiring method control 3P WIRCHK_3P: Wiring Method 3P Status


VRANGE: Voltage range VRANGE_H: Voltage Range H Status


RCL_1: Recall preset 1 PON: Power ON Status


RCL_2: Recall preset 2 SEQSTAT: Sequence Status Out


INH: Output inhibit DOUT: User programmable output signal


DINP: User programmable input signal


Channels 1-3, 5-7, and 9-10 are disabled by default.


Channels 4 and 8 are fixed to the Shutdown and Protection functions respectively, and cannot be
mapped.


**DIGital:PIN<1-3,5-10>:POLarity POSitive|NEGative**
**DIGital:PIN<1-10>:POLarity?**


Refer to Digital Connections for the designated connector pins for channels 1-10.


Sets the polarity of the remote inhibit digital channels. POSitive means a logical true signal is a voltage
high at the channel. NEGative means a logical true signal is a voltage low at the channel.



1-3, 5-10
POSitive|NEGative



POS or NEG



The polarity of channel 4 is fixed and cannot be changed.


The channel polarities are saved in non-volatile memory.


176 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **DISPlay Subsystem**


The DISPlay subsystem controls the front-panel display.


**DISPlay[:WINDow][:STATe]ON|1|OFF|0**


Turns the front-panel display on or off.


ON|1|OFF|0 (none)


Setting this OFF stops refreshing the display, turns off the display's backlight, changes the LINE LED to
orange, and disables all front-panel key operations.


**DISPlay[:WINDow]:METer:COUPling AC|DC|ACDC|PEAK, AC|DC|ACDC|PEAK**
**DISPlay[:WINDow]:METer:COUPling?**


Selects both the voltage and current measurement unit on the front panel. The first argument sets the
voltage measurement, the second argument sets the current measurement.


AC|DC|ACDC|PEAK AC, DC, ACDC, or PEAK


AC shows pure AC measurements, DC shows DC measurements, ACDC shows AC and DC
measurements, and PEAK shows the peak measurement.


The *RST command sets both this parameter to ACDC.


**DISPlay[:WINDow]:METer:COUPling:VOLTage AC|DC|ACDC|PEAK**
**DISPlay[:WINDow]:METer:COUPling:VOLTage?**


Selects the voltage measurement unit on the front panel.


AC|DC|ACDC|PEAK AC, DC, ACDC, or PEAK


AC shows pure AC measurements, DC shows DC measurements, ACDC shows AC and DC
measurements, and PEAK shows the peak measurement.


The *RST command sets this parameter to ACDC.


Keysight AC6900 Operating and Programming Guide 177


4 SCPI Programming


**DISPlay[:WINDow]:METer:COUPling:CURRentAC|DC|ACDC|PEAK**
**DISPlay[:WINDow]:METer:COUPling:CURRent?**


Selects the current measurement unit on the front panel.


AC|DC|ACDC|PEAK AC, DC, ACDC, or PEAK


AC shows pure AC measurements, DC shows DC measurements, ACDC shows AC and DC
measurements, and PEAK shows the peak measurement.


The *RST command sets this parameter to ACDC.


**DISPlay[:WINDow]:PHASe P123|ABC|UVW**
**DISPlay[:WINDow]:PHASe?**


Selects the phase-naming convention.


P123|ABC|UVW P123|ABC|UVW


**DISPlay[:WINDow]:VIEW METER_ALL|METER_3P|METER_1P**


Selects the phases to display.


METER_ALL|METER_3P|METER_1P (none)


178 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **FETCh and MEASure Subsystems**


The FETCh and MEASure subsystems return measured data. The FETCh and MEASure queries do not
take any parameters, and they have the identical syntax (such as FETCh:VOLTage:AC? and
MEASure:VOLTage:AC?).


**MEASure Subsystem**


A MEASure query or an IMMediate or BUS trigger generates a new measurement that is stored in a

buffer and then returned to the user.


Before the instrument can accept an IMMediate or BUS trigger, you must first send INITiate:ACQuire.
Otherwise, the instrument will generate SCPI error -211,"Trigger ignored" . The buffer retains the
measured data until cleared by another MEASure query, an accepted IMMediate or BUS trigger, a
*RST or INITiate:ACQuire command, or a power cycle.


**FETCh Subsystem**


A FETCh query immediately returns the previously acquired measurement from this buffer. If no
measurement is in the buffer, the instrument generates SCPI error -230,"Data corrupt or stale".


**General Measurement Operation**


Normally, it takes approximately 110 ms for a measurement to complete. If there are no MEASure
queries or triggers, the data is discarded.


When a MEASure query or IMMediate or BUS trigger is received, the instrument waits for the current
digitization to finish, discards the data, and waits for the next measurement interval to finish. The

instrument then stores the next measurement into the buffer to ensure that the entire measurement

was sampled after the MEASure query or IMMediate or BUS trigger. Therefore, with multiple
parameters, a measurement will take much longer to complete.


The acquired measurement includes both the instantaneous voltage and current. The FETCh query
may subsequently be used to return any calculated measurement item derived from this data,
including FETC:CURR:DC?, FETC:VOLT:AC?, and FETC:POW:ACDC?.


Refer to Measurement Details for additional information.


Keysight AC6900 Operating and Programming Guide 179


4 SCPI Programming


**FETCh[:SCALar]:ALL? [(@chanlist)]**
**MEASure[:SCALar]:ALL? [(@chanlist)]**


Returns all measurements, except TOTal measurements, as a comma-separated list. The optional
(@chanlist) parameter lets you designate specific output phases.


(none) <value>[.<value>...]


The measurements are returned in the order shown in the following table. TOTal measurements are

not returned and are listed last.


Measurements are returned in comma-separated NR3 format, using the format 1.23456E+00.


If a phase is not specified, the phase designated by the INSTrument[:SELect] command is returned. If
several phases are specified, all measured values of the first specified phase are returned in the
Measured Item list order. Then, the measured values of the next specified phase are returned in the
Measured Item list order, and so on for the third.


A zero (0) is returned in response to a line-to-line query during single-phase operation.







CURRent[:DC] Average DC current (A)
Set the averaging period using SENS:AVER.


CURRent:AC AC current (Arms)


CURRent:ACDC Current, AC+DC (Arms)


CURRent:AMPLitude:MAXimum Instantaneous peak current (A)


CURRent:AMPLitude:MAXimum:HOLD Peak AC current held value (A)

This returns the maximum peak current since the power-on or since it was
explicitly cleared by SENS:CURR:PEAK:CLE. The peak current (held value) is
not cleared by *RST or *RCL.


CURRent:CREStfactor Current crest factor

The crest factor is the AC current divided by the AC peak current.


FREQuency AC output frequency (Hz)
This returns the frequency setting for AC, ACDC, and EXT-AC coupling, or
+9.91000E+37 for DC and EXT-DC coupling. The instrument does not measure
frequency.


POWer[:DC] Average DC power (W)
Set the averaging period using SENS:AVER.


POWer:AC[:REAL] AC active power (W)


POWer:AC:APParent AC apparent power (VA)


180 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming







POWer:AC:REACtive AC reactive power (VAR)


POWer:AC:PFACtor AC power factor
The power factor indicates the efficiency degradation caused by the phase
difference between the AC voltage and AC current.


POWer:ACDC[:REAL] AC active power (W)


POWer:ACDC:APParent AC+DC apparent power (VA)


POWer:ACDC:REACtive AC+DC reactive power (VAR)


POWer:ACDC:PFACtor AC+DC power factor
The power factor indicates the efficiency degradation caused by the phase
difference between the AC voltage and AC current.


VOLTage[:DC] Average DC voltage (V)


Set the averaging period using SENS:AVER.


VOLTage:AC AC voltage (Vrms)


VOLTage:ACDC Voltage, AC+DC (Vrms)


VOLTage:AMPLitude:MAXimum Instantaneous peak voltage (V)


VOLTage:AMPLitude:MAXimum:HOLD Peak AC voltage held value (V)
This returns the maximum peak voltage since the power-on or since it was
explicitly cleared by SENS:VOLT:PEAK:CLE. The peak voltage (held value) is
not cleared by *RST or *RCL.


LTLVoltage:DC Line-to-line Average DC voltage (V)


LTLVoltage:AC Line-to-line AC voltage (Vrms)


LTLVoltage:ACDC Line-to-line ACDC voltage (Vrms)


LTLVoltage:AMPLitude:MAXimum Line-to-line Instantaneous peak voltage (V)







POWer[:DC]:TOTal Total Average DC power (W)


POWer:ACDC[:REAL]:TOTal Total AC active power (W)


POWer:AC:APParent:TOTal Total AC apparent power (VA)


POWer:AC:REACtive:TOTal Total AC reactive power (VAR)


POWer:AC:PFACtor:TOTal Total AC power factor


POWer:ACDC[:REAL]:TOTal Total AC active power (W)


POWer:ACDC:APParent:TOTal Total AC+DC apparent power (VA)


POWer:ACDC:REACtive:TOTal Total AC+DC reactive power (VAR)


POWer:ACDC:PFACtor:TOTal Total AC+DC power factor


Keysight AC6900 Operating and Programming Guide 181


4 SCPI Programming


**FETCh[:SCALar]:<** _**Measured_Item**_ **>? [(@chanlist)]**
**MEASure[:SCALar]:<** _**Measured_Item**_ **>? [(@chanlist)]**


Returns the < _Measured_Item_ - in the form 1.23456E+00. The < _Measured_Item_ - may be any of the
queries listed in previous table, such as VOLTage:AC? or VOLTage:ACDC? The optional (@chanlist)
parameter lets you designate specific output phases.


(none) <value>


If a phase is not specified, the phase designated by the INSTrument[:SELect] command is returned. If
several phases are specified, the measured value of the first specified phase is returned. Then, the
measured value of the next specified phase is returned in the same manner, and so on for the third.


**FETCh:<** _**Harmonic_Item**_ **>? [(@chanlist)]**
**MEASure:<** _**Harmonic_Item**_ **>? [(@chanlist)]**


Returns the harmonic data specified by < _Harmonic_Item_ >, as shown in the next table. The optional
(@chanlist) parameter lets you designate specific output phases.


(none) <value>[.<value>...]


Harmonic measurements (0th to 50th harmonic) are returned in a comma-separated NR3 format.


If a phase is not specified, the phase designated by the INSTrument[:SELect] command is returned. If
several phases are specified, all measured values of the first specified phase are returned. Then, the
measured values of the next specified phase are returned in the same manner, and so on for the third.







CURRent:HARMonic[AMPLitude] Harmonic current amplitude


CURRent:HARMonic:PHASe Harmonic current phase


CURRent:HARMonic:THD Total harmonic distortion (THD)


VOLTage:HARMonic[AMPLitude] Harmonic voltage amplitude


VOLTage:HARMonic:PHASe Harmonic voltage phase


VOLTage:HARMonic:THD Total harmonic distortion (THD)


182 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **FREQuency Subsystem**


FREQuency commands configure the instrument's output frequency.


**[SOURce:]FREQuency[:CW] <** _**value**_ **>|MINimum|MAXimum[,<** _**lower_limit**_ **>,<** _**upper_limit**_ **>]**

**[SOURce:]FREQuency[:CW]? [MINimum|MAXimum]**

**[SOURce:]FREQuency[:IMMediate] <** _**value**_ **>|MINimum|MAXimum[,<** _**lower_limit**_ **>,<** _**upper_**_
_**limit**_ **>]**

**[SOURce:]FREQuency[:IMMediate]? [MINimum|MAXimum]**


Sets the immediate AC frequency and optionally the lower and upper frequency soft limits.



1.00 to 550 Hz for "L" models in 3 phase mode
1.00 to 5,000 Hz for "H" models



+5.00000E+01



This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST default is 50 Hz.


You should set this value between the lower and upper frequency soft limits.


Frequency suffixes (HZ and KHZ) are allowed.


**[SOURce:]FREQuency:LIMit:LOWer <value>**

**[SOURce:]FREQuency:LIMit:LOWer? [MINimum|MAXimum]**


**[SOURce:]FREQuency:LIMit:UPPer <value>**

**[SOURce:]FREQuency:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower or upper AC frequency soft limit.



1.00 to 550 Hz for "L" models in 3 phase mode
1.00 to 5,000 Hz for "H" models



+1.25000E+02



The following relationship must be met: FREQ:LIM:LOW ≤FREQ or FREQ:TRIG ≤FREQ:LIM:UPP


This command is invalid if the signal source is an external signal source.


The *RST default is 1.00 Hz.


Keysight AC6900 Operating and Programming Guide 183


4 SCPI Programming


**[SOURce:]FREQuency:SYNCronous[:STATe]ON|1|OFF|0**

**[SOURce:]FREQuency:LIMit[:STATe]?**


Enables or disables frequency synchronous operation.


ON|1|OFF|0 0 (OFF) or 1 (ON)


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST command sets this parameter to OFF.


**[SOURce:]FREQuency:SYNChronous:MODE LINE|EXTernal**

**[SOURce:]FREQuency:SYNChronous:MODE?**


Sets the synchronization signal source.


LINE|EXTernal LINE|EXT


LINE - synchronizes the frequency synchronization signal to the input line.


EXTernal - synchronizes the frequency synchronization signal to an external DIgital IO input signal


The *RST command sets this parameter to LINE.


**[SOURce:]FREQuency:SYNChronous:PHASe:DELay <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]FREQuency:SYNChronous:PHASe:DELay? [MINimum|MAXimum]**


Sets the synchronization offset angle in degrees.


0 to 360 +9.00000E+01


This command is valid when the sync function is on (FREQ:SYNC ON).


The *RST command sets this parameter to FIXed.


184 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming




**[SOURce:]FREQuency:TRIGgered <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]FREQuency:TRIGgered? [MINimum|MAXimum]**


Sets the triggered AC output frequency in Hertz.



1.00 to 550 Hz for "L" models

1.00 to 5,000 Hz for "H" models



+9.00000E+01



This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST default is 50 Hz.


Frequency suffixes (HZ and KHZ) are allowed.


Keysight AC6900 Operating and Programming Guide 185


4 SCPI Programming

#### **FUNCtion Subsystem**


The FUNCtion subsystem has just one command.


**[SOURce:]FUNCtion[:SHAPe]:BANK[:INDex] <** _**index**_ **> [,(@chanlist)]**

**[SOURce:]FUNCtion[:SHAPe]:BANK[:INDex]? [(@chanlist)]**


Selects a waveform shape by bank index.


The optional (@chanlist) parameter lets you designate specific output phases.


0 - 256 <bank index>


This command is invalid when the Synchronous function is enabled.


This command is invalid if the signal source is an external signal source.


This command is invalid if the voltage compensation mode has been set to SOFT.


The *RST command sets this parameter to 0.


186 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **HCOPy Subsystem**


The HCOPy subsystem has just one command.


**HCOPy:SDUMp:DATA?**


Returns the display image in .png format.


(none) <Block>


The response Block length is 3 kB to 3.5 kB.


Keysight AC6900 Operating and Programming Guide 187


4 SCPI Programming

#### **IEEE-488 Common Commands**


IEEE-488 Common commands generally control overall instrument functions, such as reset, status,
and synchronization. All common commands consist of a three-letter mnemonic preceded by an

asterisk: *RST *IDN? *SRE 8.


***CLS**


Clear status command. Clears the event registers in all register groups. Also clears the status byte
and error queue. If *CLS immediately follows a program message terminator (<NL>), then the output
queue and the MAV bit are also cleared. Refer to Status Overview for more information.


(none) (none)


***ESE <** _**value**_ **>**

***ESE?**


Event status enable command and query. Sets the value in the enable register for the Standard Event
Status group. Each set bit of the register enables a corresponding event. All enabled events are
logically OR-ed into the ESB bit of the status byte. The query reads the enable register. Refer to

Status Overview for more information.



Decimal sum of the bits in the register, default 0.
For example, to enable bit 2 (value 4), bit 3 (value 8), and bit
7 (value 128), use decimal sum 140 (4 + 8 + 128).



< _bit value_ 


The value returned is the binary-weighted sum of all bits set in the register.


Any or all conditions can be reported to the ESB bit through the enable register. To set the enable
register mask, write a decimal value to the register using *ESE.


A *CLS does not clear the enable register, but does clear the event register.


This parameter is not affected by *RST or *RCL.


188 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


***ESR?**


Event status event query. Reads and clears the event register for the Standard Event Status group.
The event register is a read-only register that latches all standard events. Refer to Status Overview for

more information.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.


Any or all conditions can be reported to the ESB bit through the enable register. To set the enable
register mask, write a decimal value to the register using *ESE.


Once a bit is set, it remains set until cleared by this query or *CLS.


***IDN?**


Identification Query. Returns the instrument’s identification string.


(none) KEYSIGHT TECHNOLOGIES,AC6906H,JPUB002121,
1.03 IFC11.03.0016 IOC1.00.0005[512]


The fields are in the following order:

manufacturer name, model number, serial number, and firmware revision.


Keysight AC6900 Operating and Programming Guide 189


4 SCPI Programming


***OPC**


Sets the OPC (operation complete) bit in the standard event register. This occurs at the completion of
the pending operation. Refer to Status Overview for more information.


(none) (none)


The purpose of this command is to synchronize your application with the instrument. It is used in
conjunction with initiated acquisitions, initiated transients, output state changes, and output to
settling time to provide a way to poll or interrupt the computer when these pending operations
complete.


Other commands may be executed before the operation complete bit is set.


The difference between *OPC and *OPC? is that *OPC? returns "1" to the output buffer when the
current operation completes. No further commands can be sent after an *OPC? until it has responded.
In this way an explicit polling loop can be avoided. That is, the IO driver will wait for the response.


***OPC?**


Returns a 1 to the output buffer when all pending operations complete.


(none) +1


The purpose of this command is to synchronize your application with the instrument.


Other commands cannot be executed until this command completes.


***OPT?**


Returns a string identifying any installed options.


(none) OPT200 | OPT400


OPT200 and OPT400 refer to the nominal input AC line.


190 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


***PSC 0|1**

***PSC?**


Enables (1) or disables (0) the clearing of certain enable registers at power on. These include:
Questionable Data Register (STATus:QUEStionable:ENABle)
Standard Operation Register (STATus:OPERation:ENABle)
Status Byte Condition Register (*SRE)
Standard Event Enable Register (*ESE)


The *PSC command does not affect the clearing of the condition or event registers,
just the enable registers. For more information on the SCPI Status System, see Status
Subsystem Introduction.


0|1, default 1 0 or 1


***RCL <** _**0 to 10**_ **>**


Recalls a saved instrument state. This restores the instrument to a state that was previously stored

with the *SAV comman


0 to 10 (none)


All instrument settings are recalled except: (1) trigger systems are set to the Idle state, (2) calibration is
disabled, (3) nonvolatile settings are not affected.


Location 0 is recalled at power-on when the Output Power-On state is RCL0.


This command also aborts TRANsient and ACQuire operations.


***RST**


Resets the instrument to default values. These settings are described in Default Settings.


*RST forces the ABORt commands. This cancels any measurement or transient actions in process. It
resets the WTG-meas, MEAS-active, WTG-tran, and TRAN-active bits in the Operation Status
registers. Stored instrument states are not affected by *RST.


(none) (none)


This command also aborts TRANsient and ACQuire operations.


Keysight AC6900 Operating and Programming Guide 191


4 SCPI Programming


***SAV <0 to 10>**


Saves the instrument state to a nonvolatile memory location.


0 to 10 (none)


If a particular state is desired at power-on, it should be stored in location 0. Location 0 is recalled at
power-on when the Output Power-On state is RCL0.


The calibration state are NOT saved as part of the *SAV operation.


When shipped, state storage locations 1 through 10 are empty.


***SRE <** _**value**_ **>**

***SRE?**


Service request enable command and query. This sets the value of the Service Request Enable
register. This determines which bits from the Status Byte Register are summed to set the Master
Status Summary (MSS) bit and the Request for Service (RQS) summary bit. A 1 in any Service Request
Enable register bit position enables the corresponding Status Byte register bit. All such enabled bits
are then logically OR-ed to cause the MSS bit of the Status Byte register to be set. Refer to Status

Overview for more information.



Decimal sum of the bits in the register, default 0.
For example, to enable bit 2 (value 4), bit 3 (value 8), and bit
7 (value 128), use decimal sum 140 (4 + 8 + 128).



< _bit value_ 


When a serial poll is conducted in response to SRQ, the RQS bit is cleared, but the MSS bit is not.
When *SRE is cleared (by programming it with 0), the instrument cannot generate an SRQ to the

controller.


The register contents are not affected by *RST or *RCL.


192 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


***STB?**


Status byte query. Reads the Status Byte Register, which contains the status summary bits and the
Output Queue MAV bit.


(none) < _bit value_ 

The Status Byte is a read-only register and the bits are not cleared when it is read. Refer to Status

Overview for more information.


***TRG**


Trigger command. Applies a software trigger (equivalent to IEEE 488.1 Device Trigger) for both
TRANsient and ACQuire trigger groups.


(none) (none)


Generate an immediate trigger:


***TST?**


Self-test. Returns the errors found during the most recent power-on self-test.


(none) 0 (pass) or +1 (failed)


The instrument’s self-test function is performed when the instrument starts up. If a failure is detected,
the corresponding SCPI error is generated, and bit 2 of the STATus:QUEStionable register will be set.


When no failure is found the response will be +0. When one or more failures are found the response
value will be -330. In this case, SYST:ERR? will return specific failure messages (at first -330,"Self-test
error", and then the self-test error codes). See SCPI Error Messages for more information.


The *TST? command does not execute an actual self-test.


Keysight AC6900 Operating and Programming Guide 193


4 SCPI Programming

#### **INITiate Subsystem**


The INITiate commands initialize the trigger system. This moves the trigger system from the "idle" state
to the "wait-for-trigger" state, which enables the instrument to receive triggers. An event on the
selected trigger source causes the trigger to occur.


**INITiate[:IMMediate]:ACQuire**


Initiates the ACQuire (measurement) operation.


(none) (none)


If the ACQuire operation is already initiated, this command will produce error -213,"Init ignored".


**INITiate[:IMMediate]:PROGram**


Initiates the PROGram operation.


(none) (none)


If the PROGram operation is already initiated, this command will produce error -213,"Init ignored".


**INITiate[:IMMediate]:SIMulation**


Initiates the SIMulation operation.


(none) (none)


If the SIMulation operation is already initiated, this command will produce error -213,"Init ignored".


**INITiate[:IMMediate]:TRANsient**


Initiates the TRANsient operation.


(none) (none)


If the TRANsient operation is already initiated, this command will produce error -213,"Init ignored".


194 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **INSTrument Subsystem**


The INSTrument subsystem selects the phase that to be controlled.


**INSTrument[:SELect] OUTPut0|OUTPut1|OUTPut2|OUTPut3**
**INSTrument[:SELect]?**


Selects the phase to be controlled by output phase name. OUTPut0 selects all phases.


OUTPut0|OUTPut1|OUTPut2|OUTPut3 OUTP0|OUTP1|OUTP2|OUTP3


**INSTrument:NSELect 0|1|2|3**

**INSTrument:NSELect?**


Selects the phase to be controlled by output phase number. 0 selects all phases.


0|1|2|3 0|1|2|3


Keysight AC6900 Operating and Programming Guide 195


4 SCPI Programming

#### **LXI Subsystem**


The LXI subsystem controls the LXI functions.


**LXI:IDENtify[:STATe]**
**LXI:IDENtify[:STATe]?**


Turns the front-panel LXI identify indicator (blinking "Lan" annunciator) on or off. When turned on, the
front-panel "LAN" annunciator blinks to identify the instrument that is being addressed.


ON|1|OFF|0 0 (OFF) or 1 (ON)


A LAN reset, and a LAN restart all turn the LXI indicator off.


**LXI:MDNS[:STATe]**
**LXI:MDNS[:STATe]?**


Turns the LAN mDNS service on or off.


ON|1|OFF|0 0 (OFF) or 1 (ON)


A LAN reset, and a LAN restart all turn the mDNS service off.


196 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **MEMory Subsystem**


The MEMory subsystem controls the memory functions.


**MEMory:RECall[:IMMediate] 1|2**


Recalls the output settings saved in location 1 or 2. To view the contents that are stored in a memory
location, use MEMory:RECall:PREView.


1|2 (none)


**MEMory:RECall:PREView? 1|2**


Displays the memory contents saved in location 1 or 2


1|2 NR3, NR3, NR3, NR1, ...


Returns the following items in order, in a comma-separated format. All return formats are NR3, except
for waveform bank number, which is NR1.


For single-phase output : AC voltage, DC voltage, Frequency, Waveform bank number.


For single-phase three-wire output : Phase 1 AC voltage, Phase 2 AC voltage, Phase 1 DC voltage,
Phase 2 DC voltage, Frequency, Phase1 waveform bank number, Phase2 waveform bank number.


For three-phase output : Phase 1 AC voltage, Phase 2 AC voltage, Phase 3 AC voltage, Phase 1 DC
voltage, Phase 2 DC voltage, Phase 3 DC voltage, frequency, Phase 1 waveform bank number, Phase 2
waveform bank number, Phase 3 waveform bank number.


**MEMory:SAVE[:IMMediate] 1|2**


Saves the output settings to memory location 1 or 2.


1|2 (none)


Keysight AC6900 Operating and Programming Guide 197


4 SCPI Programming

#### **OUTPut Subsystem**


The OUTPut subsystem configures the output state, power-on state, coupling mode, digital pin, and
protection.


**OUTPut[:STATe] ON|1|OFF|0**
**OUTPut[:STATe]?**


Enables or disables the instrument's output.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


**OUTPut:COUPling AC|DC|ACDC**
**OUTPut:COUPling?**


Sets the output coupling mode.



AC|DC|ACDC

Default AC



AC|DC|ACDC



AC produces AC output


DC produces DC output


ACDC combines AC and DC output


The *RST command sets this parameter to ACDC.


**OUTPut:IMPedance[:STATe] ON|1|OFF|0**
**OUTPut:IMPedance[:STATe]?**


Enables or disables the output impedance control.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


198 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**OUTPut:IMPedance:REACtive <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**
**OUTPut:IMPedance:REACtive? [MINimum|MAXimum] [,(@chanlist)]**


Sets the reactive part of the output impedance in Henries. The optional (@chanlist) parameter lets you
designate specific output phases.


<value> see table NR3


The *RST command sets this parameter to 0.


This command is valid when the output impedance state is ON. Refer to the following table for valid
output impedance values:







AC6903 27 μH to 667 μH 80 μH to 2000 μH 107 μH to 2667 μH 320 μH to 8000 μH


AC6906 13 μH to 333 μH 40 μH to 1000 μH 53 μH to 1333 μH 160 μH to 4000 μH


AC6912 7 μH to 167 μH 20 μH to 500 μH 27 μH to 667 μH 80 μH to 2000 μH


AC6918 4 μH to 111 μH 13 μH to 333 μH 18 μH to 444 μH 53 μH to 1333 μH


**OUTPut:IMPedance:REAL <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**
**OUTPut:IMPedance:REAL? [MINimum|MAXimum] [,(@chanlist)]**


Sets the resistance part of the output impedance in ohms. The optional (@chanlist) parameter lets you
designate specific output phases.


ON|1|OFF|0 NR3


The *RST command sets this parameter to 0.


This command is valid when the output impedance state is ON. Refer to the following table for valid
output impedance values:







AC6903 0 Ωto 667 mΩ 0 Ωto 2000 mΩ 0 Ωto 2 667 mΩ 0 Ωto 8000 mΩ


AC6906 0 Ωto 333 mΩ 0 Ωto 1000 mΩ 0 Ωto 1333 mΩ 0 Ωto 4000 mΩ


AC6912 0 Ωto 167 mΩ 0 Ωto 500 mΩ 0 Ωto 667 mΩ 0 Ωto 2000 mΩ


AC6918 0 Ωto 111 mΩ 0 Ωto 333 mΩ 0 Ωto 444 mΩ 0 Ωto 1333 mΩ


Keysight AC6900 Operating and Programming Guide 199


4 SCPI Programming


**OUTPut:INHibit:MODE LATChing|LIVE**
**OUTPut:INHibit:MODE?**


Sets the operating mode of the remote inhibit digital pin. The inhibit function shuts down the output in
response to an external signal on the Inhibit input pin. The Inhibit mode is stored in non-volatile
memory. You can specify the polarity of the remote inhibit digital pin using

[SOURce:]DIGital:PIN:POLarity POSitive|NEGative.


LATChing|LIVE|OFF LATC,LIVE, or OFF


LATChing - a logic-true signal on the Inhibit input causes the output state to latch OFF. The output
remains disabled until the Inhibit input is returned to logic-false and the latched INH status bit is
cleared by sending the OUTPut:PROTection:CLEar command or a protection clear command from the
front panel. Note that the output remains disabled after the protection is cleared and must be reenabled with the OUTPut ON command to turn it on again.


LIVE - allows the enabled output to follow the state of the Inhibit input.


OFF - the Inhibit input is ignored.


The inhibit mode is stored in non-volatile memory.


**OUTPut:PHASe:OFF[:STATe] ON|1|OFF|0**
**OUTPut:PHASe:OFF[:STATe]?**


Enables or disables the output-off phase control when OUTPut OFF is sent. This command is invalid if
the signal source is an external signal source.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


200 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**OUTPut:PHASe:OFF:LEVel <** _**value**_ **>**

**OUTPut:PHASe:OFF:LEVel?**


Sets the output-off phase angle when OUTPut OFF is sent in degrees. This command is invalid if the
signal source is an external signal source.


0.0 - 360.0 90


The *RST command sets this parameter to 0.


**OUTPut:PHASe:ON[:STATe] ON|1|OFF|0**
**OUTPut:PHASe:ON[:STATe]?**


Enables or disables the output-on phase control when OUTPut ON is sent. This command is invalid if
the signal source is an external signal source.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


**OUTPut:PHASe:ON:LEVel <** _**value**_ **>**

**OUTPut:PHASe:ON:LEVel?**


Sets the output-on phase angle when OUTPut ON is sent in degrees. This command is invalid if the
signal source is an external signal source.


0.0 - 360.0 90


The *RST command sets this parameter to 0.


Keysight AC6900 Operating and Programming Guide 201


4 SCPI Programming


**OUTPut:PHASe:P1Offset <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:PHASe:P1Offset? [MINimum|MAXimum]**


Sets the absolute phase 1 offset angle to the specified reference in degrees. It cannot be set when in
1P or 1P3W mode. This command is invalid if the signal source is an external signal source.


0.0 - 360.0 90


The *RST command sets this parameter to 0.


**OUTPut:PHASe:P12 <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:PHASe:P12? [MINimum|MAXimum]**


Sets the difference between the phase 1 angle and the phase 2 angle in degrees. It cannot be set when
in 1P or 1P3W mode. This command is invalid if the signal source is an external signal source.


0.0 - 360.0 90


The *RST command sets this parameter to 120 in 3P, or 180 in 1P3W.


**OUTPut:PHASe:P13 <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:PHASe:P13? [MINimum|MAXimum]**


Sets the difference between the phase 1 angle and the phase 3 angle in degrees. It cannot be set when
in 1P or 1P3W mode. This command is invalid if the signal source is an external signal source.


0.0 - 360.0 90


Set the difference between P1 and P3 offset angle:





The *RST command sets this parameter to 240.


202 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**OUTPut:PON:SAFE ON|1|OFF|0**

**OUTPut:PON:SAFE?**


When enabled, holds the output off (disabled) during instrument power-on.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 1.


**OUTPut:PON:STATe RST|RCL<0-9>|AUTO**

**OUTPut:PON:STATe?**


Sets the power-on output state.


RST|RCL0|AUTO RST, RCL0, or AUTO


RST specifies that the instrument powers on in a state equivalent to *RST.


RCL0 loads the state stored in state storage memory location 0.


AUTO specifies that the instrument powers on in the power-down state. The output state is always off.


This parameter is not affected by *RST or *RCL.


This parameter is saved in nonvolatile memory.


**OUTPut:PROTection:CLEar**


Resets the latched protection. This clears the latched protection status that disables the output when
a protection condition occurs. It also clears the latched Inhibit Input function.


(none) (none)


All conditions that generate the fault must be removed before the latched status can be cleared. The
output remains in the OFF state after the fault/inhibit condition is cleared.


Keysight AC6900 Operating and Programming Guide 203


4 SCPI Programming


**OUTPut:PROTection:WDOG[:STATe] ON|1|OFF|0**
**OUTPut:PROTection:WDOG[:STATe]?**


Enables or disables the I/O watchdog timer.


0|OFF|1|ON 0 (OFF) or 1 (ON)


When the watchdog timer is enabled, the output will be disabled if there is no I/O activity on any
remote interface within the time period specified by OUTput:PROTection:WDOG:DELay. The output is
latched off but the programmed output state is not changed.


The watchdog timer function is NOT reset by front-panel activity; the output will still shut down after
the time period has elapsed.


**OUTPut:PROTection:WDOG:DELay <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:PROTection:WDOG:DELay? [MINimum|MAXimum]**


Sets the watchdog delay time.


1 to 3600 (seconds) 600


Values (seconds) must be whole numbers.


**OUTPut:SSTart[:STATe][:RISE] ON|1|OFF|0**
**OUTPut:SSTart[:STATe][:RISE]?**


Enables or disables the soft-start rise. This command is invalid if the voltage compensation mode has

been set to SOFT.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


204 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**OUTPut:SSTart[:STATe]:FALL ON|1|OFF|0**
**OUTPut:SSTart[:STATe]:FALL?**


Enables or disables the soft-start fall. This command is invalid if the voltage compensation mode has

been set to SOFT.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The *RST command sets this parameter to 0.


**OUTPut:SSTart:TIME[:RISE] <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:SSTart:TIME[:RISE]? [MINimum|MAXimum]**


Sets the soft-start rise time in seconds.


This command is only valid if the soft-start rise state is enabled.


0.1 - 30.0 10


The *RST command sets this parameter to 0.1.


**OUTPut:SSTart:TIME:FALL <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:SSTart:TIME:FALL? [MINimum|MAXimum]**


Sets the soft-start fall time in seconds. This command is only valid if the soft-start fall state is enabled.


0.1 - 3.0 1


The *RST command sets this parameter to 0.1.


**OUTPut:SSUPpression:STATeON|1|OFF|0**
**OUTPut:SSUPpression:STATe?**


Enables or disables the voltage surge suppression feature.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Keysight AC6900 Operating and Programming Guide 205


4 SCPI Programming

#### **PROGram Subsystem**


The PROGram subsystem controls the individual sequence steps.


**PROGram:CLEar**


Sets all sequence steps to their default values. This takes approximately three seconds.


(none) (none)


**PROGram:EDIT <** _**step**_ **>,<** _**freq**_ **>,<** _**ramp-bool**_ **>,<** _**ac-volt**_ **>,<** _**ramp-bool**_ **>,<** _**dc-volt**_ **>,<** _**ramp-**_
_**bool**_ **>,<** _**step-time**_ **>,<** _**output-bool**_ **>,<** _**status-bool**_ **>,<** _**trigin-bool**_ **>,<** _**trigout-bool**_ **>,<** _**bank-num-**_
_**ber**_ **>**

**PROGram:EDIT? <** _**step**_ **>**


Programs an individual sequence step.


**Parameter** **Typical Return**



<list of 12 comma-separated parameters >


see table



<list of 12 comma-separated parameters >
(does not include step number)



Program step 1: PROG:EDIT 1,60,OFF,100,OFF,0,OFF,0.5,OFF,OFF,OFF,ON,0


Settings must be programmed in the order shown in the table. Return values are returned in the order
shown in the table starting with <freq>.


**Parameter** **Value and Unit** ***RST** **Description**


< _step_ - 1 - 600 (none) The number of the step to configure


< _freq_ - 1 - 5000 50 The frequency in Hz


< _ramp-bool_ - ON|1|OFF|0 OFF Enables/disables the frequency ramp


< _ac-volt_ >* 0 - 322.0 0 The ac voltage value


< _ramp-bool_ - ON|1|OFF|0 OFF Enables/disables the ac voltage ramp


< _dc-volt_ >* 0 - ±455.0 0 The dc voltage value


< _ramp-bool_ - ON|1|OFF|0 OFF Enables/disables the dc voltage ramp


< _step-time_ - 0.0001 - 60,000 0.01 The step time in seconds


< _output-bool_ - ON|1|OFF|0 ON Enables/disables the output


< _status-bool_ - ON|1|OFF|0 OFF Enables/disables the status output


< _trigin-bool_ - ON|1|OFF|0 OFF Enables/disables the trigger input


< _trigout-bool_ - ON|1|OFF|0 OFF Enables/disables the trigger output


< _bank-number_ >* 0 - 256 0 The waveform bank number


*For 3-phase units, the query only returns the value of the phase 1.


206 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**PROGram:EDIT:FUNCtion[:SHAPe]:BANK[:INDex] <** _**step**_ **>, <** _**bank-number**_ **> [,(@chanlist)]**
**PROGram:EDIT:FUNCtion[:SHAPe]:BANK[:INDex]? <** _**step**_ **> [,(@chanlist)]**


Sets the waveform bank number of the designated sequence step.


The optional (@chanlist) parameter lets you designate specific output phases.


1 - 600 (step) (none)


0 - 256 (bank number) 0


Returns the waveform bank number of the designated step.


**PROGram:EDIT:IMPedance[:STATe] <** _**step**_ **>, ON|1|OFF|0**
**PROGram:EDIT:IMPedance[:STATe]? <** _**step**_ **>**


Enables or disables the output impedance of the step.


1 - 600 (step) (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


**PROGram:EDIT:IMPedance:REACtive <** _**step**_ **>, <** _**value**_ **> [,(@chanlist)]**
**PROGram:EDIT:IMPedance:REACtive? <** _**step**_ **> [,(@chanlist)]**


Sets the reactive component of the output impedance of the step.


The optional (@chanlist) parameter lets you designate specific output phases.


1 - 600 (step) (none)


<value> see table NR3


This command is valid when the program impedance state is ON. Refer to the following table for valid
output impedance values:







AC6903 27 μH to 667 μH 80 μH to 2000 μH 107 μH to 2667 μH 320 μH to 8000 μH


AC6906 13 μH to 333 μH 40 μH v 1000 μH 53 μH to 1333 μH 160 μH to 4000 μH


Keysight AC6900 Operating and Programming Guide 207


4 SCPI Programming







AC6912 7 μH to 167 μH 20 μH to 500 μH 27 μH to 667 μH 80 μH to 2000 μH


AC6918 4 μH to 111 μH 13 μH to 333 μH 18 μH to 444 μH 53 μH to 1333 μH


**PROGram:EDIT:IMPedance:REAL <** _**step**_ **>, <** _**value**_ **> [,(@chanlist)]**
**PROGram:EDIT:IMPedance:REAL? <** _**step**_ **> [,(@chanlist)]**


Sets the resistive component of the output impedance of the step.


The optional (@chanlist) parameter lets you designate specific output phases.


1 - 600 (step) (none)


<value> see table NR3


This command is valid when the program impedance state is ON. Refer to the following table for valid
output impedance values:







AC6903 0 Ωto 667 mΩ 0 Ωto 2000 mΩ 0 Ωto 2 667 mΩ 0 Ωto 8000 mΩ


AC6906 0 Ωto 333 mΩ 0 Ωto 1000 mΩ 0 Ωto 1333 mΩ 0 Ωto 4000 mΩ


AC6912 0 Ωto 167 mΩ 0 Ωto 500 mΩ 0 Ωto 667 mΩ 0 Ωto 2000 mΩ


AC6918 0 Ωto 111 mΩ 0 Ωto 333 mΩ 0 Ωto 444 mΩ 0 Ωto 1333 mΩ


208 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**PROGram:EDIT:JUMP <** _**step**_ **>,<** _**jump-bool**_ **>,<** _**jump-step**_ **>,<** _**jump-count**_ **>**
**PROGram:EDIT:JUMP? <** _**step**_ **>**


Configures the jump settings of the sequence step.


**Parameter** **Typical Return**


1 - 600 (number of steps) (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


1 - 600 (destination step) 2


1- 9998 (jump repetition count) 9999 = repeat indefinitely 1


Configures the jump settings of step 1: PROG:EDIT:JUMP 1, ON, 2, 1


When <jump-bool> is ON, the program jumps to the specified <jump-step>. When <jump-bool> is
OFF, the program proceeds to the next step.


Returns the jump settings in the following order: <bool>,<destination step>,<jump count>.


**PROGram:EDIT:PHASe:P1Offset <** _**step**_ **>,<** _**enable-bool**_ **>,<** _**value**_ **>**
**PROGram:EDIT:PHASe:P1Offset? <** _**step**_ **>**


Sets the phase 1 offset angle of the sequence step in degrees and enables the phase control.


**Parameter** **Typical Return**


1 - 600 (step) (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


-360.00 - 360.00 180


Sets the phase 1 offset angle of step 1: PROG:EDIT:PHAS:P1O 1,ON,180


**PROGram:EDIT:PHASe:P12 <** _**step**_ **>,<** _**enable-bool**_ **>,<** _**value**_ **>**
**PROGram:EDIT:PHASe:P12? <** _**step**_ **>**


Sets the Phase 1 and Phase 2 angle difference of the step in degrees and enables the phase control.


**Parameter** **Typical Return**


1 - 600 (step) (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


0.00 - 360.00 180


Sets the phase 1 and 2 angle difference of step 1: PROG:EDIT:PHAS:P12 1,ON,180


Keysight AC6900 Operating and Programming Guide 209


4 SCPI Programming


**PROGram:EDIT:PHASe:P13 <** _**step**_ **>,<** _**enable-bool**_ **>,<** _**value**_ **>**
**PROGram:EDIT:PHASe:P13? <** _**step**_ **>**


Sets the Phase 1 and Phase 3 angle difference of the step in degrees and enables the phase control.


**Parameter** **Typical Return**


1 - 600 (step) (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


0.00 - 360.00 180


Sets the phase 1 and 3 angle difference of step 1: PROG:EDIT:PHAS:P13 1,ON,180


**PROGram:EDIT:PHASe:RAMP <** _**step**_ **>, <OFF|LEAD|LAG>, <OFF|LEAD|LAG>, <OFF|LEAD|LAG>**
**PROGram:EDIT:PHASe:RAMP? <** _**step**_ **>**


Sets the phase signal change characteristics of the sequence step.
The first parameter sets the Phase 1 angle with the Phase 1 offset.
The second parameter sets the Phase 2 angle with the Phase 1-2 difference.
The third parameter sets the Phase 3 angle with the Phase 1-3 difference.


1 - 600 (step) (none)


<OFF|LEAD|LAG>, <OFF|LEAD|LAG>, <OFF|LEAD|LAG> OFF, OFF, OFF


Sets phase change characteristics of step 1: PROG:EDIT:PHAS:RAMP 1, LAG, OFF, OFF


OFF - turns the ramp off


LEAD - turns the ramp on with a leading phase angle


LAG - turns the ramp on with a lagging phase angle


Returns the ramp settings in the following order: <Phase 1>,<Phase 2>,<Phase 3>.


**PROGram:EDIT:PHASe:STARt <** _**step**_ **>, <** _**value**_ **>**
**PROGram:EDIT:PHASe:STARt? <** _**step**_ **>**


Sets the starting phase angle of the sequence step in degrees.


1 - 600 (step) (none)


0.0 - 360.0 180


210 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**PROGram:EDIT:PHASe:STOP <** _**step**_ **>, <** _**value**_ **>**
**PROGram:EDIT:PHASe:STOP? <** _**step**_ **>**


Sets the stopping phase angle of the sequence step in degrees.


1 - 600 (step) (none)


0.0 - 360.0 180


**PROGram:EDIT:VOLTage <** _**step**_ **>, <** _**value**_ **>**
**PROGram:EDIT:VOLTage? <** _**step**_ **>**


Sets the unbalanced ac voltage of the sequence step in volts.


1 - 600 (step) (none)


0.0 - 322.0 300


**PROGram:EDIT:VOLTage:OFFSet <** _**step**_ **>, <** _**value**_ **>**
**PROGram:EDIT:VOLTage:OFFSet? <** _**step**_ **>**


Sets the unbalanced dc voltage of the sequence step in volts.


1 - 600 (step) (none)


-455.0 to +455.0 400


Keysight AC6900 Operating and Programming Guide 211


4 SCPI Programming


**PROGram:EXECuting STOP|RUN|PAUSe, <** _**time**_ **>,<** _**loop**_ **>,<** _**step**_ **>**
**PROGram:EXECuting?**


Changes the execution state of the sequence program.


STOP|RUN|PAUSe STOP|RUN|PAUS



0.0001 - 60,000 s (elapsed time),
1- 9998 (loop count),
1 - 600 (step)



< _time_ >,< _loop_ >,< _step_ 


Runs the program at the specified time, count and step: PROG:EXEC RUN, 1, 1, 1


STOP - stops the program at the specified time, count, and step


RUN - resumes the program at the specified time, count, and step


PAUSe - pauses the program at the specified time, count, and step


**PROGram:LOOP <** _**value**_ **>**

**PROGram:LOOP?**


Sets the number of sequence repetitions.


1- 9998 (repeat count) 9999 = repeat indefinitely 100


**PROGram:STEP:ADD <** _**step**_ **>**


Adds a new program step.


1 - 600 (none)


**PROGram:STEP:CLEar <** _**step**_ **>**


Deletes a program step.


1 - 600 (none)


212 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**PROGram:STEP:END <** _**step**_ **>**
**PROGram:STEP:END?**


Sets the sequence ending step.


1 - 600 100


**PROGram:STEP:STARt <** _**step**_ **>**
**PROGram:STEP:STARt?**


Sets the sequence starting step.


1 - 600 10


Keysight AC6900 Operating and Programming Guide 213


4 SCPI Programming

#### **SENSe Subsytem**


**SENSe:AVERage <** _**value**_ **>**
**SENSe:AVERage?**


Sets the moving averaging count for both voltage and current measurements.


1 - 32 1


Averaging is not performed when the count is set to 1. This parameter does not apply to the peak

current measurement.


The *RST command sets this parameter to 1.


**SENSe:CURRent:AVERage <** _**value**_ **>**
**SENSe:CURRent:AVERage?**


Sets the moving averaging count for current measurements.


1 - 32 1


Averaging is not performed when the count is set to 1. This parameter does not apply to the peak

current measurement.


The *RST command sets this parameter to 1.


**SENSe:CURRent[:PEAK]:HOLD:CLEar**


Clears the peak held current measurement.


(none) (none)


Use FETCh:CURRent:AMPLitude:MAXimum:HOLD? to query the peak held current measurement.


214 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**SENSe:CURRent[:PEAK]:HOLD:TIME <** _**value**_ **>|MINimum|MAXimum**
**SENSe:CURRent[:PEAK]:HOLD:TIME? [MINimum|MAXimum]**


Sets the peak hold time for current measurements in seconds.



1 - 10

MAX = infinity



1



Returns +9.90000E+37 when the hold time is set to infinity.


The *RST command sets this parameter to 1.


**SENSe:VOLTage:AVERage <** _**value**_ **>**
**SENSe:VOLTage:AVERage?**


Sets the moving averaging count for voltage measurements.


1 - 32 1


Averaging is not performed when the count is set to 1. This parameter does not apply to the peak
voltage measurement.


The *RST command sets this parameter to 1.


**SENSe:VOLTage[:PEAK]:HOLD:CLEar**


Clears the peak held voltage measurement.


(none) (none)


Use FETCh:VOLTage:AMPLitude:MAXimum:HOLD? to query the peak held voltage measurement.


Keysight AC6900 Operating and Programming Guide 215


4 SCPI Programming


**SENSe:VOLTage[:PEAK]:HOLD:TIME <** _**value**_ **>|MINimum|MAXimum**
**SENSe:VOLTage[:PEAK]:HOLD:TIME? [MINimum|MAXimum]**


Sets the peak hold time for voltage measurements in seconds.



1 - 10

MAX = infinity



1



Returns +9.90000E+37 when the hold time is set to infinity.


The *RST command sets this parameter to 1.


216 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


#### **SIMulation Subsystem**

The SIMulation subsystem lets you simulate power line abnormalities.


**SIMulation:EXECuting RUN|STOP, <repeat-count>**
**SIMulation:EXECuting?**


Runs or stops a power line abnormality simulation.



RUN|STOP
1 - 9998 <repeat count>



RUN|STOP

<repeat-count>



STOP - stops the simulation at the specified repeat count


RUN - resumes the simulation at the specified repeat count


The query returns the execution state and the repeat count.


**SIMulation:POLarity POSitive|NEGative**
**SIMulation:POLarity?**


Sets the voltage polarity of the power line simulation.


POSitive|NEGative POS|NEG


**SIMulation:REPeat:COUNt <** _**value**_ **>**

**SIMulation:REPeat:COUNt?**


Sets the repeat count of the power line simulation.



1 - 9998

9999 - repeats indefinitely



100



Keysight AC6900 Operating and Programming Guide 217


4 SCPI Programming


**SIMulation:STATe ON|1|OFF|0**
**SIMulation:STATe?**


Enables or disables a power line simulation sequence.


ON|1|OFF|0 0 (OFF) or 1 (ON)


0 Disables the power line simulation


1 Enables the power line simulation


**SIMulation:T1:PHASe[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T1:PHASe[:LEVel]? [MINimum|MAXimum]**


Sets the starting phase angle of the simulation in degrees.


0.0 - 359.9 180


This command is valid when voltage variation is set to be started by Phase angle


**SIMulation:T1:PHASe:STATeON|1|OFF|0**
**SIMulation:T1:PHASe:STATe?**


Selects whether the simulation start will be set in terms of time or phase.


ON|1|OFF|0 0 (OFF) or 1 (ON)


0 - the value is set in terms of Time.


1 - the value is set in terms of Phase.


218 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**SIMulation:T1:TIME[:LEVel]<** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T1:TIME[:LEVel]? [MINimum|MAXimum]**


Sets the starting time of the simulation in seconds.


0.0000 - 0.9999 0.1


This command is valid when voltage variation is set to be started by Time.


**SIMulation:T2:TIME[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T2:TIME[:LEVel]? [MINimum|MAXimum]**


Sets the first slope time of the simulation in seconds.


0.000 - 99.990 10


**SIMulation:T3:TIME[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T3:TIME[:LEVel]? [MINimum|MAXimum]**


Sets the voltage regulation time of the simulation in seconds.


0.0000 - 9.9990 0.1


**SIMulation:T3:VOLTage[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T3:VOLTage[:LEVel]? [MINimum|MAXimum]**


Sets the regulated voltage of the simulation in volts.


0.0 - 315.0 100


Keysight AC6900 Operating and Programming Guide 219


4 SCPI Programming


**SIMulation:T4:TIME[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T4:TIME[:LEVel]? [MINimum|MAXimum]**


Sets the second slope time of the simulation in seconds.


0.000 - 99.990 10


**SIMulation:T5:CYCLe[:LEVel] <** _**value**_ **>**
**SIMulation:T5:CYCLe[:LEVel]?**


Sets the number of return cycles of the simulation in seconds.


0 - 999900 100


This command is valid when the returned state is configured to be in Cycles.


**SIMulation:T5:CYCLe:STATeON|1|OFF|0**
**SIMulation:T5:CYCLe:STATe?**


Selects whether the simulation remaining in the returned state will be set in terms of time or cycles.


ON|1|OFF|0 0 (OFF) or 1 (ON)


0 - the value is set in terms of Time.


1 - the value is set in terms of Cycles.


**SIMulation:T5:TIME[:LEVel] <** _**value**_ **>|MINimum|MAXimum**
**SIMulation:T5:TIME[:LEVel]? [MINimum|MAXimum]**


Sets the return time of the simulation in seconds.


0.000 - 99.990 10


This command is valid when the returned state is configured to be in Time.


220 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **STATus Subsystem**


Status register programming lets you determine the operating condition of the instrument at any time.
The instrument has three groups of status registers; Operation, Questionable, and Standard Event. The
Operation and Questionable status groups each consist of the Condition, Enable, and Event registers

as well as NTR and PTR filters.


The Status subsystem is also programmed using Common commands. Common commands control
additional status functions such as the Service Request Enable and the Status Byte registers. Refer to

Status Overview for more information.


**STATus:OPERation[:EVENt]?**


Queries the event of the Operation status group. This is a read-only register, which stores (latches) all
events that are passed by the Operation NTR and/or PTR filter. Reading the Operation Event register

clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


**STATus:OPERation:CONDition?**


Queries the condition of the Operation status group. This is a read-only register, which holds the
instrument's live (unlatched) operational status. Reading the Operation Condition register does not

clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


Keysight AC6900 Operating and Programming Guide 221


4 SCPI Programming


**STATus:OPERation:ENABle <** _**value**_ **>**

**STATus:OPERation:ENABle?**


Sets the value of the enable register of the Operation status group. The enable register is a mask for
enabling specific bits from the Operation Event register to set the OPER (operation summary) bit of the
Status Byte register. The STATus:PRESet command clears all enable register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the enable register: STAT:OPER:ENAB 24



< _bit value_ 


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


**STATus:OPERation:NTRansition <** _**value**_ **>**

**STATus:OPERation:NTRansition?**

**STATus:OPERation:PTRansition <** _**value**_ **>**

**STATus:OPERation:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Operation status group.
These registers serve as a polarity filter between the Operation Condition and Operation Event
registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the Operation
Condition register causes that bit in the Operation Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the Operation
Condition register causes that bit in the Operation Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the NTR register: STAT:OPER:NTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Operation Condition register sets the corresponding bit in the Operation Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Operation Condition register can set the corresponding bit in the Operation Event register.


The value returned is the binary-weighted sum of all bits set in the register.


222 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**STATus:OPERation:INSTrument[:EVENt]?**


Queries the event of the Operation Instrument status group.


This is a read-only register, which stores (latches) all events that are passed by the Operation
Instrument NTR and/or PTR filter. Reading the Operation Instrument Event register clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


**STATus:OPERation:INSTrument:CONDition?**


Queries the condition of the Operation Instrument status group.This is a read-only register, which
holds the instrument's live (unlatched) operational status. Reading the Operation Instrument
Condition register does not clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:OPERation:INSTrument:ENABle <** _**value**_ **>**

**STATus:OPERation:INSTrument:ENABle?**


Sets the value of the enable register of the Operation Instrument status group. The enable register is a
mask for enabling specific bits from the Operation Instrument Event register to set the OPER
(operation summary) bit of the Status Byte register. The STATus:PRESet command clears all enable
register bits.


Keysight AC6900 Operating and Programming Guide 223


4 SCPI Programming


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the instrument enable register: STAT:OPER:INST:ENAB 24



< _bit value_ 


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


**STATus:OPERation:INSTrument:NTRansition <** _**value**_ **>**

**STATus:OPERation:INSTrument:NTRansition?**

**STATus:OPERation:INSTrument:PTRansition <** _**value**_ **>**

**STATus:OPERation:INSTrument:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Operation Instrument status
group. These registers serve as a polarity filter between the Operation Instrument Condition and
Operation Instrument Event registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the Operation
Instrument Condition register causes that bit in the Operation Instrument Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the Operation
Instrument Condition register causes that bit in the Operation Instrument Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the NTR register: STAT:OPER:INST:NTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Operation Instrument Condition register sets the corresponding bit in the Operation Instrument Event
register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Operation Instrument Condition register can set the corresponding bit in the Operation Instrument
Event register.


The value returned is the binary-weighted sum of all bits set in the register.


224 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**STATus:OPERation:INSTrument:ISUMmary<1|2|3>[:EVENt]?**


Queries the event of the Operation Instrument Isummary status group. The parameters 1|2|3 specify
the phase.


This is a read-only register, which stores (latches) all events that are passed by the Operation
Instrument Isummary NTR and/or PTR filter. Reading the Operation Instrument Isummary Event
register clears it.


(none) < _bit value_ 

Read the operation instrument Isummary event register for phase3:


The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:CONDition?**


Queries the condition of the Operation Instrument Isummary status group.This is a read-only register,
which holds the instrument's live (unlatched) operational status. Reading the Operation Instrument
Isummary Condition register does not clear it.


(none) < _bit value_ 

Read the operation instrument Isummary condition register for phase3:


The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:ENABle <** _**value**_ **>**
**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:ENABle?**


Sets the value of the enable register of the Operation Instrument Isummary status group. The enable
register is a mask for enabling specific bits from the Operation Instrument Isummary Event register to
set the OPER (operation summary) bit of the Status Byte register. The STATus:PRESet command clears
all enable register bits.


Keysight AC6900 Operating and Programming Guide 225


4 SCPI Programming


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.



< _bit value_ 


Enable bits 3 and 4 in the instrument Isummary enable register for phase2: STAT:OPER:INST:ISUM2:ENAB 24


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:NTRansition <** _**value**_ **>**
**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:NTRansition?**
**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:PTRansition <** _**value**_ **>**
**STATus:OPERation:INSTrument:ISUMmary<1|2|3>:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Operation Instrument
Isummary status group. These registers serve as a polarity filter between the Operation Instrument
Isummary Condition and Operation Instrument Isummary Event registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the Operation
Instrument Isummary Condition register causes that bit in the Operation Instrument Isummary Event
register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the Operation
Instrument Isummary Condition register causes that bit in the Operation Instrument Isummary Event
register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.



< _bit value_ 


Enable bits 3 and 4 in the NTR register for phase2: STAT:OPER:INST:ISUM2:NTR 24


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Operation Instrument Isummary Condition register sets the corresponding bit in the Operation
Instrument Isummary Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Operation Instrument Isummary Condition register can set the corresponding bit in the Operation
Instrument Isummary Event register.


The value returned is the binary-weighted sum of all bits set in the register.


226 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**STATus:PRESet**


Initializes the transition and enable filters for both OPERation and QUEStionable register groups.


The STATus:PRESet command only affects the ENABle register and the transition filter register of the
status data structure. It does not clear any event registers or the error/event queue. To reset all event
registers and the queue within the device status reporting mechanism, use *CLS.


For status data required by SCPI, STAT:PRES sets the transition filter registers so that only positive
transitions are detected and sets the ENABle register to all zeros. The settings of the service request
enable register, parallel poll enable register, memory registers related to the *SAV command, the
instrument address, output queue, and power on status clear flag are not affected by this command.


STAT:OPER:ENAB STAT:QUES:ENAB 0 - all bits disabled


STAT:OPER:NTR STAT:QUES:NTR 0 - all bits disabled


STAT:OPER:PTR 65535 all bits enabled


STAT:QUES:PTR 65535 all bits enabled


(none) (none)


**STATus:QUEStionable[:EVENt]?**


Queries the event of the Questionable status group. This is a read-only register, which stores (latches)
all events that are passed by the Operation NTR and/or PTR filter. Reading the Questionable Status
Event register clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, to enable bit 2
(value 4) and bit 4 (value 16), the corresponding decimal value would be 20 (4 + 16).


*RST does not affect this register.


Keysight AC6900 Operating and Programming Guide 227


4 SCPI Programming


**STATus:QUEStionable:CONDition?**


Queries the condition of the Questionable status group. This is a read-only register, which holds the
instrument's live (unlatched) operational status. Reading the Questionable Status Condition register

does not clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, to enable bit 2
(value 4) and bit 4 (value 16), the corresponding decimal value would be 20 (4 + 16).


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:QUEStionable:ENABle <** _**value**_ **>**

**STATus:QUEStionable:ENABle?**


Sets the value of the enable register of the Questionable status group. The enable register is a mask for
enabling specific bits from the Questionable Event register to set the QUES (questionable summary) bit
of the Status Byte register. The STATus:PRESet command clears all enable register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 2 and 4 in the questionable enable register: STAT:QUES:ENAB 24



< _bit value_ 


For example, to enable bit 2 (value 4) and bit 4 (value 16), the corresponding decimal value would be
20 (4 + 16).


A *CLS does not clear the enable register, but does clear the event register.


228 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**STATus:QUEStionable:NTRansition <** _**value**_ **>**

**STATus:QUEStionable:NTRansition?**

**STATus:QUEStionable:PTRansition <** _**value**_ **>**

**STATus:QUEStionable:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Questionable status group.
These registers serve as a polarity filter between the Questionable Condition and Questionable Event
registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the
Questionable Condition register causes that bit in the Questionable Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the
Questionable Condition register causes that bit in the Questionable Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the questionable PTR register: STAT:QUES:PTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Questionable Condition register sets the corresponding bit in the Questionable Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Questionable Condition register can set the corresponding bit in the Questionable Event register.


The value returned is the binary-weighted sum of all bits set in the register.


**STATus:QUEStionable:INSTrument[:EVENt]?**


Queries the event of the Questionable Instrument status group.


This is a read-only register, which stores (latches) all events that are passed by the Questionable
Instrument NTR and/or PTR filter. Reading the Questionable Instrument Event register clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


Keysight AC6900 Operating and Programming Guide 229


4 SCPI Programming


**STATus:QUEStionable:INSTrument:CONDition?**


Queries the condition of the Questionable Instrument status group.This is a read-only register, which
holds the instrument's live (unlatched) operational status. Reading the Questionable Instrument
Condition register does not clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:QUEStionable:INSTrument:ENABle <** _**value**_ **>**

**STATus:QUEStionable:INSTrument:ENABle?**


Sets the value of the enable register of the Questionable Instrument status group. The enable register
is a mask for enabling specific bits from the Questionable Instrument Event register to set the QUES
(questionable summary) bit of the Status Byte register. The STATus:PRESet command clears all enable
register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the instrument enable register: STAT:QUES:INST:ENAB 24



< _bit value_ 


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


**STATus:QUEStionable:INSTrument:NTRansition <** _**value**_ **>**

**STATus:QUEStionable:INSTrument:NTRansition?**

**STATus:QUEStionable:INSTrument:PTRansition <** _**value**_ **>**

**STATus:QUEStionable:INSTrument:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Questionable Instrument
status group. These registers serve as a polarity filter between the Questionable Instrument Condition
and Questionable Instrument Event registers.


230 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the
Questionable Instrument Condition register causes that bit in the Questionable Instrument Event
register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the
Questionable Instrument Condition register causes that bit in the Questionable Instrument Event
register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.


Enable bits 3 and 4 in the NTR register: STAT:QUES:INST:NTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Questionable Instrument Condition register sets the corresponding bit in the Questionable Instrument
Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Questionable Instrument Condition register can set the corresponding bit in the Questionable
Instrument Event register.


The value returned is the binary-weighted sum of all bits set in the register.


**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>[:EVENt]?**


Queries the event of the Questionable Instrument Isummary status group. The parameters 1|2|3 specify
the phase.


This is a read-only register, which stores (latches) all events that are passed by the Questionable
Instrument Isummary NTR and/or PTR filter. Reading the Questionable Instrument Isummary Event
register clears it.


(none) < _bit value_ 

Read the questionable instrument Isummary event register for phase3:


The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


Keysight AC6900 Operating and Programming Guide 231


4 SCPI Programming


**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:CONDition?**


Queries the condition of the Questionable Instrument Isummary status group.This is a read-only
register, which holds the instrument's live (unlatched) operational status. Reading the Questionable
Instrument Isummary Condition register does not clear it.


(none) < _bit value_ 

Read the questionable instrument Isummary condition register for phase3:


The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:ENABle <** _**value**_ **>**
**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:ENABle?**


Sets the value of the enable register of the Questionable Instrument Isummary status group. The
enable register is a mask for enabling specific bits from the Questionable Instrument Isummary Event
register to set the QUES (questionable summary) bit of the Status Byte register. The STATus:PRESet
command clears all enable register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.



< _bit value_ 


Enable bits 3 and 4 in the instrument Isummary enable register for phase2: STAT:QUES:INST:ISUM2:ENAB 24


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


232 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:NTRansition <** _**value**_ **>**
**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:NTRansition?**
**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:PTRansition <** _**value**_ **>**
**STATus:QUEStionable:INSTrument:ISUMmary<1|2|3>:PTRansition?**


Sets and queries the value of negative and positive transition filters of the Questionable Instrument
Isummary status group. These registers serve as a polarity filter between the Questionable Instrument
Isummary Condition and Questionable Instrument Isummary Event registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the
Questionable Instrument Isummary Condition register causes that bit in the Questionable Instrument
Isummary Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the
Questionable Instrument Isummary Condition register causes that bit in the Questionable Instrument
Isummary Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2
(value 4), bit 3 (value 8), and bit 7 (value 128), use decimal sum 140 (4 + 8 +
128). Default 0.



< _bit value_ 


Enable bits 3 and 4 in the NTR register for phase2: STAT:QUES:INST:ISUM2:NTR 24


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Questionable Instrument Isummary Condition register sets the corresponding bit in the Questionable
Instrument Isummary Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Questionable Instrument Isummary Condition register can set the corresponding bit in the
Questionable Instrument Isummary Event register.


The value returned is the binary-weighted sum of all bits set in the register.


Keysight AC6900 Operating and Programming Guide 233


4 SCPI Programming

#### **SYSTem Subsystem**


The SYSTem subsystem controls instrument functions that are not directly related to output control,
measurement, or status functions. Note that IEEE-488 Common commands also control system
functions such as state management.


**SYSTem:BEEPer[:IMMediate]**


Issues a single beep.


(none) (none)


This command overrides the current beeper state . You can issue a beep even if the beeper is turned off
by SYSTem:BEEPer:STATe.


Programmed beeps may be useful for program development and troubleshooting.


**SYSTem:BEEPer:KCLick ON|1|OFF|0**
**SYSTem:BEEPer:KCLick?**


Disables or enables the click tone heard when you press a front-panel key.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The front-panel key click and instrument beeper settings do not affect each other.


This parameter is not affected by *RST or *RCL.


**SYSTem:BEEPer:STATe ON|1|OFF|0**

**SYSTem:BEEPer:STATe?**


Disables or enables the beep heard when an error is generated.


ON|1|OFF|0 0 (OFF) or 1 (ON)


The front-panel key click and instrument beeper settings do not affect each other.


A beep is always emitted (even with beep state OFF) when SYSTem:BEEPer is sent.


This parameter is not affected by *RST or *RCL.


234 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**SYSTem:COMMunicate:LAN:CONTrol?**


Returns the TCP port number for SCPI-RAW.


(none) <TCP port number>


**SYSTem:COMMunicate:RLSTate LOCal|REMote|RWLock**
**SYSTem :COMMunicate:RLSTate?**


Configures the remote/local/lockout state of the instrument.


LOCal|REMote|RWLock LOC, REM, or RWL


The LOCal parameter (the power-on default), disables the remote interface, the REMote parameter
enables remote control of the instrument, and RWLock enables remote control of the instrument and

locks out front-panel operation. The RWLocal parameter also locks out the operation of the Sense
switch on the rear panel.


The remote/local instrument state can also be set by other interface commands over the GPIB and
other I/O interfaces. If multiple remote interfaces are active, the interface with the most recently
changed remote/local state takes precedence.This parameter is not affected by *RST or *RCL.


**SYSTem:CONFigure:ADJust:VOLTage:FINE <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**
**SYSTem:CONFigure:ADJust:VOLTage:FINE? [MINimum|MAXimum] [,(@chanlist)]**


Sets the voltage offset value in volts. Use SYSTem:CONFigure:ADJust:VOLTage:TERMinal:MODE to
specify where the voltage offset is sensed.


The optional (@chanlist) parameter lets you designate specific output phases.



-200 to +200

(resolution = 10)



180



The one's digit is rounded.


Keysight AC6900 Operating and Programming Guide 235


4 SCPI Programming


**SYSTem:CONFigure:ADJust:VOLTage:TERMinal:MODE OUTPut|SENSe**
**SYSTem:CONFigure:ADJust:VOLTage:TERMinal:MODE?**


Set whether the sensing function is enabled or disabled for the voltage offset setting. Use
SYSTem:CONFigure:ADJust:VOLTage:FINE to set the voltage offset value.


OTERM|STERM STERM


Enables the sense terminals for the voltage offset:


SYST:CONF:ADJ:VOLT:TERM:MODE STERM


OTERM - disables the voltage offset sensing function (sensing is at the output terminals).


STERM - enables the voltage offset sensing function (sensing is at the sense terminals).


**SYSTem:CONFigure:FORMation:FRAMe[:COUNt]?**


Queries the number of units operating in parallel. Parallel operation does not apply to AC6903 units.


(none) 1|2|3


Returns a 1 if parallel operation is not being performed.


**SYSTem:CONFigure:FORMation:FRAMe:INFO? <** _**value**_ **>**


Queries information about the specified unit operating in parallel. Returns the model number, serial
number, and firmware version in comma-separated format. Parallel operation does not apply to

AC6903 units.


0|1|2|3 <model number>,
<serial number>,

<firmware version>


0 - queries the primary or standalone unit (this is the only value that applies to AC6903 units)


1 - queries the first secondary unit


2 - queries the second secondary unit


3 - queries the third secondary unit


236 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**SYSTem:CONFigure:FORMation:PMODule[:COUNt]? <** _**value**_ **>**


Queries the number of power modules in the specified unit. Each power module is rated at 6 kW.


0|1|2|3 (frame) <number of power modules>


Queries the number of power modules in the specified unit:


SYST:CONF:FORM:PMOD? 1


0 - queries the primary or standalone unit (this is the only value that applies to AC6903 units)


1 - queries the first secondary unit


2 - queries the second secondary unit


3 - queries the third secondary unit


**SYSTem:CONFigure:FORMation:PMODule:INFO? <** _**frame**_ **>, <** _**module**_ **>**


Queries information about the specified module operating in a frame.



0|1|2|3 (frame)
1-4 (module index)



<number of power modules>



Queries the specified power module in the specified unit:,1

SYST:CONF:FORM:PMOD:INFO? 1


0 - queries the primary or standalone unit (this is the only value that applies to AC6903 units)


1 - queries the first secondary unit


2 - queries the second secondary unit


3 - queries the third secondary unit


1-4 - Specifies the index of the power module. Up to four power modules can be installed in a frame.


**SYSTem:CONFigure:FORMation:PSAVer:MAXimum <** _**value**_ **>|MINimum|MAXimum**
**SYSTem:CONFigure:FORMation:PSAVer:MAXimum?**


Sets the maximum expected power for the power saver function in Watts.


0 to total output wattage of the unit <power saver limit>


Specifies the power saver limit:

SYST:CONF:FORM:PSAV:MAX 3000


The *RST command sets all instruments to their maximum rated power.


Keysight AC6900 Operating and Programming Guide 237


4 SCPI Programming


**SYSTem:CONFigure:FORMation:PSAVer:MODules E|D {,E|D) . . .**
**SYSTem:CONFigure:FORMation:PSAVer:MODules?**


Sets the specified power modules to run using the power saver function


E|D [,E|D]... E|D [,E|D]...


Enable half of the power modules in a standalone unit:


SYST:CONF:FORM:PSAV:MOD EEDD


E - the power module is disabled


D - the power module is enabled


Up to four parameter pairs can be specified. The first pair configures the power modules in the standalone or primary unit. The subsequent pairs program the power modules the secondary units.


**SYSTem:CONFigure:FORMation:PSAVer:RESet**


Resets the power saver settings. This command is invalid when the output is on.


(none) (none)


**SYSTem:CONFigure:NOUTputs 1|2|3**
**SYSTem:CONFigure:NOUTputs?**
**SYSTem:CONFigure:WIRing 1|2|3**
**SYSTem:CONFigure:WIRing?**


Specifies the output configuration. The two commands are equivalent.


1|2|3 1


1 - specifies single-phase output


2 - specifies single-phase three-wire output


3 - specifies three-phase output


This command is invalid when the output is on (OUTP ON).


This command is invalid when a sequence or a simulation is running.


238 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming



**SYSTem:DATE <yyyy>,<mm>,<dd>**
**SYSTem:DATE?**


Sets the local date of the system clock.



2016 - 2037 (year)
1-12 (month)
1-31 (day)



2021,7,28



Returns the year, month, and day in a comma-separated NR1 format.


**SYSTem:ERRor[:NEXT]?**


Reads and clears one error from the error queue.


(none)


The Err annunciator turns on when any error is in the error queue. Error retrieval is first-in-first-out,
and errors are cleared as you read them. When you have read all errors from the error queue, the Err

annunciator turns off.


If more than 16 errors have occurred, the most recent error is replaced with -350,"Error queue
overflow". No additional errors are stored until you remove errors from the queue. Reading the error
queue when it is empty yields the message +0,"No error".


The error queue is cleared by *CLS and power cycling. It is not cleared by *RST.


Errors have the following format (the error string may contain up to 255 characters).
< _error code_ >,< _error string_ 
For a list of error codes and error strings, see SCPI Error Messages.


Keysight AC6900 Operating and Programming Guide 239


4 SCPI Programming


**SYSTem:ERRor[:NEXT]:COUNt?**


Returns the number of errors in the error queue.


(none) +11


The returned value always begins with a + character, even if the number is 0.


The Err annunciator turns on when any error is in the error queue. Error retrieval is first-in-first-out,
and errors are cleared as you read them. When you have read all errors from the error queue, the Err

annunciator turns off.


If more than 16 errors have occurred, the most recent error is replaced with -350,"Error queue
overflow". No additional errors are stored until you remove errors from the queue. Reading the error
queue when it is empty yields the message +0,"No error".


The error queue is cleared by *CLS and power cycling. It is not cleared by *RST.


Errors have the following format (the error string may contain up to 255 characters).
< _error code_ >,< _error string_ 
For a list of error codes and error strings, see SCPI Error Messages.


**SYSTem:LOCal**

**SYSTem:REMote**

**SYSTem:RWLock**


These three commands are included for compatibility. Use the equivalent
SYSTem:COMMunicate:RLST when creating new programs.


**SYSTem:PASSword[:CENable] "<** _**password**_ **>"**
**SYSTem:PASSword[:CENable]?**


Enables password protection by entering the password set by SYSTem:PASSword:NEW.


"<password>" (none)


240 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**SYSTem:PASSword[:CENable]:STATe?**


Queries if password protection is enabled.


(none) 0 = disabled

1 = enabled


**SYSTem:PASSword:CDISable "<** _**password**_ **>"**


Disables password protection. Enter the existing password in quotes to disable password protection.


"<password>" (none)


**SYSTem:PASSword:NEW "<** _**existing_password**_ **>","<** _**new_password**_ **>"**


Sets a new password. Enter the existing password and the new password in quotes.


The password must be 4 to 15 characters long and may include any of these characters:


A-Z Upper case letters


a-z Lower case letters


0-9 Numeric digits


_, - underscore and hyphen



"<existing_password>"
"<new_password>"



(none)



**SYSTem:SECurity:IMMediate**


Clears all user memory (including stored states) and reboots the instrument in the *RST state.


Sanitization is per the National Industrial Security Program Operating Manual (NISPOM). Factory data
(instrument firmware, model number, serial number, MAC address and calibration data) is not erased.

After the data is cleared, the instrument is rebooted. This procedure is typically performed only when
you need to remove an instrument from a secure area. The sanitization procedure is not recommended
for use in routine applications because of the possibility of unintended loss of data.


Keysight AC6900 Operating and Programming Guide 241


4 SCPI Programming


(none) (none)


When parallel operation in use, set the rotary switches for the address and the number of secondary
units on the primary unit and all secondary units to zero, and then use this command.


If your unit includes a micro-SD card (Option MEM), you can simply remove the card to sanitize the
instrument. Refer to Removing and Installing an SD Card for details.


**SYSTem:SLEep:EXECute**


Immediately activates sleep mode.


(none) (none)


**SYSTem:VERSion?**


Returns the version of SCPI that the instrument uses.


(none) 1999.0


The query always returns 1999.0


The SCPI version cannot be determined from front panel.


**SYSTem:TIME <hh>,<mm>,<ss>**

**SYSTem:TIME?**


Sets the local time of the system clock in hours, minutes, and seconds.



0 - 23 (hour)
0-59 (minute)
0-59 (second)



20,30,00



Returns the hours, minutes, and seconds in a comma-separated NR1 format.


242 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **TRIGger Subsystem**


The TRIGger subsystem sets the measurement operation and trigger functions for various instrument
operations:


TRIGger:ACQuire - triggers a measurement acquisition


TRIGger:Program - triggers the start of a program


TRIGger:SIMulation - triggers the start of a simulation


TRIGger:TRANsient - triggers synchronizes output changes


**TRIGger:ACQuire[:IMMediate]**


Sends a software trigger to the ACQuire subsystem.


(none) (none)


Configure and trigger the ACQuire subsystem:


**TRIGger:ACQuire:SOURce IMMediate|BUS**
**TRIGger:ACQuire:SOURce?**


Sets the trigger source that starts the measurement after INIT:ACQ. Specify IMMediate to start the
measurement immediately, or BUS (the default) to wait for a software trigger (*TRG, or TRIG:ACQ).


IMMediate|BUS IMM or BUS


Power-on set this parameter to IMMediate.


**TRIGger:PROGram[:IMMediate]**


Sends a software trigger to the PROGram subsystem.


(none) (none)


Configure and trigger the PROGram subsystem:


Keysight AC6900 Operating and Programming Guide 243


4 SCPI Programming


**TRIGger:PROGram:SOURceIMMediate|BUS**
**TRIGger:PROGram:SOURce?**


Sets the trigger source that starts the program after INIT:PROG. Specify IMMediate to start the
program immediately, or BUS (the default) to wait for a software trigger (*TRG or TRIG:PROG).


IMMediate|BUS IMM or BUS


Power-on set this parameter to IMMediate.


**TRIGger:SIMulation[:IMMediate]**


Sends a software trigger to the SIMulation subsystem.


(none) (none)


Configure and trigger the SIMulation subsystem:


**TRIGger:SIMulation:SOURceIMMediate|BUS**
**TRIGger:SIMulation:SOURce?**


Sets the trigger source that starts the simulation after INIT:SIM. Specify IMMediate to start the
simulation immediately, or BUS (the default) to wait for a software trigger (*TRG or TRIG:SIM).


IMMediate|BUS IMM or BUS


Power-on set this parameter to IMMediate.


244 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**TRIGger:TRANsient[:IMMediate]**


Triggers the TRANsient subsystem.


(none) (none)


Configure and trigger the TRANsient subsystem:


**TRIGger:TRANsient:SOURce IMMediate|BUS**
**TRIGger:TRANsient:SOURce?**


Sets the trigger source for changing the output value after INIT:TRAN. Specify IMMediate (the default)
to start the measurement immediately, or BUS to wait for a software trigger (*TRG or TRIG:TRAN).


IMMediate|BUS IMM or BUS


Power-on set this parameter to IMMediate.


Keysight AC6900 Operating and Programming Guide 245


4 SCPI Programming

#### **VOLTage Subsystem**


Voltage commands program the output voltage of the instrument.


Soft limits apply only if the corresponding limit state is on, and they only apply to voltage
settings executed after the limit state is set on. Soft limits do not retroactively apply to the
existing voltage setting.


**[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] <** _**value**_ **>|MINimum|MAXimum[, <** _**low_**_
_**limit**_ **>|MINimum|MAXimum, <** _**high_limit**_ **>|MINimum|MAXimum] [,(@chanlist)]**

**[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum] [,(@chanlist)]**

**[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] <** _**value**_ **>|MINimum|MAXimum [,(@chan-**
**list)]**

**[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude]? [MINimum|MAXimum]**


Sets the immediate or triggered AC voltage level, and optionally the soft limits for the immediate
voltage. The triggered level is the value that is transferred to the output when an output step is
triggered. Units are in volts (rms).


The optional (@chanlist) parameter lets you designate specific output phases.



AC mode 160 V range: 0.0 to 161.0 V
AC mode 320 V range: 0.0 to 322.0 V



+2.00000E+01



This command takes one or three arguments. You cannot have a low or high limit without the other.


Voltage units (MV, V, KV) are supported.


This command applies to AC and AC+DC.


This command is invalid when the combined value with the DC voltage is outside the allowable range
(L range: -227.5 V to 227.5 V, H range: -455.0 V to 455.0 V).


This command is invalid If the signal source is set to external signal source
(VOLT:EXT:EXTDC:SIGN:SOUR EXT).


This command is invalid when an external analog signal is used to control the voltage or frequency
(VOLT:EXT:FUNC:MODE VPR).


The *RST command sets this parameter to 0.


246 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming




**[SOURce:]VOLTage[:LEVel]:LIMit:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage[:LEVel]:LIMit:LOWer? [MINimum|MAXimum]**

**[SOURce:]VOLTage[:LEVel]:LIMit:UPPer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage[:LEVel]:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower and upper AC voltage limits. Units are in volts (rms).



AC mode 160 V range: 0.0 to 161.0 V
AC mode 320 V range: 0.0 to 322.0 V


Limit the AC voltage to between 150 and 250 Vrms:


This command applies to AC and AC+DC.



+2.50000E+02



If the new setting conflicts with the existing immediate voltage setting (because the limit range
becomes tighter), this command coerces the voltage setting to fit with the new upper and lower limits.


A SCPI error (-222,"Data out of range") occurs if the entered parameter value is outside the range.


The *RST command sets the upper limit to 322 and lower limit to 0.


**[SOURce:]VOLTage:COMPensate:MODE DISabled|HARD|SOFT**

**[SOURce:]VOLTage:COMPensate:MODE?**


Sets the compensation mode.


DISabled|HARD|SOFT DIS|HARD|SOFT


DISabled - disables the voltage compensation.


HARD - compensates the voltage drop up to 1.5 V per load line at the load.


SOFT - compensates the voltage up to ±10 % of the output's set voltage at the load.


The *RST command sets this parameter to DISabled.


Keysight AC6900 Operating and Programming Guide 247


4 SCPI Programming


**[SOURce:]VOLTage:COMPensate:SOFT:CONTrol[:STATus] AC|DC**

**[SOURce:]VOLTage:COMPensate:SOFT:CONTrol[:STATus]?**


Sets the compensated voltage mode.


AC|DC AC|DC


AC - compensates the AC voltage. This setting is invalid if the DC voltage is not zero.


DC - compensates the DC voltage. This setting is invalid if the AC voltage is not zero.


This command is invalid when the output is ON.


The *RST command sets this parameter to AC.


**[SOURce:]VOLTage:COMPensate:SOFT:TERMinal OUTPut|SENSing**

**[SOURce:]VOLTage:COMPensate:SOFT:TERMinal?**


Sets the compensation sensing point.


OUTPut|SENSe OUTP|SENS


OUTPut - selects the output terminals as the compensation sensing point.


SENSe - selects the sense terminals as the compensation sensing point.


This command is invalid when the output is ON.


The *RST command sets this parameter to SENSing.


248 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming




**[SOURce:]VOLTage:LTLine <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:LTLine? [MINimum|MAXimum]**


Sets the line-to-line AC voltage. Units are in volts (rms).



0.0 to 322.0 in single-phase lower range
0.0 to 644.0 in single-phase upper range
0.0 to 278.8 in three-phase lower range
0.0 to 557.7 in three-phase upper range:



+2.50000E+02



Set the immediate line-to-line voltage to 150 V: VOLT:LTL 150


This command applies in single-phase, three-wire output.


This command also applies in three-phase output when the phase 1- to- phase 2 difference is 120, and
the phase 1- to- phase 3 difference is 240.


This command is invalid when an external analog signal is used to control the voltage or frequency,
and when the signal source is set to external.


The *RST command sets this parameter to 0.


**[SOURce:]VOLTage:OFFSet[:IMMediate] <** _**value**_ **>|MINimum|MAXimum [,<** _**low_limit**_ **>|MINim-**
**um|MAXimum,<** _**high_limit**_ **>|MINimum|MAXimum] [,(@chanlist)]**

**[SOURce:]VOLTage:OFFSet[:IMMediate]? [MINimum|MAXimum] [,(@chanlist)]**

**[SOURce:]VOLTage:OFFSet:TRIGgered <** _**value**_ **>|MINimum|MAXimum [,(@chanlist)]**

**[SOURce:]VOLTage:OFFSet:TRIGgered? [MINimum|MAXimum]**


Sets the immediate or triggered DC voltage in VDC, and optionally sets the soft limits for the
immediate DC voltage. The optional (@chanlist) parameter lets you designate specific output phases.



DC mode 160 V range: -227.5 to +227.5 V
DC mode 320 V range: -455.0 to +455.0 V
AC+DC mode: The total voltage must be within the voltage limits,

and the AC+DC peak must be between -455 and +455 V.


Set the immediate DC voltage to 150 V: VOLT:OFFS 150



+1.50000E+02



This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to DC and AC+DC.


Error message +160 is generated if the IMMediate voltage is set outside the MIN/MAX limits, and error
message +161 is generated if the TRIGgered voltage is set outside the MIN/MAX limits.


Voltage units (MV, V, KV) are supported.


The *RST command sets this parameter to 0.


Keysight AC6900 Operating and Programming Guide 249


4 SCPI Programming


**[SOURce:]VOLTage:OFFSet:LIMit:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:LIMit:LOWer? [MINimum|MAXimum]**

**[SOURce:]VOLTage:OFFSet:LIMit:UPPer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower and upper DC voltage soft limits.



160 V range: -227.5 to +227.5 V
320 V range: -455.0 to +455.0 V


Set the voltage limits to the be 100 and 350 V:



(none)



This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to DC and AC+DC.


Error message +166 is generated if the lower limit is beyond the MIN/MAX limits, and error message
+167 is generated if the upper limit is beyond the MIN/MAX limits.


Voltage units (MV, V, KV) are supported.


The *RST command sets the upper limit to 452 and lower limit to -452.


**[SOURce:]VOLTage:OFFSet:LTLine<** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:LTLine?[MINimum|MAXimum]**


Sets the line-to-line DC voltage. Units are in volts (rms).



-455.0 to +455.0 V in DC mode lower range
-890.0 to +890.0 in DC mode upper range



+2.50000E+02



Set the immediate line-to-line voltage to 300 V: VOLT:OFFS:LTL 300


This command applies in single-phase, three-wire output.


This command is invalid when the signal source is set to external.


The *RST command sets this parameter to 0.


250 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:GAIN <** _**channel**_ **>,<** _**value**_ **>**

**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:GAIN? <** _**channel**_ **>**


Sets the voltage gain when varying the voltage or frequency with the external analog signal EXTDC for
each A, B, and C channel..


0|1|2 - the specified channel (none)


5 - 220 10


Set the voltage gain of the external signal to 10: VOLT:PROG:EXT:EXTDC:ADJ:GAIN 10


0 - specifies channel A


1 - specifies channel B


2 - specifies channel C


The *RST command sets this parameter to 100.


**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:OFFSet <** _**channel**_ **>,<** _**value**_ **>**

**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:OFFSet? <** _**channel**_ **>**


Sets the voltage offset when varying the voltage or frequency with the external analog signal EXTDC
for each A, B, and C channel..


0|1|2 - the specified channel (none)


-200 - 200 100


Set the voltage offset of the external signal to 100: VOLT:PROG:EXT:EXTDC:ADJ:OFFS 100


0 - specifies channel A


1 - specifies channel B


2 - specifies channel C


The *RST command sets this parameter to 0.


Keysight AC6900 Operating and Programming Guide 251


4 SCPI Programming


**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:APERture <** _**value**_ **>|MINim-**
**um|MAXimum**

**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:APERture? [MINimum|MAXimum]**


Sets the external analog input signal's aperture time in seconds.


0.1 - 1.0 0.5


This command is valid when the signal source is set to external.


The *RST command sets this parameter to 0.1.


**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:POLarity <** _**channel**_ **>,**
**NORMal|INVerted**

**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:POLarity? <** _**channel**_ **> <** _**value**_ **>**


Sets the external analog input programming signal polarity for each A, B, and C channel.


0|1|2 - the specified channel (none)


NORMal|INVerted NORM|INV


Set the external signal polarity for channel A to invert: VOLT:PROG:EXT:EXTDC:SIGN:POL 0, INV


0 - specifies channel A


1 - specifies channel B


2 - specifies channel C


NORMal - specifies a signal whose polarity is the same as the input signal


INVerted - specified a signal whose polarity is the opposite as the input signal


The *RST command sets this parameter to NORMal.


252 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:SOURce EXTernal|INT_EXT**

**[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:SOURce?**


Sets the external analog input programming signal source.


EXTernal|INT_EXT EXT|INT_EXT


Set the external programming signal source to INT_EXT:

VOLT:PROG:EXT:EXTDC:SIGN:SOUR INT_EXT


EXTernal - the external signal.


INT_EXT - the internal signal and the external signal


The *RST command sets this parameter to EXTernal.


**[SOURce:]VOLTage:PROGramming:EXTernal:MODE EXTDC|VPRogram|OFF**

**[SOURce:]VOLTage:PROGramming:EXTernal:MODE?**


Sets the external analog signal's programming mode.


EXTDC|VPRogram|OFF EXTDC|VPR|OFF


EXTDC - the external signal amplifies the output voltage.


VPRogram - the external signal programs the output voltage using a fixed gain.


OFF - Turns the external analog signal programming mode off.


The *RST command sets this parameter to OFF.


This command is invalid when the output is ON.


**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:GAIN <** _**channel**_ **>,<** _**value**_ **>**

**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:GAIN? <** _**channel**_ **>**


Sets the voltage gain when varying the voltage or frequency with the external analog signal
VPRogram.


5 - 50 10


Set the voltage gain of the external signal to 10: VOLT:PROG:EXT:VPR:ADJ:GAIN 10


The *RST command sets this parameter to 10.


Keysight AC6900 Operating and Programming Guide 253


4 SCPI Programming


**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:OFFSet <** _**channel**_ **>,<** _**value**_ **>**

**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:OFFSet? <** _**channel**_ **>**


Sets the voltage offset when varying the voltage or frequency with the external signal VPRogram.


-200 - 200 100


Set the voltage offset of the external signal to 100: VOLT:PROG:EXT:VPR:ADJ:OFFS 100


The *RST command sets this parameter to 0.


**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:MAPALL|ACVoltage|DCVoltage**

**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:MAP?**


Sets the voltage programming output mapping when varying the voltage or frequency with the external analog signal.


ALL|ACVoltage|DCVoltage ALL|ACV|DCV


ALL - sets channel A to AC voltage; channel B to DC voltage; channel C to Frequency


ACVoltage - sets channel A to phase 1, AC voltage; channel B to phase 2, AC voltage; channel C to
phase 3, AC voltage.


DCVoltage - sets channel A to phase 1, DC voltage; channel B to phase 2, DC voltage; channel C to
phase 3, DC voltage.


This command is invalid for single-phase output and single-phase three-wire output.


The *RST command sets this parameter to ALL.


254 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:STATe <** _**channel**_ **>, ON|1|OFF|0**

**[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:STATe? <** _**channel**_ **>**


Sets the voltage programming output state for each channel when varying the voltage or frequency
with the external analog signal.


0|1|2 - the specified channel (none)


ON|1|OFF|0 0 (OFF) or 1 (ON)


Set the voltage programming output state for channel A: VOLT:PROG:EXT:VPR:STAT: 0, ON


0 - specifies channel A


1 - specifies channel B


2 - specifies channel C


The *RST command sets this parameter to 0.


**[SOURce:]VOLTage:PROGramming:SOURce INTernal|EXTernal**

**[SOURce:]VOLTage:PROGramming:SOURce?**


Sets the voltage programming source.


INTernal|EXTernal INT|EXT


Set the voltage programming source to external: VOLT:PROG:SOUR EXT


INTernal - programs the output voltage from the front panel or from a program.


EXTernal - programs the output voltage using an external signal.


The *RST command sets this parameter to INTernal.


**[SOURce:]VOLTage:PROTection:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:PROTection:LOWer? [MINimum|MAXimum]**


Sets the under-voltage protection value.


0 - 500.5 120


Set the under-voltage protection to -120: VOLT:PROT:LOW 120


This command is valid when the UVP function is enabled.


For single-phase three-wire output and three-phase output, this value applies to all phases.


The *RST command sets this parameter to 0.


Keysight AC6900 Operating and Programming Guide 255


4 SCPI Programming


**[SOURce:]VOLTage:PROTection:LOWer:STATe ON|1|OFF|0**

**[SOURce:]VOLTage:PROTection:LOWer:STATe?**


Enables or disables the under-voltage protection


ON|1|OFF|0 0 (OFF) or 1 (ON)


Enable under-voltage protection: VOLT:PROT:LOW:STAT ON


The *RST command sets this parameter to 0.


**[SOURce:]VOLTage:PROTection:UPPer <** _**value**_ **>|MINimum|MAXimum [,<** _**neg_limit**_ **>, <** _**pos_**_
_**limit**_ **>]**

**[SOURce:]VOLTage:PROTection:UPPer? [MINimum|MAXimum]**


Sets the over-voltage protection value in rms volts. Optionally, you can include the positive and
negative peak voltage limit values.



0 - 500.5 - the rms over-voltage value
0 - 500.5 - the optional positive peak limit
-500.5 - 0 - the optional negative peak limit



200



Set the over-voltage protection to 200 with a peak limit of 250: VOLT:PROT:UPP 200, 250


For single-phase three-wire output and three-phase output, set the limits using phase voltages.


The *RST command sets this parameter to 500.5.


**[SOURce:]VOLTage:PROTection:PEAK:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:PROTection:PEAK:LOWer? [MINimum|MAXimum]**


Sets the negative peak OVP limit in volts.


-500.5 - 0 - the negative peak voltage limit -250


Set the negative peak limit -250: VOLT:PROT:PEAK:LOW -250


The *RST command sets this parameter to -500.5.


256 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**[SOURce:]VOLTage:PROTection:PEAK:UPPer<** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:PROTection:PEAK:UPPer? [MINimum|MAXimum]**


Sets the positive peak OVP limit in volts.


0 - 500.5 - the positive peak voltage limit 250


The *RST command sets this parameter to 500.5.


**[SOURce:]VOLTage:RANGe[:UPPer] <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:RANGe[:UPPer]? [MINimum|MAXimum]**


Sets the voltage range. If the voltage range is switched, the VOLT:TRIG and VOLT:OFFS:TRIG settings
are cleared, and ABORt[:ALL] is applied.


161|322|MINimum|MAXimum +2.70000E+02


You may only change the voltage range when the output is OFF.


Entering a value from 0 to 161 sets the Low range; entering a value above 161 V sets the High range.


The *RST command sets this parameter to 161.


**[SOURce:]VOLTage:RESPonse SLOW|MEDium|FAST**

**[SOURce:]VOLTage:RESPonse?**


Sets the output response.


SLOW|MEDium|FAST SLOW|MED|FAST


SLOW - specifies slower response with high stability.


MEDium - specifies normal output response.


FAST - specifies fastest output response. This setting in invalid when units are paralleled.


This command is invalid when the output is ON.


This command is invalid when a sequence or a simulation is running.


The *RST command sets this parameter to MEDium.


Keysight AC6900 Operating and Programming Guide 257


4 SCPI Programming

#### **WAVE Subsystem**


The WAVE subsystem programs a user-defined waveform.


**WAVE:DATA:ARBitrary <** _**bank_number**_ **>, <** _**block**_ **>**
**WAVE:DATA:ARBitrary? <** _**bank_number**_ **>**


Sets a user-defined waveform with block data at the specified waveform bank.


If you overwrite the content of the bank selected with FUNC:BANK, the change is immediately applied.


This command is invalid when the synchronization function is on.


1 - 256 (waveform bank number) (none)


< _block_ - (user defined) < _block_ 

The user-defined waveform is fixed to Big Endian with a size of 4096 words or 8192 octets.


In a 16-bit integer array, each value ranges from -32767 to +32767


**WAVE:DATA:CLIP <** _**bank_number**_ **>, <** _**value**_ **>|MINimum|MAXimum**
**WAVE:DATA:CLIP? <** _**bank_number**_ **> [MINimum|MAXimum]**


Sets the crest factor of the peak clipped waveform that you specify by its bank number.


258 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


1 - 256 (bank number) (none)


0.10 - 1.40 (pclip value) 0.5


This command is invalid when the synchronization function is on.


**WAVE:DATA:IECPclip <** _**bank_number**_ **>, <** _**value**_ **>|MINimum|MAXimum**
**WAVE:DATA:IECPclip? <** _**bank_number**_ **> [MINimum|MAXimum]**


Sets the clip factor of the flat curve waveform that you specify by its bank number.


1 - 256 (bank number) (none)


0.4 - 1.0 (IECpclip value) 0.5


This command is invalid when the synchronization function is on.


**WAVE:DATA:POINt <** _**bank_number**_ **>, <** _**point**_ **>, <** _**data**_ **>**
**WAVE:DATA:POINt? <** _**bank_number**_ **>, <** _**point**_ **>**


Programs the arbitrary waveform by specifying the waveform bank number and the point.


1 - 256 (bank number) (none)


0 - 4095 (point) 1023


-32767 to +32767 32767


**WAVE:DATA:SINusoid <** _**bank_number**_ **>**


Resets the waveform to a sinusoid.


1 - 256 (bank number) (none)


This command is invalid when the synchronization function is on.


**WAVE:DATA:TYPE? <** _**bank_number**_ **>**


Queries the type of waveform at the specified bank number.


Keysight AC6900 Operating and Programming Guide 259


4 SCPI Programming


1 - 256 (bank number) (none)


Return values include:


SINusoid - sine waveform.


CLIP - peak-clipped waveform.


IECPclip - flat-curve waveform


ARBitrary - user-defined waveform


**WAVE:STATe ON|1|OFF|0**


Enables or disables the custom waveform.


ON|1|OFF|0 0 (OFF) or 1 (ON)


This command is invalid when the synchronization function is on.


The *RST command sets this parameter to 0.


260 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming

#### **Trigger System Overview**


The AC6900 Series has four different trigger subsystems.


TRANsient - this subsystem is used to change the voltage and frequency settings.


ACQuire - this subsystem is used to measure voltage, current, and power.


SIMulation - this subsystem executes power line abnormality simulations.


PROGram - this subsystem executes sequences.


**Basic Trigger States**


All trigger subsystems have three basic states: an IDLE state, an INITiated state, and a Waiting (WTG)

state.


IDLE state


When the product is turned on, all trigger subsystems are in the IDLE state. In this state, the trigger
subsystem ignores all triggers. If you send any of the following commands, the trigger subsystem is
switched to the IDLE state, regardless of its current state.


ABORt


*RST


*RCL


IEEE488.1 selected device clear, or device clear


INITiated state


When you send the INIT command while the product is in the IDLE state, the trigger function begins
operating, and the tester switches to the INITiated state. If the trigger source is set to IMMediate, the
settings are changed immediately, or the measurement, power line abnormality simulation, or
sequence starts immediately.


If the trigger source is set to BUS, the product switches to the WTG (Waiting for Trigger) state.


WTG (Waiting for Trigger) state


If a trigger is received in the WTG state, the settings are changed, or the measurement, power line
abnormality simulation, or sequence starts.


The following figure illustrates the basic functionality of the trigger system.


Keysight AC6900 Operating and Programming Guide 261


4 SCPI Programming


**Transient Trigger Example**


You can use the TRIGger:TRANsient subsystem to synchronize the changes in the output with triggers.
This is useful when you want to synchronize the changes in the output to the operation of external
devices, such as DC power supplies and electronic loads.


The programmable parameters of the TRANsient group are AC voltage, DC voltage, and frequency.
The current limit setting cannot be changed using triggers. Use the VOLTage:TRIGgered,
VOLTage:OFFSet:TRIGgered, and FREQuency:TRIGgered command to reserve the trigger settings.


VOLTage 110 Sets the voltage to 110 V


FREQuency 60 Sets the frequency to 60 Hz


VOLTage:TRIGgered 100 Sets the voltage when a trigger is received to 100 V


FREQuency:TRIGgered 50 Sets the frequency when a trigger is received to 50 Hz


TRIGger:TRANsient:SOURce BUS Sets the trigger source to BUS


INITiate:TRANsient Starts or initiates the transient function


TRIGger:TRANsient Applies a software trigger to start the output change


262 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


If you repeatedly change the output, a trigger error (-210) may occur. By using the *OPC command,
you can prevent this error.-> “Waiting for Operation Complete”


TRIGger:TRANsient;*OPC Applies a trigger and waits until the output change is complete


The INITiate:TRANsient command pulls the trigger subsystem out of the IDLE state and starts (or
initiates) the trigger function. When set to BUS, the trigger subsystem enters the WTG (Waiting For
Trigger) state. When a software trigger is subsequently received (through the TRIGger:TRANsient
command or *TRG command), the output changes begin.


When the trigger transient source is set to IMMediate instead of BUS, transient starts immediately
after INITiate:TRANsient and sets the voltage and frequency to their new values.


When the operation is complete, the trigger subsystem returns to the IDLE state again. If the ABORt
command or an equivalent command is received instead of a trigger, the changes are canceled, and
the trigger subsystem returns to the IDLE state.


You can also use the *TRG command or the IEEE488.1 get (Group Execute Trigger) command to apply
a software trigger to all trigger subsystems. If there are other trigger subsystems in the initiated state,
their trigger operations will also be executed at the same time.


**Acquire Trigger Example**


With advanced measurements, you can separate and control the starting of measurement and the
referencing of data.


To start a measurement immediately, set the trigger source to IMMediate, and then use the INITiate

command.


TRIGger:ACQuire:SOURce IMMediate Sets the trigger source to IMMediate


INITiate:ACQuire Starts or initiates the acquire function


To use software triggers to start the measurement, change the trigger source to BUS.


TRIGger:ACQuire:SOURce BUS Sets the trigger source to BUS


INITiate:ACQuire Starts or initiates the acquire function


TRIGger:ACQuire Applies a software trigger to start the measurement


When the measurement finishes, you can use the FETCh query to retrieve the measured data.


FETCh:VOLTage:ACDC? Queries the rms voltage


FETCh:CURRent:ACDC? Queries the rms current


FETCh:POWer:ACDC? Queries the power


FETCh:POWer:ACDC:APParent? Queries the apparent power


If you send a FETCh command before the measurement is complete, correct measurement data will
not be obtained. By using the *OPC command, you can obtain correct measurment data.


Keysight AC6900 Operating and Programming Guide 263


4 SCPI Programming


INITiate:ACQuire;*OPC Initiates the acquire function and waits for completion.


The INITiate:ACQuire command pulls the trigger subsystem out of the IDLE state and starts (or
initiates) the trigger function. When set to BUS, the trigger subsystem enters the WTG (Waiting For
Trigger) state. When a software trigger is subsequently received (through the TRIGger:ACQuire
command or *TRG command), the measurement starts.


When the trigger acquire source is set to IMMediate instead of BUS, the measurement starts
immediately after INITiate:ACQuire.


When the measurement finishes, the TRIGger subsystem enters the IDLE state again. If the ABORt
command or an equivalent command is received instead of a trigger, the measurement is canceled,
and the TRIGger subsystem returns to the IDLE state.


The ABORt command and IEEE488.1 selected device clear, or device clear commands abort

measurements that are in progress. These commands do not invalidate measured data that has
already been retrieved. On the other hand, the *RST and *RCL common commands not only abort a
measurement that is in progress but also invalidate the acquired measured data. If you send
*RST;:FETC:VOLT?, an error will occur because there is no measured data that the FETCh query can
retrieve and there is no new measurement that is going to be performed.


The difference between the MEASure command and the FETCh command is as follows. The MEASure

command starts a new measurement and then queries the measured data. The FETCh command
queries the measured data without first starting a new measurement. The valid measurement
parameters are exactly the same between MEASure and FETCh.


**Simulation Trigger Example**


You can use triggers to synchronize power line abnormality simulations by using the
TRIGger:SIMulation subsystem


To start a simulation immediately, set the trigger source to IMMediate, and then use the INITiate

command.


TRIGger:SIMulation:SOURce IMMediate Sets the trigger source to IMMediate


INITiate:SIMulation Starts or initiates the simulation


To use software triggers to start the simulation, change the trigger source to BUS.


TRIGger:SIMulation:SOURce BUS Sets the trigger source to BUS


INITiate:SIMulation Starts or initiates the simulation


TRIGger:SIMulation Applies a software trigger to start the simulation


INITiate:SIMulation pulls the TRIGger subsystem out of the IDLE state and starts (initiates) the trigger
function. When set to BUS, the trigger subsystem enters the WTG (Waiting For Trigger) state. When a
software trigger is subsequently received (through the TRIGger:SIMulation command or *TRG
command), the simulation starts.


264 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


When the trigger acquire source is set to IMMediate instead of BUS, the simulation starts immediately

after INITiate:SIMulation.


When the simulation finishes, the TRIGger subsystem enters the IDLE state again. If the ABORt
command or an equivalent command is received when a simulation is being executed, the simulation is
canceled, and the TRIGger subsystem returns to the IDLE state.


Send the *RST command to reset all the parameters of the power line abnormality simulation.


You can also use the *TRG command or the IEEE488.1 get (Group Execute Trigger) command to apply
a software trigger to all trigger subsystems. If there are other trigger subsystems in the initiated state,
their trigger operations will also be executed at the same time.


**Program Trigger Example**


You can use triggers to synchronize sequences by using the TRIGger:PROGram subsystem.


To start a sequence immediately, set the trigger source to IMMediate, and then use the INITiate

command.


TRIGger:PROGram:SOURce IMMediate Sets the trigger source to IMMediate


INITiate:PROGram Starts or initiates the sequence


To use software triggers to start the sequence, change the trigger source to BUS.


TRIGger:PROGram:SOURce BUS Sets the trigger source to BUS


INITiate:PROGram Starts or initiates the sequence


TRIGger:PROGram Applies a software trigger to start the sequence


INITiate:PROGram pulls the TRIGger subsystem out of the IDLE state and starts (initiates) the trigger
function. When set to BUS, the trigger subsystem enters the WTG (Waiting For Trigger) state. When a
software trigger is subsequently received (through the TRIGger:PROGram command or *TRG
command), the sequence starts.


When the trigger acquire source is set to IMMediate instead of BUS, the sequence starts immediately

after INITiate:PROGram.


When the sequence finishes, the TRIGger subsystem enters the IDLE state again. If the ABORt
command or an equivalent command is received in the WTG state or when a sequence is being
executed, the sequence is canceled, and the TRIGger subsystem returns to the IDLE state.


You can also use the *TRG command or the IEEE488.1 get (Group Execute Trigger) command to apply
a software trigger to all trigger subsystems. If there are other trigger subsystems in the initiated state,
their trigger operations will also be executed at the same time.


When all the PROGram processes are complete, the product’s settings are those of the last step. Note
that if the output is on in the last step of the sequence, the output will remain on when the sequence is
completed.


Keysight AC6900 Operating and Programming Guide 265


4 SCPI Programming

#### **Status System Overview**


**Status Registers**


**Operation Status Group**


**Questionable Status Group**


**Standard Event Status Group**


**Status Byte Register**


**Error and Output Queues**


**Status Diagrams**


This section provides a detailed description of the individual registers and register groups. The status
diagrams provide an graphical view of how the status registers and groups are interconnected.


**Status Registers**


The Operation and Questionable status groups use four different types of registers to track qualify,
flag, and enable instrument events. The Standard Event group only uses Event and Enable registers.


The instrument uses IEEE 488 and SCPI registers for the status reports. Each SCPI status register has
the following subregisters: CONDition register, EVENt register, and ENABle register.


The registers also include NTRansition and PTRansition filters.


**Item** **Description**


CONDition register A Condition register continuously monitors the instrument state and its bits are updated in real time. The
bits are not latched, and reading this register does not affect the contents.


EVENt register An Event register latches transitions that pass through the positive and negative transition registers. When
an event bit is set, it remains set until the Event register is read. Reading the Event register clears it. The
EVENt register bits are automatically set according to the changes in the CONDition register and is reset
when read. The rule varies depending on the positive and negative transition filters (PTRansition and
NTRansition).


ENABle register An Enable register defines which bits in the event register are reported to the Status Byte register. You can
write to or read from an enable register.


Transition filter The transition filters enable or disable the reporting of events when the condition changes from false to true
(PTRansition - positive) or from true to false (NTRansition - negative). Use the PTRansition (positive
transition) filter to report events when the condition changes from false to true. Use the NTRansition
(negative transition) filter to report events when the condition changes from true to false.


If both filters are enabled, events will be reported each time the status changes; if both filters are cleared,
event reporting is disabled.


266 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Operation Status Group**


These registers record signals that occur during normal operation. The group consists of a Condition,
PTR/NTR, Event, and Enable register. The outputs of the Operation Status register group are logically
OR-ed into the OPERation summary bit (7) of the Status Byte register. See Status Registers for a
description of each register.


**Bit** **Value** **Bit Name** **Description**


0-3 1 to 8 (not used) (not used)


4 16 ACQ busy A measurement is in progress.


5 32 ACQ wtg The instrument is waiting for a measurement trigger.


6 64 TRAN busy The instrument is running a sequence, a simulation, or changing settings.


7 128 TRAN wtg The instrument is waiting for a trigger to run a sequence, a simulation,
or change settings.


8 256 CV The instrument is in constant voltage (CV) output mode.


9 512 SST Indicates whether a soft start is being performed.


10 1024 SYNC Indicates whether the synchronization function is in operation.


11-12 2048 to 4096 (not used) (not used)


13 8,192 INSTrument Isummary Summary bit of the OPERation:INSTrument subregister.


14 16,384 (not used) (not used)


15 32,768 (not used) 0 is returned


**Questionable Status Group**


These registers record signals that indicate abnormal operation. The bits in this register may indicate
problems with acquired data. The group consists of a Condition, PTR/NTR, Event, and Enable register.
The outputs of the Questionable Status group are logically OR-ed into the QUEStionable summary bit
(3) of the Status Byte register. See Status Registers for a description of each register.


**Bit** **Value** **Bit Name** **Description**


0 1 OV Output is disabled by the overvoltage protection


1 2 OC Output is disabled by the overcurrent protection


2 4 HWF Output is disabled by hardware failure


3 8 LV Low voltage detected


4 16 OT Output is disabled by the overtemperature protection


5 32 WDOG Watchdog protection (caused by no SCPI IO activity over specified time)


6 64 OC2 Overcurrent internal semiconductor protection activated


7 128 UNSYNC Frequency synchronization error occurred


8 256 OP Overpower protection activated


9 512 SD Forced output shutdown


Keysight AC6900 Operating and Programming Guide 267


4 SCPI Programming


**Bit** **Value** **Bit Name** **Description**


10 1024 CL-PEAK Current Limit on PEAK (overload state)


11 2048 PL Power limit (overload state)


12 4096 CL-RMS Current Limit on RMS (overload state)


13 8192 INSTrument Summary Summary bit of the QUEStionable:INSTrument subregister


14 16,384 (not used) (not used)


15 32,768 (not used) 0 is returned


**Standard Event Status Group**


These registers are programmed by Common commands. The group consists of an Event and Enable
register. The Standard Event event register latches events relating to communication status. It is a
read-only register that is cleared when read. The Standard Event enable register functions similarly to
the enable registers of the Operation and Questionable status groups. See Status Registers for a
description of each register.


The event status register bits are set when certain events occur during instrument operation.


The register is controlled by the IEEE 488 commands *ESE, *ESE?, and *ESR?.


**Bit Value Bit Name** **Description**



0 1 Operation Complete
(OPC)



All commands before and including *OPC have completed.



1 2 (not used) 0 is returned


2 4 Query Error (QYE) The instrument tried to read an empty output buffer, a command was received before a
previous query was read, or the input and output buffers are full.



3 8 Device Dependent
Error (DDE)



A device-specific error occurred. Error Messages



4 16 Execution Error (EXE) An execution error occurred. Error Messages. A valid SCPI command may not be executed

correctly depending on the instrument's conditions.



5 32 Command Error

(CME)



A command syntax error occurred. Error Messages



6 64 Reserved 0 is returned


7 128 Power ON (PON) Power has been cycled since the last time the event register was read or cleared.


268 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Status Byte Register**


This register summarizes the information from all other status groups and stores STB and RQS
messages as defined by the IEEE 488 standard. The *STB? query reads the status byte register and
transmits the contents of the status byte register and the master status summary (MSS) message. The
*STB? query does not change the status byte, MSS, or RQS.


**Bit Value Bit Name** **Description**


0, 1 Reserved 0 is returned



2 4 Error/Event Queue
(EEQ)


3 8 Questionable Status
Summary (QUES)


4 16 Message Available
(MAV)


5 32 Event Status Sum
mary (ESB)


6 64 Request Service
(RQS/MSS)


7 128 Operation Status Summary (OPER)



One or more errors are in the error queue. Use SYSTem:ERRor? to read and delete errors.


One or more bits are set in the Questionable Data Register and the corresponding
QUEStionable status enable register bit is true. See STATus:QUEStionable:ENABle.


Data is available in the instrument's output buffer.


One or more bits are set in the Standard Event Status Register. Bits must be enabled, see

*ESE.


One or more bits are set in the Status Byte Register and may generate a Request for Service
or the Master Summary Status has one or more event bits. Bits must be enabled, see *SRE.


An event in the Operation Status register has been generated. Bits must be enabled, see

STATus:OPERation:ENABle.



**Master Status Summary and Request for Service Bits**


MSS is a real-time (unlatched) summary of all Status Byte register bits that are enabled by the Service
Request Enable register. MSS is set when the instrument has one or more reasons for requesting
service. *STB? reads the MSS in bit position 6 of the response but does not clear any bits in the Status
Byte register.


The RQS bit is a latched version of the MSS bit. Whenever the instrument requests service, it sets the
SRQ interrupt line true and latches RQS into bit 6 of the Status Byte register. When the controller does
a serial poll, RQS is cleared inside the register and returned in bit position 6 of the response. Other
Status Byte register bits are not disturbed.


**Error and Output Queues**


The Error Queue is a first-in, first-out (FIFO) data register that stores numerical and textual description
of an error or event. Error messages are stored until they are read with SYSTem:ERRor? If the queue
overflows, the last error/event in the queue is replaced with error -350,"Queue overflow".


The Output Queue is a first-in, first-out (FIFO) data register that stores instrument-to-controller
messages until the controller reads them. Whenever the queue holds messages, it sets the MAV bit (4)
of the Status Byte register.


Keysight AC6900 Operating and Programming Guide 269


4 SCPI Programming


**OPERation:INSTrument Subregister Group**


This is the subregister (16 bits) of bit 13 of the OPERation status register.


0 1 Instrument 1 Phase 1 (OPER:INST:ISUM1) summary bit


1 2 Instrument 2 Phase 2 (OPER:INST:ISUM2) summary bit


2 4 Instrument 3 Phase 3 (OPER:INST:ISUM3) summary bit


3 - 15 8 to 32,768 (not used) (not used)


**OPERation:INSTrument:ISUMmary[1|2|3] Subregister Group**


This is the subregister of bits 1 to 3 of the OPERation:INSTrument subregister. This is a 16-bit register
that contains information about the normal operating conditions of the product for each phase.


Of the parameters {1|2|3}, 1 represents Phase 1, 2 represents Phase 2, and 3 represents Phase 3.


**Bit** **Value** **Bit Name** **Description**


0-3 1 to 8 (not used) (not used)


4 16 ACQ busy A measurement is in progress.


5 32 ACQ wtg The instrument is waiting for a measurement trigger.


6 64 TRAN busy The instrument is running a sequence, a simulation, or changing settings.


7 128 TRAN wtg The instrument is waiting for a trigger to run a sequence, a simulation,
or change settings.


8 256 CV The instrument is in constant voltage (CV) output mode.


9 512 SST Indicates whether a soft start is being performed.


10 1024 SYNC Indicates whether the synchronization function is in operation.



11-14 2048 to


16,384



(not used) (not used)



15 32,768 (not used) 0 is returned


**QUEStionable:INSTrument Subregister**


This is the subregister (16 bits) of bit 13 of the QUEStionable status register.


**Bit** **Value** **Bit Name** **Description**


0 1 Instrument 1 Phase 1 (QUES:INST:ISUM1) summary bit


1 2 Instrument 2 Phase 2 (QUES:INST:ISUM2) summary bit


2 4 Instrument 3 Phase 3 (QUES:INST:ISUM3) summary bit



3 

15



8 to

32,768



(not used) (not used)



270 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**QUEStionable:INSTrument:ISUMmary[1|2|3] Subregister Group**


This is the subregister of bits 1 to 3 of the QUEStionable:INSTrument subregister. This is a 16-bit
register that contains information about the productʼs questionable events and status that occur
during operation for each phase.


Of the parameters {1|2|3}, 1 represents Phase 1, 2 represents Phase 2, and 3 represents Phase 3.


The QUEStionable status register bits may indicate that there are problems with the productʼs

measured data.


**Bit** **Value** **Bit Name Description**


0 1 OV Output is disabled by the overvoltage protection


1 2 OC Output is disabled by the overcurrent protection


2 4 HWF Output is disabled by hardware failure


3 8 LV Low voltage detected


4 16 OT Output is disabled by the overtemperature protection


5 32 WDOG Watchdog protection (caused by no SCPI IO activity over specified time)


6 64 OC2 Overcurrent internal semiconductor protection activated


7 128 UNSYNC Frequency synchronization error occurred


8 256 OP Overpower protection activated


9 512 SD Forced output shutdown


10 1024 CL-PEAK Current Limit on PEAK (overload state)


11 2048 PL Power limit (overload state)


12 4096 CL-RMS Current Limit on RMS (overload state)


13 - 14 8192 to 16,384 (not used) (not used)


15 32,768 (not used) 0 is returned


Keysight AC6900 Operating and Programming Guide 271


4 SCPI Programming


**Status Diagrams**


**Status for Single-Phase output**


272 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Status for Three-Phase output and Single-phase three-wire output**


Note that single-phase three-wire only returns results form the Isummary1 and Isummary2 groups.


Keysight AC6900 Operating and Programming Guide 273


4 SCPI Programming

#### **Default Settings**


**Front panel default settings**


**Waveform bank settings**


**Simulation settings**


**Sequence settings**


**SCPI *RST settings**


**Front panel default settings**


Note that as the instrument is used, specific front panel settings can be saved in setup memory or on

an external USB device for recall at a later time.


**Front panel setting** **AC6903** **AC6906** **AC6912** **AC6918**


Frequency 50 Hz


Voltages, AC and DC 0.0 V


Output when the POWER switch is turned on Safe


Output voltage range) Low


Upper AC voltage limit 322.0 V


Lower AC voltage limit 0.0 V


Upper DC voltage limit 455.0 V


Lower DC voltage limit -455.0 V


Upper frequency limit 5000 Hz (550 Hz on the 550 Hz limit model)


Lower frequency limit 1.00 Hz


AC current limit 1P output 33.0 A 66.0 A 132.0 A 198.0 A



1P 3W output,
3P output



11.0 A 22.0 A 44.0 A 66.0 A



Positive current peak limit 1P output 126.0 A 252.0 A 504.0 A 756.0 A



1P 3W output,
3P output



42.0 A 84.0 A 168.0 A 252.0 A



Negative current peak

limit



1P output -126.0 A -252.0 A -504.0 A -756.0 A



1P 3W output,
3P output



-42.0 A -84.0 A -168.0 A -252.0 A



Current limit operation Trip


Current limit trip time 10 s


Output overvoltage protection (OVP) 489.5 V


Positive peak OVP 489.5 V


274 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Front panel setting** **AC6903** **AC6906** **AC6912** **AC6918**


Negative peak OVP -489.5 V


Output undervoltage protection (UVP) 0.0 V


Sync function Off, 0.0 deg, synchronizes to the input power supply


Output-on phase Phase control disabled, 0.0 deg


Output-off phase Phase control disabled, 0.0 deg


1-2 phase difference 120.00°


1-3 phase difference 240.00°


Waveform bank # to execute 0 (sine wave)


Waveform bank # to edit 1


Soft start Off, 0.1 s


Soft stop Off, 0.1 s


Compensation Off, regulation adjustment correction value: 0


Output impedance Off, 0 mΩ, 0 μH


Panel display Phase All (Phase voltage display)


Voltage Rms voltage



Current, power,
power factor



Rms current



Peak voltage hold time 0 s


Peak current hold time 0 s


Measurement average Voltage 1



Current, power,
power factor



1



Sleep function OFF, 60 min


AC coupling Disabled


Harmonic analysis Current, all harmonics


Maximum expected power 3000 VA 6000 VA 12000 VA 18000 VA


Preset memory recall Confirm and recall



Preset memory
(all memories 1 and 2)



AC voltage 0.0 V


DC voltage 0.0 V


Frequency 50.00 Hz



Waveform bank

#



0



Keysight AC6900 Operating and Programming Guide 275


4 SCPI Programming


**Waveform bank settings**


These settings are saved only to an external USB device


**Waveform bank parameter** **Factory Default**


Waveform type Sine


User-defined waveform (set only from remote control) Sine


Crest factor 1.40


Clip factor 1.0


**Simulation settings**


These settings are saved only to an external USB device


**Simulation parameter** **Factory Default**


T1 setting unit Phase


Voltage regulation polarity Positive


T5 setting unit Time


Regulated voltage T3 0


Repetition count 9999 (infinity)


T1 time 0.1 s


T1 angle 0 deg


T2 time 0 s


T3 time 0.1 s


T4 time 0 s


T5 time 0.1 s


T5 return cycles 0


**Sequence settings**


These settings are saved only to an external USB device


**Sequence parameter** **Factory Default**


Starting step number 1


Ending step number 600


Repetition count 1


**Sequence parameter (for all steps)**


Frequency 50.00 Hz, ramp off


Voltages, AC and DC 0.0 V, ramp off


Jump type Next step


276 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Sequence parameter (for all steps)**


Jump destination step 0


Number of jump repetitions 1


Output impedance Off, 0 mΩ, 0 μH


Execution time 10 ms


Waveform bank number 0


Output On


Status output On


Trigger output Off


Trigger input Off


Start phase Free, 0.0 deg


End phase Free, 0.0 deg


Sudden phase change Off


Phase 1 offset Off, 0.00 deg, ramp off


Phase 1-2 difference Off, 120.00 deg, ramp off


Phase 1-3 difference Off, 240.00 deg, ramp off


**SCPI *RST settings**


These are set by an *RST (reset) command or at power-on.


**Command** ***RST**


DISPlay[:WINDow]:METer:COUPling ACDC


DISPlay[:WINDow]:METer:COUPling:CURRent ACDC


DISPlay[:WINDow]:METer:COUPling:VOLTage ACDC


INSTrument[:SELect] OUTPut0


INSTrument:NSELect 0


OUTPut[:STATe] OFF


OUTPut:COUPling ACDC


OUTPut:IMPedance[:STATe] OFF


OUTPut:IMPedance:REACtive 0


OUTPut:IMPedance:REAL 0


OUTPut:PHASe:OFF[:STATe] OFF


OUTPut:PHASe:OFF:LEVel 0


OUTPut:PHASe:ON[:STATe] OFF


OUTPut:PHASe:ON:LEVel 0


OUTPut:PHASe:P1Offset 0


OUTPut:PHASe:P12 120 in 3P; 180 in 1P3W


OUTPut:PHASe:P13 240


Keysight AC6900 Operating and Programming Guide 277


4 SCPI Programming


**Command** ***RST**


OUTPut:PON:SAFE ON


OUTPut:SSTart[:STATe][:RISE] OFF


OUTPut:SSTart[:STATe]:FALL OFF


OUTPut:SSTart:TIME[:RISE] 0.1


OUTPut:SSTart:TIME:FALL 0,1


SENSe:AVERage 1


SENSe:CURRent:AVERage 1


SENSe:CURRent[:PEAK]:HOLD:TIME 1


SENSe:VOLTage:AVERage 1


SENSe:VOLTage[:PEAK]:HOLD:TIME 1


[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] MAX


[SOURce:]CURRent:PEAK:LOWer MAX


[SOURce:]CURRent:PEAK:UPPer MAX


[SOURce:]CURRent:PROTection:STATe ON


[SOURce:]CURRent:PROTection:TRIP:DELay 10




[SOURce:]FREQuency[:CW]

[SOURce:]FREQuency[:IMMediate]



50




[SOURce:]FREQuency:LIMit:LOWer MIN


[SOURce:]FREQuency:LIMit:UPPer MAX


[SOURce:]FREQuency:TRIGgered 50


[SOURce:]FREQuency:SYNCronous[:STATe] OFF


[SOURce:]FREQuency:SYNChronous:MODE LINE


[SOURce:]FREQuency:SYNChronous:PHASe:DELay 0


[SOURce:]FUNCtion[:SHAPe]:BANK[:INDex] 0


[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] 0


[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] 0


[SOURce:]VOLTage[:LEVel]:LIMit:LOWer 0


[SOURce:]VOLTage[:LEVel]:LIMit:UPPer 322


[SOURce:]VOLTage:COMPensate:MODE DIS


[SOURce:]VOLTage:COMPensate:SOFT:CONTrol[:STATus] AC


[SOURce:]VOLTage:COMPensate:SOFT:TERMinal SENSe


[SOURce:]VOLTage:LTLine 0


[SOURce:]VOLTage:OFFSet[:IMMediate] 0


[SOURce:]VOLTage:OFFSet:LIMit:LOWer? -452


[SOURce:]VOLTage:OFFSet:LIMit:UPPer? 452


[SOURce:]VOLTage:OFFSet:TRIGgered 0


[SOURce:]VOLTage:OFFSet:LTLine 0


278 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Command** ***RST**


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:GAIN 100 (all channels)


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:ADJust:OFFSet 0 (all channels)


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:APERture 0.1


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:POLarity NORMal


[SOURce:]VOLTage:PROGramming:EXTernal:EXTDC:SIGNal:SOURce EXTernal


[SOURce:]VOLTage:PROGramming:EXTernal:MODE OFF


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:GAIN 10


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:ADJust:OFFSet 0


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:MAP ALL


[SOURce:]VOLTage:PROGramming:EXTernal:VPRogram:STATe OFF


[SOURce:]VOLTage:PROGramming:SOURce INTernal


[SOURce:]VOLTage:PROTection:LOWer 0


[SOURce:]VOLTage:PROTection:LOWer:STATe OFF


[SOURce:]VOLTage:PROTection:UPPer 500.5


[SOURce:]VOLTage:PROTection:PEAK:LOWer -500.5


[SOURce:]VOLTage:PROTection:PEAK:UPPer 500.5


[SOURce:]VOLTage:RANGe[:UPPer] 160


[SOURce:]VOLTage:RESPonse MEDium


SYSTem:CONFigure:FORMation:PSAVer:MAXimum MAX


WAVE:STATe OFF


Keysight AC6900 Operating and Programming Guide 279


4 SCPI Programming

#### **Measurement Details**


This section summarizes the mathematical processing used to extract desired measurement values
from arrays of digitized voltage and current data. I


Required mathematical processing is similar for voltage and current. All raw measurements, i.e.
individual A/D conversions and associated array elements, are understood to be DC coupled.


Additional measurement considerations must include the effects of windowing (normally used to
ensure stable readings), and any adjustments that must be made to correct for window gain (always
less than unity when compared to a rectangular window).


General definitions are as follows:


Array Length: N


Array Index Variable: n = 0 … N-1


Voltage Array: Volt[N]


Current Array Current[N]


**Measurement** **Equations**


AC voltage


DC voltage


RMS voltage


AC apparent power


AC+DC apparent power


AC reactive power


AC+DC reactive power


AC current


280 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


**Measurement** **Equations**


DC current


RMS current


Maximum current amplitude


AC Power factor


AC+DC Power factor


AC active power (Watts)


DC average power (Watts)


AC+DC Watts


Current crest factor


Keysight AC6900 Operating and Programming Guide 281


4 SCPI Programming

#### **Error and Limit Checking**


**Basic Principles**


**Range Error Checking**


**Soft Limits Checking**


**Settings Error Checking**


**Output Coupling Error Checking**


**Coupled Parameter Error Checking**


**Basic Principles**


The AC6900 Series instruments include error checking measurements and soft limits that prevent
unintended operations from being carried.


Settings are not automatically changed by either front-panel or SCPI commands, unless the changes
are obvious and expected.


Improper settings or combination of settings are not allowed for active settings.


User actions which would result in improper active settings generate error messages.


Inactive settings are not checked for errors in response to range changes. If these settings will become
active due to output mode changes, such as switching coupling from AC to DC, or due to the enabling
of soft limits, error checks are performed before making the settings active.


Error checks should be performed in response to the following events:


l Changing Settings invokes Settings Error Checking


l Enabling soft limits invokes Settings Error Checking


l Changing ranges from high to low invokes Range Error Checking


l Changing output coupling invokes Output Coupling Error Checking


l Changing to Step mode invokes Settings Error Check


**Range Error Checking**


The instrument uses the following method to check for range errors.


1. Active settings are limited by MINimum and MAXimum values. The active settings considered are

only those relevant to the output coupling. In AC+DC coupling, both DC and AC voltage settings are
active, so range checking is applied to the peak absolute value of the combined waveforms.


282 Keysight AC6900 Operating and Programming Guide


4 SCPI Programming


2. In addition to the immediate DC and AC voltage, in Step mode the triggered voltage setting

(VOLT:OFFS:TRIG or VOLT:TRIG, depending on the output coupling) is also checked to ensure that an
accepted trigger does not cause an invalid state.


3. Frequency settings are independent of range, output on/off status, and output coupling.


4. Output coupling and output range may only be changed when the output is off. Therefore, the instru
ment always checks for inconsistent settings when you try to change the output coupling or range
before turning the output on.


5. If output coupling settings are changed, the Range Error Checking and Soft Limits Checking are both

made for any parameter that becomes active as a result of the output coupling change.


6. Settings that are valid on the low output range are always valid on the high output range, but the

Range Error Checking and Soft Limits Checking are both performed whenever the instrument is asked
to change ranges in either direction.


**Soft Limits Checking**


1. The soft limits are checked only if soft limits are enabled at the time the soft limits are set or at the


time when the soft limits are enabled.


2. Soft limits can be configured for DC voltage, AC voltage, and Frequency. The soft limit check is inde
pendently performed for the AC and DC components in AC+DC coupling.


3. Soft limits also apply to the corresponding trigger values of DC voltage, AC voltage, and Frequency

but only in Step mode.


**Settings Error Checking**


These settings verify that the voltage setting is correct.


In AC and DC output modes, the setting must be between the range's MIN and MAX values.


In AC+DC mode, an additional check verifies that the peak voltage does not exceed 227.5 V (low range)
or 455 V (high range).


If soft limits are enabled, the instrument checks that the setting is within the low and high soft limits.


Settings errors are checked when the user changes the mode setting to Step mode.


**Output Coupling Error Checking**


Changing output coupling from AC+DC requires no error check. Otherwise, changing to DC coupling
runs the Volts DC settings error check, and changing to AC or AC+DC runs the Volts AC settings error

check.


Keysight AC6900 Operating and Programming Guide 283


4 SCPI Programming


**Coupled Parameter Error Checking**


Inactive parameters generally are not checked for being within range or compliant with soft limits even
if soft limits are active for the parameter type. This minimizes nuisance error messages when setting
values for inactive parameters. Instead, checks are performed when the parameter is made active.


However, parameter entries are always checked for being within the MIN and MAX values even if the
parameter is inactive. For example, VOLT:TRIG must be from 0 to 320, even if VOLT:MODE is FIXed.


If coupled parameter settings change, the instrument check on each active parameter setting.


For AC+DC mode, the peak value of the waveform cannot be outside the -455 V to 455 V range for H
range and -227.5 V to 227.5 V range for L range.


For AC mode, the voltage setting must be from 0 to the full scale value of the range, and for DC mode,
the value must be between the positive and negative full scale value of the range.


Checks are performed in the following order:


1. The Coupled Parameters Error Checking is performed first.


2. Depending on the pending front-panel entry or remote command and the instrument’s operating

status, the Coupled Parameters Error Checking may lead to the other error checks.


3. In some cases, multiple passes through the Settings Error Check are required. For example, if

OUTPut:COUPLing is changing from AC to ACDC and VOLT:OFFSet:MODE is STEP, then both the
IMMediate and TRIGgered values for DC volts are checked for being within range and for meeting the
peak voltage constraint for the currently selected range. These checks occur before the
OUTPut:COUPLing change is accepted.


284 Keysight AC6900 Operating and Programming Guide


Keysight AC6900 Operating and Programming Guide

# 5 Maintenance


**Servicing the Instrument**


**Cleaning the Instrument**


**Removing and Installing an SD Card**


**Error Messages**


**Hardware Error Codes**


5 Maintenance

#### **Servicing the Instrument**


**Types of Service Available**


If your instrument fails during the warranty period, Keysight Technologies will repair or replace it under
the terms of your warranty. After your warranty expires, Keysight offers repair services at competitive
prices. You may also purchase a service contract that extends coverage after the standard warranty.


To obtain service for your instrument, contact your nearest Keysight Service Center. Refer to
[www.keysight.com/find/assist. They will arrange to have your unit repaired or replaced, and can](http://www.keysight.com/find/assist)
provide warranty or repair–cost information where applicable. Ask the Keysight Technologies Service
Center for shipping instructions, including what components to ship. Keysight recommends that you
retain the original shipping carton for return shipments.


**Calibration**


The instrument was calibrated before shipment. To maintain long-term performance, Keysight
recommends periodic calibration. To have your instrument calibrated, contact your nearest Keysight

Service Center.


**Self-test Verification**


The instrument performs an automatic self-test at power-on. This test verifies the basic functionality of
the instrument's logic and power mesh subsystems without placing any voltages on the output.


The *TST? query returns the result of that test, but it does not run another self-test. If self-test fails,
turn the instrument off, remove all connections (front and rear), and turn the instrument back on. The

purpose of this is to eliminate signals present on external wiring that can act as antennae.


See SCPI Error Messages for a list of error messages.


**Before Returning the Unit**


If the unit is inoperative, verify that the AC power is securely connected to the instrument, the mains
outlet is live, and the power switch is on. Before returning the unit, cycle power on the unit to run the
self-test as described above. Press [Error] to view errors in the error queue. Then report those errors to
Keysight Support and be sure that the instrument was calibrated within the last year.


**Repackaging for Shipment**


To ship the unit to Keysight for service or repair:


Attach a tag to the unit identifying the owner, model number, serial number, and required service.


Place the unit in its original container with appropriate packaging material. Secure the container with
strong tape or metal bands.


286 Keysight AC6900 Operating and Programming Guide


5 Maintenance


When returning the instrument in it original container, remember to install the angle
brackets that secured the instrument to its shipping pallet. Otherwise the instrument will
not be secure in the shipping container and may be damaged.


Chassis bolts: M10X20


Torque: 22.46 Nm


Pallet bolts: M10X30


Torque: 11.18 Nm


AC6906 AC6918

AC6912


If the original shipping container is unavailable, order one of the following shipping kits to return your
instrument. Using these shipping kits will help ensure that the instrument be returned without shipping
damage.


AC6903H/L AC6903-80021 Packaging Kit for AC6903H/L


AC6906H/L AC6906-80021 Packaging Kit for AC6906H/L


AC6912H/L AC6912-80021 Packaging Kit for AC6912H/L


AC6918H/L AC6918-80021 Packaging Kit for AC6918H/L


Keysight recommends that you always insure shipments.


**User-Replaceable Parts**











Front panel louver (qty 1) AC6903-40004 AC6900-40002


Dust filter (qty 1) AC6903-40005 AC6900-80004


Rotary knob (qty 1) W1312-40180 W1312-40180


1 Rear metal cover kit w/screws N/A AC6900-50001 AC6918-50001


2 Output terminal block cover w/screws AC6903-40001 AC6900-40004 AC6918-40002


3 AC input terminal block cover w/screws AC6903-40003 AC6900-40003 AC6918-40001


4 Sensing terminal block cover w/screws AC6903-40002 AC6900-40005 N/A


5 Output under terminal cover w/screws N/A N/A AC6918-40006


6 Input under terminal cover w/screws (for 200 VAC) N/A N/A AC6918-40004


7 Input under terminal cover w/screws (for 400 VAC) N/A N/A AC6918-40005


8 Output terminal cover for Neutral w/screws N/A N/A AC6918-40003


Keysight AC6900 Operating and Programming Guide 287


5 Maintenance











D-Subminiature 25-pin plug (qty 1) AC6900-30001 AC6900-30001


Cap bolt plug (qty 8) N/A AC6900-40001


Cable tie binder (qty 4) N/A AC6900-80001


Foot stand (qty 1) AC6903-40006 N/A


**AC6903H/L**


**AC6906H/L and AC6912H/L**


**AC6918H/L**


288 Keysight AC6900 Operating and Programming Guide


5 Maintenance

#### **Cleaning the Instrument**


SHOCK HAZARD Always turn off the circuit breaker at the switchboard before cleaning.


**Cleaning the Outside**


Clean the outside of the instrument with a soft, lint-free cloth, slightly damp with water. Do not use
detergent. Do not disassemble the instrument. Do not clean the rear panel to avoid getting moisture
near the connections. Ensure that the instrument is completely dry before turning it on.


**Cleaning the Filter**


The following procedure illustrates the removal of one front panel louver.


Remove the louver


1. For units with multiple louvers, start by
removing the lowest louver and work your

way up.


2. Start by pinching the center projection
part of the lover and pulling the louver

forward.


This removes the lock pin from the hole

located in the center of the louver.


3. Next, pinch the left and right projection
parts of the lover and pull the louver

forward.


This removes the lock pins from the holes
located at the left and right sides of the unit.


4. Slide the louver downward, and remove

the louver.


This removes the hooks from the back of the

louver.


Keysight AC6900 Operating and Programming Guide 289


5 Maintenance


Clean the filter


1. Remove the filter from the louver.


2. Vacuum the dust and particles that are attached to the filter. If the filter is extremely dirty, clean it
using water-diluted neutral detergent. Ensure that the filter is completely dry before installing it.


3. When installing, note that the side that has sections cut out is the bottom side. Firmly attach the
dust filter so that the louver's tabs protrude from the top of the dust filter.


Install the louver


When installing multiple louvers, start by installing the top louver and work your way down.


1. Start by placing the louver under the
display and sliding it up.


Place the hook on the back of the louver

into the hole on the unit.


2. Slide the right side of the louver up under
the front panel.


Place the hooks on the top of the louver into

the holes on the unit.


3. Press the lock pins on both sides of
louver into the holes on the power unit.


4. Press the lock pin in the center of the

louver into the center hole on the unit.


Ensure that all hooks have also been

inserted into their corresponding holes.


5. Check that the louver is flush on the unit. There should be no gaps or bulges on the louvers.


290 Keysight AC6900 Operating and Programming Guide


5 Maintenance

#### **Removing and Installing an SD Card**


AC6900-series units with Option MEM write sensitive user-data to a micro-SD card. This is in contrast
to standard units, which write user-data to internal NAND flash memory. Having an SD card accessible
on the side or top of the instrument allows for easy removal and sanitization.


The following information applies to units with Option MEM installed:


l Option MEM units require the use of Keysight micro-SD cards, part number AC6900-85000. Other SD


cards cannot be used.


l There is no difference in functionality of Option MEM units and standard units.


l Instruments with Option MEM will not turn on if the original SD card is not installed in the unit.


l When an SD card is re-installed in the unit, the unit will turn on according to the start-up information


last saved on the SD card.


l You cannot move or save the data on the SD card to any other memory device.


l Instrument firmware can be updated using the front-panel USB port or through the communication

interface. However, the firmware for a standard unit cannot be installed on an Option MEM unit,
neither can firmware for an Option MEM unit be installed on a standard unit.


Always turn the power switch off before removing or installing the SD card.


**Procedure**


1. Remove the two cover screws along with the cover to access the SD card.


AC6903 AC6906, AC6912, AC6918


2. Remove the SD card by gently pushing on the card edge until the card is released from its housing
(shown below). Then remove the card.


3. Install the SD card by first orienting it as shown in the illustration. Then gently push the card into its
housing until it clicks into place.


Keysight AC6900 Operating and Programming Guide 291


5 Maintenance


AC6903 AC6906, AC6912, AC6918


4. Replace the cover.


Note:


Kindly sanitize SD card after installing.


The sanitization process for the SD card is the same as described in page 133.


The sanitization of the SD card is required when;


l installing the SD Card for the first time.


l installing the SD card in a different unit, regardless the unit has the same capacity or not.


292 Keysight AC6900 Operating and Programming Guide


5 Maintenance

#### **Error Messages**


**Command Errors**


**Execution Errors**


**Product Specific Errors**


**Query Errors**


**Operation Complete Errors**


**Product Dependent - configuration change**


**Product Dependent - configuration conflict**


**Product Dependent - invalid operations**


**Security errors**


**Self-test errors**


**Internal errors**


**Introduction**


The error messages below are in the following format, with a numeric error code and a quoted error
string separated by a comma:


-222,"Data out of range"


The error/event queue is empty and returns 0,"No error" when the *CLS common command is sent, the
last item in the error queue is read, or the instrument cycles power.







-100 to -199 Command errors An IEEE 488 syntax error has been detected by the instrument's parser.


-200 to -299 Execution errors An error has been detected by the instrument's execution control block.


-300 to -399 Device-specific errors A device-specific error has been detected, possibly related to a hardware failure.


-400 to -499 Query errors The instrument's output queue control has detected a problem with the message
exchange protocol.


-800 to -899 Operation errors Errors related to instrument operation such as when the product wants to report an
IEEE 488.2 operation complete event.


100 to 399 Product dependent errors Errors in this class causes the Device Dependent Error bit (bit 3) in the event status
register to be set.


500 to 599 Security errors Errors related fo password useage.


900 and above Self-test errors Self-test errors have occurred.


Keysight AC6900 Operating and Programming Guide 293


5 Maintenance


**Command Errors**


**Code** **Error Text** **Description**


-100 Command error Generic syntax error


-101 Invalid character An invalid character exists. A data element different than those allowed was recognized.


-102 Syntax error Syntax error. An unrecognized command or data type was encountered.


-103 Invalid separator Invalid separator The parser was expecting a separator and encountered an illegal char
acter.


-104 Data type error. Data type error. The parser recognized a data element differ- ent than one allowed.


-105 Get not allowed. Get not allowed. A Group Execute Trigger was received in a program message.


-108 Parameter not allowed Parameter not allowed More parameters were received than expected for the header.


-109 Missing parameter Missing parameter Fewer parameters were received than required for the header.


-110 Command header error Command header error. An error was detected in the header.


-112 Program mnemonic too long Mnemonic too long. The number of characters in the com- mand header exceeds 12 char
acters.


-113 Undefined header Undefined header. Inappropriate for the product.


-114 Header suffix out of range Invalid suffix exists in the header.



-115 Unexpected number of para
meters



Unexpected parameters were received in the header.



-120 Numeric data error Numeric data error. Generated when parsing a data element which appears to be
numeric, including the nondecimal nu- meric types.


-128 Numeric data not allowed Numeric data is not allowed.


-130 Suffix error Suffix error. Generated when parsing a suffix.


-131 Invalid suffix A suffix is invalid. The suffix does not follow the syntax, or the suffix is inappropriate for
the product.


-138 Suffix not allowed Suffix too long. The suffix contains too many characters. A suffix was encountered after a
numeric parameter that does not allow suffixes.


-140 Character data error Character data error. Generated when parsing a character data element.


-141 Invalid character data Either the character data element contains an invalid character, or the element is not

valid.


-144 Character data too long Character data too long. The character data element contains too many characters.


-150 String data error Character data is not allowed.


-151 Invalid string data String data error. Generated when parsing a string data element.


-158 String data not allowed Invalid string data. String data is not allowed.


-160 Block data error Block data error. Generated when parsing a block data element.


-170 Expression error Expression error. Generated when parsing an expression data element.


-180 Macro error Generated when defining a macro or executing a macro.


294 Keysight AC6900 Operating and Programming Guide


5 Maintenance


**Execution Errors**


**Code** **Error Text** **Description**


-200 Execution error Generic execution error


-203 Command protected Password protected program or query command cannot be executed.


-210 TRigger error Trigger error.


-211 Trigger ignored A trigger was received but ignored.


-213 Init ignored A measurement initiate operation was ignored because measurement is in progress.


-214 Trigger deadlock A deadlock occurred because a query was received before the software trigger.


-220 Parameter error Invalid parameter.


-221 Settings conflict A command was received that the product cannot execute in its present condition.


-222 Data out of range Parameter was out of range.


-223 Too much data Too many parameters were received for the requirements.


-224 Illegal parameter value Received invalid parameter data.


-230 Data corrupt or stale Received a data query before the measurement completed.


-241 Hardware missing Cannot be executed because the optional hardware is not installed.


**Product Specific Errors**


**Code** **Error Text** **Description**


-310 System Error System error


-311 Memory Error Memory error. Physical damage to the device memory.


-313 Calibration memory lost Calibration memory lost.* Damage to nonvolatile calibration data by CAL?.


-314 Save/recall memory lost Memory data lost.* Damage to nonvolatile data by SAV?.


-315 Configuration memory lost Configuration data lost. Damage to nonvolatile panel settings.


-330 Self-test error Self-test failed.


-350 Queue overflow Queue overflow.


-363 Communication error Communication error that occurs when flow control is off. This is an error when using

RS232C.



-362 Framing error in programming

message.



Framing error. This is an error when using RS232C.



-363 Buffer overflow overrun Buffer overflow error. This is an error when using RS232C.


-365 Time out error Time out error. This is an error when using RS232C.


Keysight AC6900 Operating and Programming Guide 295


5 Maintenance


**Query Errors**


**Code** **Error Text** **Description**


-400 Query error Generic query error


-410 Query INTERRUPTED Received a new command after the query was received and before the response was

read.


-420 Query UNTERMINATED The controller attempted to read the response after the device received an unsupported
query or did not received a query. The “-100 COMMAND ERROR” and this error are
stored in the error queue. The controller will time out.


-430 Query DEADLOCKED The error queue, input buffer, and output buffer are full when sending large binary data as
a response, and the transmis- sion timing is off.



+440 Query UNTERMINATED after
indefinite response


**Operation Complete Errors**



Received a separate query in semicolon-delimited format after a query that returns a
response in an indefinite form. (Example: A compound command such as *IDN?;SYST:ERR?)



**Code** **Error Text** **Description**


-800 Operation complete All selected pending operations in accordance with the IEEE 488.2, 12.5.2 synchronization protocol have completed.


**Product Dependent - configuration change**


**Code** **Error Text**


+100 Setting change denied while OUTPut ON state


+101 Setting change denied while TRANsient trigger in progress


+102 Setting change denied while SIMulation trigger in progress


+103 Setting change denied while PROGram trigger in progress


+104 Setting change denied while frequency synchronous.


+105 Setting change denied while BUSY


+106 Setting change denied while SIMulation in progress


+107 Setting change denied while PROGram in progress


+108 Setting change denied while SLEep mode


+109 Setting change denied while EXT PROGram SOURce selected


+110 Setting change denied while INT+EXT PROGram SOURce selected


+111 Setting change denied while V PROGram SOURce selected


+112 Setting change denied while SSTart in progress


+113 Setting change denied while SSTop in progress


296 Keysight AC6900 Operating and Programming Guide


5 Maintenance


**Product Dependent - configuration conflict**


**Code** **Error Text**


+201 Conflicts with OUTPut OFF state


+202 Conflicts with PROTection state


+203 Conflicts with WIRing configuration


+204 Conflicts with BUSY state.


+205 Conflicts with CURRent PROTection LIMit selected


+206 Conflicts with CURRent PROTection TRIP selected


+207 Conflicts with SSTart function enabled


+208 Conflicts with Remote Inhibit operation


+209 Conflicts with EXTernal PROGram SOURce selected


+210 Conflicts with INTernal PROGram SOURce selected


+211 Conflicts with unbalanced PHASe configuration


+212 Conflicts with unbalanced VOLTage configuration


+213 Conflicts with TRANsient trigger in progress


+214 Conflicts with SIMulation trigger in progress


+215 Conflicts with PROGram trigger in progress


+216 Conflicts with external digital input.


+217 Conflicts with OUTPut IMPedance REAL


+218 Conflicts with OUTPut IMPedance REACtive


+219 Conflicts with SSTop function enabled


+220 Conflicts with V PROGram SOURce selected


+225 Conflicts with Out of RANGe(VOLT)


+226 Conflicts with Out of RANGe(FREQ)


+227 Conflicts with Out of RANGe(IMP)


+228 Conflicts with Out of RANGe(TIME)


+230 Conflicts with existing AC VOLTage (IMMediate)


+231 Conflicts with existing AC VOLTage (TRIGgered)


+232 Conflicts with existing DC VOLTage (IMMediate)


+233 Conflicts with existing DC VOLTage (TRIGgered)


+234 Conflicts with existing AC+DC overlaid VOLTage (IMMediate)


+235 Conflicts with existing AC+DC overlaid VOLTage (TRIGgered)


+236 Overlaying too much AC+DC voltage


+237 Conflicts with non-zero DC VOLTage


+238 Conflicts with high-ranged T3 VOLTage


+239 Conflicts with existing FREQuency (IMMediate)


Keysight AC6900 Operating and Programming Guide 297


5 Maintenance


**Code** **Error Text**


+240 Conflicts with existing FREQuency (IMMediate)


+241 Conflicts with soft VOLTage LIMit settings


+242 Conflicts with soft VOLTage OFFSet LIMit settings


+243 Conflicts with soft FREQuency LIMit settings


+244 Conflicts with TRIP in DISabled state


+245 Conflicts with non-zero AC VOLTage


+250 Conflicts with VOLTage COMPensate not in DISabled state


+251 Conflicts with HARD voltage compensation


+252 Conflicts with SOFT voltage compensation


+253 Conflicts with REG-ADJ voltage compensation


+254 Conflicts with CV RESPonse MEDium or FAST


+255 Conflicts with FREQuency SYNChronize function enabled


+256 Conflicts with OUTPut IMPedance function enabled


+257 Conflicts with non-zero waveform BANK active


+258 Conflicts with PHASe ON


+259 Conflicts with PHASe OFF


+260 Conflicts with PHAS.CHG in PROGram


**Product Dependent - invalid operations**


**Code** **Error Text**


+300 Invalid phase number


+301 Invalid WAVE BANK name


+302 Name already used by other BANK


+303 Block data is too long


+304 Block data is too short.


+306 Channel list is forbidden


+311 Illegal PROGram name


+312 PROGram nane already exists


+313 PROGram not found


+314 PROGram not selected


+315 PROGram not running


+316 Cannot delete selected PROGram


+317 Invalid STEP index


+318 Power saver unsupported


298 Keysight AC6900 Operating and Programming Guide


5 Maintenance


**Security errors**


**Code** **Error Text**


+501 Wrong password


+502 Illegal password format


**Self-test errors**


**Code** **Error Text**


+901 Detected empty power module slot


+902 Detected mulfunctioning power module


+903 Invalid primary/sedondary configuration


**Internal errors**


The +1000 error is an internal error of the product.


Keysight AC6900 Operating and Programming Guide 299


5 Maintenance

#### **Hardware Error Codes**


Hardware error codes appear on the front panel display when the instrument is being operated.


**Protection errors**


**Protection code** **Description** **Symptom and remedy**



PROT-00-00

PROT-00-01

PROT-00-02



RMS OVP The output overvoltage protection has been activated, 00=Phase 1, 01=Phase 2,
02=phase 3.


This occurs when the measured rms voltage exceeds the OVP setting for
approximately 1 second.



PROT-01-00 P.U OVP (PFC) An internal overvoltage protection has been activated. A voltage greater than or
equal to the set voltage has been generated from the output side of the unit, or
the output wires may have shorted during single-phase three-wire output or
three-phase output. Alternatively, the unit may be malfunctioning. If the PROT
occurs even after the problem appears to be resolved, stop using the unit immediately, and contact your nearest Keysight Service Center to request repairs.


PROT-02-00 P.U OHP The internal overheat protection has been activated. Leave the unit on, and wait
for about 10 minutes. If the protection has cleared after 10 minutes, the unit
may have been installed incorrectly, or the dust filter may be clogged. If there
are no problems with the installation or the dust filter, contact your nearest Keysight Service Center to request repairs.


PROT-02-00 OPP The power limit protection has been activated.



PROT-02-13

PROT-02-14

PROT-02-15



UNBALANCE POWER The output power of the internal inverter is biased, 13=Phase 1, 14=Phase 2,
15=phase 3.


When using single-phase output or parallel operation, check the wiring. If there
are no problems, contact your nearest Keysight Service Center to request
repairs.



PROT-03-00 OCP The current limit protection has been activated.



PROT-03-3

PROT-03-4

PROT-03-5



UNBALANCE CURR The output current of the internal inverter is biased, 3=Phase 1, 4=Phase 2,
5=phase 3.


When using single-phase output or parallel operation, check the wiring. If there
are no problems, contact your nearest Keysight Service Center to request
repairs.



PROT-04-00 P.U FAN ERR A power module fan has failed.


300 Keysight AC6900 Operating and Programming Guide


5 Maintenance



**Protection code** **Description** **Symptom and remedy**



PROT-06-00

PROT-06-01

PROT-06-02



OVERLOAD The overload protection function has been activated, 00=Phase 1, 01=Phase 2,
02=phase 3.


The output current has exceeded the rated value or the current limit. Fix the
problem that caused the overload to occur and then press OUTPUT again. The
overload should be cleared, and the output should turn on.



PROT-06-05 OVERLOAD L.3 The overload protection function has been activated due to an output short.
Remove the shorting bar for three-phase output operation.



PROT-07-00

PROT-07-01

PROT-07-02



UVP The output undervoltage protection has been activated, 00=Phase 1, 01=Phase
2, 02=phase 3.


This occurs when the measured rms voltage is lower than the UVP setting for
approximately 1 second.



PROT-08-00 P.U FAULT (ALL) All the power modules are paused.


PROT-08-01 P.U FAULT Power module malfunction. At least one of the power modules has malfunctioned. Contact your nearest Keysight Service Center to request repairs.


PROT-10-00 WDOG TIMEOUT The communication monitoring (watchdog) function has activated. This is activated when a SCPI command is not received during the specified timeout period.



PROT-11-00

PROT-11-02

PROT-11-04


PROT-12-00

PROT-12-02

PROT-12-04



P.U INPUT VOLT An input open phase was detected.
00=between L1 and L2, 02=between L2 and L3, 04=between L3 and L1.


There may be a problem with the input wiring or an interruption may have
occurred. Turn the power off, check the input wiring, and then turn the power

back on.


P.U INPUT An input overvoltage or frequency error was detected. 00=L1, 02=L2, 04=L3


There may be a problem with the input wiring. Turn the power off, check the
input wiring, and then turn the power back on.



PROT-15-00 CAL DATA ERR Calibration value error. Contact your nearest Keysight Service Center to request
repairs.


PROT-19-00 EXT ALARM SIGNAL_IN A Disable occurred on the external signal, and the output was turned off.


PROT-19-01 EXT ALARM REMOTE An external alarm signal was received from the remote input terminal.


PROT-29-00 AC INPUT LOW The input voltage dropped lower than the rated value and then recovered. There
may be a problem with the input wiring or an interruption may have occurred.
Turn the power off, check the input wiring, and then turn the power back on.


For all other PROT error codes, contact your nearest Keysight Service Center to request repairs.


Keysight AC6900 Operating and Programming Guide 301


5 Maintenance


**Fault errors**


**Fault code** **Description** **Symptom and remedy**


FAULT-01-00 P.U ERR Power module error. Stop using the product, and contact your nearest Keysight
Service Center to request repairs.


FAULT-06-00 P.U UNDTCT (ALL) Not all of the power modules can be detected. Contact your nearest Keysight
Service Center to request repairs.


FAULT-11-00 P.U SWITCHING ERR Internal switch status error. Contact your nearest Keysight Service Center to
request repairs.


FAULT-14-00 EEPROM MAC (IFC) Internal communication error, EEPROM value error. Contact your nearest Keysight Service Center to request repairs.


FAULT-14-01 INT-COMM (IFC-IOC) Firmware update error. An error occurred during a firmware update through the
front panel USB port. Cycle power and use a different USB memory device to
update the firmware. If the error occurred other than during a firmware update,
contact your nearest Keysight Service Center to request repairs.


FAULT-19-00 DIGITAL I/O DETACHED Check that the supplied AC6900-30001, external Digital I/O Connector (D-Sub
25 Pin Plug) is connected to the DIGITAL I/O port on the rear panel. Turn the
power off and then back on.


FAULT-20-00 MEAS FAIL Measurement error. Contact your nearest Keysight Service Center to request
repairs.


FAULT-22-00 VERSION (IFC-IOC) In parallel operation, the combination of the firmware versions of the primary
and secondary units are not correct. Update the firmware, and align the ver
sions.


FAULT-23-00 OPTION ERR Option board installation error. Install the board correctly. If the board is
installed correctly, contact your nearest Keysight Service Center to request
repairs.


FAULT-29-00 AC INPUT LOW The input voltage has become lower than the rated value. There is a problem
with the input wiring. Turn the power off, check the input wiring, and then turn
the power back on.


FAULT-31-00 INT-COMM (P.D) Parallel operation system error. The parallel operation configuration is in error. If
the addresses and the number of secondary units are set correctly, contact your
nearest Keysight Service Center to request repairs.


For all other FAULT error codes, contact your nearest Keysight Service Center to request repairs.


302 Keysight AC6900 Operating and Programming Guide


### **Index**

*CLS 188


*ESE 188


*ESR? 189


*IDN? 189


*OPC 190


*OPC? 190


*OPT? 190


*RCL 191


*RST 191


*SAV 192


*SRE 192


*STB? 193


*TRG 193


*TST? 193





A



output 147


DIGital Subsyetem 175


DISPlay Subsystem 177


DNS Hostname


configuration 140


E


End-Or-Identify 152


Environment 38


Error Messages 133, 293


command 294


configuration

change 296


configuration


conflict 297


execution 295


internal 299


invalid operations 298


operation complete 296


product-specific 295


query 296


security 299


self-test 299


ESE 188


ESR? 189


F


Factory Reset 191


Fault errors 302


FETCh Subsystem 179


Firmware 17



ABORt Subsystem 171


AC Voltage


programming 88


Analog


connections 71


Analog Input 123


Auto DNS


configuration 140


B


Breaker Sizes 53



C


Calibration 286


Circuit Breaker


requirements 53


Cleaning 289


filter 289


CLS 188


Command Separators 151


Command Terminators 151


communication


digital IO 145


remote interface 136


Contacting Keysight 17


Current 59


limits 95


CURRent Subsystem 173


Custom Waveform 121


D


Date and time 131


DC Voltage


programming 90


Default Settings


front panel 274


Digital


onnections 72


digital IO 145


Digital IO


configure 148


input 146



Keysight AC6900 Operating and Programming Guide 303


Floor Mounting 45


Frequency


limits 96


synchronization 101


FREQuency Subsyetem 183


Front panel 18


display 21


FUNCtion Subsystem 186


G


GPIB


connections 55


status 141


GPIB Board 46


H


Hardware errors 134, 300


Harmonic Analysis 116


HCOPy Subsystem 187


Help 60


Help Menu 134


Help system 60


HiSLIP protocol 144


I


IDN? 189


IEEE-488 Common Com

mands 188


INITiate Subsystem 194


Input VA 53


Inspect 38



INSTrument Subsystem 195


Interface


connections 55


Internal memory


sanitization 133


Introduction 150


SCPI Language 150


IP


configuration 139


Items Supplied 38


IVI driver 150


K


Key click 130


Keywords 151


L


LAN


connections 56


settings 138


status 137


Limits


programming 94


Line voltage 24


Load Wire


AC6906; AC6912 con

nections 64-65


AC6918 connections 67


connections 87


preparation 62



requirements 63


LXI Subsystem 196


M


MEASure Subsystem 179


Measurement


details 280


settings 104


MEMory Subsystem 197


Menu


front panel 82


Menu tree 82


Message Available 192-193


Moving Considerations 39


O


Obtaining Service 286


OPC 190


OPC? 190


OPT? 190


Options 16


Outline Diagrams 40


Output


impedance 99, 119


offset 131


phase control 100


preset 129


response 120


soft start 100


turn-on 98



304 Keysight AC6900 Operating and Programming Guide


Output Sequences 112


OUTPut Subsystem 198


Output Voltage 58


P


Parallel


communication


cables 75


connections 74


load connections 77


output cables 76


synchronization


cables 75


Parallel Output 124


Parameter Types 152


Phase labels 130


Phase voltage 24


Power Cord


AC6903 48


AC6906; AC6912 49


AC6906; AC6918 50


specifications 47


Power Line


simulations 109


Power module


management 131


PROGram Subsystem 206


Protection


errors 300


programming 93



Q


Queries 151


Querying


Status Byte Register 193


Questionable Data Sum

mary 192-193


R


Rack Mounting 44


Ratings 23


RCL 191


Rear panel 22


remote interface 136


Repackaging 286


Reset (*RST)


settings 277


Returning the Unit 286


RST 191


S


Safety 38


Safety Symbols 12


Safety Warnings 7


Sanitization 133


SAV 192


SCPI 150


SCPI Language 150


Introduction 150


Screen saver 130


SD card 291



Self-test 193, 286


SENSe Subsyetem 214


Sense Terminal


wiring 69, 107


Sense terminal


settings 106


Sequences 112


Service 286


set current limit 59


set voltage 58


SIMulation Subsystem 217


Simulations 109


Sleep mode 130


Socket services 144


Soft Start 100


Specifications 26


SRE 192


Standard Event


Summary 192-193


Standard Operation

Register 192-193


Standard Operation Sum
mary 192-193


Status Byte 192


Status Byte Register 192

193


Status Overview 266


STATus Subsystem 221


STB? 193


Store and Recall 126



Keysight AC6900 Operating and Programming Guide 305


Supplemental Char

acteristics 28


Syntax Conventions 152


SYSTem Subsystem 234


T


Technical Support 17


Telnet session 143


TRG 193


TRIGger Subsystem 243


Trigger System


overview 261


TST? 193


U


USB


connections 55


status 136


Using Device Clear 154


V


Voltage 58


limits 95


VOLTage Subsyetem 246


Voltage Surge


suppression 99


W


Wait-for-trigger 193


WAVE Subsystem 258


Web interface 141


306 Keysight AC6900 Operating and Programming Guide


