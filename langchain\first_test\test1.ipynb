{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-16T01:47:19.686432Z", "start_time": "2025-07-16T01:47:07.961862Z"}}, "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "from langchain.output_parsers import MarkdownListOutputParser\n", "\n", "pdf_emaxple_path = r\"D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\"\n", "loader = PyMuPDFLoader(pdf_emaxple_path)\n", "docs = loader.load()\n", "# print(len(docs))\n", "# docs[0]\n", "txt = StrOutputParser().parse(docs[51].page_content)\n", "# print(txt)\n", "\n", "llm = OllamaLLM(model=\"glm4:9b\", temperature=0.1)\n", "template = (\"\"\"\n", "请你仔细分析文本内容{txt}，尽可能将他还原为Markdown格式，然后输出给我(只输出Markdown格式的内容)\n", "\"\"\")\n", "\n", "\n", "output_parser = MarkdownListOutputParser()\n", "format_instructions = output_parser.get_format_instructions()\n", "prompt = PromptTemplate(template=template,\n", "                        input_variables=[\"txt\"],\n", "                        partial_variables={\"format_instructions\": format_instructions},)\n", "\n", "chain = prompt | llm\n", "for chunk in chain.stream({\"txt\": txt}):\n", "    print(chunk, end=\"\", flush=True)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```markdown\n", "## 程控命令\n", "\n", "### 3.2 通用命令\n", "\n", "#### 命令使用：\n", "如不特别说明，命令可用于设置或者查询。\n", "若一个命令仅用于设置或查询，或者启动一个事件，命令说明中会单独进行说明。\n", "\n", "***CLS**\n", "- 功能描述：清除状态。设置状态字节（STB）、标准事件寄存器（ESR）和问题操作寄存的事件部分为零。该命令不改变屏蔽和转移寄存器的值，并清除输出缓冲区。\n", "- 设置格式：`*CLS`\n", "- 举例：    `*CLS` 清空仪器状态\n", "- 说明：    仅设置。\n", "\n", "***ESE <Value>**\n", "- 功能描述：设置或查询标准事件状态使能寄存器。0禁止。1使能。\n", "- 设置格式：`*ESE <value>`\n", "- 查询格式：`*ESE?`\n", "- 参数说明：\n", "  - `<Value>` 整型数值，各个位的二进制加权和，位映射将表3.1\n", "  - 范围 [0，255]。\n", "\n", "**举例**: `*ESE 60` 使能第4+8+16+32相应位即第2、3、4、5位。\n", "\n", "***ESR?**\n", "- 功能描述：读取事件状态寄存器的值，并清0该寄存器。见表3.1\n", "- 查询格式：`*ESR?`\n", "- 返回值：\n", "  - 整形数值，各个位的二进制加权和，位映射表见表3.1\n", "  - 范围 [0，255]。\n", "- 说明：    仅查询。\n", "\n", "| 位 | 值 | 说明 |\n", "|----|----|------|\n", "| 0  | 1  | 操作完成 |\n", "| 1  | 2  | 未用   |\n", "| 提示，尽可能将他还原为Markdown格式，然后输出给我(只输出Markdown格式的内容) |\n", "\n", "```"]}], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T02:57:05.846332Z", "start_time": "2025-07-16T02:56:42.522699Z"}}, "cell_type": "code", "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "from langchain_core.output_parsers import StrOutputParser # 只需要这个解析器\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "\n", "# 你的 PDF 路径\n", "pdf_example_path = r\"D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\"\n", "\n", "# 加载文档\n", "loader = PyMuPDFLoader(pdf_example_path)\n", "docs = loader.load()\n", "\n", "# 假设第 51 页是你感兴趣的内容\n", "# 最好添加一个检查，以防 PDF 页数不足 52 页导致 IndexError\n", "if len(docs) > 51:\n", "    txt = docs[60].page_content\n", "else:\n", "    print(f\"警告：PDF 只有 {len(docs)} 页。无法访问第 51 页。\")\n", "    txt = \"未找到示例文本内容。\" # 提供一个备用或处理错误\n", "\n", "# 初始化 LLM\n", "llm = OllamaLLM(model=\"glm4:9b\", temperature=0.1)\n", "\n", "# --- 更新后的提示词模板 ---\n", "# 明确指示 LLM 如何处理表格\n", "template = (\"\"\"\n", "你是一个专业的文档格式转换助手。\n", "请你仔细分析以下文本内容，并将其尽可能准确地还原为标准的Markdown格式。\n", "对于文本中描述的每个命令或功能块，请遵循以下格式要求：\n", "1.  使用 Markdown 标题（例如 `# 命令名称` 或 `## 命令名称`）来表示命令或功能块的名称。\n", "2.  在该标题下，将命令的**功能描述、设置格式、查询格式、参数说明、举例**等所有相关信息，都以**无序列表（使用 `-` 作为列表标记）**的形式详细列出。\n", "3.  如果无序列表项内部还有子项（例如参数说明下的具体参数），请使用**嵌套的无序列表（即缩进后继续使用 `-`）**。\n", "4.  **如果文本内容包含表格，请务必将其还原为标准的 Markdown 表格格式。确保表格的列对齐和内容完整。**\n", "5.  只输出Markdown格式的内容，不要添加任何额外的解释或前言。\n", "\n", "文本内容:\n", "{txt}\n", "\"\"\")\n", "\n", "prompt = PromptTemplate(\n", "    template=template,\n", "    input_variables=[\"txt\"],\n", ")\n", "\n", "# --- 链定义 ---\n", "chain = prompt | llm | StrOutputParser() # 明确使用 StrOutputParser\n", "\n", "print(\"--- 正在生成 Markdown 输出 ---\")\n", "for chunk in chain.stream({\"txt\": txt}):\n", "    print(chunk, end=\"\", flush=True)\n", "\n", "print(\"\\n--- 生成完成 ---\")"], "id": "7d7fe8109ae991d5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 正在生成 Markdown 输出 ---\n", "```markdown\n", "## [:SOURce[1]|2]:FREQuency:MULTiplier <FreqMult>\n", "- 功能描述：该命令为信号源频率设置频率倍乘。当设置频率倍乘为大于1的数值时，倍乘指示符“倍乘”显示在频率编辑区的右侧，此时，显示频率值 = 射频输出频率值 × 倍乘因子，但此时真实的频率输出仍然为未加倍乘因子前的频率，当频率倍乘设为1时，指示符号消失。\n", "- 设置格式：[:SOURce[1]|2]:FREQuency:MULTiplier <val>\n", "- 查询格式：[:SOURce[1]|2]:FREQuency:MULTiplier?\n", "- 参数说明：\n", "  - <FreqMult> 整型数据类型，倍乘因子。\n", "    - 范围：[1，72]。\n", "- 举例：\n", "  - :FREQuency:MULTiplier 8\n", "    - 信号发生器通道A的频率倍乘为8。\n", "  - :SOURce2:FREQuency:MULTiplier 2\n", "    - 信号发生器通道B的频率倍乘为2。\n", "- 复位状态：1\n", "- 按键路径：【频率】—>[频率设置]->[频率倍乘]\n", "\n", "## [:SOURce[1]|2]:FREQuency:OFFSet <FreqOffs>\n", "- 功能描述：该命令设置频率偏置值，当设置频率偏置不为零时，偏置指示符“偏置”在频率编辑框的右侧，显示值变为加上偏置后频率值，此时，显示频率值 = 射频输出频率值 × 频率倍乘＋频率偏置，但此时真实的频率输出仍然为未加频率倍乘及频率偏置前的频率，当频率偏置设为零时，指示符消失。\n", "- 设置格式：[:SOURce[1]|2]:FREQuency:OFFSet <val>\n", "- 查询格式：[:SOURce[1]|2]:FREQuency:OFFSet?\n", "- 参数说明：\n", "  - <FreqOffs> 频率偏置。\n", "    - 范围：[-325GHz，+325GHz]。\n", "- 举例：\n", "  - :FREQuency:OFFSetr 10GHz\n", "    - 信号发生器通道A频率偏置为10GHz。\n", "- 复位状态：0Hz\n", "- 按键路径：【频率】—>[频率设置]->[频率偏置]\n", "\n", "## [:SOURce[1]|2]:FREQuency:REFerence <FreqRef>\n", "- 功能描述：该命令设置频率参考值，该设置值在频率参考开关开启的情况下可正常使用，参见“[:SOURce[1]|2]:FREQuency:REFerence:STATe”命令。此时设置的任何连续波输出信号都将减去频率参考数值。例如：当前连续波输出频率为1GHz，如果设置该频率参考为1GHz，此时显示的连续波输出频率将以该频率参考为0Hz基准，所以频率显示区域将显示为0Hz，实际信号发生器输出频率为1GHz，如将连续波频率设为1MHz，此时频率显示区域将显示为1MHz，实际输出频率为1.001GHz。\n", "- 设置格式：[:SOURce[1]|2]:FREQuency:REFerence <val>\n", "- 查询格式：[:SOURce[1]|2]:FREQuency:REFerence?\n", "```\n", "--- 生成完成 ---\n"]}], "execution_count": 21}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-16T06:00:27.110100Z", "start_time": "2025-07-16T06:00:19.629589Z"}}, "cell_type": "code", "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "\n", "# --- 项目配置 ---\n", "# 定义 PDF 文件路径。请根据你的实际情况修改此路径。\n", "pdf_example_path = r\"D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\"\n", "\n", "# --- 阶段 1: PDF 文本提取 ---\n", "def extract_pdf_content(file_path: str):\n", "    \"\"\"\n", "    从指定的 PDF 文件中提取所有页面的文本内容。\n", "    \"\"\"\n", "    try:\n", "        loader = PyMuPDFLoader(file_path)\n", "        docs = loader.load()\n", "        print(f\"成功加载 PDF 文件: {file_path}, 总页数: {len(docs)}\")\n", "        return docs\n", "    except FileNotFoundError:\n", "        print(f\"错误：文件未找到，请检查路径: {file_path}\")\n", "        return []\n", "    except Exception as e:\n", "        print(f\"加载 PDF 时发生错误: {e}\")\n", "        return []\n", "\n", "all_pages_docs = extract_pdf_content(pdf_example_path)\n", "\n", "raw_pdf_text_input = all_pages_docs[55].page_content + \"\\n\" + all_pages_docs[56].page_content\n", "\n", "# 初始化 LLM\n", "llm = OllamaLLM(model=\"glm4:9b\", temperature=0.1)\n", "\n", "# --- 阶段 3: 基于 LLM 的信息抽取与过滤链 (核心部分) ---\n", "# 这个提示词的目标是：精准地从文本中提取SCPI命令的核心信息，并严格忽略所有无关内容。\n", "extraction_template = (\"\"\"\n", "你是一个专业的文档内容分析和SCPI命令提取助手。\n", "请你仔细阅读以下“文本内容”，并从中**精准地抽取所有与“SCPI命令(Standard Commands for Programmable Instruments)”相关的核心信息**。\n", "SCPI命令是一种基于文本的标准化命令语言，用于控制可编程测试和测量仪器。它定义了一套通用的命令集，使得不同厂商的仪器可以通过相同的命令进行控制，从而简化了自动化测试系统的开发。\n", "\n", "核心SCPI命令信息包括：\n", "-   **命令名称** (例如：`*CLS`, `:OUTPut:ALL[:STATe]`)\n", "-   **功能描述**\n", "-   **设置格式**\n", "-   **查询格式**\n", "-   **参数说明** (包括参数名称、类型、取值范围、默认值等)\n", "-   **返回值**\n", "-   **使用举例**\n", "-   任何与SCPI命令语法、行为或其子系统直接相关的详细说明。\n", "\n", "请**严格忽略并去除**以下无关内容：\n", "-   页眉、页脚、页码。\n", "-   **所有不属于SCPI命令详细描述的章节标题和编号**，例如：\n", "    -   `3 程控命令`\n", "    -   `3.3 仪器子系统命令`\n", "    -   `命令使用：`\n", "    -   任何以 `` 开头，后面跟着命令名和页码的目录式列表（例如 ` OUTPut ····················································· 50`）。\n", "-   任何非SCPI命令的引言、说明性段落、通用指导、或与SCPI命令本身无关的文本。\n", "-   任何重复或冗余的信息。\n", "\n", "**只输出你抽取到的核心SCPI命令相关文本**。不要添加任何额外的解释、前言、总结或Markdown格式。保持原始文本的结构（例如，如果参数说明是列表，就保留为列表形式）。\n", "\n", "文本内容:\n", "{txt}\n", "\"\"\")\n", "\n", "extraction_prompt = PromptTemplate(\n", "    template=extraction_template,\n", "    input_variables=[\"txt\"],\n", ")\n", "\n", "# 抽取链：输入原始文本，输出过滤后的文本\n", "# 这里我们只构建并执行抽取链\n", "extraction_chain = extraction_prompt | llm | StrOutputParser()\n", "\n", "print(\"--- 正在进行信息抽取与过滤 ---\")\n", "# 调用抽取链，传入已合并的原始 PDF 文本\n", "for chunk in extraction_chain.stream({\"txt\": raw_pdf_text_input}):\n", "    print(chunk, end=\"\", flush=True)\n", "\n", "print(\"\\n--- 信息抽取与过滤完成 ---\")"], "id": "a2b4e2ca9c132c09", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功加载 PDF 文件: D:\\liuzhihao\\FinalCode\\input\\Ceyear\\1466系列信号发生器程控手册.pdf, 总页数: 276\n", "--- 正在进行信息抽取与过滤 ---\n", "打开状态时，若设置该通道的调制输出为关闭，则该通道的射频输出也会被关闭。\n", "设置格式：:OUTPut[1]|2:MODulation[:STATe] ON|OFF|1|0\n", "参数说明：\n", "<State>\n", "布尔型数据，取值如下：\n", "ON | 1： 调制开，\n", "OFF | 0：调制关。\n", "举例：:OUTPut:MODulation:ALL 1 设置所有通道调制总开关状态为开。\n", "复位状态：0\n", "按键路径：无\n", "\n", ":OUTPut[1]|2[:STATe] <State>\n", "功能描述：该命令设置信号发生器每个通道的射频输出状态，当该通道无打开状态时，若设置该通道的射频输出为关闭，则该通道的调制输出也会被关闭。\n", "设置格式：:OUTPut[1]|2[:STATe] ON|OFF|1|0\n", "参数说明：\n", "<State>\n", "布尔型数据，取值如下：\n", "ON | 1： 输出开，\n", "OFF | 0：输出关。\n", "举例：:OUTPut:ALL 1 设置信号发生器所有通道射频输出状态为开。\n", "复位状态：0\n", "按键路径：无\n", "--- 信息抽取与过滤完成 ---\n"]}], "execution_count": 58}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}