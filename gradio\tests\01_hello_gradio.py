# 导入gradio库，这是创建机器学习/深度学习模型交互界面的工具
import gradio as gr

# 定义核心功能函数
# 这个函数接收两个参数：name(名字)和intensity(问候强度)
# 问候强度决定了返回结果中感叹号的数量
def greet(name, intensity):
    # 返回拼接的问候语，感叹号的数量由intensity决定
    return "Hello, " + name + "!" * int(intensity)

# 创建Gradio界面
# Interface是Gradio的核心类，用于创建简单的输入-输出界面
demo = gr.Interface(
    # fn参数指定要包装的函数，这里是greet函数
    fn=greet,
    # inputs参数定义输入组件
    # 这里使用列表指定两个输入组件：文本框(text)和滑块(slider)
    # 第一个输入对应name参数，第二个输入对应intensity参数
    inputs=["text", "slider"],
    # outputs参数定义输出组件
    # 这里只有一个输出组件：文本(text)，对应greet函数的返回值
    outputs=["text"],
)

# 启动界面
# share参数决定是否生成可共享的公共链接
# 设置为True后会生成一个可以通过互联网访问的URL
demo.launch(share=True)

# 补充说明：
# 运行方式：
# 使用 python 01_hello_gradio.py 运行时，修改代码后需要重新启动才能看到变化
# 使用 gradio 01_hello_gradio.py 运行时，修改代码后刷新浏览器即可看到变化
# 程序运行后会启动本地服务器，并提供两个访问地址：
# 本地地址（如 http://127.0.0.1:7860）只能在本机访问
# 公共地址（如 https://xxx.gradio.app）可以通过互联网访问
# 浏览器打开地址后，可以看到包含两个输入控件（文本框和滑块）和一个输出区域的界面：
# 在文本框中输入名字
# 拖动滑块选择问候强度
# 点击"Submit"按钮后会显示问候语，感叹号的数量由滑块位置决定