from langchain_ollama.chat_models import ChatOllama
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

output_parser = StrOutputParser()

llm = ChatOllama(
    model="qwen3:8b",
    temperature=0,
    # other params...
)

prompt = ChatPromptTemplate.from_messages([
    ("system", "You are world class technical documentation writer."),
    ("user", "{input}")
])
chain = prompt | llm | output_parser

print(chain.invoke(input={"input": "how can langsmith help with testing?"},
                    stream=True))
