

case = 4

if __name__ == "__main__":
    if case == 1:
        from langchain_core.runnables import RunnablePassthrough
        # 上游输入为 {"name": "xiaoming"}
        chain = RunnablePassthrough.assign(
            age=lambda x: 18,  # 新增键 "age"
            name=lambda x: x["name"].upper()  # 覆盖键 "name"
        )

        result = chain.invoke({"name": "xiaoming"})
        print(result)  # 输出：{"name": "小明", "age": 18}（保留原输入，新增age）

    if case == 2:
        from langchain_core.runnables import RunnableLambda

        # 1. 数据清洗：将字符串转为大写
        clean_chain = RunnableLambda(lambda x: x["text"].upper())
        print(clean_chain.invoke({"text": "hello"}))  # 输出："HELLO"

        # 2. 调试打印：输出中间结果
        debug_chain = RunnableLambda(lambda x: (print(f"中间结果: {x}"), x)[1])  # 打印后返回原数据

    if case == 3:
        from langchain_core.runnables import RunnableMap

        # 定义两个独立链（无依赖）
        chain_color = lambda _: "红色"  # 生成颜色
        chain_animal = lambda _: "老虎"  # 生成动物

        # 并行执行，结果合并为字典
        parallel_chain = RunnableMap({
            "color": chain_color,
            "animal": chain_animal
        })

        print(parallel_chain.invoke({}))  # 输出：{"color": "红色", "animal": "老虎"}

    if case == 4:
        from langchain_core.runnables import RunnableLambda

        # 创建一个可运行的组件，用于打印中间结果并返回原始输入
        debug_chain = RunnableLambda(lambda x: (print(f"中间结果: {x}"), x)[1])

        # 定义输入数据
        input_data = {"key": "value"}

        # 调用组件并传入输入数据
        result = debug_chain.invoke(input_data)

        print("最终结果:", result)