from langchain.prompts.chat import(
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)

template = (
    "You are a helpful assistant that translates {input_language} to {output_language}"
)

system_message_prompt = SystemMessagePromptTemplate.from_template(template)

human_template = "{text}"
human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)

chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])

chat_prompt.format_messages(input_language="English",
                            output_language="Chinese",
                            text="I love programming.")

print(chat_prompt.format_messages(input_language="English",
                                    output_language="Chinese",
                                    text="I love programming."))

