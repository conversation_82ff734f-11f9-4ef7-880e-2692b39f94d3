{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-08T02:12:33.485518Z", "start_time": "2025-07-08T02:12:33.431380Z"}}, "source": ["from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(model=\"qwen2.5:7b\")"], "outputs": [], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:13:43.598470Z", "start_time": "2025-07-08T02:13:43.233048Z"}}, "cell_type": "code", "source": "llm.invoke(\"中国的首都是那里？不需要介绍\")", "id": "33f92a4e2466e90e", "outputs": [{"data": {"text/plain": ["AIMessage(content='中国的首都是北京。', additional_kwargs={}, response_metadata={'model': 'qwen2.5:7b', 'created_at': '2025-07-08T02:13:43.5954848Z', 'done': True, 'done_reason': 'stop', 'total_duration': 359096300, 'load_duration': 28120500, 'prompt_eval_count': 36, 'prompt_eval_duration': 220303700, 'eval_count': 6, 'eval_duration': 110672100, 'model_name': 'qwen2.5:7b'}, id='run--7b8c393b-da7d-470d-9fe0-18adcdc09e42-0', usage_metadata={'input_tokens': 36, 'output_tokens': 6, 'total_tokens': 42})"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:13:25.594932Z", "start_time": "2025-07-08T02:13:25.111811Z"}}, "cell_type": "code", "source": "llm.invoke(\"中国的首都是那里？不需要介绍\").content", "id": "7a3c80a70edbceea", "outputs": [{"data": {"text/plain": ["'中国的首都是北京。'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["### __基本示例：提示+模型+输出解释器__\n", "最基本和常见的用例是将提示模板和模型链接在一起,为了看看这是如何工作的,让我们创建一个接受主题并生成小红书短文的链:"], "id": "a53055ce2986bee4"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:14:06.333739Z", "start_time": "2025-07-08T02:14:00.657774Z"}}, "cell_type": "code", "source": ["from langchain_ollama import ChatOllama\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser # 仍然使用 StrOutputParser\n", "\n", "# 实例化模型\n", "model = ChatOllama(model=\"qwen2.5:7b\", temperature=0.7) # 适当提高温度让模型更灵活\n", "\n", "# 这将指导模型如何生成小红书短文\n", "prompt_structured = ChatPromptTemplate.from_template(\n", "    \"\"\"请根据下面的主题，为小红书写一篇营销短文。\n", "    文章应该包含以下几个部分，请严格按照以下格式输出，不要有多余内容：\n", "\n", "    标题：【在这里写上吸引人的标题】\n", "    正文：\n", "    - 开头：简短有力地吸引眼球\n", "    - 特点：介绍产品的主要特点和亮点\n", "    - 体验：分享使用产品后的场景或感受\n", "    - 总结：用一句话总结并引导用户行动\n", "\n", "    主题：{topic}\n", "    \"\"\"\n", ")\n", "\n", "output_parser = StrOutputParser() # 依然是简单的字符串解析器\n", "\n", "# 链保持不变\n", "chain_structured = prompt_structured | model | output_parser\n", "\n", "# 调用链\n", "response_structured = chain_structured.invoke({\"topic\": \"蜜雪冰城\"})\n", "\n", "print(response_structured)"], "id": "54b82d29c654b639", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["标题：【甜蜜来袭！蜜雪冰城，给你的不只是夏天的清凉】\n", "\n", "正文：\n", "- 开头：炎热的夏日里，一杯清爽的饮料能带来怎样的享受？蜜雪冰城，将是你最好的选择。在这个充满热情与活力的季节里，让我们一起探索这份独特的甜蜜吧！\n", "\n", "- 特点：蜜雪冰城不仅拥有丰富的口味和创意无限的产品组合，更以优质原料和独特口感著称。无论是经典奶盖茶、还是新颖水果冷饮，每一款饮品都蕴含着匠心独运的秘密。更重要的是，在这里你可以根据个人喜好自由搭配，创造属于自己的专属风味。\n", "\n", "- 体验：想象这样一个场景，烈日当空时分，你走进蜜雪冰城，选择一款心仪的饮品坐在户外休息区享受片刻宁静。轻柔的音乐、舒适的座椅与一杯冰凉解渴的饮料相得益彰，仿佛整个世界都变得温柔起来。那一刻，所有的疲惫似乎都被冲淡了。\n", "\n", "- 总结：无论你是想要尝试新奇口味还是重温经典记忆，在蜜雪冰城都能找到那份属于你的甜蜜时刻。快来加入我们吧！让我们一起享受这份简单而纯粹的快乐。\n"]}], "execution_count": 16}, {"metadata": {}, "cell_type": "markdown", "source": ["### __Prompt提示__\n", "prompt是一个`BasePromptTemplate` ，这意味着它接受模板变量的字典并生成一个`PromptValue`。 `PromptValue` 是一个完整提示的包装器,可以传递给LLM（它接受一个字符串作为输入）或 ChatModel （它接受一个序列作为输入的消息）。它可以与任何一种语言模型类型一起使用，因为它定义了生成 `BaseMessage` 和生成字符串的逻辑。"], "id": "a89b9c823a93fe07"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:14:16.981182Z", "start_time": "2025-07-08T02:14:16.975759Z"}}, "cell_type": "code", "source": ["prompts = ChatPromptTemplate.from_template(\n", "    \"\"\"请根据下面的主题，为小红书写一篇营销短文。\n", "    文章应该包含以下几个部分，请严格按照以下格式输出，不要有多余内容：\n", "\n", "    标题：【在这里写上吸引人的标题】\n", "    正文：\n", "    - 开头：简短有力地吸引眼球\n", "    - 特点：介绍产品的主要特点和亮点\n", "    - 体验：分享使用产品后的场景或感受\n", "    - 总结：用一句话总结并引导用户行动\n", "\n", "    主题：{topic}\n", "    \"\"\"\n", ")\n", "propmt_value = prompts.format_prompt(topic=\"蜜雪冰城\")\n", "propmt_value"], "id": "3c6e38ab17d5429e", "outputs": [{"data": {"text/plain": ["ChatPromptValue(messages=[HumanMessage(content='请根据下面的主题，为小红书写一篇营销短文。\\n    文章应该包含以下几个部分，请严格按照以下格式输出，不要有多余内容：\\n\\n    标题：【在这里写上吸引人的标题】\\n    正文：\\n    - 开头：简短有力地吸引眼球\\n    - 特点：介绍产品的主要特点和亮点\\n    - 体验：分享使用产品后的场景或感受\\n    - 总结：用一句话总结并引导用户行动\\n\\n    主题：蜜雪冰城\\n    ', additional_kwargs={}, response_metadata={})])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["### __Model 模型__\n", "然后PromptValue被传递给model ,在本例中,我们的 model 是 ChatModel ,这意味着它将输出BaseMessage"], "id": "13aefd8a15eda3ef"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:14:45.852099Z", "start_time": "2025-07-08T02:14:40.160224Z"}}, "cell_type": "code", "source": ["message = model.invoke(propmt_value).content\n", "message"], "id": "40b04da262669c9d", "outputs": [{"data": {"text/plain": ["'标题：【清爽一夏，蜜雪冰城给你甜蜜降温！】\\n\\n正文：\\n- 开头：炎炎夏日，一杯清凉解渴的饮品就是最好的慰藉。今天，就让我们一起走进蜜雪冰城，享受那份专属于夏天的甜蜜与清凉吧！\\n\\n- 特点：蜜雪冰城以其独特的口感和丰富多样的口味而闻名。无论是经典奶茶、鲜果茶还是冰淇淋产品，每一款都经过精心调配，保证每一口都是满满的幸福感。此外，蜜雪冰城还承诺使用优质原料，让你品尝到最纯正的味道。\\n\\n- 体验：想象一下，在一个炎热的午后，你坐在街角的蜜雪冰城店铺里，轻轻啜一口那醇厚顺滑的奶茶，或是享受着新鲜水果与冰块碰撞出的独特滋味。那种从舌尖到心间的清凉感，仿佛夏日里的微风拂过脸庞，带来了一丝久违的舒适。\\n\\n- 总结：在这个夏天，不妨给自己一个理由，来蜜雪冰城感受那份独特的甜蜜吧！无论你是独自一人还是和朋友相聚，这里总有一款饮品能让你感到愉悦。快来打卡吧，让我们一起享受这份清凉与甜蜜！\\n\\n行动召唤：点击下方链接或前往最近的蜜雪冰城门店体验更多惊喜！'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "execution_count": 19}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:15:33.976014Z", "start_time": "2025-07-08T02:15:33.971026Z"}}, "cell_type": "code", "source": "output_parser.invoke(message)", "id": "60ee27ac0fee1c9f", "outputs": [{"data": {"text/plain": ["'标题：【清爽一夏，蜜雪冰城给你甜蜜降温！】\\n\\n正文：\\n- 开头：炎炎夏日，一杯清凉解渴的饮品就是最好的慰藉。今天，就让我们一起走进蜜雪冰城，享受那份专属于夏天的甜蜜与清凉吧！\\n\\n- 特点：蜜雪冰城以其独特的口感和丰富多样的口味而闻名。无论是经典奶茶、鲜果茶还是冰淇淋产品，每一款都经过精心调配，保证每一口都是满满的幸福感。此外，蜜雪冰城还承诺使用优质原料，让你品尝到最纯正的味道。\\n\\n- 体验：想象一下，在一个炎热的午后，你坐在街角的蜜雪冰城店铺里，轻轻啜一口那醇厚顺滑的奶茶，或是享受着新鲜水果与冰块碰撞出的独特滋味。那种从舌尖到心间的清凉感，仿佛夏日里的微风拂过脸庞，带来了一丝久违的舒适。\\n\\n- 总结：在这个夏天，不妨给自己一个理由，来蜜雪冰城感受那份独特的甜蜜吧！无论你是独自一人还是和朋友相聚，这里总有一款饮品能让你感到愉悦。快来打卡吧，让我们一起享受这份清凉与甜蜜！\\n\\n行动召唤：点击下方链接或前往最近的蜜雪冰城门店体验更多惊喜！'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "execution_count": 20}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T02:17:36.464491Z", "start_time": "2025-07-08T02:17:31.337547Z"}}, "cell_type": "code", "source": ["for chunk in chain_structured.stream({\"topic\": \"蜜雪冰城\"}):\n", "    print(chunk, end=\"\", flush=True)"], "id": "92e38edb4ebc702a", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["标题：【夏日清凉，尽在蜜雪冰城！】\n", "\n", "正文：\n", "- 开头：炎热的夏天，一杯清爽的饮料是必不可少的。今天给大家推荐的就是蜜雪冰城——一个专为追求品质与口感双重享受的你准备的品牌。\n", "- 特点：蜜雪冰城以其独特的饮品配方和优质的原材料著称。无论是经典的奶茶还是创新口味的果茶，都能在这里找到属于你的那一款。更值得一提的是，蜜雪冰城承诺使用新鲜水果和优质茶叶，让你每一口都感受到自然与健康的双重体验。\n", "- 体验：记得上次在烈日下工作了一天后，我走进了蜜雪冰城，点了一份招牌芒果奶绿。那瞬间的凉爽仿佛吹来一阵清风，而那股淡淡的芒果香更是令人回味无穷。更重要的是，在这里不仅能够品尝到美味，还能感受到那份来自品牌对品质的坚持。\n", "- 总结：无论你是想在夏日午后寻找一丝清凉，还是希望与朋友们分享这份甜蜜，蜜雪冰城都是一个不错的选择。快来尝试一下吧，相信你也会爱上这里的每一款饮品！"]}], "execution_count": 21}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}