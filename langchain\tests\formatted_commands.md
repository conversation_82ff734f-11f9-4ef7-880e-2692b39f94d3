# 程控命令文档格式化

## 错误代码表

未找到错误代码表


## 命令详情

### `*CLS` 功能描述：清除状态。设置状态字节（STB）、标准事件寄存器（ESR）和问题操作寄 

- **功能描述**：清除状态。设置状态字节（STB）、标准事件寄存器（ESR）和问题操作寄 
存的事件部分为零。该命令不改变屏蔽和转移寄存器的值，并清除输出缓冲
区。
- **设置格式**：`*CLS 
 
举例：    *CLS  清空仪器状态 
说明：    仅设置。 
*ESE <Value> 
功能描述：设置或查询标准事件状态使能寄存器。0禁止。1使能。  
 *ESE <value>`
- **查询格式**：`*ESE? 
参数说明：  
<Value> 
整型数值，各个位的二进制加权和，位映射将表3.1 
范围 [ 0，255 ]。 
举例:       *ESE 60 使能第4+8+16+32 相应位即第2、3、4、5位。 
*ESR? 
功能描述：读取事件状态寄存器的值，并清0该寄存器。见表3.1 
 *ESR? 
返回值： 
整形数值，各个位的二进制加权和，位映射表见表3.1 
范围 [ 0，255 ]。 
说明：    仅查询。 
表3.1 
标准事件位映射 
位 
值 
说明 
0 
1 
操作完成 
1 
2 
未用 
提 示`
- **举例**：``
- **说明**：

### `*ESE` <Value> 

- **功能描述**：设置或查询标准事件状态使能寄存器。0禁止。1使能。
- **设置格式**：`*ESE <value>`
- **查询格式**：`*ESE? 
参数说明：  
<Value> 
整型数值，各个位的二进制加权和，位映射将表3.1 
范围 [ 0，255 ]。 
举例:       *ESE 60 使能第4+8+16+32 相应位即第2、3、4、5位。 
*ESR? 
功能描述：读取事件状态寄存器的值，并清0该寄存器。见表3.1 
 *ESR? 
返回值： 
整形数值，各个位的二进制加权和，位映射表见表3.1 
范围 [ 0，255 ]。 
说明：    仅查询。 
表3.1 
标准事件位映射 
位 
值 
说明 
0 
1 
操作完成 
1 
2 
未用 
提 示`
- **举例**：``
- **说明**：

### `*ESR`

- **功能描述**：读取事件状态寄存器的值，并清0该寄存器。见表3.1 
查询格式： *ESR? 
返回值： 
整形数值，各个位的二进制加权和，位映射表见表3.1 
范围 [ 0，255 ]。 
说明：    仅查询。 
表3.1 
标准事件位映射 
位 
值 
说明 
0 
1 
操作完成 
1 
2 
未用 
提 示
- **举例**：``
- **说明**：

