{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["### __LLM 模型包装器__\n", "输入是字符串，输出是对字符串的补全（适合纯文本生成场景）"], "id": "5204f9d60aa2d896"}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-14T01:56:43.577531Z", "start_time": "2025-07-14T01:56:33.550510Z"}}, "source": ["from langchain_ollama import OllamaLLM\n", "from langchain.prompts import PromptTemplate\n", "\n", "# 初始化基础 LLM 模型（直接处理文本输入输出）\n", "llm = OllamaLLM(\n", "    model=\"qwen2.5:7b\",\n", "    temperature=0.7,  # 控制随机性（0-1），越高越有创意\n", "    max_tokens=64    # 最大生成长度\n", ")\n", "\n", "print(\"\\n---直接调用---\\n\")\n", "# 直接调用（简单用法）\n", "print(llm.invoke(\"讲一个关于程序员的笑话。\"))\n", "\n", "print(\"\\n---使用 PromptTemplate 构建结构化提示---\\n\")\n", "# 最佳实践：使用 PromptTemplate 构建结构化提示\n", "prompt = PromptTemplate(\n", "    input_variables=[\"topic\"],\n", "    template=\"请围绕{topic}创作一个幽默故事，结尾要有反转。\"\n", ")\n", "formatted_prompt = prompt.format(topic=\"AI取名\")\n", "print(llm.invoke(formatted_prompt))"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "---直接调用---\n", "\n", "当然可以，这里有一个程序员相关的笑话：\n", "\n", "为什么那个程序员不喜欢在户外工作？\n", "\n", "因为他害怕意外的“bug”掉下来！（这里的“bug”是指程序中的错误或问题）\n", "\n", "这个笑话利用了“bug”这个词在编程和自然界中都有含义的特点，制造了一个有趣的双关效果。希望你喜欢这个笑话！\n", "\n", "---使用 PromptTemplate 构建结构化提示---\n", "\n", "在一个风和日丽的下午，小镇上的居民们聚集在老橡树下，讨论着一件大事——为新成立的社区图书馆命名。大家议论纷纷，有的人想用“智慧之光”，有的人提议叫“知识宝库”，还有的则主张简单直接地称为“读书乐园”。\n", "\n", "这时，一个名叫李小明的年轻人站了出来，“我有一个建议，”他清了清嗓子说，“我们为什么不叫它‘智能书屋’呢？”\n", "\n", "众人都愣住了。李小明解释道：“这个名字既体现了我们社区对科技的拥抱，又暗含了这里能够培养智慧的寓意。”\n", "\n", "“可是，”老张皱着眉头问道，“什么是‘智能书屋’啊？听起来就像是一个机器人商店！”\n", "\n", "“哈哈！”李小明笑着说，“其实呢，我还有一个计划。我会找来最先进的AI技术，让这个图书馆成为一个真正的‘智能书屋’——它不仅能自动推荐书籍，还能根据读者的兴趣进行个性化服务。”\n", "\n", "大家被逗笑了，纷纷议论着这个创意的疯狂与有趣。\n", "\n", "然而，就在大家还在讨论时，李小明突然收到了一条短信：“恭喜您！您的项目已经获得了科技局的支持。我们将为社区提供最新的人工智能解决方案，并且‘智能书屋’将成为第一个试点项目。”\n", "\n", "原来，李小明早就在背后悄悄地准备了一切。他不仅找到了最好的AI技术供应商，还策划了这次的建议，只为让大家觉得“智能书屋”这个名字只是一个幽默而富有创意的想法。\n", "\n", "这下子，整个社区都被这个惊喜弄得手足无措，纷纷鼓掌欢呼起来，庆祝他们的图书馆即将成为科技与智慧的结晶——一个真正意义上的“智能书屋”。\n"]}], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": ["#### __关键区别：__\n", "\n", "- **输入**：直接传入字符串（纯文本）\n", "- **输出**：返回 `str` 类型的生成文本\n", "- **适用场景**：不需要对话历史的单轮文本生成（如摘要、翻译、创作）"], "id": "947faba2276ca453"}, {"metadata": {}, "cell_type": "markdown", "source": ["### __聊天模型包装器__\n", "输入是消息列表，输出是 AI 的回复消息（适合多轮对话场景）"], "id": "51113258a1683854"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T01:58:20.262910Z", "start_time": "2025-07-14T01:58:17.853746Z"}}, "cell_type": "code", "source": ["from langchain.schema import (\n", "    AIMessage,         # AI 回复的消息\n", "    HumanMessage,      # 用户输入的消息\n", "    SystemMessage      # 系统指令（设定 AI 角色）\n", ")\n", "from langchain_ollama import ChatOllama\n", "\n", "# 初始化聊天模型（支持消息列表输入输出）\n", "chat = ChatOllama(\n", "    model=\"qwen2.5:7b\",\n", "    temperature=0.8    # 取名场景需要更多创意，调高随机性\n", ")\n", "\n", "# 构建消息列表（多轮对话历史）\n", "messages = [\n", "    SystemMessage(content=\"你是一个专业取名大师，擅长为AI创业公司取英文名。要求：简洁易记，包含AI元素，有科技感。\"),\n", "    HumanMessage(content=\"帮我给一家专注于AI图像生成的公司取名字\"),\n", "    AIMessage(content=\"以下是几个候选名字：\\n1. PictAIst（AI绘图师）\\n2. AIvision（AI视觉）\\n3. PixelPulseAI\"),\n", "    HumanMessage(content=\"还有其他推荐吗？我希望名字更有未来感。\")\n", "]\n", "\n", "# 调用模型生成回复\n", "response = chat.invoke(messages)\n", "print(\"AI 回复：\\n\", response.content)"], "id": "393dc1b07382ef49", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AI 回复：\n", " 当然可以，以下是一些更具未来感的名字建议：\n", "\n", "1. FutureLoom（未来织造，象征编织未来的图像）\n", "2. Neural<PERSON><PERSON><PERSON>（神经画布，强调神经网络技术）\n", "3. VizionaryAI（视觉先知，具有前瞻性）\n", "4. QuantumPixels（量子像素，结合了前沿科技与图像生成）\n", "5. <PERSON><PERSON><PERSON><PERSON><PERSON>（想象新星，充满创新感）\n", "\n", "这些名称都体现了未来的科技感和AI图像生成的核心业务。\n"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": ["#### __关键区别：__\n", "\n", "- **输入**：传入 `Message` 对象列表（包含对话历史）\n", "- **输出**：返回 `AIMessage` 对象（包含回复内容和元数据）\n", "- **适用场景**：多轮对话（如聊天机器人、问答系统、角色扮演）"], "id": "697f04346cfcea88"}, {"metadata": {}, "cell_type": "markdown", "source": ["#### **对比总结**\n", "\n", "| 特性             | LLM 模型包装器             | 聊天模型包装器                                         |\n", "| ---------------- | -------------------------- | ------------------------------------------------------ |\n", "| **输入类型**     | 字符串（纯文本）           | 消息列表（System+Human+AI）                            |\n", "| **输出类型**     | 字符串（`str`）            | 消息对象（`AIMessage`）                                |\n", "| **对话历史支持** | 不支持（每次调用独立）     | 支持（保留历史上下文）                                 |\n", "| **适用场景**     | 单轮文本生成（摘要、创作） | 多轮对话（聊天、问答）                                 |\n", "| **典型用法**     | `llm(\"生成一个故事\")`      | `chat.invoke([SystemMessage(...), HumanMessage(...)])` |\n", "\n", "#### **实用技巧**\n", "\n", "1. **避免混用**：\n", "   - 若需对话功能，优先用 `Chat<PERSON>llama`（即使单轮对话）\n", "   - 仅在纯文本生成且无需对话历史时用 `OllamaLLM`\n", "2. **系统消息最佳实践**：\n", "   - 明确限定输出格式（如 \"请只返回名字，不要解释\"）\n", "   - 控制领域范围（如 \"仅限英文名字\"）\n", "   - 调整语气（如 \"用幽默的方式回答\"）\n", "3. **流式输出（实时显示）**：\n", "```python\n", "for chunk in chat.stream(messages):\n", "    print(chunk.content, end=\"\", flush=True)\n", "```\n", "4. **错误处理**：\n", "```python\n", "try:\n", "    response = chat.invoke(messages)\n", "except Exception as e:\n", "    print(f\"模型调用失败：{e}\")\n", "    # 可选：使用备选模型或降级策略\n", "```"], "id": "386fa3e0c0f95568"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "aef2119a21c2bf79"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}