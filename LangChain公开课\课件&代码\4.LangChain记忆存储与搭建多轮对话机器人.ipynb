{"cells": [{"cell_type": "markdown", "id": "e521dcdc-c8c0-452f-b330-534459b35fa2", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 4.LangChain记忆存储与搭建多轮对话机器人"]}, {"cell_type": "code", "execution_count": 2, "id": "904a6dfe-cf33-49a2-8cbb-8d335aad0132", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv \n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "id": "6d07d20d-bb4b-4dd8-be89-832a0cf2dd78", "metadata": {}, "source": ["### 1. 构建多轮对话的流式智能问答系统"]}, {"cell_type": "markdown", "id": "5d628728-e3ee-4e6d-bbec-618aded3d751", "metadata": {}, "source": ["&emsp;&emsp;在`lang<PERSON><PERSON>n`中构建一个基本的问答机器人仅需要使用一个`Chain`便可以快速实现，如下所示："]}, {"cell_type": "code", "execution_count": 42, "id": "3ed84d9c-20cc-4342-b358-fc204a5fd633", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好呀！我是小智，一名乐于助人的AI助手。很高兴认识你！😊\n", "\n", "我的主要特点有：\n", "1. **知识丰富** - 我掌握各领域的知识，可以回答各种问题\n", "2. **多语言能力** - 可以用中文、英文等多种语言交流\n", "3. **耐心友善** - 我会认真倾听并尽力提供帮助\n", "4. **持续学习** - 我的知识会不断更新完善\n", "5. **免费服务** - 完全免费为你提供帮助\n", "\n", "我可以帮你：\n", "- 解答各类问题\n", "- 提供学习/工作建议\n", "- 协助写作/翻译\n", "- 日常聊天解闷\n", "- 以及其他任何我能帮上忙的事情！\n", "\n", "虽然我是AI，但我会用最真诚的态度来帮助你。有什么想问的或需要帮忙的，尽管告诉我吧！✨\n"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain.chat_models import init_chat_model\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain.chat_models import init_chat_model\n", "\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你叫小智，是一名乐于助人的助手。\"),\n", "    (\"user\", \"{input}\")\n", "])\n", "\n", "# 使用 DeepSeek 模型\n", "model = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")  \n", "\n", "# 直接使用模型 + 输出解析器\n", "basic_qa_chain = chatbot_prompt | model | StrOutputParser()\n", "\n", "# 测试\n", "question = \"你好，请你介绍一下你自己。\"\n", "result = basic_qa_chain.invoke(question)\n", "print(result)"]}, {"cell_type": "markdown", "id": "e14e5896-234a-4240-859e-3d78ac1a1dd8", "metadata": {}, "source": ["- 添加多轮对话记忆"]}, {"cell_type": "markdown", "id": "fe44f306-61fe-4d53-952a-75504ac6d385", "metadata": {}, "source": ["&emsp;&emsp;在<PERSON><PERSON>hain中，我们可以通过人工拼接消息队列，来为每次模型调用设置多轮对话记忆。"]}, {"cell_type": "code", "execution_count": 22, "id": "79fdd8dc-a164-47b3-be21-76895d6f596f", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder"]}, {"cell_type": "code", "execution_count": 26, "id": "9790b487-82ac-4c10-b777-fd1bbfa6caa9", "metadata": {}, "outputs": [], "source": ["chatbot_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessage(\n", "            content=\"你叫小智，是一名乐于助人的助手。\"\n", "        ),\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "cc89120b-1ee2-405c-82c5-6e276784684d", "metadata": {}, "outputs": [], "source": ["basic_qa_chain = chatbot_prompt | model | StrOutputParser()"]}, {"cell_type": "code", "execution_count": 29, "id": "15120163-e597-4145-b3fd-38aeeff4ce90", "metadata": {}, "outputs": [], "source": ["messages_list = [\n", "    HumanMessage(content=\"你好，我叫陈明，好久不见。\"),\n", "    AIMessage(content=\"你好呀！我是小智，一名乐于助人的AI助手。很高兴认识你！\"),\n", "]"]}, {"cell_type": "code", "execution_count": 30, "id": "50271ead-0857-49aa-83d8-aefa66194fc7", "metadata": {}, "outputs": [], "source": ["question = \"你好，请问我叫什么名字。\""]}, {"cell_type": "code", "execution_count": 31, "id": "40fbabe7-44dd-4635-89ab-a2ce38019b1c", "metadata": {}, "outputs": [], "source": ["messages_list.append(HumanMessage(content=question))"]}, {"cell_type": "code", "execution_count": 32, "id": "4c6699fe-7292-4c65-9280-ae84cbb030b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='你好，我叫陈明，好久不见。', additional_kwargs={}, response_metadata={}),\n", " AIMessage(content='你好呀！我是小智，一名乐于助人的AI助手。很高兴认识你！', additional_kwargs={}, response_metadata={}),\n", " HumanMessage(content='你好，请问我叫什么名字。', additional_kwargs={}, response_metadata={})]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["messages_list"]}, {"cell_type": "code", "execution_count": 33, "id": "44a2a038-9520-422c-86dc-36acb108bc03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["哈哈，你刚刚告诉我你叫陈明呀！看来我们真的是\"好久不见\"了呢～需要我帮忙记住什么其他信息吗？(◕‿◕)\n"]}], "source": ["result = basic_qa_chain.invoke({\"messages\": messages_list})\n", "print(result)"]}, {"cell_type": "markdown", "id": "76b11106-bdaf-4c96-a407-a85ca2a44933", "metadata": {}, "source": ["完整的多轮对话函如下："]}, {"cell_type": "code", "execution_count": 4, "id": "6cc997f1-8a6f-47a6-abbb-edda2a410970", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔹 输入 exit 结束对话\n", "🤖 小智： 你好！很高兴遇见你。有什么我可以帮助你的吗？\n", "🤖 小智： 我是小智，一名能够提供信息查询、解决问题和聊天交流的人工智能助手。我会尽力帮助你解答疑惑或完成任务。你需要什么帮助呢？\n"]}], "source": ["from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_ollama import ChatOllama\n", "\n", "# model  = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")\n", "model = ChatOllama(model=\"qwen2.5:7b\")\n", "parser = StrOutputParser()\n", "\n", "prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])\n", "\n", "chain = prompt | model | parser\n", "\n", "messages_list = []  # 初始化历史\n", "print(\"🔹 输入 exit 结束对话\")\n", "while True:\n", "    user_query = input(\"👤 你：\")\n", "    if user_query.lower() in {\"exit\", \"quit\"}:\n", "        break\n", "\n", "    # 1) 追加用户消息\n", "    messages_list.append(HumanMessage(content=user_query))\n", "\n", "    # 2) 调用模型\n", "    assistant_reply = chain.invoke({\"messages\": messages_list})\n", "    print(\"🤖 小智：\", assistant_reply)\n", "\n", "    # 3) 追加 AI 回复\n", "    messages_list.append(AIMessage(content=assistant_reply))\n", "\n", "    # 4) 仅保留最近 50 条\n", "    messages_list = messages_list[-50:]\n"]}, {"cell_type": "markdown", "id": "ea5f7255-73e8-4933-a835-e0b4f71bb295", "metadata": {}, "source": ["- 流式打印聊天信息"]}, {"cell_type": "markdown", "id": "7a598492-1007-4302-a00f-22397a42a22f", "metadata": {}, "source": ["&emsp;&emsp;此外还有一个问题是，大家经常看到的问答机器人其实都是采用流式传输模式。用户输入问题，等待模型直接返回回答，然后用户再输入问题，模型再返回回答，这样循环下去，用户输入问题和模型返回回答之间的时间间隔太长，导致用户感觉机器人反应很慢。所以`LangChain`提供了一个`astream`方法，可以实现流式输出，即一旦模型有输出，就立即返回，这样用户就可以看到模型正在思考，而不是等待模型思考完再返回。\n", "\n", "\n", "&emsp;&emsp;实现的方法也非常简单，只需要在调用模型时将`invoke`方法替换为`astream`方法，然后使用`async for`循环来获取模型的输出即可。代码如下："]}, {"cell_type": "code", "execution_count": 2, "id": "2e23ae26-75b4-467c-a522-58586cd8b58b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["您好！我叫小智，是一名来自阿里云的超大规模语言模型。我的任务是帮助用户获得他们想要的信息或解答他们的问题，无论是日常生活的小知识、学习上的疑难解析还是工作中的信息查询，都可以来寻求我的帮助。如果您有任何需要咨询的内容，请随时告诉我，我会尽力提供支持和帮助！"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain.chat_models import init_chat_model\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_ollama import ChatOllama\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你叫小智，是一名乐于助人的助手。\"),\n", "    (\"user\", \"{input}\")\n", "])\n", "\n", "# 使用 DeepSeek 模型\n", "# model = init_chat_model(model=\"deepseek-chat\", model_provider=\"deepseek\")  \n", "model = ChatOllama(model=\"qwen2.5:7b\")\n", "\n", "# 直接使用提示模版 +模型 + 输出解析器\n", "qa_chain_with_system = chatbot_prompt | model | StrOutputParser()\n", "\n", "# 异步实现流式输出\n", "async for chunk in qa_chain_with_system.astream({\"input\": \"你好，请你介绍一下你自己\"}):\n", "    print(chunk, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b6ab2caa-3fc1-4293-91dd-d68dad16f5c2", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔹 输入 exit 结束对话\n", "Hello! 你好吗？有什么我可以帮助你的吗？Xihu, or West Lake, is located in Hangzhou, Zhejiang Province, China. It's about 15 kilometers (9.3 miles) west of the city center. The lake is surrounded by hills and scenic areas, including the Longjing Tea Plantation and several historic temples and gardens. Many visitors come to enjoy its natural beauty, take a boat ride, or visit cultural attractions like the Leifeng Pagoda.谢谢你的夸奖！我还有很多功能可以帮助你哦。你对西湖（Xihu）想知道些什么？比如它的位置、历史、著名景点或者如何前往等信息。告诉我你的具体需求，我会尽力提供帮助的！在日本，“一周”通常被称为“一週間”（かずかん，kuzukian）。这个词由两个部分组成：\n", "\n", "1. **一（いち）** - 表示“一”\n", "2. **週間（しゅうかん）** - 意思是“周”，其中“週”表示“周”或“星期”，“間”在这里是用来连接词尾，使得整个表达更加自然和流畅。\n", "\n", "所以，“一周”就是指日本的一周时间。如果你有任何其他问题或者需要进一步的帮助，请随时告诉我！"]}], "source": ["prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])\n", "\n", "chain = prompt | model | parser\n", "\n", "messages_list = []  # 初始化历史\n", "print(\"🔹 输入 exit 结束对话\")\n", "while True:\n", "    user_query = input(\"👤 你：\")\n", "    if user_query.lower() in {\"exit\", \"quit\"}:\n", "        break\n", "\n", "    # 1) 追加用户消息\n", "    messages_list.append(HumanMessage(content=user_query))\n", "\n", "    # 2) 调用模型\n", "    async for chunk in chain.astream({\"messages\": messages_list}):\n", "        print(chunk, end=\"\", flush=True)\n", "\n", "    # 3) 追加 AI 回复\n", "    messages_list.append(AIMessage(content=assistant_reply))\n", "\n", "    # 4) 仅保留最近 50 条\n", "    messages_list = messages_list[-50:]"]}, {"cell_type": "markdown", "id": "60aba029-d360-4ab9-860c-590508217148", "metadata": {}, "source": ["&emsp;&emsp;如上所示展示的问答效果就是我们在构建大模型应用时需要实现的流式输出效果。接下来我们就进一步地，使用`gradio`来开发一个支持在网页上进行交互的问答机器人。"]}, {"cell_type": "markdown", "id": "bcc0b5d9-d30d-475b-aae5-8e90766eae3b", "metadata": {}, "source": ["&emsp;&emsp;首先需要安装一下`gradio`的第三方依赖包，"]}, {"cell_type": "code", "execution_count": 31, "id": "48b5f6d6-f9c1-43a9-9197-108ea4f4dcd6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting gradio\n", "  Downloading gradio-5.33.0-py3-none-any.whl.metadata (16 kB)\n", "Collecting aiofiles<25.0,>=22.0 (from gradio)\n", "  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (4.9.0)\n", "Collecting fastapi<1.0,>=0.115.2 (from gradio)\n", "  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)\n", "Collecting ffmpy (from gradio)\n", "  Using cached ffmpy-0.6.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting gradio-client==1.10.2 (from gradio)\n", "  Using cached gradio_client-1.10.2-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting groovy~=0.1 (from gradio)\n", "  Using cached groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)\n", "Requirement already satisfied: httpx>=0.24.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (0.28.1)\n", "Collecting huggingface-hub>=0.28.1 (from gradio)\n", "  Downloading huggingface_hub-0.32.4-py3-none-any.whl.metadata (14 kB)\n", "Collecting jinja2<4.0 (from gradio)\n", "  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting markupsafe<4.0,>=2.0 (from gradio)\n", "  Using cached MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl.metadata (4.1 kB)\n", "Collecting numpy<3.0,>=1.0 (from gradio)\n", "  Downloading numpy-2.3.0-cp312-cp312-win_amd64.whl.metadata (60 kB)\n", "Requirement already satisfied: orjson~=3.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (3.10.18)\n", "Requirement already satisfied: packaging in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (24.2)\n", "Collecting pandas<3.0,>=1.0 (from gradio)\n", "  Downloading pandas-2.3.0-cp312-cp312-win_amd64.whl.metadata (19 kB)\n", "Collecting pillow<12.0,>=8.0 (from gradio)\n", "  Using cached pillow-11.2.1-cp312-cp312-win_amd64.whl.metadata (9.1 kB)\n", "Requirement already satisfied: pydantic<2.12,>=2.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (2.11.5)\n", "Collecting pydub (from gradio)\n", "  Using cached pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting python-multipart>=0.0.18 (from gradio)\n", "  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (6.0.2)\n", "Collecting ruff>=0.9.3 (from gradio)\n", "  Downloading ruff-0.11.13-py3-none-win_amd64.whl.metadata (26 kB)\n", "Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)\n", "  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting semantic-version~=2.0 (from gradio)\n", "  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Collecting starlette<1.0,>=0.40.0 (from gradio)\n", "  Downloading starlette-0.47.0-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)\n", "  Downloading tomlkit-0.13.3-py3-none-any.whl.metadata (2.8 kB)\n", "Collecting typer<1.0,>=0.12 (from gradio)\n", "  Using cached typer-0.16.0-py3-none-any.whl.metadata (15 kB)\n", "Requirement already satisfied: typing-extensions~=4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from gradio) (4.14.0)\n", "Collecting uvicorn>=0.14.0 (from gradio)\n", "  Downloading uvicorn-0.34.3-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting fsspec (from gradio-client==1.10.2->gradio)\n", "  Using cached fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)\n", "Collecting websockets<16.0,>=10.0 (from gradio-client==1.10.2->gradio)\n", "  Using cached websockets-15.0.1-cp312-cp312-win_amd64.whl.metadata (7.0 kB)\n", "Requirement already satisfied: idna>=2.8 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from anyio<5.0,>=3.0->gradio) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from anyio<5.0,>=3.0->gradio) (1.3.1)\n", "Collecting starlette<1.0,>=0.40.0 (from gradio)\n", "  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2.9.0.post0)\n", "Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (0.4.1)\n", "Collecting click>=8.0.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting rich>=10.11.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached rich-14.0.0-py3-none-any.whl.metadata (18 kB)\n", "Requirement already satisfied: colorama in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from click>=8.0.0->typer<1.0,>=0.12->gradio) (0.4.6)\n", "Requirement already satisfied: certifi in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx>=0.24.1->gradio) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpx>=0.24.1->gradio) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from httpcore==1.*->httpx>=0.24.1->gradio) (0.16.0)\n", "Collecting filelock (from huggingface-hub>=0.28.1->gradio)\n", "  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: requests in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from huggingface-hub>=0.28.1->gradio) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from huggingface-hub>=0.28.1->gradio) (4.67.1)\n", "Requirement already satisfied: six>=1.5 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.17.0)\n", "Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio)\n", "  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.19.1)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio)\n", "  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from requests->huggingface-hub>=0.28.1->gradio) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in e:\\01_木羽研发\\11_trafficvideo\\langchain_venv\\lib\\site-packages (from requests->huggingface-hub>=0.28.1->gradio) (2.4.0)\n", "Downloading gradio-5.33.0-py3-none-any.whl (54.2 MB)\n", "   ---------------------------------------- 0.0/54.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.0/54.2 MB ? eta -:--:--\n", "   ---------------------------------------- 0.5/54.2 MB 2.4 MB/s eta 0:00:23\n", "   - -------------------------------------- 1.6/54.2 MB 4.0 MB/s eta 0:00:14\n", "   --- ------------------------------------ 4.5/54.2 MB 7.9 MB/s eta 0:00:07\n", "   ------ --------------------------------- 8.4/54.2 MB 11.3 MB/s eta 0:00:05\n", "   --------- ------------------------------ 13.1/54.2 MB 13.7 MB/s eta 0:00:04\n", "   ------------ --------------------------- 17.3/54.2 MB 14.7 MB/s eta 0:00:03\n", "   --------------- ------------------------ 21.5/54.2 MB 15.4 MB/s eta 0:00:03\n", "   ------------------- -------------------- 26.0/54.2 MB 16.3 MB/s eta 0:00:02\n", "   ---------------------- ----------------- 30.4/54.2 MB 16.9 MB/s eta 0:00:02\n", "   ------------------------- -------------- 34.9/54.2 MB 17.3 MB/s eta 0:00:02\n", "   ---------------------------- ----------- 39.1/54.2 MB 17.7 MB/s eta 0:00:01\n", "   -------------------------------- ------- 43.5/54.2 MB 18.0 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 48.2/54.2 MB 18.3 MB/s eta 0:00:01\n", "   -------------------------------------- - 52.4/54.2 MB 18.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 54.2/54.2 MB 18.1 MB/s eta 0:00:00\n", "Using cached gradio_client-1.10.2-py3-none-any.whl (323 kB)\n", "Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)\n", "Using cached fastapi-0.115.12-py3-none-any.whl (95 kB)\n", "Using cached groovy-0.1.2-py3-none-any.whl (14 kB)\n", "Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)\n", "Using cached MarkupSafe-3.0.2-cp312-cp312-win_amd64.whl (15 kB)\n", "Downloading numpy-2.3.0-cp312-cp312-win_amd64.whl (12.7 MB)\n", "   ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "   ------------------ --------------------- 6.0/12.7 MB 28.4 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 9.2/12.7 MB 24.8 MB/s eta 0:00:01\n", "   ---------------------------------------- 12.7/12.7 MB 20.5 MB/s eta 0:00:00\n", "Downloading pandas-2.3.0-cp312-cp312-win_amd64.whl (11.0 MB)\n", "   ---------------------------------------- 0.0/11.0 MB ? eta -:--:--\n", "   ------------------ --------------------- 5.0/11.0 MB 25.1 MB/s eta 0:00:01\n", "   --------------------------------- ------ 9.2/11.0 MB 24.8 MB/s eta 0:00:01\n", "   ---------------------------------------- 11.0/11.0 MB 21.4 MB/s eta 0:00:00\n", "Using cached pillow-11.2.1-cp312-cp312-win_amd64.whl (2.7 MB)\n", "Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)\n", "Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)\n", "Using cached starlette-0.46.2-py3-none-any.whl (72 kB)\n", "Downloading tomlkit-0.13.3-py3-none-any.whl (38 kB)\n", "Using cached typer-0.16.0-py3-none-any.whl (46 kB)\n", "Using cached websockets-15.0.1-cp312-cp312-win_amd64.whl (176 kB)\n", "Using cached click-8.2.1-py3-none-any.whl (102 kB)\n", "Downloading huggingface_hub-0.32.4-py3-none-any.whl (512 kB)\n", "Using cached fsspec-2025.5.1-py3-none-any.whl (199 kB)\n", "Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)\n", "Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "Using cached rich-14.0.0-py3-none-any.whl (243 kB)\n", "Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Downloading ruff-0.11.13-py3-none-win_amd64.whl (11.5 MB)\n", "   ---------------------------------------- 0.0/11.5 MB ? eta -:--:--\n", "   -------------------- ------------------- 6.0/11.5 MB 28.4 MB/s eta 0:00:01\n", "   ------------------------------- -------- 9.2/11.5 MB 25.9 MB/s eta 0:00:01\n", "   ---------------------------------------- 11.5/11.5 MB 22.6 MB/s eta 0:00:00\n", "Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "Downloading uvicorn-0.34.3-py3-none-any.whl (62 kB)\n", "Using cached ffmpy-0.6.0-py3-none-any.whl (5.5 kB)\n", "Using cached filelock-3.18.0-py3-none-any.whl (16 kB)\n", "Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Installing collected packages: pytz, pydub, websockets, tzdata, tomlkit, shellingham, semantic-version, ruff, python-multipart, pillow, numpy, mdurl, markupsafe, groovy, fsspec, filelock, ffmpy, click, aiofiles, uvicorn, starlette, pandas, markdown-it-py, jinja2, huggingface-hub, safehttpx, rich, gradio-client, fastapi, typer, gradio\n", "\n", "   ----------------------------------------  0/31 [pytz]\n", "   --- ------------------------------------  3/31 [tzdata]\n", "   --- ------------------------------------  3/31 [tzdata]\n", "   --------- ------------------------------  7/31 [ruff]\n", "   ----------- ----------------------------  9/31 [pillow]\n", "   ----------- ----------------------------  9/31 [pillow]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ------------ --------------------------- 10/31 [numpy]\n", "   ---------------- ----------------------- 13/31 [groovy]\n", "   ------------------ --------------------- 14/31 [fsspec]\n", "   ------------------------ --------------- 19/31 [uvicorn]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   --------------------------- ------------ 21/31 [pandas]\n", "   ---------------------------- ----------- 22/31 [markdown-it-py]\n", "   ------------------------------ --------- 24/31 [huggingface-hub]\n", "   ------------------------------ --------- 24/31 [huggingface-hub]\n", "   -------------------------------- ------- 25/31 [safehttpx]\n", "   --------------------------------- ------ 26/31 [rich]\n", "   ------------------------------------ --- 28/31 [fastapi]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   -------------------------------------- - 30/31 [gradio]\n", "   ---------------------------------------- 31/31 [gradio]\n", "\n", "Successfully installed aiofiles-24.1.0 click-8.2.1 fastapi-0.115.12 ffmpy-0.6.0 filelock-3.18.0 fsspec-2025.5.1 gradio-5.33.0 gradio-client-1.10.2 groovy-0.1.2 huggingface-hub-0.32.4 jinja2-3.1.6 markdown-it-py-3.0.0 markupsafe-3.0.2 mdurl-0.1.2 numpy-2.3.0 pandas-2.3.0 pillow-11.2.1 pydub-0.25.1 python-multipart-0.0.20 pytz-2025.2 rich-14.0.0 ruff-0.11.13 safehttpx-0.1.6 semantic-version-2.10.0 shellingham-1.5.4 starlette-0.46.2 tomlkit-0.13.3 typer-0.16.0 tzdata-2025.2 uvicorn-0.34.3 websockets-15.0.1\n"]}], "source": ["# 安装 Gradio\n", "! pip install gradio"]}, {"cell_type": "markdown", "id": "1aa6c086-e0c5-4a25-a477-97ec2525d04f", "metadata": {}, "source": ["&emsp;&emsp;完整实现的代码如下："]}, {"cell_type": "code", "execution_count": 40, "id": "5216a90c-739b-42d6-8ad4-d55cf8ca0001", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2957933/3207025181.py:36: User<PERSON><PERSON>ning: You have not specified a value for the `type` parameter. Defaulting to the 'tuples' format for chatbot messages, but this is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style dictionaries with 'role' and 'content' keys.\n", "  chatbot = gr.<PERSON><PERSON><PERSON>(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://0.0.0.0:7860\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://localhost:7860/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Keyboard interruption in main thread... closing server.\n"]}, {"data": {"text/plain": []}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["import gradio as gr\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import SystemMessage, HumanMessage, AIMessage\n", "\n", "# ──────────────────────────────────────────────\n", "# 1. 模型、Prompt、Chain\n", "# ──────────────────────────────────────────────\n", "model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "parser = StrOutputParser()\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "        MessagesPlaceholder(variable_name=\"messages\"),  # 手动传入历史\n", "    ]\n", ")\n", "\n", "qa_chain = chatbot_prompt | model | parser   # LCEL 组合\n", "\n", "# ──────────────────────────────────────────────\n", "# 2. Gradio 组件\n", "# ──────────────────────────────────────────────\n", "CSS = \"\"\"\n", ".main-container {max-width: 1200px; margin: 0 auto; padding: 20px;}\n", ".header-text {text-align: center; margin-bottom: 20px;}\n", "\"\"\"\n", "\n", "def create_chatbot() -> gr.Blocks:\n", "    with gr.<PERSON><PERSON>(title=\"DeepSeek Chat\", css=CSS) as demo:\n", "        with gr.<PERSON>n(elem_classes=[\"main-container\"]):\n", "            gr.Markdown(\"# 🤖 <PERSON><PERSON>hain B站公开课 By九天Hector\", elem_classes=[\"header-text\"])\n", "            gr.Markdown(\"基于 LangChain LCEL 构建的流式对话机器人\", elem_classes=[\"header-text\"])\n", "\n", "            chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                height=500,\n", "                show_copy_button=True,\n", "                avatar_images=(\n", "                    \"https://cdn.jsdelivr.net/gh/twitter/twemoji@v14.0.2/assets/72x72/1f464.png\",\n", "                    \"https://cdn.jsdelivr.net/gh/twitter/twemoji@v14.0.2/assets/72x72/1f916.png\",\n", "                ),\n", "            )\n", "            msg = gr.Textbox(placeholder=\"请输入您的问题...\", container=False, scale=7)\n", "            submit = gr.<PERSON><PERSON>(\"发送\", scale=1, variant=\"primary\")\n", "            clear = gr.<PERSON>(\"清空\", scale=1)\n", "\n", "        # ---------------  状态：保存 messages_list  ---------------\n", "        state = gr.State([])          # 这里存放真正的 Message 对象列表\n", "\n", "        # ---------------  主响应函数（流式） ----------------------\n", "        async def respond(user_msg: str, chat_hist: list, messages_list: list):\n", "            # 1) 输入为空直接返回\n", "            if not user_msg.strip():\n", "                yield \"\", chat_hist, messages_list\n", "                return\n", "\n", "            # 2) 追加用户消息\n", "            messages_list.append(HumanMessage(content=user_msg))\n", "            chat_hist = chat_hist + [(user_msg, None)]\n", "            yield \"\", chat_hist, messages_list      # 先显示用户消息\n", "\n", "            # 3) 流式调用模型\n", "            partial = \"\"\n", "            async for chunk in qa_chain.astream({\"messages\": messages_list}):\n", "                partial += chunk\n", "                # 更新最后一条 AI 回复\n", "                chat_hist[-1] = (user_msg, partial)\n", "                yield \"\", chat_hist, messages_list\n", "\n", "            # 4) 完整回复加入历史，裁剪到最近 50 条\n", "            messages_list.append(AIMessage(content=partial))\n", "            messages_list = messages_list[-50:]\n", "\n", "            # 5) 最终返回（Gradio 需要把新的 state 传回）\n", "            yield \"\", chat_hist, messages_list\n", "\n", "        # ---------------  清空函数 -------------------------------\n", "        def clear_history():\n", "            return [], \"\", []          # 清空 Chatbot、输入框、messages_list\n", "\n", "        # ---------------  事件绑定 ------------------------------\n", "        msg.submit(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "        submit.click(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "        clear.click(clear_history, outputs=[chatbot, msg, state])\n", "\n", "    return demo\n", "\n", "\n", "# ──────────────────────────────────────────────\n", "# 3. 启动应用\n", "# ──────────────────────────────────────────────\n", "demo = create_chatbot()\n", "demo.launch(server_name=\"0.0.0.0\", server_port=7860, share=False, debug=True)\n"]}, {"cell_type": "markdown", "id": "fb3c47bc-59d5-4339-8a4c-4231b37e5eeb", "metadata": {}, "source": ["&emsp;&emsp;运行后，在浏览器访问`http://127.0.0.1:7860`即可进行问答交互。"]}, {"cell_type": "markdown", "id": "d9bd576d-4cbe-4b0b-b4e6-233a70f06719", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506121740968.png\" alt=\"image-20250612174010864\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "23804aae-73f4-4e81-b310-7675ec653d8d", "metadata": {}, "source": ["具体代码解释如下："]}, {"cell_type": "markdown", "id": "dd52a976-a597-49f1-80a1-23baa1de38b6", "metadata": {}, "source": ["##### 🧱 1. 模块说明\n", "\n", "```python\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import SystemMessage, HumanMessage, AIMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "import gradio as gr\n", "```\n", "\n", "* `init_chat_model`：初始化 DeepSeek 等聊天模型。\n", "* `ChatPromptTemplate`：用于构建聊天 Prompt 模板。\n", "* `MessagesPlaceholder`：用于占位历史消息。\n", "* `HumanMessage` / `AIMessage`：构建多轮消息结构。\n", "* `StrOutputParser`：将模型输出转换为字符串。\n", "* `gradio`：构建网页界面。\n", "\n", "---\n", "\n", "##### 🧠 2. Prompt 构建与模型初始化\n", "\n", "```python\n", "model = init_chat_model(\"deepseek-chat\", model_provider=\"deepseek\")\n", "parser = StrOutputParser()\n", "\n", "chatbot_prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessage(content=\"你叫小智，是一名乐于助人的助手。\"),\n", "    MessagesPlaceholder(variable_name=\"messages\"),\n", "])\n", "\n", "qa_chain = chatbot_prompt | model | parser\n", "```\n", "\n", "* **SystemMessage**：初始化系统角色设定（小智）。\n", "* **MessagesPlaceholder**：用变量名 `messages` 占位历史消息。\n", "* **qa\\_chain**：组合为 LangChain Expression Language 链。\n", "\n", "---\n", "\n", "##### 🔄 3. 手动管理消息列表\n", "\n", "```python\n", "state = gr.State([])\n", "```\n", "\n", "我们用 `gr.State` 存储所有历史消息（列表）。每次用户发送消息，都会：\n", "\n", "* append 一个 `HumanMessage`。\n", "* 流式调用模型并不断更新回复。\n", "* append 一个 `AIMessage`。\n", "* 最后裁剪：`messages_list = messages_list[-50:]`。\n", "\n", "---\n", "\n", "##### 🌊 4. 流式响应函数\n", "\n", "```python\n", "async def respond(user_msg: str, chat_hist: list, messages_list: list):\n", "    if not user_msg.strip():\n", "        yield \"\", chat_hist, messages_list\n", "        return\n", "\n", "    messages_list.append(HumanMessage(content=user_msg))\n", "    chat_hist = chat_hist + [(user_msg, None)]\n", "    yield \"\", chat_hist, messages_list\n", "\n", "    partial = \"\"\n", "    async for chunk in qa_chain.astream({\"messages\": messages_list}):\n", "        partial += chunk\n", "        chat_hist[-1] = (user_msg, partial)\n", "        yield \"\", chat_hist, messages_list\n", "\n", "    messages_list.append(AIMessage(content=partial))\n", "    messages_list = messages_list[-50:]\n", "    yield \"\", chat_hist, messages_list\n", "```\n", "\n", "* **支持 async 流式输出**。\n", "* **动态更新最后一轮对话**。\n", "* **通过 `yield` 实时反馈到前端**。\n", "\n", "---\n", "\n", "##### 🧼 5. 清空历史函数\n", "\n", "```python\n", "def clear_history():\n", "    return [], \"\", []\n", "```\n", "\n", "用于点击 \"清空\" 按钮时重置历史记录、输入框和消息状态。\n", "\n", "---\n", "\n", "##### 🧩 6. Gradio 界面构建\n", "\n", "```python\n", "msg.submit(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "submit.click(respond, [msg, chatbot, state], [msg, chatbot, state])\n", "clear.click(clear_history, outputs=[chatbot, msg, state])\n", "```\n", "\n", "* **事件绑定**：用户提交文本 → 调用 `respond` → 返回新状态。\n", "* **Gradio Chatbot 组件**：使用 `avatar_images` 设置人机头像。\n", "* **Gradio State**：跨组件共享并持久化消息列表。\n", "\n", "---\n", "\n", "##### ✅ 总结\n", "\n", "| 功能模块      | 实现方式                                              |\n", "| --------- | ------------------------------------------------- |\n", "| 对话模型      | DeepSeek via `init_chat_model`                    |\n", "| Prompt 模板 | ChatPromptTemplate + System + MessagesPlaceholder |\n", "| 消息管理      | 手动管理 + `gr.State` 保存并裁剪最近 50 条                    |\n", "| 多轮对话      | 用户/AI Message 列表构建并传入 LCEL 链                      |\n", "| UI 界面     | Gradio Blocks + Chatbot 组件 + 清空按钮                 |\n", "| 流式输出      | 使用 `qa_chain.astream()` 持续生成回复                    |"]}, {"cell_type": "markdown", "id": "e8fe3aed-b71c-44c8-98d9-1aaa23ec862b", "metadata": {}, "source": ["当然这只是最简单的问答机器人实现形式，实际上企业应用的问答机器人往往需要更加复杂的逻辑，比如用户权限管理、上下文记忆等，更多内容详见《大模型与Agent开发》课程讲解。"]}], "metadata": {"kernelspec": {"display_name": "LangGraphChatBot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}