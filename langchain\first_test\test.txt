3 程控命令
3.1 命令说明

45


3 程控命令

命令说明 ···················································· 45

通用命令 ···················································· 45

仪器子系统命令 ·············································· 50
3.1 命令说明

该章节提供了详细的命令参考信息，便于实现远程控制，具体包括：

完整的语法格式及参数列表；

对于SCPI 非标准命令，列出语法图示；

详细的功能描述及关联的命令说明；

支持的命令格式（设置 或 查询）；

参数说明，包含：数据类型、取值范围及默认值（单位）；

按键路径；

命令兼容的同类仪器中的仪器型号，若不注明则说明当前命令只适用于1466。

其它说明。

通用命令和仪器子系统命令章节部分，首先列出命令顺序列项，方便用户查询使用。
3.2 通用命令

通用命令用来控制仪器状态寄存器、状态报告、同步、数据存储及其它通用功能，
通用命令的用法和作用适用不同的仪器。所有的通用命令都可以通过命令字中的第一个“*”
被识别，在IEEE488.2 中详细定义了这些通用命令。以下是IEEE488.2 通用命令的解释和
说明：

*CLS ······················································· 46

*ESE ······················································· 46

*ESR? ······················································ 46

*IDN? ······················································· 47

*OPC ······················································· 47

*RCL ······················································· 47

*RST ······················································· 47

*SAV ······················································· 48

*SRE ······················································· 48

*STB? ······················································ 48

*TRG ······················································· 49

*TST? ······················································· 49

*WAI ······················································· 49

3 程控命令
3.2 通用命令
46






命令使用：
如不特别说明，命令可用于设置或者查询。
若一个命令仅用于设置或查询，或者启动一个事件，命令说明中会单独进行说明。

*CLS
功能描述：清除状态。设置状态字节（STB）、标准事件寄存器（ESR）和问题操作寄
存的事件部分为零。该命令不改变屏蔽和转移寄存器的值，并清除输出缓冲
区。

设置格式：*CLS

举例：    *CLS  清空仪器状态
说明：    仅设置。
*ESE <Value>
功能描述：设置或查询标准事件状态使能寄存器。0禁止。1使能。
设置格式： *ESE <value>
查询格式： *ESE?
参数说明：
<Value>
整型数值，各个位的二进制加权和，位映射将表3.1
范围 [ 0，255 ]。
举例:       *ESE 60 使能第4+8+16+32 相应位即第2、3、4、5位。
*ESR?
功能描述：读取事件状态寄存器的值，并清0该寄存器。见表3.1
查询格式： *ESR?
返回值：
整形数值，各个位的二进制加权和，位映射表见表3.1
范围 [ 0，255 ]。
说明：    仅查询。
表3.1
标准事件位映射
位
值
说明
0
1
操作完成
1
2
未用
提 示
3 程控命令
3.2 通用命令

47


2
4
查询错误
3
8
设备相关错误
4
16
执行错误
5
32
命令错误
6
64
本地键按下
7
128
开机
*IDN?
功能描述： 返回仪器标识。
查询格式： *IDN?
返回值：   <ID>："生产厂家,<仪器型号>,<串号>,<固件版本号>" 例如：Ceyear
Technologies,1466,2017008,1.0.0
举例：     *IDN?
说明：     仅查询。
*OPC
功能描述：该命令用于设置或查询信号发生器标准事件寄存器中的第0位。查询返回值
为1表示所有接收的指令已完成。
设置格式：*OPC
查询格式：*OPC?
举例：    *OPC? 如果等待的操作完成时，返回1，否则等待。
*RCL <Value>
功能描述： 该命令从指定的信号发生器内部寄存器调用仪器状态。
设置格式： *RCL <value>
参数说明：  整形数值
范围[ 0，99 ]。
举例：      *RCL 1
说明：      仅设置。
*RST
功能描述： 该命令完成信号发生器复位功能，将仪器复位到厂家默认的状态。
设置格式： *RST
举例：    *RST 复位信号发生器到厂家默认状态
说明：     仅设置。
