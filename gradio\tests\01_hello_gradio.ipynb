{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "示例1-创建一个简单的交互界面", "id": "41f57dd929240cc5"}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-04T06:49:01.350320Z", "start_time": "2025-07-04T06:49:01.175570Z"}}, "source": ["# 导入gradio库，这是创建机器学习/深度学习模型交互界面的工具\n", "import gradio as gr\n", "\n", "# 定义核心功能函数\n", "# 这个函数接收两个参数：name(名字)和intensity(问候强度)\n", "# 问候强度决定了返回结果中感叹号的数量\n", "def greet(name, intensity):\n", "    # 返回拼接的问候语，感叹号的数量由intensity决定\n", "    return \"Hello, \" + name + \"!\" * int(intensity)\n", "\n", "# 创建Gradio界面\n", "# Interface是Gradio的核心类，用于创建简单的输入-输出界面\n", "demo = gr.Interface(\n", "    # fn参数指定要包装的函数，这里是greet函数\n", "    fn=greet,\n", "    # inputs参数定义输入组件\n", "    # 这里使用列表指定两个输入组件：文本框(text)和滑块(slider)\n", "    # 第一个输入对应name参数，第二个输入对应intensity参数\n", "    inputs=[\"text\", \"slider\"],\n", "    # outputs参数定义输出组件\n", "    # 这里只有一个输出组件：文本(text)，对应greet函数的返回值\n", "    outputs=[\"text\"],\n", ")\n", "\n", "# 启动界面\n", "# share参数决定是否生成可共享的公共链接\n", "# 设置为True后会生成一个可以通过互联网访问的URL\n", "# demo.launch(share=True)\n", "demo.launch(share=False)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7872\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7872/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "execution_count": 15}, {"metadata": {}, "cell_type": "markdown", "source": ["建议使用实际类而非字符串，就像下面这样，而不是第一个例子中使用的字符串。\n", "\n", "当你使用字符串如 `\"text\"` 或 `\"slider\"` 时，$Gradio$ 会自动为你创建默认配置的组件。这种方式适合快速原型开发，但 不能自定义组件的属性。\n", "\n", "而使用 `gr.Textbox()`、`gr.Slider()` 等类实例化的方式，你可以：\n", "\n", "- 设置标签（label）\n", "- 设置默认值（value）\n", "- 控制输入输出区域大小（lines, max_lines）\n", "- 设置最小/最大值、步长等参数（min, max, step）"], "id": "cc697f7960889849"}, {"metadata": {}, "cell_type": "markdown", "source": "示例2-使用类实例化组件", "id": "3ba741be9149e7f5"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-04T06:35:17.055230Z", "start_time": "2025-07-04T06:35:16.867049Z"}}, "cell_type": "code", "source": ["import gradio as gr\n", "\n", "def greet(name, intensity):\n", "    return \"Hello, \" + name + \"!\" * intensity\n", "\n", "demo = gr.Interface(\n", "    fn=greet,\n", "    inputs=[gr.Text(label=\"名字\"), gr.Slider(label=\"打招呼程度\",value=2, minimum=1, maximum=10, step=1)],\n", "    outputs=[gr.Textbox(label=\"greeting\", lines=3)],\n", ")\n", "\n", "demo.launch()"], "id": "65640742d0a8c094", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7869\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7869/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": ["**补充说明：**\n", "\n", "1. **运行方式：**\n", "\n", "- 使用 python 01_hello_gradio.py 运行时，修改代码后需要重新启动才能看到变化\n", "- 使用 gradio 01_hello_gradio.py 运行时，修改代码后刷新浏览器即可看到变化\n", "\n", "2. **运行后会启动本地服务器，并提供两个访问地址：**\n", "\n", "- 本地地址（如 http://127.0.0.1:7860）只能在本机访问\n", "- 公共地址（如 https://xxx.gradio.app）可以通过互联网访问\n", "\n", "3. **浏览器打开地址后，可以看到包含两个输入控件（文本框和滑块）和一个输出区域的界面：**\n", "\n", "- 在文本框中输入名字\n", "- 拖动滑块选择问候强度\n", "- 点击\"Submit\"按钮后会显示问候语，感叹号的数量由滑块位置决定"], "id": "3ed2aa215fc83f51"}, {"metadata": {}, "cell_type": "markdown", "source": "", "id": "fb8ff2ddcb9fb559"}, {"metadata": {}, "cell_type": "markdown", "source": "示例3-多输入与多输出", "id": "fd4311e56022204b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-04T06:45:14.446664Z", "start_time": "2025-07-04T06:45:14.270524Z"}}, "cell_type": "code", "source": ["import gradio as gr\n", "\n", "def greet(name, is_morning, temperature):\n", "    salutation = \"Good morning\" if is_morning else \"Good evening\"\n", "    greeting = f\"{salutation} {name}. It is {temperature} degrees today\"\n", "    celsius = (temperature - 32) * 5 / 9\n", "    return greeting, round(c<PERSON><PERSON>, 2)\n", "\n", "demo = gr.Interface(\n", "    fn=greet,\n", "    inputs=[\"text\", \"checkbox\", gr.<PERSON><PERSON>(0, 100)],\n", "    outputs=[\"text\", \"number\"],\n", ")\n", "demo.launch()"], "id": "7e5a084af3fa612d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7870\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7870/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": "示例4-上传图片", "id": "f3bdedaf71e51535"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-04T06:52:34.886591Z", "start_time": "2025-07-04T06:52:34.615350Z"}}, "cell_type": "code", "source": ["import numpy as np\n", "import gradio as gr\n", "\n", "def sepia(input_img):\n", "    sepia_filter = np.array([\n", "        [0.393, 0.769, 0.189],\n", "        [0.349, 0.686, 0.168],\n", "        [0.272, 0.534, 0.131]\n", "    ])\n", "    sepia_img = input_img.dot(sepia_filter.T)\n", "    sepia_img /= sepia_img.max()\n", "    return sepia_img\n", "\n", "demo = gr.Interface(sepia, gr.Image(), \"image\")\n", "demo.launch()"], "id": "f0e09752054879fb", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7873\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7873/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "execution_count": 16}, {"metadata": {}, "cell_type": "markdown", "source": "示例5-上传文件", "id": "499d40d257c8c289"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-04T06:11:29.743386Z", "start_time": "2025-07-04T06:11:29.553188Z"}}, "cell_type": "code", "source": ["import gradio as gr\n", "import os\n", "\n", "# 定义处理函数\n", "def print_pdf_name(pdf_file):\n", "    \"\"\"\n", "    接收一个上传的文件路径，返回文件名\n", "    :param pdf_file: str, 上传的文件路径\n", "    :return: str, 文件名\n", "    \"\"\"\n", "    basename = os.path.basename(pdf_file)\n", "    # 使用 os.path.basename 提取文件名\n", "    return f\"你上传的文件是：{basename}\"\n", "\n", "# 创建 Gradio 界面\n", "demo = gr.Interface(\n", "    fn=print_pdf_name,              # 指定功能函数\n", "    inputs=gr.File(label=\"上传 PDF 文件\"),  # 允许用户上传文件\n", "    outputs=gr.Textbox(label=\"文件名\")     # 显示输出结果\n", ")\n", "\n", "# 启动界面\n", "demo.launch()\n"], "id": "3b1b1a19d1ccd9e6", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7863\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7863/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-08T08:43:14.580387Z", "start_time": "2025-07-08T08:43:09.570263Z"}}, "cell_type": "code", "source": ["import gradio as gr\n", "import fitz  # PyMuPDF\n", "import os\n", "from PIL import Image\n", "import io\n", "\n", "\n", "# 处理函数：读取 PDF 指定页内容（文本 + 图像）\n", "def preview_pdf(pdf_file, page_number):\n", "    \"\"\"\n", "    :param pdf_file: str, PDF 文件路径\n", "    :param page_number: int, 要预览的页码（从 1 开始）\n", "    :return: tuple(text, image)，分别为文本内容和图像预览\n", "    \"\"\"\n", "    try:\n", "        doc = fitz.open(pdf_file)\n", "        total_pages = len(doc)\n", "\n", "        if page_number < 1 or page_number > total_pages:\n", "            return f\"错误：页码超出范围，该文件共有 {total_pages} 页。\", None\n", "\n", "        page = doc.load_page(page_number - 1)  # 页面索引从 0 开始\n", "\n", "        # 提取文本内容\n", "        text = page.get_text()\n", "\n", "        # 提取页面图像（可选）\n", "        pix = page.get_pixmap(dpi=150)  # 设置 DPI 提高清晰度\n", "        img_data = pix.tobytes(\"png\")\n", "        img = Image.open(io.BytesIO(img_data))\n", "\n", "        doc.close()\n", "\n", "        return text, img\n", "\n", "    except Exception as e:\n", "        return f\"发生错误：{str(e)}\", None\n", "\n", "\n", "# 创建 Gradio 界面\n", "demo = gr.Interface(\n", "    fn=preview_pdf,\n", "    inputs=[\n", "        gr.File(label=\"上传 PDF 文件\"),\n", "        gr.Number(label=\"输入要预览的页码\", value=1, precision=0)\n", "    ],\n", "    outputs=[\n", "        gr.Textbox(label=\"提取的文本内容\", lines=10),\n", "        gr.Image(label=\"PDF 页面图像预览\")\n", "    ],\n", "    examples=[\n", "        [r\"D:\\liuzhihao\\MyProject\\LLM\\example\\input\\Ceyear\\1466系列信号发生器程控手册.pdf\", 1],\n", "        [r\"D:\\liuzhihao\\MyProject\\LLM\\example\\input\\Keysight\\9018-18183.pdf\", 2]\n", "    ],\n", "    title=\"📄 PDF 预览器\",\n", "    description=\"上传 PDF 文件，并输入页码以预览其内容（支持文本和图像）。\"\n", ")\n", "\n", "# 启动界面\n", "if __name__ == \"__main__\":\n", "    demo.launch()\n"], "id": "ab4a9d28cb2a825a", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7860\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"http://127.0.0.1:7860/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 1}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}