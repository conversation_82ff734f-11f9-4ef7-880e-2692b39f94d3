{"cells": [{"cell_type": "markdown", "id": "7da753e4-a17e-4d79-89f1-2ccee935b4e4", "metadata": {}, "source": ["## <font face=\"仿宋\">课程说明："]}, {"cell_type": "markdown", "id": "b316716c-9254-484c-b19c-061fddb6e502", "metadata": {}, "source": ["- 体验课内容节选自[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)完整版付费课程"]}, {"cell_type": "markdown", "id": "a55f6b3b-4bdb-4cf9-80f4-4b0950d56eb8", "metadata": {}, "source": ["&emsp;&emsp;体验课时间有限，若想深度学习大模型技术，欢迎大家报名由我主讲的[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)"]}, {"cell_type": "markdown", "id": "395b3522-d8b3-4246-8be6-8ef0464eaa40", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171642034.jpg\" alt=\"bef0897853f861af5f4211442be446b\" style=\"zoom:15%;\" />"]}, {"cell_type": "markdown", "id": "a1de4c0f-25cc-43ca-a946-c1dd4e62386b", "metadata": {}, "source": ["**[《2025大模型Agent智能体开发实战》(夏季班)](https://ix9mq.xetslk.com/s/2lPSMo)为【100+小时】体系大课，总共20大模块精讲精析，零基础直达大模型企业级应用！**"]}, {"cell_type": "markdown", "id": "78151d8d-a1b8-4409-a321-d742e80e273c", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172010074.png\" alt=\"a55d48e952ed59f8d93e050594843bc\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "ca3b720c-bcad-4ed4-a26b-00567f46645d", "metadata": {}, "source": ["部分项目成果演示"]}, {"cell_type": "code", "execution_count": 3, "id": "236f0661-2453-4a35-9749-b8e1d3680ca4", "metadata": {}, "outputs": [], "source": ["from IPython.display import Video"]}, {"cell_type": "markdown", "id": "619d1aca-f86a-4912-a197-b608b066f596", "metadata": {}, "source": ["- **MateGen项目演示**"]}, {"cell_type": "code", "execution_count": 4, "id": "8ce8298a-875e-4acb-84a4-347a989e36e0", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/MG%E6%BC%94%E7%A4%BA%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "21220db2-bf1c-4669-90e9-46be9510cdd4", "metadata": {}, "source": ["- **智能客服项目演示**"]}, {"cell_type": "code", "execution_count": 5, "id": "4f3dce8f-d1ec-4fad-930d-5d0b08a31986", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E6%A1%88%E4%BE%8B%E8%A7%86%E9%A2%91.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "c2f0f7bb-2893-471a-81f8-508e0ce3f10a", "metadata": {}, "source": ["- **Dify项目演示**"]}, {"cell_type": "code", "execution_count": 6, "id": "315526bc-9ad2-4772-9184-eea4a2897a35", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/2f1b47f42c65fd59e8d3a83e6cb9f13b_raw.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "4aace7fd-3119-4c7b-9d33-fa3c118c8205", "metadata": {}, "source": ["- **LangChain&LangGraph搭建Multi-Agnet**"]}, {"cell_type": "code", "execution_count": 7, "id": "1ab0e566-79ce-4714-87a4-0cf7ebad6524", "metadata": {}, "outputs": [{"data": {"text/html": ["<video src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\" controls  width=\"800\"  height=\"400\">\n", "      Your browser does not support the <code>video</code> element.\n", "    </video>"], "text/plain": ["<IPython.core.display.Video object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Video(\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/%E5%8F%AF%E8%A7%86%E5%8C%96%E6%95%B0%E6%8D%AE%E5%88%86%E6%9E%90Multi-Agent%E6%95%88%E6%9E%9C%E6%BC%94%E7%A4%BA%E6%95%88%E6%9E%9C.mp4\", width=800, height=400)"]}, {"cell_type": "markdown", "id": "ab63ad11-1e0d-4a9c-bfab-f2cb3e26189c", "metadata": {}, "source": ["此外，若是对大模型底层原理感兴趣，也欢迎报名由我和菜菜老师共同主讲的[《2025大模型原理与实战课程》(夏季班)](https://ix9mq.xetslk.com/s/3VITgV)"]}, {"cell_type": "markdown", "id": "bffc79ee-7458-4103-bc34-0b2d513dac0c", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171650709.png\" alt=\"4a11b7807056e9f5b281278c0e37dad\" style=\"zoom:20%;\" />"]}, {"cell_type": "markdown", "id": "7b480939-0d7b-4205-acd3-823406118923", "metadata": {}, "source": ["**两门大模型课程夏季班目前上新特惠+618年中钜惠双惠叠加，合购还有更多优惠哦~<span style=\"color:red;\">详细信息扫码添加助教，回复“大模型”，即可领取课程大纲&查看课程详情👇</span>**"]}, {"cell_type": "markdown", "id": "262b64bf-5409-4276-a036-b6bd69040267", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171644321.jpeg\" alt=\"img\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "e458cfda-268b-4534-9c07-f496dc607f1a", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506101749045.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "f271ad3a-f365-4410-ab51-0eaf2319aafe", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "6331180f-5f74-464b-93ae-156ea561ed59", "metadata": {}, "source": ["# <center> LangChain快速入门与Agent开发实战\n", "# <center> Part 7.Lang<PERSON>hain接入MCP技术实现流程"]}, {"cell_type": "markdown", "id": "fd21736b-d47f-4b70-99eb-9bf82824d2eb", "metadata": {}, "source": ["&emsp;&emsp;MCP，全称是Model Context Protocol，模型上下文协议，由Claude母公司Anthropic于去年11月正式提出。"]}, {"cell_type": "markdown", "id": "a4bfa825-63af-4334-a87f-4bdcd17d22d2", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250318201338022.png\" alt=\"image-20250318201338022\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "1b5c5b64-4210-4f5e-bf4a-57e0b4d3c7ff", "metadata": {}, "source": ["> - Anthropic MCP发布通告：https://www.anthropic.com/news/model-context-protocol\n", "> - MCP GitHub主页：https://github.com/modelcontextprotocol"]}, {"cell_type": "markdown", "id": "4dbc7dc5-8a0e-4bee-99f5-0fedde324fe9", "metadata": {}, "source": ["MCP的核心作用，是统一了Agent开发过程中，大模型调用外部工具的技术实现流程，从而大幅提高Agent开发效率。在MCP诞生之前，不同的外部工具各有不同的调用方法，要连接这些外部工具开发Agent，就必须“每一把锁单独配一把钥匙”，开发工作非常繁琐："]}, {"cell_type": "markdown", "id": "665a85c4-bbb5-4d5e-8b8f-e73846a70512", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250403170211085.png\" alt=\"image-20250403170211085\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "18faecd8-9cf9-45b8-8993-55b4fd880d0d", "metadata": {}, "source": ["而MCP的诞生，则统一了这些外部工具的调用流程，使得无论什么样的工具，都可以借助MCP技术按照统一的一个流程快速接入到大模型中，从而大幅加快Agent开发效率。这就好比现在很多设备都可以使用type-c和电脑连接类似。"]}, {"cell_type": "markdown", "id": "b15868c7-439d-4815-93a8-b2cdf1027cdb", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250403170238895.png\" alt=\"image-20250403170238895\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "34167e06-b6ec-43dd-a2cb-c3e3b57f440e", "metadata": {}, "source": ["从技术实现角度来看，我们可以将MCP看成是Function calling的一种封装，通过server-client架构和一整套开发工具，来规范化Function calling开发流程。"]}, {"cell_type": "markdown", "id": "8b09fb8e-707f-4995-88c6-862a9f5bb0f7", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250318202116026.png\" alt=\"image-20250318202116026\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "5ac04bc8-aed5-4d27-9ad9-a4acd3e464f8", "metadata": {}, "source": ["&emsp;&emsp;此前我开设过很多MCP相关公开课，在学习本节内容前，建议先简单了解MCP完整技术体系，可以选择以下公开课进行参考："]}, {"cell_type": "markdown", "id": "aa582f90-d380-4441-846c-7f65b3fa39da", "metadata": {}, "source": ["- 《7分钟讲清楚MCP是什么？》：https://www.bilibili.com/video/BV1uXQzYaEpJ/?\n", "- 《MCP技术开发入门实战！》：https://www.bilibili.com/video/BV1NLXCYTEbj/\n", "- 《MCP企业级智能体开发实战！》：https://www.bilibili.com/video/BV1n1ZuYjEzf/\n", "- 《主流客户端接入MCP实战》：https://www.bilibili.com/video/BV1dCo7YdEgK/\n", "- 《从零到一开发&部署专属MCP工具》：https://www.bilibili.com/video/BV1VHL6zsE5F/\n", "- 《流式HTTP MCP服务器开发流程》：https://www.bilibili.com/video/BV1P7VSzwEXL/"]}, {"cell_type": "markdown", "id": "7910a022-7471-400c-921f-80313605af74", "metadata": {}, "source": ["MCP技术参考资料详见大模型技术社区。"]}, {"cell_type": "markdown", "id": "7dd96ba6-48fd-4e9a-b6c2-77d9e419a5ac", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171816930.png\" alt=\"57d34aa34ec04a98b020018c55b242d\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "335d5845-7fac-44a7-bbad-567a3115dc78", "metadata": {}, "source": ["### 一、MCP基础实现流程"]}, {"cell_type": "markdown", "id": "1961c137-76e8-457b-afb7-83a43e74b783", "metadata": {}, "source": ["&emsp;&emsp;`langchain-mcp-adapters` 项目主要为`<PERSON><PERSON><PERSON><PERSON>`和`LangGraph`提供`MCP`的接入和兼容接口，其工作流程主要如下图所示："]}, {"cell_type": "markdown", "id": "6a903574-cf5c-4175-834a-d1177d4537fc", "metadata": {}, "source": ["<div align=\"center\"><img src=\"https://muyu20241105.oss-cn-beijing.aliyuncs.com/images/202506091749172.png\" width=\"80%\"></div>\n", "\n", "&emsp;&emsp;"]}, {"cell_type": "markdown", "id": "70a60f31-8ad5-4a6d-948d-4a210fff405f", "metadata": {}, "source": ["&emsp;&emsp;实际上`load_mcp_tools()` 返回的是标准的 `Lang<PERSON>hain` 工具，所以是完全可以直接在`LangChain`环境中进行使用的。同时，完全支持`stdio`、`Http SSE`和`Streamable HTTP`三种不同的通讯协议。"]}, {"cell_type": "markdown", "id": "7ec46df9-3111-4084-a6dd-48f7b5c79cc6", "metadata": {}, "source": ["&emsp;&emsp;接下来，我们先尝试手动实现一遍`MCP`实践流程，然后再考虑将已经部署好的`server`带入中，作为`tools`进行调用。"]}, {"cell_type": "markdown", "id": "effffb3b-7a50-4b5d-9b98-26f84cc835c2", "metadata": {}, "source": ["&emsp;&emsp;一个极简的天气查询MCP调用流程如下："]}, {"cell_type": "markdown", "id": "abdcd69d-fcf8-4aec-94a6-f4ac57585791", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250318172155677.png\" alt=\"image-20250318172155677\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "bd62f749-82f6-4829-8034-c08bbd39e7f5", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172023699.png\" alt=\"image-20250617202327646\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "086d07e0-68e3-4a25-a563-56b8a495fe52", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "ac601f7c-7c02-47c2-97b2-2b49343b9ff5", "metadata": {}, "source": ["- 借助uv创建MCP运行环境"]}, {"cell_type": "markdown", "id": "45cabc50-64a4-436b-a480-e16b8247b853", "metadata": {}, "source": ["**方法 1：使用 `pip` 安装（适用于已安装 `pip` 的系统）**\n", "\n", "```bash\n", "pip install uv\n", "```\n", "\n", "**方法 2：使用 `curl` 直接安装**\n", "\n", "如果你的系统没有 `pip`，可以直接运行：\n", "\n", "```bash\n", "curl -LsSf https://astral.sh/uv/install.sh | sh\n", "```\n", "\n", "这会自动下载 `uv` 并安装到 `/usr/local/bin`。"]}, {"cell_type": "markdown", "id": "cc129a72-b7fe-410c-b777-eb2c91d46a30", "metadata": {}, "source": ["- 创建 MCP 客户端项目"]}, {"cell_type": "markdown", "id": "902af937-7a18-4e59-af27-5d0ab43c150a", "metadata": {}, "source": ["```bash\n", "# 创建项目目录\n", "uv init mcp-client\n", "cd mcp-client\n", "```"]}, {"cell_type": "markdown", "id": "a1544202-244b-4f2a-a8eb-191702796d54", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202503171503701.png\" alt=\"image-20250317150300621\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "39d32fd2-4c93-4f02-b3a7-ebc6b0b53d09", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171833764.png\" alt=\"image-20250617183314728\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "f2ad6d61-6fab-405f-877e-9e5aede605a7", "metadata": {}, "source": ["- 创建MCP客户端虚拟环境"]}, {"cell_type": "markdown", "id": "2a6aee97-d706-432d-9a9b-de7fa69ef0d9", "metadata": {}, "source": ["```bash\n", "# 创建虚拟环境\n", "uv venv\n", "\n", "# 激活虚拟环境\n", "source .venv/bin/activate\n", "```"]}, {"cell_type": "markdown", "id": "15581215-a8ac-4a6b-97b8-d23e856c09d6", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202503171509604.png\" alt=\"image-20250317150947534\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "26c53da1-489e-4610-8c04-11503af2a743", "metadata": {}, "source": ["然后即可通过add方法在虚拟环境中安装相关的库。"]}, {"cell_type": "markdown", "id": "f19fca88-633a-420f-800a-8e70766eb5d4", "metadata": {}, "source": ["```bash\n", "# 安装 MCP SDK\n", "uv add mcp openai python-dotenv httpx\n", "```"]}, {"cell_type": "markdown", "id": "1800a8dd-99a6-4da5-ac7f-6d49554c3bc6", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171833398.png\" alt=\"image-20250617183357233\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "c04418a9-a2d5-4024-aae2-d32c13ad51f7", "metadata": {}, "source": ["- 编写用于天气查询的server服务器代码："]}, {"cell_type": "markdown", "id": "1d9a8532-61c1-4c1a-8681-51a83b7a93ce", "metadata": {}, "source": ["这里我们需要在服务器上创建一个server.py，并写入如下代码："]}, {"cell_type": "markdown", "id": "bd889dae-6699-4856-8a28-e0766cc524c3", "metadata": {}, "source": ["```python\n", "import json\n", "import httpx\n", "from typing import Any\n", "from mcp.server.fastmcp import FastMCP\n", "\n", "# 初始化 MCP 服务器\n", "mcp = FastMCP(\"WeatherServer\")\n", "\n", "# OpenWeather API 配置\n", "OPENWEATHER_API_BASE = \"https://api.openweathermap.org/data/2.5/weather\"\n", "API_KEY = \"YOUR_API_KEY\"  # 请替换为你自己的 OpenWeather API Key\n", "USER_AGENT = \"weather-app/1.0\"\n", "\n", "async def fetch_weather(city: str) -> dict[str, Any] | None:\n", "    \"\"\"\n", "    从 OpenWeather API 获取天气信息。\n", "    :param city: 城市名称（需使用英文，如 Beijing）\n", "    :return: 天气数据字典；若出错返回包含 error 信息的字典\n", "    \"\"\"\n", "    params = {\n", "        \"q\": city,\n", "        \"appid\": API_KEY,\n", "        \"units\": \"metric\",\n", "        \"lang\": \"zh_cn\"\n", "    }\n", "    headers = {\"User-Agent\": USER_AGENT}\n", "\n", "    async with httpx.AsyncClient() as client:\n", "        try:\n", "            response = await client.get(OPENWEATHER_API_BASE, params=params, headers=headers, timeout=30.0)\n", "            response.raise_for_status()\n", "            return response.json()  # 返回字典类型\n", "        except httpx.HTTPStatusError as e:\n", "            return {\"error\": f\"HTTP 错误: {e.response.status_code}\"}\n", "        except Exception as e:\n", "            return {\"error\": f\"请求失败: {str(e)}\"}\n", "\n", "def format_weather(data: dict[str, Any] | str) -> str:\n", "    \"\"\"\n", "    将天气数据格式化为易读文本。\n", "    :param data: 天气数据（可以是字典或 JSON 字符串）\n", "    :return: 格式化后的天气信息字符串\n", "    \"\"\"\n", "    # 如果传入的是字符串，则先转换为字典\n", "    if isinstance(data, str):\n", "        try:\n", "            data = json.loads(data)\n", "        except Exception as e:\n", "            return f\"无法解析天气数据: {e}\"\n", "\n", "    # 如果数据中包含错误信息，直接返回错误提示\n", "    if \"error\" in data:\n", "        return f\"⚠️ {data['error']}\"\n", "\n", "    # 提取数据时做容错处理\n", "    city = data.get(\"name\", \"未知\")\n", "    country = data.get(\"sys\", {}).get(\"country\", \"未知\")\n", "    temp = data.get(\"main\", {}).get(\"temp\", \"N/A\")\n", "    humidity = data.get(\"main\", {}).get(\"humidity\", \"N/A\")\n", "    wind_speed = data.get(\"wind\", {}).get(\"speed\", \"N/A\")\n", "    # weather 可能为空列表，因此用 [0] 前先提供默认字典\n", "    weather_list = data.get(\"weather\", [{}])\n", "    description = weather_list[0].get(\"description\", \"未知\")\n", "\n", "    return (\n", "        f\"🌍 {city}, {country}\\n\"\n", "        f\"🌡 温度: {temp}°C\\n\"\n", "        f\"💧 湿度: {humidity}%\\n\"\n", "        f\"🌬 风速: {wind_speed} m/s\\n\"\n", "        f\"🌤 天气: {description}\\n\"\n", "    )\n", "\n", "@mcp.tool()\n", "async def query_weather(city: str) -> str:\n", "    \"\"\"\n", "    输入指定城市的英文名称，返回今日天气查询结果。\n", "    :param city: 城市名称（需使用英文）\n", "    :return: 格式化后的天气信息\n", "    \"\"\"\n", "    data = await fetch_weather(city)\n", "    return format_weather(data)\n", "\n", "if __name__ == \"__main__\":\n", "    # 以标准 I/O 方式运行 MCP 服务器\n", "    mcp.run(transport='stdio')\n", "```"]}, {"cell_type": "markdown", "id": "16df50c8-1e64-4b9e-b69f-1422ee9ea8fd", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20250318174907749.png\" alt=\"image-20250318174907749\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "95f325be-6285-4c51-8142-30546aac59dc", "metadata": {}, "source": ["- 创建write_server.py"]}, {"cell_type": "markdown", "id": "4d531703-f415-4ccd-8e89-d8d08b3dcbfc", "metadata": {}, "source": ["&emsp;&emsp;为了更好的测试多MCP工具调用流程，这里我们继续创建一个write_server.py服务器："]}, {"cell_type": "markdown", "id": "3cefe60e-6361-4416-8826-2eaeac67e626", "metadata": {}, "source": ["```python\n", "import json\r\n", "import httpx\r\n", "from typing import Any\r\n", "from mcp.server.fastmcp import FastMCP\r\n", "\r\n", "# 初始化 MCP 服务器\r\n", "mcp = FastMCP(\"WriteServer\")\r\n", "USER_AGENT = \"write-app/1.0\"\r\n", "\r\n", "@mcp.tool()\r\n", "async def write_file(content: str) -> str:\r\n", "    \"\"\"\r\n", "    将指定内容写入本地文件。\r\n", "    :param content: 必要参数，字符串类型，用于表示需要写入文档的具体内容。\r\n", "    :return：是否成功写入\r\n", "    \"\"\"\r\n", "    return \"已成功写入本地文件。\"\r\n", "\r\n", "if __name__ == \"__main__\":\r\n", "    # 以标准 I/O 方式运行 MCP 服务器\r\n", "    mcp.run(transport='stdio')\n", "```"]}, {"cell_type": "markdown", "id": "8340ac06-de3f-4b94-b7b5-a898c156de97", "metadata": {}, "source": ["- 天气查询客户端client创建流程"]}, {"cell_type": "markdown", "id": "7acf8606-20cc-4ac1-bd3f-59edc777def6", "metadata": {}, "source": ["&emsp;&emsp;然后创建一个可以和server进行通信的客户端，需要注意的是，该客户端需要包含大模型调用的基础信息。我们需要编写一个client.py脚本，这个脚本内容非常复杂，完整代码如下："]}, {"cell_type": "markdown", "id": "51602d5c-2da2-401b-bf8c-0e6aedd2af9a", "metadata": {}, "source": ["```python\n", "import asyncio\r\n", "import json\r\n", "import logging\r\n", "import os\r\n", "import shutil\r\n", "from contextlib import AsyncExitStack\r\n", "from typing import Any, Dict, List, Optional\r\n", "\r\n", "import httpx\r\n", "from dotenv import load_dotenv\r\n", "from openai import OpenAI  # OpenAI Python SDK\r\n", "from mcp import ClientSession, StdioServerParameters\r\n", "from mcp.client.stdio import stdio_client\r\n", "\r\n", "# Configure logging\r\n", "logging.basicConfig(\r\n", "    level=logging.INFO, format=\"%(asctime)s - %(levelname)s - %(message)s\"\r\n", ")\r\n", "\r\n", "\r\n", "# =============================\r\n", "# 配置加载类（支持环境变量及配置文件）\r\n", "# =============================\r\n", "class Configuration:\r\n", "    \"\"\"管理 MCP 客户端的环境变量和配置文件\"\"\"\r\n", "\r\n", "    def __init__(self) -> None:\r\n", "        load_dotenv()\r\n", "        # 从环境变量中加载 API key, base_url 和 model\r\n", "        self.api_key = os.getenv(\"LLM_API_KEY\")\r\n", "        self.base_url = os.getenv(\"BASE_URL\")\r\n", "        self.model = os.getenv(\"MODEL\")\r\n", "        if not self.api_key:\r\n", "            raise ValueError(\"❌ 未找到 LLM_API_KEY，请在 .env 文件中配置\")\r\n", "\r\n", "    @staticmethod\r\n", "    def load_config(file_path: str) -> Dict[str, Any]:\r\n", "        \"\"\"\r\n", "        从 JSON 文件加载服务器配置\r\n", "        \r\n", "        Args:\r\n", "            file_path: JSON 配置文件路径\r\n", "        \r\n", "        Returns:\r\n", "            包含服务器配置的字典\r\n", "        \"\"\"\r\n", "        with open(file_path, \"r\") as f:\r\n", "            return json.load(f)\r\n", "\r\n", "\r\n", "# =============================\r\n", "# MCP 服务器客户端类\r\n", "# =============================\r\n", "class Server:\r\n", "    \"\"\"管理单个 MCP 服务器连接和工具调用\"\"\"\r\n", "\r\n", "    def __init__(self, name: str, config: Dict[str, Any]) -> None:\r\n", "        self.name: str = name\r\n", "        self.config: Dict[str, Any] = config\r\n", "        self.session: Optional[ClientSession] = None\r\n", "        self.exit_stack: AsyncExitStack = AsyncExitStack()\r\n", "        self._cleanup_lock = asyncio.Lock()\r\n", "\r\n", "    async def initialize(self) -> None:\r\n", "        \"\"\"初始化与 MCP 服务器的连接\"\"\"\r\n", "        # command 字段直接从配置获取\r\n", "        command = self.config[\"command\"]\r\n", "        if command is None:\r\n", "            raise ValueError(\"command 不能为空\")\r\n", "\r\n", "        server_params = StdioServerParameters(\r\n", "            command=command,\r\n", "            args=self.config[\"args\"],\r\n", "            env={**os.environ, **self.config[\"env\"]} if self.config.get(\"env\") else None,\r\n", "        )\r\n", "        try:\r\n", "            stdio_transport = await self.exit_stack.enter_async_context(\r\n", "                stdio_client(server_params)\r\n", "            )\r\n", "            read_stream, write_stream = stdio_transport\r\n", "            session = await self.exit_stack.enter_async_context(\r\n", "                ClientSession(read_stream, write_stream)\r\n", "            )\r\n", "            await session.initialize()\r\n", "            self.session = session\r\n", "        except Exception as e:\r\n", "            logging.error(f\"Error initializing server {self.name}: {e}\")\r\n", "            await self.cleanup()\r\n", "            raise\r\n", "\r\n", "    async def list_tools(self) -> List[Any]:\r\n", "        \"\"\"获取服务器可用的工具列表\r\n", "\r\n", "        Returns:\r\n", "            工具列表\r\n", "        \"\"\"\r\n", "        if not self.session:\r\n", "            raise RuntimeError(f\"Server {self.name} not initialized\")\r\n", "        tools_response = await self.session.list_tools()\r\n", "        tools = []\r\n", "        for item in tools_response:\r\n", "            if isinstance(item, tuple) and item[0] == \"tools\":\r\n", "                for tool in item[1]:\r\n", "                    tools.append(Tool(tool.name, tool.description, tool.inputSchema))\r\n", "        return tools\r\n", "\r\n", "    async def execute_tool(\r\n", "        self, tool_name: str, arguments: Dict[str, Any], retries: int = 2, delay: float = 1.0\r\n", "    ) -> Any:\r\n", "        \"\"\"执行指定工具，并支持重试机制\r\n", "\r\n", "        Args:\r\n", "            tool_name: 工具名称\r\n", "            arguments: 工具参数\r\n", "            retries: 重试次数\r\n", "            delay: 重试间隔秒数\r\n", "\r\n", "        Returns:\r\n", "            工具调用结果\r\n", "        \"\"\"\r\n", "        if not self.session:\r\n", "            raise RuntimeError(f\"Server {self.name} not initialized\")\r\n", "        attempt = 0\r\n", "        while attempt < retries:\r\n", "            try:\r\n", "                logging.info(f\"Executing {tool_name} on server {self.name}...\")\r\n", "                result = await self.session.call_tool(tool_name, arguments)\r\n", "                return result\r\n", "            except Exception as e:\r\n", "                attempt += 1\r\n", "                logging.warning(\r\n", "                    f\"Error executing tool: {e}. Attempt {attempt} of {retries}.\"\r\n", "                )\r\n", "                if attempt < retries:\r\n", "                    logging.info(f\"Retrying in {delay} seconds...\")\r\n", "                    await asyncio.sleep(delay)\r\n", "                else:\r\n", "                    logging.error(\"Max retries reached. Failing.\")\r\n", "                    raise\r\n", "\r\n", "    async def cleanup(self) -> None:\r\n", "        \"\"\"清理服务器资源\"\"\"\r\n", "        async with self._cleanup_lock:\r\n", "            try:\r\n", "                await self.exit_stack.aclose()\r\n", "                self.session = None\r\n", "            except Exception as e:\r\n", "                logging.error(f\"Error during cleanup of server {self.name}: {e}\")\r\n", "\r\n", "\r\n", "# =============================\r\n", "# 工具封装类\r\n", "# =============================\r\n", "class Tool:\r\n", "    \"\"\"封装 MCP 返回的工具信息\"\"\"\r\n", "\r\n", "    def __init__(self, name: str, description: str, input_schema: Dict[str, Any]) -> None:\r\n", "        self.name: str = name\r\n", "        self.description: str = description\r\n", "        self.input_schema: Dict[str, Any] = input_schema\r\n", "\r\n", "    def format_for_llm(self) -> str:\r\n", "        \"\"\"生成用于 LLM 提示的工具描述\"\"\"\r\n", "        args_desc = []\r\n", "        if \"properties\" in self.input_schema:\r\n", "            for param_name, param_info in self.input_schema[\"properties\"].items():\r\n", "                arg_desc = f\"- {param_name}: {param_info.get('description', 'No description')}\"\r\n", "                if param_name in self.input_schema.get(\"required\", []):\r\n", "                    arg_desc += \" (required)\"\r\n", "                args_desc.append(arg_desc)\r\n", "        return f\"\"\"\r\n", "Tool: {self.name}\r\n", "Description: {self.description}\r\n", "Arguments:\r\n", "{chr(10).join(args_desc)}\r\n", "\"\"\"\r\n", "\r\n", "\r\n", "# =============================\r\n", "# LLM 客户端封装类（使用 OpenAI SDK）\r\n", "# =============================\r\n", "class LLMClient:\r\n", "    \"\"\"使用 OpenAI SDK 与大模型交互\"\"\"\r\n", "\r\n", "    def __init__(self, api_key: str, base_url: Optional[str], model: str) -> None:\r\n", "        self.client = OpenAI(api_key=api_key, base_url=base_url)\r\n", "        self.model = model\r\n", "\r\n", "    def get_response(self, messages: List[Dict[str, Any]], tools: Optional[List[Dict[str, Any]]] = None) -> Any:\r\n", "        \"\"\"\r\n", "        发送消息给大模型 API，支持传入工具参数（function calling 格式）\r\n", "        \"\"\"\r\n", "        payload = {\r\n", "            \"model\": self.model,\r\n", "            \"messages\": messages,\r\n", "            \"tools\": tools,\r\n", "        }\r\n", "        try:\r\n", "            response = self.client.chat.completions.create(**payload)\r\n", "            return response\r\n", "        except Exception as e:\r\n", "            logging.error(f\"Error during LLM call: {e}\")\r\n", "            raise\r\n", "\r\n", "\r\n", "# =============================\r\n", "# 多服务器 MCP 客户端类（集成配置文件、工具格式转换与 OpenAI SDK 调用）\r\n", "# =============================\r\n", "class MultiServerMCPClient:\r\n", "    def __init__(self) -> None:\r\n", "        \"\"\"\r\n", "        管理多个 MCP 服务器，并使用 OpenAI Function Calling 风格的接口调用大模型\r\n", "        \"\"\"\r\n", "        self.exit_stack = AsyncExitStack()\r\n", "        config = Configuration()\r\n", "        self.openai_api_key = config.api_key\r\n", "        self.base_url = config.base_url\r\n", "        self.model = config.model\r\n", "        self.client = LLMClient(self.openai_api_key, self.base_url, self.model)\r\n", "        # (server_name -> Server 对象)\r\n", "        self.servers: Dict[str, Server] = {}\r\n", "        # 各个 server 的工具列表\r\n", "        self.tools_by_server: Dict[str, List[Any]] = {}\r\n", "        self.all_tools: List[Dict[str, Any]] = []\r\n", "\r\n", "    async def connect_to_servers(self, servers_config: Dict[str, Any]) -> None:\r\n", "        \"\"\"\r\n", "        根据配置文件同时启动多个服务器并获取工具\r\n", "        servers_config 的格式为：\r\n", "        {\r\n", "          \"mcpServers\": {\r\n", "              \"sqlite\": { \"command\": \"uvx\", \"args\": [ ... ] },\r\n", "              \"puppeteer\": { \"command\": \"npx\", \"args\": [ ... ] },\r\n", "              ...\r\n", "          }\r\n", "        }\r\n", "        \"\"\"\r\n", "        mcp_servers = servers_config.get(\"mcpServers\", {})\r\n", "        for server_name, srv_config in mcp_servers.items():\r\n", "            server = Server(server_name, srv_config)\r\n", "            await server.initialize()\r\n", "            self.servers[server_name] = server\r\n", "            tools = await server.list_tools()\r\n", "            self.tools_by_server[server_name] = tools\r\n", "\r\n", "            for tool in tools:\r\n", "                # 统一重命名：serverName_toolName\r\n", "                function_name = f\"{server_name}_{tool.name}\"\r\n", "                self.all_tools.append({\r\n", "                    \"type\": \"function\",\r\n", "                    \"function\": {\r\n", "                        \"name\": function_name,\r\n", "                        \"description\": tool.description,\r\n", "                        \"input_schema\": tool.input_schema\r\n", "                    }\r\n", "                })\r\n", "\r\n", "        # 转换为 OpenAI Function Calling 所需格式\r\n", "        self.all_tools = await self.transform_json(self.all_tools)\r\n", "\r\n", "        logging.info(\"\\n✅ 已连接到下列服务器:\")\r\n", "        for name in self.servers:\r\n", "            srv_cfg = mcp_servers[name]\r\n", "            logging.info(f\"  - {name}: command={srv_cfg['command']}, args={srv_cfg['args']}\")\r\n", "        logging.info(\"\\n汇总的工具:\")\r\n", "        for t in self.all_tools:\r\n", "            logging.info(f\"  - {t['function']['name']}\")\r\n", "\r\n", "    async def transform_json(self, json_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:\r\n", "        \"\"\"\r\n", "        将工具的 input_schema 转换为 OpenAI 所需的 parameters 格式，并删除多余字段\r\n", "        \"\"\"\r\n", "        result = []\r\n", "        for item in json_data:\r\n", "            if not isinstance(item, dict) or \"type\" not in item or \"function\" not in item:\r\n", "                continue\r\n", "            old_func = item[\"function\"]\r\n", "            if not isinstance(old_func, dict) or \"name\" not in old_func or \"description\" not in old_func:\r\n", "                continue\r\n", "            new_func = {\r\n", "                \"name\": old_func[\"name\"],\r\n", "                \"description\": old_func[\"description\"],\r\n", "                \"parameters\": {}\r\n", "            }\r\n", "            if \"input_schema\" in old_func and isinstance(old_func[\"input_schema\"], dict):\r\n", "                old_schema = old_func[\"input_schema\"]\r\n", "                new_func[\"parameters\"][\"type\"] = old_schema.get(\"type\", \"object\")\r\n", "                new_func[\"parameters\"][\"properties\"] = old_schema.get(\"properties\", {})\r\n", "                new_func[\"parameters\"][\"required\"] = old_schema.get(\"required\", [])\r\n", "            new_item = {\r\n", "                \"type\": item[\"type\"],\r\n", "                \"function\": new_func\r\n", "            }\r\n", "            result.append(new_item)\r\n", "        return result\r\n", "\r\n", "    async def chat_base(self, messages: List[Dict[str, Any]]) -> Any:\r\n", "        \"\"\"\r\n", "        使用 OpenAI 接口进行对话，并支持多次工具调用（Function Calling）。\r\n", "        如果返回 finish_reason 为 \"tool_calls\"，则进行工具调用后再发起请求。\r\n", "        \"\"\"\r\n", "        response = self.client.get_response(messages, tools=self.all_tools)\r\n", "        # 如果模型返回工具调用\r\n", "        if response.choices[0].finish_reason == \"tool_calls\":\r\n", "            while True:\r\n", "                messages = await self.create_function_response_messages(messages, response)\r\n", "                response = self.client.get_response(messages, tools=self.all_tools)\r\n", "                if response.choices[0].finish_reason != \"tool_calls\":\r\n", "                    break\r\n", "        return response\r\n", "\r\n", "    async def create_function_response_messages(self, messages: List[Dict[str, Any]], response: Any) -> List[Dict[str, Any]]:\r\n", "        \"\"\"\r\n", "        将模型返回的工具调用解析执行，并将结果追加到消息队列中\r\n", "        \"\"\"\r\n", "        function_call_messages = response.choices[0].message.tool_calls\r\n", "        messages.append(response.choices[0].message.model_dump())\r\n", "        for function_call_message in function_call_messages:\r\n", "            tool_name = function_call_message.function.name\r\n", "            tool_args = json.loads(function_call_message.function.arguments)\r\n", "            # 调用 MCP 工具\r\n", "            function_response = await self._call_mcp_tool(tool_name, tool_args)\r\n", "            # 🔍 打印返回值及其类型\r\n", "            # print(f\"[DEBUG] tool_name: {tool_name}\")\r\n", "            # print(f\"[DEBUG] tool_args: {tool_args}\")\r\n", "            # print(f\"[DEBUG] function_response: {function_response}\")\r\n", "            # print(f\"[DEBUG] type(function_response): {type(function_response)}\")\r\n", "            messages.append({\r\n", "                \"role\": \"tool\",\r\n", "                \"content\": function_response,\r\n", "                \"tool_call_id\": function_call_message.id,\r\n", "            })\r\n", "        return messages\r\n", "\r\n", "    async def process_query(self, user_query: str) -> str:\r\n", "        \"\"\"\r\n", "        OpenAI Function Calling 流程：\r\n", "         1. 发送用户消息 + 工具信息\r\n", "         2. 若模型返回 finish_reason 为 \"tool_calls\"，则解析并调用 MCP 工具\r\n", "         3. 将工具调用结果返回给模型，获得最终回答\r\n", "        \"\"\"\r\n", "        messages = [{\"role\": \"user\", \"content\": user_query}]\r\n", "        response = self.client.get_response(messages, tools=self.all_tools)\r\n", "        content = response.choices[0]\r\n", "        logging.info(content)\r\n", "        if content.finish_reason == \"tool_calls\":\r\n", "            tool_call = content.message.tool_calls[0]\r\n", "            tool_name = tool_call.function.name\r\n", "            tool_args = json.loads(tool_call.function.arguments)\r\n", "            logging.info(f\"\\n[ 调用工具: {tool_name}, 参数: {tool_args} ]\\n\")\r\n", "            result = await self._call_mcp_tool(tool_name, tool_args)\r\n", "            messages.append(content.message.model_dump())\r\n", "            messages.append({\r\n", "                \"role\": \"tool\",\r\n", "                \"content\": result,\r\n", "                \"tool_call_id\": tool_call.id,\r\n", "            })\r\n", "            response = self.client.get_response(messages, tools=self.all_tools)\r\n", "            return response.choices[0].message.content\r\n", "        return content.message.content\r\n", "\r\n", "    async def _call_mcp_tool(self, tool_full_name: str, tool_args: Dict[str, Any]) -> str:\r\n", "        \"\"\"\r\n", "        根据 \"serverName_toolName\" 格式调用相应 MCP 工具\r\n", "        \"\"\"\r\n", "        parts = tool_full_name.split(\"_\", 1)\r\n", "        if len(parts) != 2:\r\n", "            return f\"无效的工具名称: {tool_full_name}\"\r\n", "        server_name, tool_name = parts\r\n", "        server = self.servers.get(server_name)\r\n", "        if not server:\r\n", "            return f\"找不到服务器: {server_name}\"\r\n", "        resp = await server.execute_tool(tool_name, tool_args)\r\n", "        \r\n", "        # 🛠️ 修复点：提取 TextContent 中的文本（或转成字符串）\r\n", "        content = resp.content\r\n", "        if isinstance(content, list):\r\n", "            # 提取所有 TextContent 对象中的 text 字段\r\n", "            texts = [c.text for c in content if hasattr(c, \"text\")]\r\n", "            return \"\\n\".join(texts)\r\n", "        elif isinstance(content, dict) or isinstance(content, list):\r\n", "            # 如果是 dict 或 list，但不是 TextContent 类型\r\n", "            return json.dumps(content, ensure_ascii=False)\r\n", "        elif content is None:\r\n", "            return \"工具执行无输出\"\r\n", "        else:\r\n", "            return str(content)\r\n", "\r\n", "    async def chat_loop(self) -> None:\r\n", "        \"\"\"多服务器 MCP + OpenAI Function Calling 客户端主循环\"\"\"\r\n", "        logging.info(\"\\n🤖 多服务器 MCP + Function Calling 客户端已启动！输入 'quit' 退出。\")\r\n", "        messages: List[Dict[str, Any]] = []\r\n", "        while True:\r\n", "            query = input(\"\\n你: \").strip()\r\n", "            if query.lower() == \"quit\":\r\n", "                break\r\n", "            try:\r\n", "                messages.append({\"role\": \"user\", \"content\": query})\r\n", "                messages = messages[-20:]  # 保持最新 20 条上下文\r\n", "                response = await self.chat_base(messages)\r\n", "                messages.append(response.choices[0].message.model_dump())\r\n", "                result = response.choices[0].message.content\r\n", "                # logging.info(f\"\\nAI: {result}\")\r\n", "                print(f\"\\nAI: {result}\")\r\n", "            except Exception as e:\r\n", "                print(f\"\\n⚠️  调用过程出错: {e}\")\r\n", "\r\n", "    async def cleanup(self) -> None:\r\n", "        \"\"\"关闭所有资源\"\"\"\r\n", "        await self.exit_stack.aclose()\r\n", "\r\n", "\r\n", "# =============================\r\n", "# 主函数\r\n", "# =============================\r\n", "async def main() -> None:\r\n", "    # 从配置文件加载服务器配置\r\n", "    config = Configuration()\r\n", "    servers_config = config.load_config(\"servers_config.json\")\r\n", "    client = MultiServerMCPClient()\r\n", "    try:\r\n", "        await client.connect_to_servers(servers_config)\r\n", "        await client.chat_loop()\r\n", "    finally:\r\n", "        try:\r\n", "            await asyncio.sleep(0.1)\r\n", "            await client.cleanup()\r\n", "        except RuntimeError as e:\r\n", "            # 如果是因为退出 cancel scope 导致的异常，可以选择忽略\r\n", "            if \"Attempted to exit cancel scope\" in str(e):\r\n", "                logging.info(\"退出时检测到 cancel scope 异常，已忽略。\")\r\n", "            else:\r\n", "                raise\r\n", "\r\n", "if __name__ == \"__main__\":\r\n", "    asyncio.run(main())\n", "```"]}, {"cell_type": "markdown", "id": "2acaa29e-e3db-4196-ab43-4346782fa365", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/1742291307705.jpg\" alt=\"1742291307705\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "b635253a-07ed-4f9b-ab5b-2f287a6c8e4a", "metadata": {}, "source": ["- 创建.env文件"]}, {"cell_type": "markdown", "id": "ec040873-757e-4688-b015-e1e84ff79a41", "metadata": {}, "source": ["&emsp;&emsp;接下来继续创建一个`.env`文件，来保存大模型调用的API-KEY"]}, {"cell_type": "markdown", "id": "eee8821b-e0d6-4372-9bb8-b2738e1ee006", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202503171539087.png\" alt=\"image-20250317153902986\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "6297a066-b5e5-44ee-b07c-abd911c6380a", "metadata": {}, "source": ["并写入如下内容："]}, {"cell_type": "markdown", "id": "02f7250f-67f1-49f7-a77f-3efc58186218", "metadata": {}, "source": ["```bash\n", "BASE_URL=https://api.deepseek.com\n", "MODEL=deepseek-chat\n", "OPENAI_API_KEY=YOUR_DEEPSEEK_API_KEY\n", "```"]}, {"cell_type": "markdown", "id": "e1346ce7-ab4e-4aac-bf55-a74d123fd40b", "metadata": {}, "source": ["- 创建servers_config.json文件"]}, {"cell_type": "markdown", "id": "3617a4d4-f260-49e0-a1ba-2e166efee62c", "metadata": {}, "source": ["&emsp;&emsp;接下来继续创建servers_config.json文件，用于保存MCP工具基本信息："]}, {"cell_type": "markdown", "id": "9adc973d-67fc-426d-934c-b244703e3488", "metadata": {}, "source": ["```json\n", "{\r\n", "  \"mcpServers\": {\r\n", "    \"weather\": {\r\n", "      \"command\": \"python\",\r\n", "      \"args\": [\"weather_server.py\"],\r\n", "      \"transport\": \"stdio\"\r\n", "    },\r\n", "    \"write\": {\r\n", "      \"command\": \"python\",\r\n", "      \"args\": [\"write_server.py\"],\r\n", "      \"transport\": \"stdio\"\r\n", " ```   }\r\n", "  }\r\n", "}\r\n"]}, {"cell_type": "markdown", "id": "ab8032b7-fa49-4d5e-8bd2-296b1352b5aa", "metadata": {}, "source": ["此时完整项目结构如下："]}, {"cell_type": "markdown", "id": "003932f1-632c-49fb-8534-68c9d4ff37e0", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172025805.png\" alt=\"image-20250617202549758\" style=\"zoom: 33%;\" />"]}, {"cell_type": "markdown", "id": "21516da1-8724-4eea-b82d-fa7d5ad6bb44", "metadata": {}, "source": ["- 运行MCP客户端+服务器"]}, {"cell_type": "markdown", "id": "77c246ca-b135-42ba-900e-0ea859325214", "metadata": {}, "source": ["&emsp;&emsp;最后在命令行中执行如下命令，即可开启对话：\n", "\n", "```bash\n", "uv run client.py\n", "```"]}, {"cell_type": "markdown", "id": "7ee5fd4f-f991-427d-9b40-296073bdc92b", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171855700.png\" alt=\"image-20250617185517285\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "32f96a89-07b6-4cec-b26b-c4e10ded33a4", "metadata": {}, "source": ["至此，即完成了一次简单的MCP执行流程。"]}, {"cell_type": "markdown", "id": "fec1817b-be9f-49ce-a806-04097d31d618", "metadata": {}, "source": ["## 3.4 MCP+Lang<PERSON>hain基础调用流程"]}, {"cell_type": "markdown", "id": "914408c8-3ec0-43a8-ac27-4bfe69941953", "metadata": {}, "source": ["&emsp;&emsp;`<PERSON><PERSON><PERSON><PERSON>`调用`MCP`是可以将`MCP`的工具直接转换为`<PERSON><PERSON><PERSON><PERSON>`的工具，然后通过预定义的`MCP_Client`实现与外部`MCP`的读写操作，换而言之就是我们需要改写原先的client，将原先的Function calling调用逻辑修改为LangChain调用逻辑："]}, {"cell_type": "markdown", "id": "efb57530-3adb-493e-b56e-e4f0b8171f69", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172028187.png\" alt=\"image-20250617202831129\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "d69d0934-e957-4893-bbb8-d288c69cf93d", "metadata": {}, "source": ["```python\n", "\"\"\"\r\n", "多服务器 MCP + LangChain Agent 示例\r\n", "---------------------------------\r\n", "1. 读取 .env 中的 LLM_API_KEY / BASE_URL / MODEL\r\n", "2. 读取 servers_config.json 中的 MCP 服务器信息\r\n", "3. 启动 MCP 服务器（支持多个）\r\n", "4. 将所有工具注入 LangChain Agent，由大模型自动选择并调用\r\n", "\"\"\"\r\n", "\r\n", "import asyncio\r\n", "import json\r\n", "import logging\r\n", "import os\r\n", "from typing import Any, Dict, List\r\n", "\r\n", "from dotenv import load_dotenv\r\n", "from langchain import hub\r\n", "from langchain.agents import AgentExecutor, create_openai_tools_agent\r\n", "from langchain.chat_models import init_chat_model\r\n", "from langchain_mcp_adapters.client import MultiServerMCPClient\r\n", "from langchain_mcp_adapters.tools import load_mcp_tools\r\n", "\r\n", "# ────────────────────────────\r\n", "# 环境配置\r\n", "# ────────────────────────────\r\n", "\r\n", "class Configuration:\r\n", "    \"\"\"读取 .env 与 servers_config.json\"\"\"\r\n", "\r\n", "    def __init__(self) -> None:\r\n", "        load_dotenv()\r\n", "        self.api_key: str = os.getenv(\"LLM_API_KEY\") or \"\"\r\n", "        self.base_url: str | None = os.getenv(\"BASE_URL\")  # DeepSeek 用 https://api.deepseek.com\r\n", "        self.model: str = os.getenv(\"MODEL\") or \"deepseek-chat\"\r\n", "        if not self.api_key:\r\n", "            raise ValueError(\"❌ 未找到 LLM_API_KEY，请在 .env 中配置\")\r\n", "\r\n", "    @staticmethod\r\n", "    def load_servers(file_path: str = \"servers_config.json\") -> Dict[str, Any]:\r\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\r\n", "            return json.load(f).get(\"mcpServers\", {})\r\n", "\r\n", "# ────────────────────────────\r\n", "# 主逻辑\r\n", "# ────────────────────────────\r\n", "async def run_chat_loop() -> None:\r\n", "    \"\"\"启动 MCP-Agent 聊天循环\"\"\"\r\n", "    cfg = Configuration()\r\n", "    os.environ[\"DEEPSEEK_API_KEY\"] = os.getenv(\"LLM_API_KEY\", \"\")\r\n", "    if cfg.base_url:\r\n", "        os.environ[\"DEEPSEEK_API_BASE\"] = cfg.base_url\r\n", "    servers_cfg = Configuration.load_servers()\r\n", "\r\n", "    # 把 key 注入环境，LangChain-OpenAI / DeepSeek 会自动读取\r\n", "    os.environ[\"OPENAI_API_KEY\"] = cfg.api_key\r\n", "    if cfg.base_url:  # 对 DeepSeek 之类的自定义域名很有用\r\n", "        os.environ[\"OPENAI_BASE_URL\"] = cfg.base_url\r\n", "\r\n", "    # 1️⃣ 连接多台 MCP 服务器\r\n", "    mcp_client = MultiServerMCPClient(servers_cfg)\r\n", "\r\n", "    tools = await mcp_client.get_tools()         # LangChain Tool 对象列表\r\n", "\r\n", "    logging.info(f\"✅ 已加载 {len(tools)} 个 MCP 工具： {[t.name for t in tools]}\")\r\n", "\r\n", "    # 2️⃣ 初始化大模型（DeepSeek / OpenAI / 任意兼容 OpenAI 协议的模型）\r\n", "    llm = init_chat_model(\r\n", "        model=cfg.model,\r\n", "        model_provider=\"deepseek\" if \"deepseek\" in cfg.model else \"openai\",\r\n", "    )\r\n", "\r\n", "    # 3️⃣ 构造 Lang<PERSON>hain Agent（用通用 prompt）\r\n", "    prompt = hub.pull(\"hwchase17/openai-tools-agent\")\r\n", "    agent = create_openai_tools_agent(llm, tools, prompt)\r\n", "    agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)\r\n", "\r\n", "    # 4️⃣ CLI 聊天\r\n", "    print(\"\\n🤖 MCP Agent 已启动，输入 'quit' 退出\")\r\n", "    while True:\r\n", "        user_input = input(\"\\n你: \").strip()\r\n", "        if user_input.lower() == \"quit\":\r\n", "            break\r\n", "        try:\r\n", "            result = await agent_executor.ainvoke({\"input\": user_input})\r\n", "            print(f\"\\nAI: {result['output']}\")\r\n", "        except Exception as exc:\r\n", "            print(f\"\\n⚠️  出错: {exc}\")\r\n", "\r\n", "    # 5️⃣ 清理\r\n", "    await mcp_client.cleanup()\r\n", "    print(\"🧹 资源已清理，Bye!\")\r\n", "\r\n", "# ────────────────────────────\r\n", "# 入口\r\n", "# ────────────────────────────\r\n", "if __name__ == \"__main__\":\r\n", "    logging.basicConfig(level=logging.INFO, format=\"%(asctime)s - %(levelname)s - %(message)s\")\r\n", "    asyncio.run(run_chat_loop())\r\n", "\n", "```"]}, {"cell_type": "markdown", "id": "497d3ad9-8e17-4363-a20b-c2f0367f54fe", "metadata": {}, "source": ["&emsp;&emsp;`<PERSON><PERSON><PERSON><PERSON>`接入`MCP`的核心原理为： `weather_server.py` → 启动为子进程 → `stdio` 通信 → `MCP` 协议 → 转换为 `LangChain` 工具 → `LangChain Agent` 执行读写，核心转换过程为：：\n", "\n", "1. `@mcp.tool()` → 标准 `<PERSON><PERSON><PERSON><PERSON>`\n", "2. `stdio_client()` → 自动处理 `read/write` 流，其中`read` 表示从 `MCP` 服务器读取响应的流，`write` 表示向 `MCP` 服务器发送请求的流，对于 `stdio weather_server.py`，它们就是子进程的 `stdout` 和 `stdin`\n", "3. `load_mcp_tools()` → 一键转换所有工具"]}, {"cell_type": "markdown", "id": "53a133cf-24b2-4fc0-a987-74595351ff70", "metadata": {}, "source": ["实际对话过程如下所示："]}, {"cell_type": "markdown", "id": "73fedb20-6a38-4a8f-8320-25a9b8f14b9e", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506171915621.png\" alt=\"image-20250617191543679\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "470b27c3-950a-4682-a32e-15d9b60f3605", "metadata": {}, "source": ["&emsp;&emsp;完整的代码已经上传至百度网盘中的`langchain_rag.py`文件中，大家可以扫描下方二维码免费领取"]}, {"cell_type": "markdown", "id": "3a6312f6-97a8-4927-9d46-7b1f4082da24", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506172030599.png\" alt=\"image-20250617203037548\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "470ce690-8f77-4f45-9e65-8a7a2bd76182", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/202506102031014.png\" alt=\"6d9391e440ee8df1466cef1bce40705\" style=\"zoom:50%;\" />"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}