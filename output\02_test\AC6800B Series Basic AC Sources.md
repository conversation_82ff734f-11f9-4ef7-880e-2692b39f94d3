### Operating and Service Guide

# Keysight AC6800B Series Basic AC Sources


Notices 4


**Copyright Notice** **4**
**Manual Part Number** **4**

**Edition** **4**

**Published by** **4**
**Warranty** **4**
**Technology Licenses** **4**
**U.S. Government Rights** **5**


Safety and Regulatory Information 6


**General Information** **6**

**Safety Warnings** **6**
**Product Grounding** **6**
**General Warnings** **7**
**Environmental Warnings** **7**
**Shock Hazard** **8**

**Installation Warnings** **9**
**Heavy Weight** **9**
**Equipment Cautions** **10**
**General Cautions** **10**

**Installation Cautions** **10**

**Operational Cautions** **10**
**Moving Cautions** **10**
**Safety Symbols** **11**


1 Getting Started 13


**Before Installation or Use** **14**

**Introduction to the Instrument** **19**

**Instrument Ratings** **23**
**Installing an Optional Interface Board** **24**
**Rack <PERSON>** **25**
**Connecting the Power Cord** **28**
**Switchboard and Circuit Breaker Requirements** **32**
**Quick Start** **35**

**Interface Connections** **39**

**Remote Interface Configuration** **41**
**Output Power Connections** **51**
**Voltage Ranges and Limits** **57**
**Error Checking and Soft Limits** **59**


2 User Information 63


**Welcome** **64**

**Front Panel Menu Reference** **66**

**General Front-Panel Organization** **68**
**Selecting the Output Voltage Programming Source and Output Mode** **71**
**Specifying Output Coupling** **72**
**Programming AC Output** **73**
**Programming DC Voltage** **78**
**Setting Limit Values** **79**


2 Keysight AC6800B Series Operating and Service Guide


**Turning the Output On and Off** **81**
**Storing and Retrieving Instrument States** **82**
**Configuring Instrument Preferences** **84**
**Calibrating from the Front Panel** **86**
**Configuring Overcurrent Protection** **87**
**Configuring Watchdog Protection** **88**
**Configuring and Clearing Measurements** **89**
**Using External Analog Control** **90**
**Using Fault Inhibit Control** **94**
**Viewing Error Messages** **98**


3 SCPI Programming Reference 99


**Introduction to SCPI** **100**

**Status Overview** **105**

**Command Quick Reference** **110**
**ABORt Subsystem** **119**
**CALibrate Subsystem** **120**
**CURRent Subsytem** **125**
**DIGital Subsystem** **127**
**DISPlay Subsystem** **128**
**FETCh and MEASure Subsystems** **130**
**FREQuency Subsystem** **135**
**HCOPy Subsystem** **137**
**IEEE-488 Common Commands** **138**

**INITiate Subsystem** **144**
**LXI Subsystem** **145**
**OUTPut Subsystem** **146**
**SENSe Subsytem** **149**
**STATus Subsystem** **150**
**SYSTem Subsystem** **155**
**TRIGger Subsystem** **160**
**VOLTage Subsystem** **163**
**Default Settings** **169**
**SCPI Error Messages** **170**


4 Calibration, Verification, and Service 173


**Calibration Overview** **174**

**Calibration Procedure** **175**

**Performance Verification** **181**

**Performance Test Records** **189**

**Service and Maintenance** **193**


License Files 204


Keysight AC6800B Series Operating and Service Guide 3


Notices

#### **Notices**

##### **Copyright Notice**


© Keysight Technologies, Inc. 2017, 2019

##### **Manual Part Number**


AC6800-90901

##### **Edition**


Edition 2, April 2019

##### **Published by**


Keysight Technologies, Inc.
550 Clark Drive, Suite 101

Budd Lake, New Jersey 07828

USA

##### **Warranty**


THE MATERIAL CONTAINED IN THIS DOCUMENT IS PROVIDED "AS IS," AND IS SUBJECT TO BEING

CHANGED, WITHOUT NOTICE, IN FUTURE EDITIONS. FURTHER, TO THE MAXIMUM EXTENT

PERMITTED BY APPLICABLE LAW, KEYSIGHT DISCLAIMS ALL WARRANTIES, EITHER EXPRESS OR

IMPLIED WITH REGARD TO THIS MANUAL AND ANY INFORMATION CONTAINED HEREIN, INCLUDING

BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A

PARTICULAR PURPOSE. KEYSIGHT SHALL NOT BE LIABLE FOR ERRORS OR FOR INCIDENTAL OR

CONSEQUENTIAL DAMAGES IN CONNECTION WITH THE FURNISHING, USE, OR PERFORMANCE OF

THIS DOCUMENT OR ANY INFORMATION CONTAINED HEREIN. SHOULD KEYSIGHT AND THE USER

HAVE A SEPARATE WRITTEN AGREEMENT WITH WARRANTY TERMS COVERING THE MATERIAL IN

THIS DOCUMENT THAT CONFLICT WITH THESE TERMS, THE WARRANTY TERMS IN THE SEPARATE

AGREEMENT WILL CONTROL.

##### **Technology Licenses**


The hardware and/or software described in this document are furnished under a license and may be
used or copied only in accordance with the terms of such license.


4 Keysight AC6800B Series Operating and Service Guide


Notices

##### **U.S. Government Rights**


The Software is “commercial computer software,” as defined by Federal Acquisition Regulation
(“FAR”) 2.101. Pursuant to FAR 12.212 and 27.405-3 and Department of Defense FAR Supplement
(“DFARS”) 227.7202, the U.S. government acquires commercial computer software under the same
terms by which the software is customarily provided to the public. Accordingly, Keysight provides the
Software to U.S. government customers under its standard commercial license, which is embodied in
its End User License Agreement (EULA), a copy of which can be found at
[http://www.keysight.com/find/sweula. The license set forth in the EULA represents the exclusive](http://www.keysight.com/find/sweula)
authority by which the U.S. government may use, modify, distribute, or disclose the Software. The
EULA and the license set forth therein, does not require or permit, among other things, that Keysight:
(1) Furnish technical information related to commercial computer software or commercial computer
software documentation that is not customarily provided to the public; or (2) Relinquish to, or
otherwise provide, the government rights in excess of these rights customarily provided to the public
to use, modify, reproduce, release, perform, display, or disclose commercial computer software or
commercial computer software documentation. No additional government requirements beyond
those set forth in the EULA shall apply, except to the extent that those terms, rights, or licenses are
explicitly required from all providers of commercial computer software pursuant to the FAR and the
DFARS and are set forth specifically in writing elsewhere in the EULA. Keysight shall be under no
obligation to update, revise or otherwise modify the Software. With respect to any technical data as
defined by FAR 2.101, pursuant to FAR 12.211 and 27.404.2 and DFARS 227.7102, the U.S.
government acquires no greater than Limited Rights as defined in FAR 27.401 or DFAR 227.7103-5 (c),
as applicable in any technical data.


Keysight AC6800B Series Operating and Service Guide 5


Safety and Regulatory Information

#### **Safety and Regulatory Information**


This procedure requires the user to send SCPI commands to the instrument. Connect to
the instrument via LAN or USB. Use the Keysight IO Libraries to send SCPI commands.

##### **General Information**


The equipment is for industrial use.


Equipment operators are subject to all applicable safety regulations. Along with the warning and
safety notices in this manual, all relevant safety, accident prevention, and environmental regulations
must also be followed. In particular, the operators of the equipment:


l Must be informed of the relevant safety requirements.

l Must have read and understood the operating manual before using the equipment.

l Must use the designated and recommended safety equipment.


The following general safety precautions must be observed during all phases of operation of this
instrument. Failure to comply with these precautions or with specific warnings or instructions
elsewhere in this manual violates safety standards of design, manufacture, and intended use of the
instrument. Keysight Technologies assumes no liability of the customer’s failure to comply with the
requirements.

##### **Safety Warnings**


A WARNING notice denotes a hazard. It calls attention to an operating procedure, practice, or the like
that, if not correctly performed or adhered to, could result in personal injury or death. Do not proceed
beyond a WARNING notice until the indicated conditions are fully understood and met.


Should network communication issues occur, the instrument settings shown in the
Browser Web Control page may not represent the actual state of the instrument. This may
result in unexpected hazardous voltages on the output and sense connections that could
result in personal injury, death, or damage to a device under test. Before touching the output or sense connections or connecting to a device under test, always verify the state of

the instrument.

##### **Product Grounding**


The instrument is a Class 1 product and is provided with a grounding-type power cord set.
The instrument chassis and cover are connected to the instrument electrical ground to
minimize shock hazard. The ground pin of the cord set plug must be firmly connected to
the electrical ground (safety ground) terminal at the power outlet. Any interruption of the
protective earth (grounding) conductor or disconnection of the protective earth terminal
will cause a potential shock hazard that could result in personal injury or death.


6 Keysight AC6800B Series Operating and Service Guide


Safety and Regulatory Information

##### **General Warnings**


Do not use this product in any manner not specified by the manufacturer. The protective
features of this product may be impaired if it is used in a manner not specified in the
operation instructions.


Instruments that appear damaged or defective should be made inoperative and secured
against unintended operation until they can be repaired by qualified service personnel.


The instrument contains an internal fuse, which is not user accessible.


DO NOT REMOVE COVERS. NO OPERATOR SERVICEABLE PARTS INSIDE. REFER

SERVICING TO QUALIFIED SERVICE PERSONNEL.


SECURELY TURN OFF THE CIRCUIT BREAKER ON THE SWITCHBOARD BEFORE

HANDLING THE POWER CORDS. TO AVOID AN ELECTRIC SHOCK, THE PROTECTIVE

CONDUCTOR TERMINAL MUST BE CONNECTED TO AN ELECTRICAL GROUND.


NE PAS RETIRER LES COUVERTURE DE LA BOITE. AUCUN OPÉRATEUR SERVICEABLE
PIÈCES À L'INTÉRIEUR. CONFIER L'ENTRETIEN DE PERSONNEL QUALIFIÉ.


CORRECTEMENT COUPER LE DISJONCTEUR ARMOIRE AVANT DE MANIPULER LES
CORDONS D'ALIMENTATION. POUR ÉVITER TOUT CHOC ÉLECTRIQUE, LE
CONDUCTEUR DE PROTECTION BORNE DOIT ÊTRE RELIÉE À UNE MASSE ÉLECTRIQUE.

##### **Environmental Warnings**


This product is designed for safe indoor use. Use indoors only.


Do not operate the instrument near flammable gases or fumes.


To prevent the possibility of explosion or fire, do not use the product near alcohol, thinner
or other combustible materials, or in an atmosphere containing such vapors.


Do not install the product near a heater, in direct sunlight, or in areas subject to drastic
temperature changes. The operating temperature range is 0 to 40 °C (32 to 104 °F), and
the storage temperature range is -10 to 60 °C (14 to 140 °F).


Do not install the product in high-humidity locations, such as near a boiler, humidifier, or
water supply. The operating humidity range is 20% to 80% relative humidity (no
condensation), and the storage humidity range is 90% relative humidity or less (no
condensation). Condensation may occur even within the operating humidity range. In
such cases, do not use the instrument until the condensation dries up completely.


Do not install the product in a corrosive atmosphere or in environments containing
sulfuric acid mist, etc. This may cause corrosion of various conductors and bad contacts of
connectors inside the instrument leading to malfunction and failure, or in the worst case,

a fire.


Do not install the product in a dusty location. Accumulation of dust can lead to electric

shock or fire.


Keysight AC6800B Series Operating and Service Guide 7


Safety and Regulatory Information

##### **Shock Hazard**


Before making any load or sense connections be sure to turn the POWER switch off and
remove the power plug from an outlet or turn off the circuit breaker of switchboard.


When the power switch is turned off while the output is on, residual voltage still remains
at the output terminals.


Do not touch the output terminal block for at least 20 seconds after the power switch is

tuned off.


When installing the switch between the OUTPUT terminal block and the load, be sure to
turn the POWER switch off and remove the power plug from an outlet or turn off the circuit

breaker of the switchboard.


Be sure to turn the switch off before connecting the load to the terminal at the load end of

the switch.


Do not touch the switch terminal or the output terminal when the output is on.


Do not remove the instrument covers. There are no customer-serviceable parts inside.
Some circuits are active and have power briefly after the power switch is turned off.


To prevent electric shock, unplug the unit before cleaning.


For protection from electrical shock, the power cord ground must not be defeated. If only
a two-contact electrical outlet is available, connect the instrument’s chassis ground
screw (see above) to a good earth ground.


This product is an IEC Safety Class I equipment (equipment with a protective conductor
terminal). Be sure to ground (earth) the unit.

Connect the protective conductor terminal to earth ground.


In DC mode, L is at positive potential and N is at negative potential when setting the
positive value. The opposite is true when setting the negative value.


Do not use the terminal block with the terminal cover removed.


Turn off the switchboard circuit breaker before connecting the cord.


This product includes protective earth terminals. To minimize shock hazard, the
instrument must be connected to the AC power mains through a grounded power cable,
with the ground wire firmly connected to an electrical ground (safety ground) at the power
outlet. Any interruption of the protective (grounding) conductor or disconnection of the
protective earth terminal will cause a potential shock hazard that could result in personal
injury.


If a capacitor, battery, or similar device is connected as a load in DC mode, voltage
remains at the section connected to the output terminal block even when the output is off
until the load energy is discharged. The discharge time of the internal capacitor when no
load is connected is approximately 0.1 seconds. To prevent the possibility of electric
shock, do not touch the output terminal block.


8 Keysight AC6800B Series Operating and Service Guide


Safety and Regulatory Information

##### **Installation Warnings**


Have a qualified engineer connect the power cord to the switchboard.


Protective circuits inside the instrument, including input fuses, are connected to match
the input terminal polarity. Make sure the colors of the wires connected to the input
terminals (L, N, and GND) are correct.


Verify that all safety precautions are taken. Make all connections to the unit before
applying power. Note the instrument's external markings described under "Safety
Symbols."


You cannot use standard rack mounting support rails, as they would block the airflow
needed for cooling.

##### **Heavy Weight**


Danger to hands and feet. To avoid personal injury and damage to the instrument, always
use a sturdy cart or other suitable device to move the instrument. Do not lift the
instrument alone; always use two people to lift the instrument.


Keysight AC6800B Series Operating and Service Guide 9


Safety and Regulatory Information

##### **Equipment Cautions**


A CAUTION notice denotes a hazard. It calls attention to an operating procedure, practice, or the like
that, if not correctly performed or adhered to, could result in damage to the product or loss of
important data. Do not proceed beyond a CAUTION notice until the indicated conditions are fully

understood and met.

##### **General Cautions**


Do not place objects on the instrument. Placing objects, especially heavy objects, on top
of the product can cause failures.

##### **Installation Cautions**


Do not block the air intake at the front of the instrument or the exhaust at the rear.


Large voltage distortion on the AC power line can lead to malfunction. Do not connect the
instrument to a generator or a similar device.


Do not use the product in a location where strong magnetic or electric fields are nearby or
a location where large amounts of distortion or noise are present on the input power
supply waveform. The product may malfunction.


Do not install the product on an inclined surface or location subject to vibrations. The
product may fall or tip over, causing damage or injuries or both.

##### **Operational Cautions**


You cannot set the voltage limit when the instrument is being controlled by external
analog signals. An excessive external voltage may damage the load.


When the output is turned on, several volts of undershoot or overshoot may appear for a
time period on the order of ten microseconds.

##### **Moving Cautions**


Be sure to include this manual when transporting the instrument.


Turn off the power switch before moving the instrument. Moving the product while the
power is turned on can cause electric shock or instrument damage.


Remove all wiring before moving the instrument. Moving the product with the cables
connected can cause wires to break or injuries due to the product falling over.


When transporting the product, be sure to use the original packing materials. Damage
may result from vibrations or from the product falling during transportation.


10 Keysight AC6800B Series Operating and Service Guide


Safety and Regulatory Information

##### **Safety Symbols**


Direct current


Alternating current


Frame or chassis terminal


Standby supply. Unit is not completely disconnected from AC mains

when switch is off.


Caution, risk of electric shock


Caution, refer to accompanying documents


Earth ground terminal



ISM1-A



The CE mark is a registered trademark of the European Community. The

text indicates that the instrument is an Industrial Scientific and Medical

Group 1 Class A product (CISPR 11, Clause 4).


The TUV mark is a registered trademark of the European community.



ICES/NMB-001 This mark indicates product compliance with the Canadian Inter
ference- Causing Equipment Standard.


The RCM mark is a registered trademark of the Spectrum Management
Agency of Australia. This signifies compliance with the Australian EMC
Framework regulations under the terms of the Radio Communications

Act of 1992.


South Korean Class A EMC Declaration

This equipment is Class A suitable for professional use and is for use in
electromagnetic environments outside of the home.


Contains one or more of the 6 hazardous substances above the max
imum concentration value (MCV), 40 Year EPUP.



ICES/NMB
001



This ISM device complies with Canadian ICES-001. Cet appareil ISM est

conforme à la norme NMB-001 du Canada.



Keysight AC6800B Series Operating and Service Guide 11


Safety and Regulatory Information


**Waste Electrical and Electronic Equipment (WEEE)**


This product complies with the WEEE Directive) marketing requirement. The affixed product label (see
below) indicates that you must not discard this electrical/electronic product in domestic household

waste.


Product Category: With reference to the equipment types in the WEEE directive Annex 1, this product
is classified as “Monitoring and Control instrumentation” product. Do not dispose in domestic

household waste.


To return unwanted products, contact your local Keysight office, or see


[about.keysight.com/en/companyinfo/environment/takeback.shtml for more information.](http://about.keysight.com/en/companyinfo/environment/takeback.shtml)


**South Korea Class A EMC declaration**


Information to the user:


This equipment has been conformity assessed for use in business environments. In a residential
environment this equipment may cause radio interference.


This EMC statement applies to the equipment only for use in business environment.


12 Keysight AC6800B Series Operating and Service Guide


Keysight AC6800B Series Operating and Service Guide

## 1 Getting Started


**Before Installation or Use**


**Introduction to the Instrument**


**Instrument Ratings**


**Installing an Optional Interface Board**


**Rack Mounting**


**Connecting the Power Cord**


**Switchboard and Circuit Breaker Requirements**


**Quick Start**


**Interface Connections**


**Remote Interface Configuration**


**Output Power Connections**


**Voltage Ranges and Limits**


**Error Checking and Soft Limits**


1 Getting Started

##### **Before Installation or Use**


**Inspect the Unit**


When you receive your instrument, inspect it for obvious shipping damage. If there is damage, notify
the shipping carrier and nearest Keysight Sales and Support Office immediately. Refer to
[www.keysight.com/find/assist. Save all packing and shipping materials in case the unit must be](http://www.keysight.com/find/assist)

returned or moved.


**Check for Items Supplied**


Verify that you received the following items.


AC6800B Series AC Source


Line cord (AC6801B only)


AC input cover (AC6803B and AC6804B only)


Ferrite core and cable tie (two supplied with AC6803B)


CD - IO Libraries Media Suite


Safety Information booklet


Certificate Of Calibration and envelope


Refer to the box contents list for any additional items that may be included with your shipment. If
anything is missing, please contact your nearest Keysight Sales and Support Office.


**Review Safety Information**


This AC source is a Safety Class 1 instrument, which means it has a protective earth terminal. That
terminal must be connected to earth ground through a power source equipped with an earth ground.
Refer to the Safety Notices for general safety information. Before installation or operation, check the
instrument and review this guide for safety warnings and instructions. Safety warnings for specific
procedures are located at appropriate places throughout this guide.


14 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Observe Environmental Conditions**


Do not operate the instrument near flammable gases or fumes.


The AC6800B Series instruments are Overvoltage Category II instruments that should only be
operated in a controlled, indoor environment subject to the following restrictions:


Operating: 0 to 40 °C (32 to 104 °F), 20% to 80% relative humidity, noncondensing


Storage: –10 to 60 °C (14 to 140 °F), 90% or less relative humidity, noncondensing


Altitude: Up to 2000 m


**Provide Adequate Air Flow**


Do not block the air intake at the front of the instrument or the exhaust at the rear.


The dimensions of each model are shown below. Fans cool the instrument by drawing air through the
front and exhausting it out the back. Allow at least 8 inches (20 cm) of space at the front and back of

the unit for air circulation.


All dimensions are in mm.


**AC6801B**


Keysight AC6800B Series Operating and Service Guide 15


1 Getting Started


**AC6802B**


16 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**AC6803B**


Keysight AC6800B Series Operating and Service Guide 17


1 Getting Started


**AC6804B**


18 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Introduction to the Instrument**


**Front Panel at a Glance**


**Front Panel Display at a Glance**


**Rear Panel at a Glance**


**Instrument Ratings**


**Front Panel at a Glance**


The following table lists the main parts of the front panel, from left to right:


The [Power] switch turns the unit on or off. The indicator next to this switch

shows the display status. Green indicates normal operation. Yellow indicates that
the display is in screen saver mode or that the instrument is in the boot-up process. Press any key to exit screen saver mode.


The status LEDs light to indicate when a protection event has occurred and when
the output is on.


The display allows you to configure and monitor the instrument.


Keysight AC6800B Series Operating and Service Guide 19


1 Getting Started


[Meter] returns the display to metering mode. Pressing it repeatedly cycles the
display through all three formats (METER_VI, METER_VIP, and METER_ALL).

[Menu] opens the top level of the command menu. Pressing it a second time
returns to the metering mode display.

[Protect] brings the user to the Protect menu. This is equivalent to

[Menu] > Protect .

[Back] backs out of a menu without activating any changes.

[Help] describes the displayed menu control.

[Error] displays messages in the error queue.


The [Error] and [Help] keys provide access to text that is accessed
via a vertically scrolling text box. Use the up and down arrow keys
to scroll through multiline text, one screen at a time.


To retrieve error messages remotely, send the SYSTem:ERRor?
query. The SCPI Status Byte and Standard Event Registers provide

an overview of error conditions.


The puqt arrows move around the command menus and select characters in
alphanumeric entry fields. The [Select] key makes a selection in a menu and
enters the edit mode for numeric parameters.


[On/Off] enables or disables the output.

[Voltage] specifies the voltage settings.

[Freq] specifies the frequency settings.


[0] through [9] enter numbers.

[.] enters the decimal point.

[–] toggles between positive and negative numbers.

[!] and [#] increment or decrement voltage or frequency settings and select letters
in alphanumeric entry fields.

[E] enters the letter E to allow you to enter an exponent to the right of it.

[-] backspaces to delete characters.

[Enter] enters a value. If you exit a field without pressing [Enter], the value is
ignored.


20 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Front Panel Display at a Glance**


Metering field Shows the measured output.


Status field Displays the instrument status:
OFF = the output is off.
CV = the output is in constant voltage mode.
OC = the output is disabled by the overcurrent protection.
OT = the overtemperature protection has tripped.
OP = the overpower protection has tripped.
CLPK = the instrument has gone beyond its peak current limit.
CLrms = the instrument has gone beyond its RMS current limit.
PL = the instrument has gone beyond its power limit.
SF = the output is disabled by a sense fault protection.
WDG = watchdog protection - no I/O activity.

INH = the remote inhibit is active


Range field Displays the current voltage range (HIGH, LOW, or AUTO).


Settings field Displays the output settings.


Interface field Indicates the following remote interface activity:

Err = an error has occurred (press [Error] to display the error message)
Lan = the LAN is connected and has been configured
IO = there is activity on one of the remote interfaces


Keysight AC6800B Series Operating and Service Guide 21


1 Getting Started


**Rear Panel at a Glance**


For protection from electrical shock, the power cord ground must not be defeated. If only a
two-contact electrical outlet is available, connect the instrument’s chassis ground screw
(see above) to a good earth ground.


**AC6801B**


**AC6802B**


**AC6803B**


**AC6804B**


22 Keysight AC6800B Series Operating and Service Guide


##### **Instrument Ratings**



1 Getting Started





Rated voltage range 1 to 155 Vrms/2 to 310 Vrms


Maximum rms current 5 A/2.5 A 10 A/5 A 20 A/10 A 40 A/20 A


Maximum power 500 VA 1 kVA 2 kVA 4 kVA


Frequency range 40 Hz to 500 Hz


**Output Rating for DC mode (155 V/310 V range)**


Rated voltage range 1.4 to 219 Vrms/2.8 to 438 Vrms


Maximum DC current 4 A/2 A 8 A/4 A 16 A/8 A 32 A/16 A


Maximum power 400 VA 800 VA 1.6 kVA 3.2 kVA


**Input Ratings**


Voltage rating 100 to 120 Vrms/200 to 240 Vrms, 50 Hzor 60 Hz, single-phase


Voltage range 90 to 132 Vrms/180 to 264 Vrms (auto detected when thepower is turned on)


Frequency range 47 Hzto 63 Hz


Apparent power 800 VA or less 1600 VA or less 3200 VA or less 6400 VA or less


Power factor 0.9 (typical)



Maximum input current 8 A/4A@ 100V/200V
6.7A/3.5A@ 120V/230V


**Environment**



16 A/8A@ 100V/200V

13.4 A/7.0 A@ 120/230V



32A/16A@ 100V/200V

26.8 A/14.0 A@ 120/230V



64 A/32A@ 100V/200V

53.6 A/28.0 A@ 120/230V



Operating environment Indoor use, OvervoltageCategory II


Temperature and humidity range 0 to 40 °C (32 to 104 °F), 20% to 80% R.H. non-condensing


Altitude Up to 2000 meters


Acoustic noise < 70 dbA


**Physical**



Dimensions (with safety covers) 428 × 128 × 350 mm


168.5 x 50.5 x 138”


Weight Approx. 8 kg

(17.64 lb)



428 × 128 × 350 mm


168.5 x 50.5 x 138”


Approx. 11 kg

(24.25 lb)



428 × 128 × 550 mm


169 x 50.5 x 216.5”


Approx. 16 kg

(35.27 lb)



428 × 256 × 600

mm\168.5"×101"×236")


Approx. 32 kg

(70.55 lb)



Input terminal IEC 320 inlet M4 terminal block M6 terminal block M6 terminal block


Output terminal M4 terminal block M4 terminal block M4 terminal block M6 terminal block


Keysight AC6800B Series Operating and Service Guide 23


1 Getting Started

##### **Installing an Optional Interface Board**


The rear-panel slot can hold either the GPIB interface board (option AC68GPBU), the analog output
interface board (option AC68BALGU), or the fault inhibit interface board (option AC68BFIU).


Consult with your Keysight sales representative or distributor for information regarding the
availability of option AC68BALGU or option AC68BFIU.


To install a board:


1. Check that the power switch is turned off.


2. Touch the grounded metal to discharge your static electricity.


3. Unscrew the slot cover screws and remove the cover. Retain the cover for use in case the interface


board is ever removed.


4. Verify that the switch at the bottom rear of the slot is in the up position. It should only be down during

the firmware update process.


5. Slide the board all the way into the connector at the back of the slot.


6. Use the slot cover screws to secure the board.


24 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Rack Mounting**


This section requires option AC68BRAC3 (for models AC6801B, AC6802B, and AC6803B)
or AC68BRAC6 (for model AC6804B).


This section contains instructions for installing the instruments in a 19-inch EIA rack.


**Verification of Option Kit**


Verify that you received the following items. If anything is missing, please contact your nearest
Keysight Sales and Support Office.


**Rack Mounting Kit AC68BRAC3 for AC6801B, AC6802B, and AC6803B**


1 Bracket 2


2 Flat Head Screws M4×0.7×10 4


3 Clip Nuts for Rack Frame 10-32 0.5-in. 4


4 Dress Screws with Nylon Washer, Phantom Gray 4


**Rack Mounting Kit AC68BRAC6 for AC6804B**


1 Bracket 2


2 Flat Head Screws M4×0.7×10 8


3 Clip Nuts for Rack Frame 10-32 0.5-in. 8


4 Dress Screws with Nylon Washer, Phantom Gray 8


**Installation**


Do not block the air intake at the front of the instrument or the exhaust at the rear.


To prevent the instrument from falling, install suitable support angles (not included)
to support the instrument as shown.


Keysight AC6800B Series Operating and Service Guide 25


1 Getting Started


**Rack Mounting Models AC6801B, AC6802B, and AC6803B**


Remove the feet from the bottom

panel.


Using the accompanying M4×10
flat head screws, install the brack
ets.


Using the accompanying dress
screws and clip nuts (10-32),

mount the instrument in the rack.


26 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Rack Mounting Model AC6804B**


Remove the feet from the bottom panel.


Using the accompanying M4×10 flat head
screws, install the brackets.


Using the accompanying dress screws and clip
nuts (10-32), mount the instrument in the rack.


Keysight AC6800B Series Operating and Service Guide 27


1 Getting Started

##### **Connecting the Power Cord**


The power cord used with the product varies depending on the model. This product complies with IEC
Overvoltage Category II (energy-consuming equipment supplied from a fixed installation).


The AC6801B line cord has a molded plug on both ends.
The AC6802B is a single-phase, cord-connected device that requires a plug

and cord set.

The AC6802B line cord must have a plug on the utility side; you cannot hard
wire the instrument to the utility.


**Possible Electric Shock**
This product is an IEC Safety Class I equipment (equipment with a protective
conductor terminal). Be sure to ground (earth) the unit.

Connect the protective conductor terminal to earth ground.


**AC6801B**


Connect the power cord to the AC connector on the back of the instrument. Then connect the other
end to a properly grounded power outlet. You will use this plug to disconnect from mains power.


Use the supplied power cord to connect to the AC line. If the supplied power cord cannot be used due
to the rated voltage or the plug shape, have a qualified engineer replace it with an appropriate power
cord of length 3 m or less. If obtaining a power cord is difficult, contact Keysight.


The power cord with a plug can be used to disconnect the instrument from the AC line in an
emergency. Connect the plug to an easily accessible power outlet so that the plug can be removed
from the outlet at any time. Be sure to allow enough space around the power outlet.


To connect the power cord:


1. Check that the AC power supply meets the instrument's nominal input rating, which is any nominal
voltage from 100 to 120 VAC or 200 to 240 VAC. The frequency is 50 or 60 Hz.


Large voltage distortion on the AC power line can lead to malfunction. Do not
connect the instrument to a generator or a similar device.


2. Check that the power switch is turned off.


3. Connect the power cord to the AC input receptacle on the rear panel.


4. Insert the power plug into an outlet.


28 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**AC6802B, AC6803B, and AC6804B**


The AC6802B requires use of a flexible plug and cord set which must be supplied by the user. The line
cord must have a plug on the utility side; you cannot hard-wire the instrument to utility mains.


The AC6803B and AC6804B instruments may be connected either by a flexible plug and cord set
supplied by the user or may, alternatively, be hard wired to the utility mains. See Switchboard and
circuit breaker requirements for guidance on branch circuit and circuit breaker sizing.


A switchboard circuit breaker disconnect must be provided when connecting
AC6803B and AC6804B models, regardless of whether the connection is
made with a flexible cord or by hard wiring the device to the AC utility.


**Possible Electric Shock**

Turn off the switchboard circuit breaker before connecting the cord.

Do not use the terminal block with the terminal cover removed.


**Possible Fire**

Have a qualified engineer connect the power cord to the switchboard.


**Make sure connections are correct**

Protective circuits inside the instrument, including input fuses, are connected
to match the input terminal polarity. Make sure the colors of the wires connected to the input terminals (L, N, and GND) are correct.


1. Check that the AC power supply meets the instrument's nominal input rating, which is any nominal
voltage from 100 to 120 VAC or 200 to 240 VAC. The frequency is 50 or 60 Hz.


Large voltage distortion on the AC power line can lead to malfunction. Do
not connect the instrument to a generator or a similar device.


2. Check that the power switch is turned off.


3. On Model AC6802B, remove the terminal cover and cable clamp attached to the AC Input terminal

block.


Keysight AC6800B Series Operating and Service Guide 29


1 Getting Started


4. Securely connect the power cord to match the L (line), N (neutral), and GND (ground) of the AC
Input terminal block. The protective earth terminal (ground) requires an extra length of wire compared
to the Line and Neutral wires. Always connect the ground wire first.


5. Install the terminal cover over the terminal block.


AC6802B


Place the cord inside the cable clamp and attache the clamp as shown:


30 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


AC6803B


Install one of the two ferrite cores shipped with the unit close to the power cord strain relief as shown:


AC6804B


Attach crimp terminals to the switchboard end of the power cord (the end without terminals). For
termination, attach a crimp-style terminal to each wire that meets the terminal screws of the
switchboard to be connected, and then securely connect the wires to the terminal screws.
Connection must be performed by qualified personnel.


Turn off the switchboard.


Connect the power cord to match the L, N, and GND of the switchboard.


Keysight AC6800B Series Operating and Service Guide 31


1 Getting Started

##### **Switchboard and Circuit Breaker Requirements**


Turn off the switchboard circuit breaker to disconnect the instrument from the AC line in an

emergency. The breaker must be suitably located and easily reached, and it must be
marked as the disconnecting device for the equipment.


This section is provided for guidance only; consult with local experts to ensure strict
compliance with all local electrical code and safety requirements. These requirements
take precedence over any guidance provided in this section.


Please note the following switchboard and circuit breaker requirements.


Rated current:

AC6802B: 20 A

AC6803B: 40 A

AC6804B: 80 A


Dedicate the circuit breaker for the AC6802B, AC6803B, and AC6804B.


Keep the switchboard easily accessible at all times.


Apply a label to the switchboard, clearly identifying the disconnecting device and its associated
model, as shown below:


The tables below provides information about worst case current consumption for all AC6800B Series
models for various nominal AC mains voltages. Entries are obtained by dividing the worst case power
consumption (in VA) by the nominal mains voltage and rounding to the next highest integer value. Line
currents for other nominal voltages may be calculated similarly.


32 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Maximum Input VA**


Input VA (Max) 800 1600 3200 6400


**Approximate Maximum Current**


100 8 16 32 64


120 7 13 27 53


200 4 8 16 32


208 4 8 15 31


230 3 7 14 28


240 3 7 13 27


**Breakers**


Different regions of the world have different sizing requirements for branch circuit conductors and
circuit breakers. In Europe and other regions where IEC standards apply, circuits breakers generally
are rated at 100% utilization, meaning that a device connected to a dedicated branch circuit may draw
a maximum current up to the circuit breaker rating. In the United States, the NEC generally specifies
what is known as the "80% rule" which requires branch circuits to be rated at 1.25 times the maximum
nameplate rating of the connected device.


Standard circuit breaker sizes also vary by region. The table below provides guidance for circuit
breaker sizing for various nominal mains voltages. The guidance for North American mains voltages
(120, 208, and 240 V) includes the 1.25 factor associated with the 80% rule. Guidance for the other
voltages (100, 200, and 230 V) assumes 100% utilization. By comparing the table above with the table
below, it may be seen that for the AC6801B and AC6802B models, the smallest standard size breaker
affords more than enough capability to supply the device. The AC6803B and AC6804B products drawn
higher currents and therefore are more likely to require higher breaker current ratings and dedicated

circuits.


For convenience, notes below the table give standard breaker sizes per IEC/EN 60898-1 and the NEC

for the United States.


Consult with local authorities to ensure full compliance with electrical code and safety requirements
before connecting any AC6800B Series instrument.


**Breaker Sizes**


100 15 20 35 70


120 15 20 40 70


200 15 15 20 35


Keysight AC6800B Series Operating and Service Guide 33


1 Getting Started


208 15 15 20 40


230 16 16 16 32


240 15 15 20 40


**IEC 60898-1 and European Standard EN 60898-1 Standard Sizes**


6, 10, 13, 16, 20, 25, 32, 40, 50, 63, 80, and 100 A


**NEMA Standard Sizes (also commonly used in Japan)**


15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, and 100 A


34 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Quick Start**


**Turn the Unit On and Off**


**Use the Menu System**


**Enter Numeric and Alphanumeric Values**


**Set the Output Voltage**


**Set the Output Current Limit**


**Enable the Output**


**Use Built-in Help System**


**Turn the Unit On and Off**


To turn the instrument on:


1. Check that the power switch is turned off ( **O** ) and that nothing is connected to any output terminal


block on the instrument.


2. Check that the power cord is of the correct type and correctly connected.


3. Push the ( **l** ) side of the power switch. If any unusual sound, unusual odor, fire, or smoke is perceived,

remove the power plug from the outlet or turn off the switchboard.


4. The display lights up after a few seconds. An automatic power-on self-test ensures that the instrument

is operational.


It takes about 10 seconds for the instrument to initialize before it is ready for

use.


If the instrument does not turn on, verify that the power cord is firmly connected (power-line voltage is
automatically sensed at power-on). Also make sure that the instrument is connected to an energized
power source. If the LED next to the power switch is off, there is no AC power connected. If the LED is
amber, the instrument is in standby mode with AC power connected, and if it is green, the instrument

is on.


If a self-test error occurs, a message is displayed on the front panel. For other self-test errors, see

Service and Maintenance for instructions.


Push the ( O ) side of the POWER switch to turn the instrument off.


Keysight AC6800B Series Operating and Service Guide 35


1 Getting Started


**Use the Menu System**


Press the [Menu] key to access the top level of the command menu, shown below.


The first line displays the menu path (Menu:\), and the second line indicates the items that are
available at the present menu level (Output, Measure, and so on). The third line indicates the items
under the highlighted function in the second line.


Use the left and right navigation keys to move across the menu items, and press [Select] to select the
highlighted item and move to the next menu level.


The lowest menu level programs the selected item. Use the navigation keys to select an item. Use the
numeric entry keys to enter a value, then press [Enter] . Press [Help] at the lowest menu level for
detailed help.


Press [Back] to back out of a menu level without saving changes, and press [Menu] to return to the top
menu level without saving changes.


Press [Meter] to return to Meter View.


**Enter Numeric and Alphanumeric Values**


When the focus is on a text box, its label is highlighted within a dashed outline and its present value is
displayed in white text on a dark background. Pressing any key on the numeric keypad erases the
existing value and allows you to type the rest of the number. Use the [u] and [t] keys to move the
cursor. Press the [-] key to erase the character to the left of the cursor.


To enter letters or other non-numeric characters, such as when entering a DNS host name in

[Menu] > System > IO > LAN > Modify > Name, use the up and down arrows to scroll through the list of

characters.


When finished, press either [Enter] or [Select] to enter the data.


In general, pressing [Enter] or [Select] after entering a value into a text box causes the new value to
become effective immediately. The exceptions to this rule are when you change the values in

[Menu] > Output > Voltage > AC, [Menu] > Output > Voltage > DC, and [Menu] > Output > Frequency .
These require you to select the DONE button and press [Enter] .


36 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Set the Output Voltage**


**Method 1**


Press [Menu] > Output > Voltage, then choose AC or DC .


Use the left and right navigation keys to navigate to the setting to be changed.


In the following display, the voltage setting is selected. Enter the desired setting using the numeric
keypad. Then press [Select] . Use the arrows to navigate to the DONE button and press [Enter] .


You can also use the [!] and [#] keys to adjust the value. Values take effect when the output is turned

on.


**Method 2**


Press [Voltage] to select the voltage entry field. Enter the desired setting using the numeric keypad.
Then press [Enter] .


If you make a mistake, either use the backspace key to delete the number, press [Back] to back out of
the menu, or press [Meter] to return to meter mode.


**Set the Output Current Limit**


To limit the output current, press [Menu] > Output > Current .


Use the [Select] key to enable or disable latching. Specify the AC and DC current limits, and press

[Enter] to save your changes or [Back] to discard them.


If Enable Latching is checked, a current limiting event longer than approximately three seconds will
disable the output and indicate OC protection fault. If this box is not checked, a current limiting event
will decrease the output voltage until the current goes below the limit.


**Enable the Output**


Press On/Off to enable the output. If a load is connected to the output, the front panel display will
indicate that it is drawing current. Otherwise, the current reading will be zero. The status indicator
shows the output’s status.


For a description of the status indicators, refer to Front Panel Display at a Glance.


Keysight AC6800B Series Operating and Service Guide 37


1 Getting Started


**Use Built-in Help System**


**View help**


Press [Help] to view help at any time. If you are at a menu screen, you will get help for navigating
through the menus, and if you are at a screen that allows you to view or edit settings, you will get help
pertaining to those particular settings.


Press any key other than a navigation arrow to exit Help.


**View the help information for displayed messages.**


Whenever a limit is exceeded or any other invalid configuration is found, the instrument will display a
message, including error code information.


Press any key other than a navigation arrow to exit Help.


38 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Interface Connections**


**GPIB Connection**


**USB Connection**


**LAN Connection**


This section describes how to connect to the instrument's various communication interfaces. For

additional information, refer to Remote Interface Configuration.


To begin, please install the Keysight IO Libraries Suite from the Keysight Automation-Ready CD that is
shipped with your instrument.


For detailed interface connection information, refer to the USB/LAN/GPIB Interfaces

Connectivity Guide, located on the Keysight Automation-Ready CD.


**GPIB Connection**


1. Connect your computer to your instrument's optional GPIB interface card using a GPIB interface


cable.


2. Use the Connection Expert utility of the Keysight IO Libraries Suite to connect to the installed GPIB


interface card.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments.


**USB Connection**


1. Connect the instrument's rear-panel USB device port to a USB port on your computer.


2. With the Connection Expert utility of the Keysight IO Libraries Suite running, the computer will auto
matically recognize the instrument. This may take several seconds. When the instrument is recognized, your computer will display the VISA alias, IDN string, and VISA address. This information is

located in the USB folder.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments.


**LAN Connection**


1. Connect a LAN cable from the instrument's rear-panel LAN port to the site LAN or your computer. The

instrument's factory default LAN settings automatically obtain an IP address from the network using a
DHCP server (DHCP is on). The DHCP server will register the instrument’s host name with the dynamic

DNS server. The host name and IP address can then be used to communicate with the instrument. If

you are using a private LAN, you can leave all LAN settings as they are. The instrument will automatically choose an IP address using AutoIP if a DHCP server is not present. The instrument assigns
itself an IP address from the block 169.254.nnn. The Lan indicator appears in the lower right corner of


Keysight AC6800B Series Operating and Service Guide 39


1 Getting Started


the display when the LAN port has been configured.


2. Use the Connection Expert utility of the Keysight IO Libraries Suite to add the instrument and verify a

connection. To add the instrument, you can request the Connection Expert to discover the instrument. If the instrument cannot be found, add the instrument using its host name or IP address.


3. You can now use Interactive IO within the Connection Expert to communicate with your instrument,

or you can program your instrument using various programming environments. You can also use your
computer's Web browser to communicate with the instrument. See Using the Web Interface .


40 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Remote Interface Configuration**


USB Configuration


GPIB Configuration


LAN Configuration


Modifying the LAN Settings


Using the Web Interface


Using Telnet


Using Sockets


Using HiSLIP


This section describes how to configure each remote interface.


This instrument supports remote interface communication over GPIB (optional), USB, and LAN
(default). All three interfaces are "live" at power up, and they may be used simultaneously. To use
these interfaces, install the Keysight IO Libraries software from the Keysight Automation-Ready CD
and connect the instrument to your PC.


The front-panel IO annunciator indicates remote interface activity. The Lan annunciator appears when
the LAN port is connected and configured. This instrument continually monitors the instrument's LAN
port and automatically reconfigures it when the instrument is disconnected and then reconnected to a

network.


The instrument ships with an Automation-Ready CD that contains Keysight IO Libraries Suite
software, which must be installed to enable remote-interface operations. The CD automatically starts
and provides information on installing the software. The CD also includes the _Keysight Technologies_
_USB/LAN/GPIB Connectivity Guide_, which contains additional information.


To begin configuring the remote interface from the front panel, log in to the Admin menu by pressing

[Menu] > System > Admin > Login . The default password is blank. Then press

[Menu] > System > Admin > IO to enable the desired interfaces.


Then press [Menu] > System > IO to configure the individual interfaces.


The Enable LAN services control enables or disables LAN services such as VXI-11, HiSLIP, sockets,

and telnet. However, the LAN interface, home page and configuration page remain enabled for the

Web interface.


Keysight AC6800B Series Operating and Service Guide 41


1 Getting Started


**USB Configuration**


The Keysight IO Libraries are required to control the instrument through the USB interface.


Use a standard USB cable to connect the instrument to the computer. There are no configurable USB
parameters, but you can retrieve the USB connect string using the front panel.




[Menu] > System > IO > USB


The USB connect string appears.



Not available



The instrument complies with USB Specification 2.0, USBTMC Specification 1.0, and USBTMCUSB488 Specification 1.0. The maximum data rate is 12.5 Mbps, the vendor ID is 0x2A8D, and the
product ID values are shown in the table below.


AC6801B 0x1A02


AC6802B 0x1B02


AC6803B 0x1C02


AC6804B 0x1D02


**GPIB Configuration**


The GPIB interface requires the optional GPIB interface board and uses a standard IEEE-488 cable to
connect to the computer.


Each device on the GPIB (IEEE-488) interface must have a unique whole number address between 0
and 30 (default 5). Your computer’s GPIB interface card address must not conflict with any instrument
on the interface bus. To change the GPIB address:




[Menu] > System > IO > GPIB


Use the numeric keypad to enter a value from 0 to 30. Then press [Enter] .



Not available



This setting is nonvolatile; it will not be changed by power cycling or *RST.


**LAN Configuration**


The following sections describe the primary front-panel LAN configuration functions. There are no
equivalent SCPI commands.


To begin configuring the remote interface from the front panel, press [Menu] > System > IO > LAN .


42 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


After changing LAN settings, you must save the changes by pressing
System > IO > LAN > Apply . Saving changes restarts the LAN connection with the
new settings. LAN settings are nonvolatile; they will not be changed by power
cycling or *RST. To cancel your changes, select System > IO > LAN > Cancel .


By default, DHCP is on, which may enable communication over LAN. The acronym DHCP stands for
Dynamic Host Configuration Protocol, a protocol for assigning dynamic IP addresses to networked
devices. With dynamic addressing, a device can have a different IP address every time it connects to

the network.


**Viewing Active Settings**


To view the current LAN settings:




[Menu > System > IO > LAN > Settings


Scroll with the up and down arrows.



Not available



As you scroll through the list of settings, you will see the following:


These may be different from settings requested in the front panel menu due to the configuration of the
network, and you cannot edit the settings from this screen.


Keysight AC6800B Series Operating and Service Guide 43


1 Getting Started


**Resetting the LAN**


You can perform an LXI reset of the LAN settings. This resets DHCP (ON), DNS server address
configuration, mDNS state (ON), and Web password (blank). These settings are optimized for
connecting your instrument to a site network. They should also work well for other network
configurations.


You can reset the all LAN settings to their factory defaults and restart networking. These default
settings are listed under Default Settings.




[Menu] > System > IO > LAN > Reset

or

[Menu] > System > IO > LAN > Defaults


Select Reset to activate the selected LAN settings and restart networking.


**Modifying the LAN Settings**



Not available



Press [Menu] > System > IO > LAN > Modify to modify the LAN settings.


**IP Address**


Press [Menu] > System > IO > LAN > Modify > IP to configure the instrument addressing.


If Manual is selected, additional parameters appear, as shown below.




[Menu] > System > IO > LAN > Modify > IP


Select Auto or Manual . See below for a full description.


The configurable parameters include:



Not available



44 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


Auto Automatically configures instrument addressing. When selected, the instrument will first try to obtain an IP address
from a DHCP server. If a DHCP server is found, the DHCP server will assign an IP address, Subnet Mask, and Default
Gateway to the instrument. If a DHCP server is unavailable, the instrument will try to obtain an IP address using AutoIP.
AutoIP automatically assigns an IP address, Subnet Mask, and Default Gateway addresses on networks that do not

have a DHCP server.


Manual Manually configures instrument addressing by entering values in the three fields (listed below) that only appear when

Manual is selected.



IP

Address


Subnet

Mask


DEF

Gateway



Specifies the instrument's Internet Protocol (IP) address, which is required for all IP and TCP/IP communications with
the instrument. An IP Address is of the form nnn.nnn.nnn.nnn, where each nnn is a decimal number from 0 to 255 with
no leading zeros (for example, ************).


Allows the instrument to determine whether a client IP address is on the same local subnet. The same numbering notation applies as for the IP Address. When a client IP address is on a different subnet, all packets must be sent to the
Default Gateway.


Specifies the IP Address of the default gateway that allows the instrument to communicate with systems not on the
local subnet, as determined by the subnet mask setting. The same numbering notation applies as for the IP Address. A
value of 0.0.0.0 indicates that no default gateway is defined.



Dot-notation addresses ("nnn.nnn.nnn.nnn" where "nnn" is a value from 0 to 255) must be

expressed with care, as most PC web software interprets byte values with leading zeros as octal
(base 8) numbers. For example, "***************" is actually equivalent to decimal "************"
because ".020" is interpreted as "16" expressed in octal, and ".011" as "9". To avoid confusion, use
decimal values from 0 to 255, without leading zeros.


**Host Name**


A host name is the host portion of the domain name, which is translated into an IP address.


**Front Panel** **SCPI Command**




[Menu] > System > IO > LAN > Modify > Name


You can enter any value from the numeric keypad. For additional characters, use the up/down
navigation keys to enter an alpha character by scrolling through the selection list that appears when
you press the keys. Use the left/right navigation keys to traverse the text field. Use the backspace
key to delete a value. Press [Enter] when you are finished.



Not available



Host Name - This field registers the supplied name with the selected naming service. The name may
contain upper and lower case letters, numbers, and dashes (-). If the field is left blank, no name is
registered. The maximum length is 15 characters.


Each instrument is shipped with a default host name with the format: K-modelnumber-serialnumber,
where modelnumber is the unit’s 7-character model number (e.g. AC6803B), and serialnumber is the
last five characters of the 10-character serial number located on the label on the top of the unit, for
example 45678.


Keysight AC6800B Series Operating and Service Guide 45


1 Getting Started


**DNS Server and WINS Server**


DNS is an internet service that translates domain names into IP addresses. It is also needed for the

instrument to find and display its host name assigned by the network. Normally, DHCP discovers the
DNS address information; you only need to change this if DHCP is unused or not functional.


WINS configures the Windows service of the instrument. This is similar to the DNS service that

translates domain names into IP addresses.




[Menu] > System > IO > LAN > Modify > DNS

or

[Menu] > System > IO > LAN > Modify > WINS


Select Primary Address or Secondary Address. See below for a full description.



Not available



Primary Address - This field enters the primary address of the server. Contact your LAN administrator
for details. The same numbering notation applies as for the IP Address. A value of 0.0.0.0 indicates

that no default server is defined.


Secondary Address - This field enters the secondary address of the server. Contact your LAN
administrator for details. The same numbering notation applies as for the IP Address. A value of

0.0.0.0 indicates that no default server is defined.


Dot-notation addresses ("nnn.nnn.nnn.nnn" where "nnn" is a value from 0 to 255) must be

expressed with care, as most PC web software interprets byte values with leading zeros as octal
(base 8) numbers. For example, "***************" is actually equivalent to decimal "************"
because ".020" is interpreted as "16" expressed in octal, and ".011" as "9". To avoid confusion, use
decimal values from 0 to 255, without leading zeros.


**mDNS Service Name**


The multicast Domain Name System (mDNS) service name, which can be up to 63 characters long, is
registered with the selected naming service.






[Menu] > System > IO > LAN > Modify > mDNS


You can enter any value from the numeric keypad. For additional characters, use the up/down navigation keys
to enter an alpha character by scrolling through the selection list that appears when you press the keys. Use
the left/right navigation keys to traverse the text field. Use the backspace key to delete a value. Press [Enter]
when you are finished.



Not available



mDNS Service Name - This field registers the service name with the selected naming service. The
name may contain upper and lower case letters, numbers, and dashes (-). If the field is left blank, no
name is registered.


46 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


Each instrument ships with a default service name with the format: Keysight-modelnumberdescription-serialnumber, where modelnumber is the unit’s 7-character model number (e.g.
AC6803B), description is the description, and serialnumber is the serial number on the instrument's

label.


**Services**


This enables and disables LAN services.




[Menu] > System > IO > LAN > Modify > Services .


Check (enable) or uncheck (disable) services as desired.



Not available



The configurable services include: VXI-11, Telnet, Web control, Sockets, and mDNS.


You can enable or disable Web control using the Web page 'Browser Web Control' tab.


**Using the Web Interface**


Your instrument has a built-in Web interface that lets you control it directly from the Web browser on
your computer. With the Web interface, you can access the front panel control functions including the
LAN configuration parameters. Up to six simultaneous connections are allowed. With additional
connections, performance will be reduced.


The built-in Web interface only operates over the LAN. It requires Internet Explorer
7, Firefox, or Chrome. You also need the Java (Sun) Plug-in. This is included in the

Java Runtime Environment.


The Web interface is enabled when shipped. To launch the Web interface:


Keysight AC6800B Series Operating and Service Guide 47


1 Getting Started


1. Open your computer's Web browser.


2. Enter the instrument’s host name or IP address into the browser’s Address field. The following wel
come page will appear.


48 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


3. Click on the Browser Web Control tab in the navigation bar on the left to begin controlling your instru

ment.


Should network communication issues occur, the instrument settings shown in the
Browser Web Control page may not represent the actual state of the instrument. This
may result in unexpected hazardous voltages on the output and sense connections
that could result in personal injury, death, or damage to a device under test. Before
touching the output or sense connections or connecting to a device under test, always
verify the state of the instrument.


4. Click the View and Modify Configuration tab for information about the instrument and its connectivity.


5. For additional help about any of the pages, click the **Help with this Page** tab.


Keysight AC6800B Series Operating and Service Guide 49


1 Getting Started


If desired, you can control access to the Web interface using password protection. As shipped from the
factory, no password is set. To set a password, click View & Modify Configuration . Refer to the online
help for details.


**Using Telnet**


In a DOS command window, enter the command telnet host name 5024 where host name is the

instrument's host name or IP address, and 5024 is the instrument’s telnet port.


You should get a Telnet session box with a title indicating that you are connected to the instrument.
Type the SCPI commands at the prompt.


**Using Sockets**


The instrument allows any combination of up to six simultaneous data socket,
control socket, and telnet connections to be made.


The instrument uses port 5025 for SCPI socket services. A data socket on this port can be used to
send and receive ASCII/SCPI commands, queries, and query responses. All commands must be
terminated with a newline for the message to be parsed. All query responses will also be terminated

with a newline.


The socket programming interface also allows a control socket connection. The control socket can be
used by a client to send device clear and to receive service requests. Unlike the data socket, which
uses a fixed port number, the port number for a control socket varies and must be obtained by sending
the following SCPI query to the data socket: SYSTem:COMMunicate:TCPip:CONTrol?


After the port number is obtained, a control socket connection can be opened. As with the data socket,
all commands to the control socket must be terminated with a newline, and all query responses

returned on the control socket will be terminated with a newline.


To send a device clear, send the string "DCL" to the control socket. When the power system has
finished performing the device clear it echoes the string "DCL" back to the control socket.


Service requests are enabled for control sockets using the Service Request Enable register. Once
service requests have been enabled, the client program listens on the control connection. When SRQ
goes true the instrument will send the string "SRQ +nn" to the client. The "nn" is the status byte value,
which the client can use to determine the source of the service request.


**Using HiSLIP**


The High-Speed LAN Instrument Protocol (HiSLIP) is a protocol for TCP-based instrument control. It
includes conventional test and measurement protocol capabilities with minimal performance impact.


[For technical details regarding HiSLIP, see www.ivifoundation.org.](http://www.ivifoundation.org/)


50 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Output Power Connections**


**Preparation**


**Connecting the Load Cables**


**Connecting the Remote Sense Wires**


**Preparation**


Wire Requirements


For connecting the load, use noncombustible load wires rated to carry the maximum rated output

current.


**Nominal Cross-Sectional Area (mm** **[2]** **) AWG Reference cross-sectional area (mm²) Allowable Current (A) (Ta=30 °C)**


0.9 18 0.82 17


1.25 16 1.31 19


2 14 2.08 27


3.5 12 3.31 37


5.5 10 5.26 49


8 8 8.37 61


14 5 13.3 88


The values vary depending conditions such as the wire covering (insulator) and material (allowable
temperature) and whether they are multi-core cables. For cables other than those specified in the
table above, please consult with qualified personnel.


**Output Terminal Cover**


The output terminal is located at or near the center of the rear panel. The terminal cover location is
outlined in blue. Note that the sense connector on your unit my not be in the exact location as the one
shown in this figure.


Keysight AC6800B Series Operating and Service Guide 51


1 Getting Started


**Connecting the Load Cables**


**Possible Electric Shock**


Before making any load or sense connections be sure to turn the POWER
switch off and remove the power plug from an outlet or turn off the circuit

breaker of switchboard.


When the power switch is turned off while the output is on, residual voltage
still remains at the output terminals.


Do not touch the output terminal block for at least 20 seconds after the
power switch is tuned off.


There is a danger of electric shock. Do not use the terminal block with the

terminal cover removed.


In DC mode, L is at positive potential and N is at negative potential when
setting the positive value. The opposite is true when setting the negative

value.


52 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


1. Check that the power switch is turned off.


2. Remove the terminal cover attached to the OUTPUT terminal block.


3. Securely connect the load wires to the OUTPUT terminal block. The length of the load cables should

be less than 30 meters. If the load has a ground (GND) terminal, be sure to connect it to the G terminal
of the instrument's OUTPUT terminal block. Be sure to use a wire that is greater than or equal to the

diameter of the wires used to connect the load.


4. Attach the terminal cover that you removed above using the lower holes.


The L and N terminals of the OUTPUT terminal block are isolated from the AC power line,
and the polarity does not constitute a problem in terms of safety. Grounding can be furnished using L or N.


Keysight AC6800B Series Operating and Service Guide 53


1 Getting Started


5. Unlock the ferrite core and open it.


6. Close the ferrite core. Avoid catching the wire on the ferrite core. Attach the ferrite core within 10 cm

from the OUTPUT terminal block (as indicated by the arrows below). Lock the ferrite core securely in
place. To avoid moving the ferrite core, attach the cable tie to fix the position of the ferrite core.


**When the load is located at a distance from the instrument**


The load may be away from the instrument. You can use remote control to turn the output off, but not
to turn off the POWER switch. If the load is at a distance from the instrument, install a switch between

the OUTPUT terminal block and the load to prevent electric shock, then turn the switch off.


For the switch circuit, use a two-pole type switch that cuts off L and N wires simultaneously. The
current rating of the switch must be greater than or equal to the instrument's maximum current.


**Possible electric shock**


When installing the switch between the OUTPUT terminal block and the load, be sure to
turn the POWER switch off and remove the power plug from an outlet or turn off the circuit

breaker of the switchboard.


Be sure to turn the switch off before connecting the load to the terminal at the load end of

the switch.


Do not touch the switch terminal or the output terminal when the output is on.


**Connecting the Remote Sense Wires**


**Possible Electric Shock**


Before making any load or sense connections be sure to turn the POWER switch off and
remove the power plug from an outlet or turn off the circuit breaker of switchboard.


54 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


Remote sensing compensates for voltage drops in long load cables by monitoring the output voltage
directly at the load, The sensing function can compensate up to 1 Vrms for a single load line. Always
select a load wire that is thick enough to prevent the voltage drop in the wire from exceeding the
compensation voltage.


1. Check that the power switch is turned off.


2. Remove the sense connector cover and the sense pug from the rear panel.


3. Make your connections as shown in the following figure. Connect the Ls (line sense) wire to pin 3. Con
nect the Ns (neutral sense) wire to pin 1. Sense wires size should be from AWG28 to AWG20. Strip the
sense wires back approximately 7 mm.


4. Connect the sense leads as close to the load as possible. Do NOT bundle the sense wire-pair together

with the load leads; keep the load wires and sense wires separate. Keep the wire-pair as short as possible and twist or bundle it to reduce lead inductance and noise pickup.


Keysight AC6800B Series Operating and Service Guide 55


1 Getting Started


5. Install the sense plug and the sense connector cover.


**Remote Sense Operation**


Turn the sensing function on and off using the SENSE switch located below the LAN connector on the
rear panel. Make sure the output is off when flipping the sense switch.


Flipping the SENSE switch up (RMT) turns the remote sense function on. Flipping it down (LCL) turns
remote sensing off. The above figure shows the remote sense function turned off.


The following sense fault will allow the unit to continue to operate, but the output voltage will
fluctuate by several volts.


l When the sense wires come loose (open) during operation


The following sense faults will trigger a sense fault protection (SF) on the front panel and disable the

output.


l When the sense wiring is reversed at the load

l When the sense wires are shorted together

l When both sense wires are connected to the Line at the load


l When both sense wires are connected to the Neutral at the load


l When the voltage drop exceeds 1 Vrms for a single load wire


A local lockout (LLO) command sent via the remote interface disables the operation of the
sense switch. To enable the operation of the sense switch, Use a communication
command to clear the local lockout (LLO) command


56 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Voltage Ranges and Limits**


The instrument has two voltage ranges: 155 V (low) and 310 V (high). It can also autorange between
the two ranges in AC or DC mode. The instrument will not switch voltage ranges (155 V, 310 V, or
AUTO), with the output on. If you attempt to do so, the output shuts off. Also, if you switch the range to
155 V when the voltage is set above 137.5 V the instrument sets the output voltage to 0 V.


Autoranging automatically switches to the 155 V or 310 V range according to the specified voltage. As
it does so, if the output is on, the instrument turns the output off for approximately 0.5 seconds and
turns it on again after the range switches.


The tables below indicate the output voltage setting ranges and maximum output current.


155 V 0.0 to 157.5 V -222.5 to +222.5 V


310 V 0.0 to 315.0 V -445.0 to +445.0 V


AC6801B 155 V 5 A 4 A


310 V 2.5 A 2 A


AC6802B 155 V 10 A 8 A


310 V 5 A 4 A


AC6803B 155 V 20 A 16 A


310 V 10 A 8 A


AC6804B 155 V 40 A 32 A


310 V 20 A 16 A


Keysight AC6800B Series Operating and Service Guide 57


1 Getting Started


The following chart shows the maximum current output by voltage.


**Upper and lower voltage limits**


The voltage limit ranges are shown below. Set the limits so that the upper limit is greater than or
equal to the lower limit.


AC Mode 155 V 0.0 to 157.5 V 0.0 to 315.0 V


310 V 0.0 to 315.0 V 0.0 to 315.0 V


DC mode 155 V -445.0 to 222.5 V -222.5 to 445.5 V


310 V -445.0 to +445.0 V -445.0 to +445.0 V


58 Keysight AC6800B Series Operating and Service Guide


1 Getting Started

##### **Error Checking and Soft Limits**


The AC6800 Series instruments include error checking measurements and soft limits that prevent
unintended operations from being carried.


**Basic Principles**


Settings are not automatically changed by either front-panel or SCPI commands, unless the changes
are obvious and expected.


Improper settings or combination of settings are not allowed for active settings.


User actions which would result in improper active settings generate error messages.


Inactive settings are not checked for errors in response to range changes. If these settings will
become active due to output mode changes, such as switching coupling from AC to DC, or due to the
enabling of soft limits, error checks are performed before making the settings active.


Error checks should be performed in response to the following events:


Changing Settings invokes Settings Error Checking


Enabling soft limits invokes Settings Error Checking


Changing ranges from high to low invokes Range Error Checking


Changing output coupling invokes Output Coupling Error Checking


Changing to Step mode invokes Settings Error Check


Keysight AC6800B Series Operating and Service Guide 59


1 Getting Started


**Range Error Checking**


The instrument uses the following method to check for range errors.


1. Active settings are limited by MINimum and MAXimum values. The active settings considered are only

those relevant to the output coupling. In AC+DC coupling, both DC and AC voltage settings are active, so range checking is applied to the peak absolute value of the combined waveforms.


2. In addition to the immediate DC and AC voltage, in Step mode the triggered voltage setting

(VOLT:OFFS:TRIG or VOLT:TRIG, depending on the output coupling) is also checked to ensure that an
accepted trigger does not cause an invalid state.


3. Frequency settings are independent of range, output on/off status, and output coupling.


4. Output coupling and output range may only be changed when the output is off. Therefore, the instru
ment always checks for inconsistent settings when you try to change the output coupling or range
before turning the output on.


5. If output coupling settings are changed, the Range Error Checking and Soft Limits Checking are both

made for any parameter that becomes active as a result of the output coupling change.


6. Settings that are valid on the low output range are always valid on the high output range, but the

Range Error Checking and Soft Limits Checking are both performed whenever the instrument is asked
to change ranges in either direction.


**Soft Limits Checking**


1. The soft limits are checked only if soft limits are enabled at the time the soft limits are set or at the time


when the soft limits are enabled.


2. Soft limits can be configured for DC voltage, AC voltage, and Frequency. The soft limit check is inde
pendently performed for the AC and DC components in AC+DC coupling.


3. Soft limits also apply to the corresponding trigger values of DC voltage, AC voltage, and Frequency

but only in Step mode.


**Settings Error Checking**


These settings verify that the voltage setting is correct.


In AC and DC output modes, the setting must be between the range's MIN and MAX values.


In AC+DC mode, an additional check verifies that the peak voltage does not exceed 194.5 V (low
range) or 389 V (high range).


If soft limits are enabled, the instrument checks that the setting is within the low and high soft limits.


Settings errors are checked when the user changes the mode setting to Step mode.


60 Keysight AC6800B Series Operating and Service Guide


1 Getting Started


**Output Coupling Error Checking**


Changing output coupling from AC+DC requires no error check. Otherwise, changing to DC coupling
runs the Volts DC settings error check, and changing to AC or AC+DC runs the Volts AC settings error

check.


**Coupled Parameter Error Checking**


Inactive parameters generally are not checked for being within range or compliant with soft limits even
if soft limits are active for the parameter type. This minimizes nuisance error messages when setting
values for inactive parameters. Instead, checks are performed when the parameter is made active.


However, parameter entries are always checked for being within the MIN and MAX values even if the
parameter is inactive. For example, VOLT:TRIG must be from 0 to 310, even if VOLT:MODE is FIXed.


If coupled parameter settings change, the instrument check on each active parameter setting.


For AC+DC mode, the peak value (√2×AC +|DC|) cannot exceed 389 V.


For AC mode, the voltage setting must be from 0 to the full scale value of the range, and for DC mode,
the value must be between the positive and negative full scale value of the range.


Checks are performed in the following order:


1. The Coupled Parameters Error Checking is performed first.


2. Depending on the pending front-panel entry or remote command and the instrument’s operating

status, the Coupled Parameters Error Checking may lead to the other error checks.


3. In some cases, multiple passes through the Settings Error Check are required. For example, if

OUTPut:COUPLing is changing from AC to ACDC and VOLT:OFFSet:MODE is STEP, then both the
IMMediate and TRIGgered values for DC volts are checked for being within range and for meeting the
peak voltage constraint for the currently selected range. These checks occur before the
OUTPut:COUPLing change is accepted.


Keysight AC6800B Series Operating and Service Guide 61


Keysight AC6800B Series Operating and Service Guide

## 2 User Information


**Welcome**


**Front Panel Menu Reference**


**General Front-Panel Organization**


**Selecting the Output Voltage Programming Source and Output Mode**


**Specifying Output Coupling**


**Programming AC Output**


**Programming DC Voltage**


**Setting Limit Values**


**Turning the Output On and Off**


**Storing and Retrieving Instrument States**


**Configuring Instrument Preferences**


**Calibrating from the Front Panel**


**Configuring Overcurrent Protection**


**Configuring Watchdog Protection**


**Configuring and Clearing Measurements**


**Using External Analog Control**


**Using Fault Inhibit Control**


**Viewing Error Messages**


2 User Information

##### **Welcome**


This manual includes user, service, and programming information for the Keysight AC6800B Series of
compact AC sources, which can be readily used on the bench or in a test rack. The simple user
interface allows you to easily access and view setup and measurement information directly from the
front panel or programmatically.


Choose from models up to 4000 VA, all with 0 to 310 Vrms and 40 to 500 Hz output capability. Both
LAN/LXI Core and USB interfaces are standard. You can optionally choose to add GPIB or an analog
interface board to add basic transient signals.


**Instrument Driver**


In addition to using SCPI commands, you can also control the instrument using the IVI driver, available
[at ivifoundation.org. The newest version can also be downloaded at](http://ivifoundation.org/)
[www.keysight.com/find/AC6800firmware.](http://www.keysight.com/find/AC6800firmware)


**Models and Options**


The AC6800B Series includes four models and four options, as shown below.


AC6801B 500


AC6802B 1,000


AC6803B 2,000


AC6804B 4,000


The AC6800B Series has four options, described below.


AC68BRAC3 Rack mounting kit for AC6801B, AC6802B, and AC6803B


AC68BRAC6 Rack mounting kit for AC6804B


AC68GPBU GPIB interface board


AC68BALGU Analog interface board - controls the output with external analog signals in two modes:


EXT-AC: The AC output voltage varies according to the input DC signal.
EXT-DC: The input waveform is amplified and output.


**Documentation, Firmware, and Technical Support**


[You can download the latest version of this document from www.keysight.com/find/AC6800B-doc .](http://www.keysight.com/find/AC6800B-doc)
[The latest version is also available for mobile devices at www.keysight.com/find/AC6800B-](http://www.keysight.com/find/AC6800B-mobilehelp)
[mobilehelp.](http://www.keysight.com/find/AC6800B-mobilehelp)


[For the latest firmware revision go to the product page at www.keysight.com/find/AC6800B.](http://www.keysight.com/find/AC6800B)


64 Keysight AC6800B Series Operating and Service Guide


2 User Information


If you have questions about your shipment, or if you need information about warranty, service, or
technical support, contact Keysight Technologies.


**Contacting Keysight Technologies**


United States: (*************

Europe: 31 20 547 2111
Japan: 0120-421-345


[Use www.keysight.com/find/assist to contact Keysight worldwide.](http://www.keysight.com/find/assist)


Keysight AC6800B Series Operating and Service Guide 65


2 User Information

##### **Front Panel Menu Reference**


This is an overview of the front-panel menus.


Press [Menu] to access the front-panel menus.


For a menu navigation tutorial, see Use the Menu System.


Output Voltage Sets output voltage and limits


AC Sets AC voltage and limits


DC Sets DC voltage and limits


Prog Programs voltage with optional analog board


Frequency Sets output frequency and limits


Range Selects voltage range


Coupling Selects output coupling


Current Sets output current limits


Phase Sets output-on phase


Measure IpkHold Displays or clears peak held current


Coupling Selects the measurement coupling


Average Selects number of measurements to average


Protect Current Configures current limiting


WDog Configures IO watchdog


Clear Clears protection conditions and displays output status


States Reset Resets instrument to the reset (*RST) state


SaveRecall Saves and recalls instrument settings


PowerOn Selects the power-on instrument state


66 Keysight AC6800B Series Operating and Service Guide


2 User Information



System IO Configures LAN, USB, and optional GPIB


LAN Configures LAN IO


Settings View the currently active network settings


Modify Modify the network configuration

(IP, Name, DNS, WINS, mDNS, Services)


Apply Applies the configuration changes and restarts unit


Cancel Cancels the configuration changes


Reset Performs an LXI LCI reset of LAN settings and restarts


Defaults Resets LAN settings to factory defaults and restarts


USB Displays USB identification string


GPIB Display or change the GPIB address


Preferences Displays Preferences commands


Display Configures screen saver and start-up meter view


Saver Configures the screen saver


View Configures the start-up meter view


Lock/Unlock Locks the front panel keys with a password



Admin

(must log in)



Displays Admin commands


Login/Logout Logs in and out of Admin functions


Cal Displays calibration commands


DC Calibrates DC voltage


AC Calibrates AC voltage


Current Calibrates current


Count View the calibration count


Date Saves the calibration date


Save Saves the calibration data


IO Enables/disables the LAN, USB, and GPIB


Sanitize Performs NISPOM secure erase of all user data


Password Changes the administration password



About Displays model, options, serial number, and firmware


Keysight AC6800B Series Operating and Service Guide 67


2 User Information

##### **General Front-Panel Organization**


**Keypad Layout**


**Item** **Description**


Mains Switch Rocker switch for turning the instrument on (1) and off (0). The LINE indicator turns green when

power is on.


Display 64 × 256 monochrome dot matrix display


Status LEDs PROTECT Lights in red when a protection feature has been activated


OUTPUT Lights in green when output is ON


System Keys [Meter] Selects Metering Mode displays and cycles through three display formats (METER_VI, METER_
VIP, and METER_ALL).


[Menu] Toggles between the top Menu level and the Metering Mode display (METER_VI, METER_VIP, or
METER_ALL).


[Protect] Opens the Protect menu (equivalent to [Menu] > Protect ).


[Back] Backs out of current menu, eventually returning to the Metering Mode display. Not a backspace

for numeric entries.


[Help] Displays context-sensitive help messages.


[Error] Displays messages in ERROR queue.



Navigation Keys [p] [q] [t]

[u]



Navigate within menus, display fields, or data entry fields.




[Select] Selects a menu item to go down in a menu tree. Also confirms a user entered value, such as a

GPIB address.


68 Keysight AC6800B Series Operating and Service Guide


2 User Information


**Item** **Description**


Settings Keys [On/Off] Toggles the output ON or OFF.


[Voltage] Opens the VAC or VDC (depending on output coupling mode) Settings fields in the Metering Mode
display (See Section 6).


[Freq] Opens the Frequency Settings field in the Metering Mode display.



Numeric Entry
Keys




[0] to [9] Enters a numeric digit.


[.] Enters a decimal point.


[-] Toggles the negative sign.


[E] Specifies an exponent for a numeric entry. For example, 1.23E2 indicates 123.


[-] Deletes the most recently entered digit. When a numeric field is first selected, this key erases the
entire entry.


[!] [#] Increment or decrement a Settings field. No effect on numbers entered under the [Menu] key.


[Enter] Finishes a numeric entry.



You can immediately change values by directly entering numbers into the Settings field.


**Display Layout**


This section describes the instrument's display. The default Metering Mode is shown below.


**Field** **Description**


Metering Measurement information (content varies by mode)


Status Operating status (content varies by mode)


Range Output range setting: LOW, HIGH, or AUTO


Settings Settings for VAC, VDC, and Frequency


Interface ERROR and LAN status. Shows IO in rightmost cell when remote control via LAN is operational.


The possible entries for the Status, Range, and Interface fields are shown below.


Keysight AC6800B Series Operating and Service Guide 69


2 User Information


Status Off Output is disabled


CV Constant voltage mode


CLrms Current limit mode


PL Power limit mode


OV Overvoltage protection; output OFF


OC Overcurrent protection (rms/avg); output OFF


OCPK Overcurrent protection (peak); output OFF


OP Overpower protection; output OFF


OT Overtemperature protection; output OFF


LV Low voltage protection (AC input); output OFF


WDG Watchdog timeout error; output OFF


SF Sense fault protection; output OFF


Range LOW Selected range up to 155 VAC (219 VDC)


HIGH Selected range up to 310 VAC (438 VDC)


AUTO Autoranging selected (not available in EXT-AC or EXT-DC mode)


Interface Err One or more messages in error queue


LAN (displayed) LXI LAN "No fault"


LAN (blinking) LXI LAN "Identify"


(LAN section blank) LXI LAN "Fault"


IO Remote activity via the LAN connection


If AUTO is selected prior to entering either of the external programming modes, the range selected by
autoranging is retained until changed by the user. The range field is updated to reflect the actual
range when the external programming mode is first entered (if AUTO is selected).


70 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Selecting the Output Voltage Programming Source and Output Mode**


**Select the Output Voltage Programming Source**


The EXT-AC and EXT-DC modes require the optional analog interface board (option
AC68BALGU). You cannot use the analog output interface board with the GPIB interface

because both boards use the same slot.


Without the analog interface board, only the Internal option is available. This controls the output
voltage by the instrument. The Internal option is also selected by *RST.


EXT_AC configures the instrument to behave as a variable gain amplifier controlled by an external DC
level. The internal frequency control remains in effect.


EXT_DC mode configures the instrument to behave as a fixed gain power amplifier. Internal controls
for voltage and frequency are disabled.


You cannot set the voltage limit when the instrument is being controlled by external
analog signals. An excessive external voltage may damage the load.


Front Panel:


1. Select **[Menu] > Output > Voltage > Prog** .


2. Select **Internal**, **EXT_AC**, or **EXT_DC** and press **[Select]** .


SCPI: VOLTage:PROGramming:SOURce


**Select the Output Mode**


You can switch the output among the five modes shown below when the output is turned off.


The EXT-AC and EXT-DC modes require the optional analog interface board. You cannot
use the analog output interface board with the GPIB interface because both boards use

the same slot.


AC Mode Produce AC output


DC mode Produce DC output


AC+DC mode Superimpose DC voltage on the AC output


EXT-AC mode Output sine waves using external DC signals


EXT-DC mode Amplify and output the waveform applied externally


Keysight AC6800B Series Operating and Service Guide 71


2 User Information

##### **Specifying Output Coupling**


The instruments support three types of output coupling:


AC+DC AC output with positive or negative DC offset


AC AC output


DC Positive or negative (bipolar) DC output


In AC+DC mode the combined peak voltage must be between -222.5 and +222.5 V for the low range
or -445 and +445 V for the high range.


Turn the output off to change the output coupling. The instrument performs error checking to ensure
that pending active settings remain within range.


Front Panel: [Menu] > Output > Coupling


SCPI: OUTPut:COUPling AC|DC|ACDC


72 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Programming AC Output**


You can program the voltage, frequency, frequency limits, and starting phase angle of the AC output.
Before programming the output, select the appropriate output mode and voltage range.


**Output Mode**


Front Panel: [Menu] > Output > Voltage > AC


SCPI: OUTPut:COUPling AC


**Voltage Range**


Front Panel: [Menu] > Output > Range


SCPI: [SOURce:]VOLTage:RANGe[:UPPer] 155|310|MINimum|MAXimum


The output must be off to change the range settings. The instrument checks to ensure that currently
active settings remain within the new range.


Keysight AC6800B Series Operating and Service Guide 73


2 User Information


**Voltage**


Front Panel:


Press [Voltage], enter the desired value with the numeric keypad, and press [Enter] . You may also
change the voltage and voltage limits by pressing [Menu] > Output > Voltage > AC . To specify the
voltage limits, check the Enable Limits box to enable the Lo Limit and Hi Limit .


The voltage limits do not limit output; they are safety functions that prevent unintentional
settings. For example, setting the voltage **Lo Limit** and **Hi Limit** to 10 and 20 Vrms would
prevent you from specifying 175 Vrms when you intended 17.5 Vrms.


Use the [Back] key or QUIT button to exit without saving changes, or use the DONE button to save the
changes.


SCPI:


[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]


[SOURce:]VOLTage[:LEVel]:LIMit[:STATe]


[SOURce:]VOLTage[:LEVel]:LIMit:LOWer


[SOURce:]VOLTage[:LEVel]:LIMit:UPPer


74 Keysight AC6800B Series Operating and Service Guide


2 User Information


**Frequency**


You can set the output frequency from 40 to 500 Hz for AC mode and AC+DC mode, regardless of
whether the output is on. Set the limits so that the upper limit is greater than or equal to the lower

limit.


Select [Menu] > Output > Frequency . Then enter the desired frequency and limit values on the numeric
keypad and select DONE . To specify the frequency limits, check the Enable Limits box to enable the

Lo Limit and Hi Limit .


The frequency limits do not limit output; they prevent unintentional settings. For example,
setting a frequency **Lo Limit** and **High Limit** of 40 and 45 would prevent you from accidentally entering a frequency of 425 instead of 42.5.


SCPI:


[SOURce:]FREQuency[:CW]


[SOURce:]FREQuency:LIMit[:STATe]


[SOURce:]FREQuency:LIMit:LOWer


[SOURce:]FREQuency:LIMit:UPPer


Keysight AC6800B Series Operating and Service Guide 75


2 User Information


**Phase Angle**


To select the output-on starting phase for AC output at turn-on, select [Menu] > Output > Phase .


Select the Synchronize checkbox to enable output phase control, otherwise the turn-on phase will be

random.


The output always turns off at the zero-crossing phase to ensure the instrument's output
capacitor discharges when there is no load.


SCPI: TRIGger:SYNChronize:PHASe[:ON]


**Typical Command Sequence**


A typical command sequence is shown below:


OUTPut:COUPling AC Select AC mode.


VOLTage:RANGe 155 Select the 155 V range.


VOLTage 110 Specify 110 V.


FREQuency 55 Specify 55 Hz.


OUTPut ON Turn the output on.


When the output is turned on, you cannot change the output mode or voltage range. You must turn
the output off first. However, you can change AC voltage and frequency settings when the output is

on.


The upper and lower limits may be set if the limit feature is enabled. To save your changes, navigate to
Done and press [Select] or [Enter] . You may exit without saving changes by pressing the [Back] key or
by navigating to QUIT and pressing [Select] or [Enter] . If soft limits are enabled, an error check
confirms that the frequency setting is between the low and high limits.


76 Keysight AC6800B Series Operating and Service Guide


2 User Information


VOLTage:RANGe 155 Select the 155 V range.


VOLTage:LIMit:UPPer MAX Sets upper limit to maximum, 315 V


VOLTage:LIMit:LOWer MIN Sets lower limit to minimum, 0 V


VOLTage 120 Sets voltage to 120 V


FREQuency:LIMit:UPPer MAX Sets upper limit to maximum, 500 Hz


FREQuency:LIMit:LOWer MIN Sets lower limit to minimum, 40 Hz


FREQuency 70 Sets frequency to 70 Hz


The commands above set the limits to accommodate the widest possible VOLTage and FREQuency
settings within the given range. On the 155-V voltage range, the VOLTage setting cannot exceed

157.5 V.


In AC+DC mode, the voltage can be specified only when the AC and DC voltage settings are within the
voltage limit range and the peak of the AC+DC waveform is between -445 and 445 V.


Keysight AC6800B Series Operating and Service Guide 77


2 User Information

##### **Programming DC Voltage**


The AC6800B Series can output DC voltage. When the output is turned on, you cannot change the
output mode or voltage range. You must turn the output off first. You can set the voltage regardless of
whether the output is on or off.


The general procedure for specifying DC output is:


**Step Description** **Front Panel** **SCPI Command**


1 Turn the output off (optional). [On/Off] OUTPut OFF


2 Select the DC voltage mode. [Menu] > Output - Coupling - DC OUTPut:COUPling DC



3 Select the DC voltage range (155 V, 310 V, or
AUTO) [1] .


4 Specify the DC voltage limit to be slightly larger than the voltage to be output.




[Menu] > Output - Range VOLTage:RANGe


[Menu] > Output - Voltage - DC VOLTage:OFFSet:LIMit



5 Specify the DC voltage to be output. VOLTage:OFFSet


6 Turn the output on. [On/Off] OUTPut ON


1 The DC voltage range for the 155 V range is ±222.5 V, and for the 310 V range it is ±445.0 V.


**DC Voltage Limit**


The instrument allows you to limit the allowable values for the voltage offset. The following example
allows the VOLTage:OFFSet to be set to any value within the given range. The factory default lower
limit of DC mode is 0.0 V, so remember to change the limit if you need to output a negative DC voltage.


VOLTage:RANGe 310 Select the 310 V range.


VOLTage:OFFSet:LIMit:UPPer MAX Set the upper limit to 445 V.


VOLTage:OFFSet:LIMit:LOWer MIN Set the lower limit to -445 V.


The *RST default for VOLTage:OFFSet:LIMit:LOWer is to 0 for safety reasons.


78 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Setting Limit Values**


The limit function limits the instrument's output to prevent damage to the load. You should specify
limits before applying output to the load, but you can set limits with the OUTPUT on. Note that the limit
value takes precedence over the setting value for voltage and frequency. If the current setting value
exceeds the limit range when the voltage or frequency limit value is changed, the setting value
(voltage or frequency) is set to a limit value that is closest to the current setting value.


**Current limit value and current limit operation**


The instrument has programmable AC and DC current limits. If the load attempts to draw more
average DC current or RMS AC current than the programmed limit, or if the unit attempts to draw
more peak current than the system-defined peak limit, the instrument will take action to prevent

excessive current.


The current detection response is not instantaneous.


If the instrument draws average DC current or RMS AC current that exceeds the limit, it displays OC in
the Status field and sets the CL-RMS bit (bit 12) in the STATus:QUEStionable register.


If the instrument detects peak current exceeding the limit, it displays OCPK in the Status field and sets
the CL-PEAK bit (bit 10) in the STATus:QUEStionable register.


Front Panel:


[Menu] > Protect > Current


or


[Menu] > Output > Current


Both of the above sequences set the same current limits.


SCPI:


CURR:PROT:STAT 0 The voltage output reduces the current without tripping the output. [[1]]


CURR:PROT:STAT 1 The output turns off after the voltage reduction continues for a given period.


[1]
Calculated for the RMS value. Due to the relation between the processing time of measurement and
the voltage resolution, it may take a few seconds to exceed the current limit.


Whenever the instrument detects peak current exceeding the limit, it raises an OVERLOAD warning,
and sets the CL-PEAK bit (bit 10) on the STATus:QUEStionable register.


The current limit value of the output current can be specified as shown in the following table.


Keysight AC6800B Series Operating and Service Guide 79


2 User Information









AC Mode 0.1 to 5.25 A 0.2 to 10.5 A 0.4 to 21.0 A 0.8 to 42.0 A


DC mode 0.1 to 4.2 A 0.2 to 8.4 A 0.4 to 16.8 A 0.8 to 33.6 A


80 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Turning the Output On and Off**


Press [On/Off] to toggle the output on and off.


ON: The specified voltage is output and the Output LED turns green.


OFF: The output voltage turns off (high impedance mode) and the Output LED turns off.


The output is always off at power-on.


If a protection function trips, the output is turned off. However, if the current limit operation is limit
control and the overload protection function (current limit) trips, the output is not turned off.


To prevent the possibility of electric shock, do not touch the output terminal block.


If a capacitor, battery, or similar device is connected as a load in DC mode, voltage
remains at the section connected to the output terminal block even when the output is off
until the load energy is discharged. The discharge time of the internal capacitor when no
load is connected is approximately 0.1 seconds. To prevent the possibility of electric
shock, do not touch the output terminal block.


When the output is turned on, several volts of undershoot or overshoot may appear for a
time period on the order of ten microseconds.


**Principle of Output on/off**


The AC6800B Series instruments do not disconnect output from the internal circuits mechanically
using switches and relays. Instead, when the output is turned off, the instruments electrically increase
output impedance to limit the output without chattering.


The resistance in the high-impedance condition is shown below.


155 V approx. 40 kΩ approx. 20 kΩ approx. 10 kΩ approx. 5 kΩ


310 V approx. 80 kΩ approx. 40 kΩ approx. 20 kΩ approx. 10 kΩ


Keysight AC6800B Series Operating and Service Guide 81


2 User Information

##### **Storing and Retrieving Instrument States**


**State Storage**


The instrument stores the parameters shown in the table below at five-second intervals. To ensure
that settings changes are saved before power-down, wait at least five seconds after changing a
setting before turning the instrument off. Otherwise, the last settings may not be stored.


The instrument's output is always off at power up.


**Stored Settings**


Output mode (AC, DC, AC+DC, EXT-AC, or EXT-DC)


Output voltage range (155 V, 310 V, or AUTO)


Voltages, AC and DC


Frequency


Measured value display (RMS, PEAK, AVG, or W)


Limit values AC Voltage limits (lower and upper)


Current limit


Frequency limits (lower and upper)


DC Voltage limits (lower and upper)


Current limit


Current limit operation


**Save and Recall Instrument States**


You can save instrument states into 10 nonvolatile state storage locations. This allows you to switch
among several configurations quickly, or to test sudden changes in output voltage or frequency.
Select [Menu] > States > SaveRecall to save or recall a state. Specify the state storage location (0
through 9) and select either Recall or Save .


If a state is recalled with the output on and includes an output mode or voltage range change, a short
alarm sounds and the state is not recalled when the output mode or voltage range switches. If this
happens, turn the output off, recall the memory, and then turn the output back on.


SCPI: *RCL and *SAV


82 Keysight AC6800B Series Operating and Service Guide


2 User Information


**Reset the Instrument to Factory Default Values**


Select [Menu] > States > Reset > Reset to reset the instrument to its factory defaults.


SCPI: *RST


**Configure the Instrument's Power-on State**


Select [Menu] > States > PowerOn to configure the instrument's power-on state.


Reset state (*RST) is the factory default.


Recall state 0 will recall the state saved in location 0.


Auto resume causes the unit to power up in its power-down state. State changes occurring in the last
three to four seconds before power-down may not be restored. For this option, the output is always off
regardless of whether it was on at power-down.


Keysight AC6800B Series Operating and Service Guide 83


2 User Information

##### **Configuring Instrument Preferences**


Press [Menu] > System - Preferences to configure the display and to lock and unlock the instrument.


**Configure the Screen Saver**


Press [Menu] - System > Preferences > Display > Saver to configure the screen saver.


Screen saver – enables the screen saver function.


Saver delay – specifies the time in minutes between the last time any communication occurs with the
instrument or a key is pressed until the screen saver goes into effect.


Wake on I/O – restores the display whenever any I/O interface activity occurs.


SCPI:


SYSTem:SSAVer[:STATe]


SYSTem:SSAVer:DELay


SYSTem:SSAVer:RWAKeup


**Specify the Startup Meter View**


1. Press **[Menu] > System > Preferences > Display > View** to select the instrument's initial meter


view.


2. Press **[Select]** and use the up and down arrows to highlight your selection.


3. Press **[Select]** to confirm your selection and then press **[Meter]** to exit. This setting does not change

the current meter view; it only changes upon cycling power. For example, the image below shows the
starting meter view with **Volt, Curr, Power** selected.


84 Keysight AC6800B Series Operating and Service Guide


2 User Information


**Lock and Unlock the Front Panel**


Be certain that you enter the password carefully and remember it. You cannot clear the
password by cycling instrument power.


Press [Menu] - System > Preferences > Lock to lock the front panel.


There is no factory default password.


The password must be 4 to 15 characters long and may include any of these characters:


A-Z Upper case letters


a-z Lower case letters


0-9 Numeric digits


+ - ( ) ., <space> Plus, minus, parentheses, period, comma, space


To unlock the instrument, press any key other than [Meter] and enter the password.


Keysight AC6800B Series Operating and Service Guide 85


2 User Information

##### **Calibrating from the Front Panel**


Log in with the administration password at [Menu] > System > Admin > Login and then use

[Menu] > System > Admin > Cal to enter the calibration menu, shown below. Remember to log out with

[Menu] > System > Admin > Logout after you have completed your calibration tasks.


**Node** **Meaning**


DC Calibrate DC voltage programming and measurement (low and high ranges).


AC Calibrate AC voltage programming and measurement (low and high ranges).


Current Calibrate current measurement (low and high ranges).


Count View the calibration count. The calibration count is incremented whenever calibration is saved, the administration
password is changed, or the instrument firmware is updated.


Be sure to read and record the calibration count when you first receive your instrument from the factory.


Date Enter the calibration date, which can be up to 15 characters.


Save Save the updated calibration constants.


For calibration details, see Calibration.


86 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Configuring Overcurrent Protection**


Use the [Menu] > Protect menu to configure or clear overcurrent protection. Note that this same menu
can be found under [Menu] > Output - Protect .


**Configure Overcurrent Protection**


To configure overcurrent protection, select [Menu] > Protect > Current .


The current limits are based on the RMS value for both AC and DC current (DC and RMS values are
equivalent for DC-only signals). If AC+DC output coupling is selected, the AC Current Limit applies.


If Enable Latching is checked, a current limiting event longer than approximately three seconds will
disable the output and indicate OC protection fault. If this box is not checked, a current limiting event
will decrease the output voltage until the current goes below the limit.


Current limit settings vary by model, as shown below. The AC limits apply in AC and AC+DC output
coupling modes.


AC6801B 0.1 to 5.3 0.1 to 4.2


AC6802B 0.2 to 10.5 0.2 to 8.4


AC6803B 0.4 to 21.0 0.4 to 16.8


AC6804B 0.8 to 42.0 0.8 to 33.6


SCPI: [SOURce:]CURRent:PROTection:STATe


**Clear Overcurrent Protection**


To clear an overcurrent protection condition, select [Menu] > Protect > Clear .


The current output status condition is displayed to help you ensure that clearing the protection
condition is appropriate and likely to produce expected results.


SCPI: OUTPut:PROTection:CLEar


Keysight AC6800B Series Operating and Service Guide 87


2 User Information

##### **Configuring Watchdog Protection**


The watchdog protection feature places the output in protection mode if a SCPI command or query is
not received within the specified delay period, which may be set between 1 and 3600 seconds (factory
default 60).


Press [Menu] - Protect > WDog to configure watchdog protection. It is disabled by factory default.


SCPI:


OUTPut:PROTection:WDOG[:STATe]


OUTPut:PROTection:WDOG:DELay


88 Keysight AC6800B Series Operating and Service Guide


2 User Information

##### **Configuring and Clearing Measurements**


The Measure menu [Menu] > Measure allows you to view and clear the current peak hold value,
configure the measurement coupling, and specify the number of readings to average.


**View and Clear IpkHold**


Press [Menu] > Measure > IpkHold to display the current peak hold value. Higher peak values
overwrite previously measured lower values until power is cycled or until IpkHold is reset using Clear .


The number displayed next to ApkH is not editable; you may either press Clear or [Back] to exit. Using

[Back] does not clear the value.


SCPI: SENSe:CURRent[:PEAK]:HOLD:CLEar


**Configure Measurement Coupling**


Press [Menu] > Measure > Coupling to configure measurement coupling (AC, DC, or AC+DC). The
factory default is AC+DC.


AC+DCThe RMS value of the output is presented in the metering displays.
AC The AC component of the output is presented in the metering displays.
DC The average (DC) component of the output is presented in the metering displays.


The Meter All display is not affected by measurement coupling selection.


**Specify Number of Readings to Average**


Press [Menu] > Measure > Average to specify the number of readings to average for measurements.
The drop-down menu allows you to choose 1, 2, 4, 8, or 16. Press [Select] to make your selection.


SCPI: SENSe:AVERage


Keysight AC6800B Series Operating and Service Guide 89


2 User Information

##### **Using External Analog Control**


**EXT-AC Mode**


**EXT-DC Mode**


The analog interface board (option AC68BALGU) supports two external analog programming methods,
EXT-AC and EXT-DC. In EXT-AC mode the voltage of the output AC waveform (sine wave) is varied
according to the input DC signal. In EXT-DC mode the input waveform is simplify amplified and output.


You cannot set the voltage limit when the AC source is being controlled using external
analog signals. Accidentally applying an excessive external voltage may damage the

load.


**Analog controls**


**Control Description**


INPUT BNC terminal for applying the external signal. The input is electrically isolated from the output terminals of the AC

source.


ATT Input attenuator switch


GAIN Variable resistor for fine-adjusting the gain (voltage amplification ratio)


OFFSET Variable resistor for fine-adjusting the offset.


90 Keysight AC6800B Series Operating and Service Guide


2 User Information


**EXT-AC Mode**


In EXT-AC mode, the unit outputs AC voltage ranging from 0 V to 155 V (when the 155 V range is
selected) or 0 V to 310 V (when the 310 V range is selected) with respect to a DC input signal ranging

from 0 V to ±10 V.


The ATT switch must be set ON. Otherwise the load can be damaged due to excessive
voltage.


Step 1. Turn the AC source off


Step 2. Turn the ATT switch on. The allowable input DC voltage range is -10 V to +10 V.


Step 3. Connect an external signal (generator) to the INPUT terminal.


Step 4. Turn the AC source on.


Step 5. Select EXT_AC mode.


Step 6. Select a voltage range (155 V or 310 V). Autoranging is not available in analog mode. If Auto
was selected before the instrument entered EXT-AC mode, the presently selected range is retained.


Step 7. Set the frequency (40 Hz to 500 Hz). You can also set the frequency using the Meter display
(see step 9).


Step 8. Apply the external signal to the INPUT terminal.


Step 9. Turn the output on. The analog output voltage is displayed on the Meter menu. Note that the
frequency settings can be adjusted from the Meter display in the settings area.


Keysight AC6800B Series Operating and Service Guide 91


2 User Information


**Adjusting the Offset**


You can fine-adjust the offset by turning the OFFSET adjustment using a Phillips screwdriver. Adjust
the offset so that the output voltage is minimum with the input BNC connector shorted.


**Adjusting the Gain**


You can fine-adjust the gain by turning the GAIN adjustment using a Phillips screwdriver . Adjust the
gain so that the output voltage is 155 Vac (in the 155 V range) when 10 VDC is applied to the input

BNC connector.


**EXT-DC Mode**


In EXT-DC mode, the input waveform is simply amplified and output.


With the ATT switch set OFF, the AC source outputs a voltage 100 or 200 times the input
voltage ranging from -2.19 V to +2.19 V. With the ATT switch set ON, the AC source outputs voltage ranging from -219 V to +219 V (when 155 V range is selected) or -438 V to
+438 V (when 310 V range is selected) with respect to a signal input ranging from -10 V to

+10 V.


Step 1. Turn the AC source off


Step 2. Set the ATT switch On or Off.
Off: The input voltage range is -1.90 V to +1.90 V (peak value) The allowable input DC voltage range is

-10 V to +10 V.

On: The input voltage range is -10 V to +10 V.


Step 3. Connect an external signal (generator) to the INPUT terminal.


Step 4. Turn the AC source on.


Step 5. Select EXT_DC mode.


Step 6. Select a voltage range (155 V or 310 V). Autoranging is not available in analog mode. If Auto
was selected before the instrument entered EXT-AC mode, the presently selected range is retained.


Step 7. If an AC voltage is being applied, set the frequency (40 Hz to 500 Hz). To display the measured
value accurately, set the frequency so that it matches the frequency of the external input signal. If the
set frequency is out of synchronization, the measured value will be unstable.


92 Keysight AC6800B Series Operating and Service Guide


2 User Information


Step 8. Apply the external signal to the INPUT terminal.


Step 9. Turn the output on. The analog output voltage is displayed on the Meter menu.


**Adjusting the Offset**


You can fine-adjust the offset by turning the OFFSET adjustment using a Phillips screwdriver. Adjust
the offset so that the output voltage is as close to 0 V (DC) as possible with the ATT switch turned off
and the input BNC connector shorted.


**Adjusting the Gain**


You can fine-adjust the gain by turning the GAIN adjustment using a Phillips screwdriver . Adjust the
gain so that the output voltage is 155 Vac (in the 155 V range) when 1.55 Vac is applied to the input

BNC connector with the ATT switch turned off.


Keysight AC6800B Series Operating and Service Guide 93


2 User Information

##### **Using Fault Inhibit Control**


**Live Mode**


**Latching Mode**


**Inhibit Example**


This section describes the functionality, the hardware and firmware requirements of the AC68BFIU

Fault Inhibit Interface board for the AC68xxB Power Sources.


The Fault Inhibit interface board (option AC68BFIU) lets an external input signal control the output
state of instrument. The input signal is level triggered. The signal is referenced to chassis ground. The
input polarity and the operating mode are user-configurable.


The AC68BFIU Fault Inhibit Interface board can only be installed in the "B" version AC

power sources.


**Inhibit Connections**


Electrical Characteristics


**+ Input characteristic** **Description**


Maximum voltage rating: 16.5 VDC (Input is 5 V tolerant with internal pull-up)


Maximum low-level input voltage: 0.8 VDC


Minimum high-level input voltage: 2 VDC


Typical high-level current: 6 mA (@ 16.5 VDC)


Typical low-level current: 2 mA (@ 0 V with internal 2.2 k pull-up)


Minimum pulse width: 3 ms


Referenced to: Chassis ground pin


Wire Connections


Make your connections as shown in the following figure. Connect the signal wire to the + pin. Connect
the return wire to the ground pin 1. Wires size should be from AWG28 to AWG20. Strip the wires back
approximately 7 mm.


94 Keysight AC6800B Series Operating and Service Guide


2 User Information


**Live Mode**


Live mode - allows the enabled output to follow the state of the inhibit signal. When the inhibit signal
is true, the output is disabled; when the inhibit signal is false, the output is enabled. The inhibit signal
is not latched, and the inhibit event is not treated as a protection event. Therefore, a protection clear is
not required when switching between inhibit true and inhibit false.


Step 1. Turn the AC source off


Step 2. Connect an external switch or circuit to the Inhibit terminals.


Step 3. Turn the AC source on.


Step 4. Select Live as the inhibit mode.


SCPI: OUTPut:INHibit:MODE


Step 5. Select a polarity for the Live inhibit mode.


SCPI: DIGital:PIN:POLarity


Step 6. Turn the output on. When the inhibit signal disables the output, the status indicator displays
"INH". The voltage and current meters will display zero.


Keysight AC6800B Series Operating and Service Guide 95


2 User Information


**Latching Mode**


Latching mode - disables the output when the inhibit signal is true. This is treated as a protection
event, and output remains disabled regardless of subsequent inhibit signal changes. To return the
output to normal operation, the Inhibit input must remain false and the latched inhibit signal must be
cleared (see "Clearing a latched Inhibit Signal" at the end of this section). The output remains off after
the protection is cleared and must be re-enabled to turn it on again.


Step 1. Turn the AC source off


Step 2. Connect an external switch or circuit to the Inhibit terminals.


Step 3. Turn the AC source on.


Step 4. Select Latching as the inhibit mode.


SCPI: OUTPut:INHibit:MODE


Step 5. Select a polarity for the Latching Inhibit mode.


SCPI: DIGital:PIN:POLarity


Step 6. Turn the output on. When the inhibit signal disables the output, the status indicator displays
"INH". The voltage and current meters will display zero.


**Inhibit Example**


As shown in the following figure, you can connect the Inhibit input to a manual switch or external
control signal that will short the inhibit pin to ground whenever it is necessary to disable the output.
Negative polarity must be programmed for the inhibit pin in this case.


96 Keysight AC6800B Series Operating and Service Guide


2 User Information


In Live mode, when the switch is closed (Inhibit signal is true) the output is disabled. When the switch
is opened (Inhibit signal is false), the output will once again follow the output state setting.


In latching mode, when the switch is closed (inhibit signal is true) the output is disabled. This is treated
as a protection event, and output remains disabled regardless of subsequent inhibit signal changes.
To return the output to normal operation, the switch must be opened and the latched inhibit signal
must be cleared (see "Clearing a latched Inhibit Signal" at the end of this section). The output remains
off after the protection is cleared and must be re-enabled to turn it on again.


**Clearing a latched Inhibit Signal**


To clear a latched inhibit signal, select [Menu] > Protect > Clear . Then select Clear.


SCPI: OUTPut:PROTection:CLEar


The output remains off after the protection function is cleared.


Keysight AC6800B Series Operating and Service Guide 97


2 User Information

##### **Viewing Error Messages**


The Err annunciator in the lower right corner indicates messages in the error queue.


Press [Error] to view errors in the error queue. You can also use the SCPI SYSTem:ERRor? query to
view error messages.


Errors in the error queue are stored and retrieved on a first-in, first-out basis. Once an error has been
read, either by remote command or from the front panel, it is cleared. The only exception is self-test
errors, which cannot be cleared until the underlying condition is corrected.


If no errors are in the error queue, "No errors" appears when you press [Error] .


See SCPI Error Messages for a list of error messages.


98 Keysight AC6800B Series Operating and Service Guide


Keysight AC6800B Series Operating and Service Guide

## 3 SCPI Programming Reference


**Introduction to SCPI**


**Status System Overview**


**Command Quick Reference**


**Commands by Subystem**


**ABORt Subsystem**
**CALibrate Subsystem**
**CURRent Subsystem**
**DIGital Subsytem**
**DISPlay Subsystem**
**FETCh/MEASure Subsystem**
**FREQuency Subsystem**
**HCOPy Subsystem**
**IEEE-488 Common Commands**

**INITiate Subsystem**
**LXI Subsystem**
**OUTPut Subsystem**
**SENSe Subsystem**
**STATus Subsystem**
**SYSTem Subsystem**
**TRIGger Subsystem**
**VOLTage Subsystem**


**Default Settings**


**SCPI Error Messages**


3 SCPI Programming Reference

##### **Introduction to SCPI**


Introduction


**Keywords**


**Queries**


**Command Separators and Terminators**


**Syntax Conventions**


**Parameter Types**


**Device Clear**


**Typical Command Processing Times**


**Introduction**


This manual describes instrument programming using Standard Commands for Programmable
Instruments (SCPI) over LAN, USB, and GPIB (optional). Select the interface type from the front panel,
and be sure to understand the SCPI syntax and functions before you send SCPI commands and
queries.


The SCPI language is an ASCII-based programming language for test and measurement instruments.
SCPI has two types of commands, common and subsystem.


**IEEE-488.2 Common Commands**


The IEEE-488.2 standard defines common commands that perform functions such as reset, self-test,
and status operations. Common commands always begin with an asterisk ( - ), are three characters
long, and may include parameters. The command keyword is separated from the first parameter by a

space.


**Subsystem Commands**


Subsystem commands perform specific instrument functions. They extend one or more levels below
the root in a hierarchical structure, or tree. Associated commands are grouped together under a
common node, thus forming subsystems. A portion of the OUTPut subsystem is shown below to
illustrate the tree system. Note that the brackets around a keyword, such as in [:STATe], indicate an
optional keyword.


:OUTPut


[:STATe] ON|1|OFF|0

:COUPling AC|DC|ACDC

:PROTection

:CLEar


100 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**Keywords**


Keywords, also referred to as headers, are instructions recognized by the instrument. Common
commands are also keywords.


OUTPut is a root keyword, STATe, COUPling, and PROTection are second-level keywords, and CLEar
are third-level keywords. Colons ( : ) separate the keyword levels.


The command syntax shows most commands (and some parameters) as a mixture of upper- and
lower-case letters. The upper-case letters indicate the abbreviated spelling for the command. For
shorter program lines, you can send the abbreviated form. For better readability, send the long form.


In the above examples, OUTP and OUTPUT are both acceptable. You can use upper- or lower-case
letters. Therefore, OUTPUT, outp, and OuTp are all acceptable. Other forms, such as OUT, will

generate an error.


**Queries**


Following a keyword with a question mark ( ? ) turns it into a query (Example: VOLTage?,
VOLTage:TRIGgered?). If a query contains parameters, place the question mark after the last
keyword, before the parameters. Insert a space between the question mark and the first parameter.


You can query the value of most parameters. For example, to query the output voltage, send:


VOLTage?


You can also query the minimum or maximum allowable voltage:


VOLTage? MIN

VOLTage? MAX


Wait for all of the results of a query to be returned before sending another command or query.
Otherwise, a _Query Interrupted_ error will occur and the unreturned data will be lost.


**Command Separators and Terminators**


**Separators**


Colons ( : ) separate keyword levels. Blank spaces separate command parameters from their
corresponding keyword. If a command requires more than one parameter, a comma separates
parameters. In the following example, the frequency (100), low frequency limit (90), and high
frequency limit (110) are separated by commas. Note the space after the word FREQuency.


FREQuency 100,90,110


Semicolons ( ; ) separate commands within the same subsystem. This lets you send several
subsystem commands within the same message string. For example, the following command:


FREQuency 100,90,110;MODE FIXed


Keysight AC6800B Series Operating and Service Guide 101


3 SCPI Programming Reference


is the same as sending the following commands:


FREQuency 100,90,110

FREQuency:MODE FIXed


You can also combine commands of different subsystems within a message string. In this case, you
must use a colon to return the command parser to the root level in order to access another
subsystem. For example, you could specify the frequency and turn on the output as follows:


FREQuency 100,90,110;:OUTPut ON


The colon after the semicolon returns the command parser to the root.


**Terminators**


A command string must terminate with a new line (<NL>) character. The IEEE-488 EOI (End-OrIdentify) message is interpreted as a <NL> character and can be used to terminate a command string
in place of an <NL>. A carriage return followed by a new line (<CR><NL>) is also accepted. Command
string termination always resets the SCPI command path to the root level.


**Syntax Conventions**


Triangle brackets ( < - ) indicate that a parameter. For example, in the command syntax DISPlay

[:WINDow]:TEXT "< _string_ >", the < _string_ - parameter is inside triangle brackets. These brackets are not
sent with the command string. For example, you could send DISPlay:WINDow:TEXT "Test in
progress".


A vertical bar ( | ) separates multiple parameter choices for a given command string. For example,
AC|DC|ACDC in the OUTPut:COUPling command indicates that you can specify AC, DC, or ACDC. The
bar is not sent with the command string.


Square brackets ( [ ] ) indicate that a keyword or parameter is optional. These brackets are not sent
with the command string. If you do not specify a value for an optional parameter, the instrument
ignores the parameter. In the DISPlay[:WINDow]:TEXT example above, the optional [:WINDow] level
means that DISPlay:TEXT is the same as DISPlay:WINDow:TEXT.


**Parameter Types**


The SCPI language defines several data formats to be used in commands and queries.


**Numeric Parameters**


Commands that require numeric parameters will accept all commonly used decimal representations
of numbers including optional signs, decimal points, and scientific notation. If a command accepts only
certain specific values, the instrument will automatically round the input numeric parameters to the
accepted values. The following command requires a numeric parameter for the voltage value:


[SOURce:]CURRent < _value_ >|MINimum|MAXimum


102 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


Note that special values for numeric parameters such as MINimum and MAXimum are also accepted.
Instead of selecting a specific value for the voltage parameter, you can substitute MIN to set the
voltage to its minimum allowable value, or MAX to set it to its maximum allowable value.


You can also optionally include the following engineering unit suffixes with numeric parameters:


Voltage V


Current A


Wattage W


Apparent Power VA


Reactive Power VAR


Degrees DEG


Frequency HZ


You may also put the following prefixes before a unit of measure:


U micro 1/1,000,000


M milli 1/1,000


K kilo 1,000


As with other SCPI keywords, both units and prefixes are case-insensitive. Thus, you could indicate
millivolts as mv, mV, Mv, or MV.


**Discrete Parameters**


Discrete parameters specify settings that have a limited number of values (like IMMediate, EXTernal,
or BUS). Like command keywords, they may have short and long forms. You can mix upper- and lowercase letters. Query responses always return the short form in upper-case letters. The following
command requires a discrete parameter:


DISPlay:VIEW METER_VI|METER_VP|METER VIP


**Boolean Parameters**


Boolean parameters represent a single binary condition that is either true (1 or ON) or false (0 or OFF).
A boolean query always returns 0 or 1. The following command requires a boolean parameter:


DISPlay ON|1|OFF|0


**ASCII String Parameters**


String parameters can contain virtually any ASCII characters. A string must begin and end with
matching single (') or double (") quotation marks. To include the quote delimiter as part of the string,
enter it twice without any characters in between. The following command uses a string parameter:


DISPlay:TEXT "Test in progress . . ."


Keysight AC6800B Series Operating and Service Guide 103


3 SCPI Programming Reference


**Arbitrary Block Program or Response Data**


Definite-length block data <Block> allows any type of device-dependent data to be programmed or
returned as a series of 8-bit binary data bytes. This is particularly useful for transferring large
quantities of data or 8-bit extended ASCII codes.


**Device Clear**


Device Clear is an IEEE-488 low-level bus message that you can use to return the instrument to a
responsive state. Different programming languages and IEEE-488 interface cards provide access to
this capability through their own unique commands. The status registers, the error queue, and all
configuration states are left unchanged when a Device Clear message is received.


Device Clear performs the following actions:


If a measurement is in progress, it is aborted.


The instrument returns to the trigger idle state.


The instrument's input and output buffers are cleared.


The instrument is prepared to accept a new command string.


The ABORt command is the recommended method to terminate an instrument

operation.


**Typical Command Processing Times**


The table below documents some typical, average command processing times (milliseconds) for
various commands and queries. This can help you determine the impact of some common SCPI

commands on total test time.


The command processing time is the time until the next command is accepted. It does not include
hardware response time.


*CLS 5 6 Clear the status data.


*RCL 233 230 Recall the contents of a state storage location.


*RST 233 230 Perform a device reset.


*SAV 13 14 Save the current settings.


FREQuency 15 17 Set the AC output frequency.


MEASure:CURRent:AC? 333 333 Query the AC current.


MEASure:VOLTage:AC? 333 333 Query the AC voltage.


OUTPut OFF 19 23 Turn the output OFF.


OUTPut ON 9 11 Turn the output on.


VOLTage 16 18 Set the AC voltage.


[1]
Using Keysight IO Libraries.


104 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **Status Overview**


This section provides a detailed description of the individual registers and register groups. The status
diagram below provides an graphical view of how the status registers and groups are interconnected.


Keysight AC6800B Series Operating and Service Guide 105


3 SCPI Programming Reference


**Status Registers**


**Operation Status Group**


**Questionable Status Group**


**Standard Event Status Group**


**Status Byte Register**


**Error and Output Queues**


**Status Registers**


The Operation and Questionable status groups use four different types of registers to track qualify,
flag, and enable instrument events. The Standard Event group only uses Event and Enable registers.


The instrument uses IEEE 488 and SCPI registers for the status reports. Each SCPI status register has
the following subregisters:


CONDition register


EVENt register


ENABle register


The registers also include NTRansition and PTRansition filters.


**Item** **Description**


CONDition register A Condition register continuously monitors the instrument state and its bits are updated in real time. The bits

are not latched, and reading this register does not affect the contents.


EVENt register An Event register latches transitions that pass through the positive and negative transition registers. When
an event bit is set, it remains set until the Event register is read. Reading the Event register clears it. The
EVENt register bits are automatically set according to the changes in the CONDition register and is reset
when read. The rule varies depending on the positive and negative transition filters (PTRansition and
NTRansition).


ENABle register An Enable register defines which bits in the event register are reported to the Status Byte register. You can
write to or read from an enable register.


Transition filter The transition filters enable or disable the reporting of events when the condition changes from false to true
(PTRansition - positive) or from true to false (NTRansition - negative).


If both filters are enabled, events will be reported each time the status changes; if both filters are cleared,
event reporting is disabled.


**Operation Status Group**


These registers record signals that occur during normal operation. The group consists of a Condition,
PTR/NTR, Event, and Enable register. The outputs of the Operation Status register group are logically
OR-ed into the OPERation summary bit (7) of the Status Byte register. See Status Registers for a
description of each register.


106 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**OPERation status register**


The OPERation status register contains information about conditions that are part of the instrument's
normal operation.


**Bit** **Value** **Bit Name** **Description**


0-3 1 to 8 (not used) (not used)


4 16 MEAS-active A measurement is in progress.


5 32 WTG-meas The instrument is waiting for a TRIGger for the measurement (ACQuire) trigger subsystem.


6 64 WTG-tran The instrument is waiting for a TRIGger for the TRANsition trigger subsystem.


7 128 (not used) (not used)


8 256 CV The instrument is in constant voltage (CV) output mode


9-15 512 to 32,768 (not used) (not used)


**Questionable Status Group**


These two register groups record signals that indicate abnormal operation. The bits in this register
may indicate problems with acquired data. The group consists of a Condition, PTR/NTR, Event, and
Enable register. The outputs of the Questionable Status group are logically OR-ed into the
QUEStionable summary bit (3) of the Status Byte register. See Status Registers for a description of
each register.


**Bit Value Bit Name** **Description**


0 1 OV (Overvoltage Protection) Output is disabled by the overvoltage protection


1 2 OC (Overcurrent protection) Output is disabled by the overcurrent protection


2 4 HWF Output is disabled by hardware failure


3 8 LV Low voltage detected


4 16 OT Output is disabled by the overtemperature protection


5 32 WDOG Watchdog protection (caused by no SCPI IO activity over specified time)


6 64 INH Remote inhibit is active


7 128 (not used) 0 is returned


8 256 CAL Calibration failure


9 512 OP Overpower protection


10 1024 CL-PEAK Current Limit on PEAK (overload state)


11 2048 PL Power limit (overload state)


12 4096 CL-RMS Current Limit on RMS (overload state)


13 8192 SF Sense Fault protection


14 16384 MEAS-OVLD Measurement overload detected


15 32768 (not used) 0 is returned


Keysight AC6800B Series Operating and Service Guide 107


3 SCPI Programming Reference


**Standard Event Status Group**


These registers are programmed by Common commands. The group consists of an Event and Enable
register. The Standard Event event register latches events relating to communication status. It is a
read-only register that is cleared when read. The Standard Event enable register functions similarly
to the enable registers of the Operation and Questionable status groups. See Status Registers for a
description of each register.


The event status register bits are set when certain events occur during instrument operation.


The register is controlled by the IEEE 488 commands *ESE, *ESE?, and *ESR?.


**Bit Value Bit Name** **Description**



0 1 Operation Complete (OPC)



All commands before and including *OPC have completed.



1 2 (not used) 0 is returned


2 4 Query Error (QYE) The instrument tried to read an empty output buffer, a command was received before a previous
query was read, or the input and output buffers are full.



3 8 Device Dependent
Error (DDE)


4 16 Execution Error

(EXE)


5 32 Command Error

(CME)



A device-specific error occurred. Error Messages


An execution error occurred. Error Messages. A valid SCPI command may not be executed correctly depending on the instrument's conditions.


A command syntax error occurred. Error Messages



6 64 Reserved 0 is returned


7 128 Power ON (PON) Power has been cycled since the last time the event register was read or cleared.


108 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**Status Byte Register**


This register summarizes the information from all other status groups and stores STB and RQS
messages as defined by the IEEE 488 standard. The *STB? query reads the status byte register and
transmits the contents of the status byte register and the master status summary (MSS) message.
The *STB? query does not change the status byte, MSS, or RQS.


**Bit Value Bit Name** **Description**


0 1 Reserved Reserved for future use; always set to 0.


1 2 Reserved Reserved for future use; always set to 0.



2 4 Error/Event Queue
(EEQ)


3 8 Questionable

Status Summary
(QUES)


4 16 Message Available
(MAV)


5 32 Event Status Sum
mary (ESB)


6 64 Request Service
(RQS/MSS)


7 128 Operation Status
Summary (OPER)



One or more errors are in the error queue. Use SYSTem:ERRor? to read and delete errors.


One or more bits are set in the Questionable Data Register and the corresponding QUEStionable
status enable register bit is true. See STATus:QUEStionable:ENABle.


Data is available in the instrument's output buffer.


One or more bits are set in the Standard Event Status Register. Bits must be enabled, see *ESE.


One or more bits are set in the Status Byte Register and may generate a Request for Service or
the Master Summary Status has one or more event bits. Bits must be enabled, see *SRE.


An event in the Operation Status register has been generated. Bits must be enabled, see

STATus:OPERation:ENABle.



**Master Status Summary and Request for Service Bits**


MSS is a real-time (unlatched) summary of all Status Byte register bits that are enabled by the Service
Request Enable register. MSS is set when the instrument has one or more reasons for requesting
service. *STB? reads the MSS in bit position 6 of the response but does not clear any bits in the Status
Byte register.


The RQS bit is a latched version of the MSS bit. Whenever the instrument requests service, it sets the
SRQ interrupt line true and latches RQS into bit 6 of the Status Byte register. When the controller does
a serial poll, RQS is cleared inside the register and returned in bit position 6 of the response. Other
Status Byte register bits are not disturbed.


**Error and Output Queues**


The Error Queue is a first-in, first-out (FIFO) data register that stores numerical and textual description
of an error or event. Error messages are stored until they are read with SYSTem:ERRor? If the queue
overflows, the last error/event in the queue is replaced with error -350,"Queue overflow".


The Output Queue is a first-in, first-out (FIFO) data register that stores instrument-to-controller
messages until the controller reads them. Whenever the queue holds messages, it sets the MAV bit
(4) of the Status Byte register.


Keysight AC6800B Series Operating and Service Guide 109


3 SCPI Programming Reference

##### **Command Quick Reference**


**ABORt Subsystem**


ABORt[:ALL] Aborts both TRANsient and ACQuire operations.


ABORt:ACQuire Aborts all ACQuire operations.


ABORt:TRANsient Aborts all TRANsient actions.


**CALibrate Subsystem**


**Command/Query** **Description**


CALibrate:COUNt? Returns the calibration count.


CALibrate:CURRent:OFFSet < _value_ - Starts the DC current limit calibration for the specified voltage range.


CALibrate:DATA < _value_ - Enters the calibration value read from a multimeter.



CALibrate:DATE "< _date_ >"

CALibrate:DATE?



Sets the calibration date.



CALibrate:LEVel P1|P2|P3|P4|P5 Advances to the next calibration point.


CALibrate:PASSword "< _password_ >" Sets the Admin password, which is used for calibration.


CALibrate:SAVE Saves the calibration data and date, not the administration password, into nonvolatile

memory.



CALibrate:STATe ON|1|OFF|0 [,<password>]

CALibrate:STATe?



Enables or disables calibration mode.



CALibrate:VOLTage[:LEVel] < _value_ - Selects the AC voltage range to calibrate.


CALibrate:VOLTage:OFFset < _value_ - Selects the DC voltage range to calibrate.


**CURRent Subsytem**


**Command/Query** **Description**




[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] < _value_ >|MINimum|MAXimum

[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum]


[SOURce:]CURRent:OFFSet[:IMMediate] < _value_ >|MINimum|MAXimum

[SOURce:]CURRent:OFFSet[:IMMediate]? [MINimum|MAXimum]


[SOURce:]CURRent:PROTection:STATe ON|1|OFF|0

[SOURce:]CURRent:PROTection:STATe?



Sets the immediate AC current limit in amps
(rms).


Sets the immediate DC current limit in amps.


Enables or disables current protection.



110 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference



**DIGital Subsystem**




[SOURce:]DIGital:PIN:POLarity POSitive|NEGative

[SOURce:]DIGital:PIN:POLarity?


**DISPlay Subsystem**



Sets the polarity of the remote inhibit
digital pin.



**Command/Query** **Description**



DISPlay[:WINDow]:METer:COUPling AC|DC|ACDC
DISPlay[:WINDow]:METer:COUPling?


DISPlay[:WINDow][:STATe] ON|1|OFF|0
DISPlay[:WINDow][:STATe]?


DISPlay[:WINDow]:TEXT "< _string_ >"
DISPlay[:WINDow]:TEXT?



Selects the data to show on the meter display.


Turns the front-panel display on or off.


Displays a text message on the display.



DISPlay[:WINDow]:TEXT:CLEar Clears the text message from the display.



DISPlay[:WINDow]:VIEW METER_VI|METER_VIP|METER_ALL
DISPlay[:WINDow]:VIEW?



Selects the parameters to display on the front panel.



Keysight AC6800B Series Operating and Service Guide 111


3 SCPI Programming Reference


**FETCh and MEASure Subsystems**


**Command/Query** **Description**



FETCh:ALL?

MEASure:ALL?


FETCh: CURRent[:DC]?
MEASure: CURRent[:DC]?


FETCh:CURRent:AC?

MEASure:CURRent:AC?


FETCh:CURRent:ACDC?

MEASure:CURRent:ACDC?


FETCh:CURRent:AMPLitude:MAXimum[:INSTant]?
MEASure:CURRent:AMPLitude:MAXimum[:INSTant]?


FETCh: CURRent:AMPLitude:MAXimum:HOLD?

MEASure: CURRent:AMPLitude:MAXimum:HOLD?


FETCh: CURRent:CREStfactor?

MEASure: CURRent:CREStfactor?


FETCh:FREQuency?
MEASure:FREQuency?


FETCh:POWer[:DC]?
MEASure:POWer[:DC]?


FETCh: POWer:AC[:REAL]?
MEASure: POWer:AC[:REAL]?


FETCh: POWer:AC:APParent?

MEASure: POWer:AC:APParent?


FETCh: POWer:AC:PFACtor?

MEASure: POWer:AC:PFACtor?


FETCh:POWer:AC:REACtive?

MEASure:POWer:AC:REACtive?


FETCh: POWer:ACDC[:REAL]?
MEASure: POWer:ACDC[:REAL]?


FETCh: POWer:ACDC:APParent?

MEASure: POWer:ACDC:APParent?


FETCh: POWer:ACDC:PFACtor?

MEASure: POWer:ACDC:PFACtor?


FETCh: POWer:ACDC:REACtive?

MEASure: POWer:ACDC:REACtive?


FETCh:VOLTage[:DC]?
MEASure:VOLTage[:DC]?


FETCh: VOLTage:AC?
MEASure: VOLTage:AC?


FETCh: VOLTage:ACDC?
MEASure: VOLTage:ACDC?



Returns all measurements, except FREQuency, as a comma-separated list.


Average DC current (A)


AC current (Arms)


Current, AC+DC (Arms)


Peak current (A)


Peak AC current held value (A)


Crest factor


AC output frequency (Hz)


Average DC power (W)


AC active power (W)


AC apparent power (VA)


AC power factor


AC reactive power (VAR)


AC active power (W)


AC+DC apparent power (VA)


AC+DC power factor


AC+DC reactive power (VAR)


Average DC voltage (V)


AC output voltage (Vrms)


Voltage, AC+DC (Vrms)



112 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference



**FREQuency Subsystem**


**Command/Query** **Description**




[SOURce:]FREQuency[:CW] < _value_ >|MINimum|MAXimum[,< _lower__
_limit_ >,< _upper_limit_ >]

[SOURce:]FREQuency[:CW]? [MINimum|MAXimum]


[SOURce:]FREQuency[:IMMediate] < _value_ >|MINimum|MAXimum

[,< _lower_limit_ >,< _upper_limit_ >]

[SOURce:]FREQuency[:IMMediate]? [MINimum|MAXimum]


[SOURce:]FREQuency:LIMit:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]FREQuency:LIMit:LOWer? [MINimum|MAXimum]


[SOURce:]FREQuency:LIMit:UPPer < _value_ >|MINimum|MAXimum

[SOURce:]FREQuency:LIMit:UPPer? [MINimum|MAXimum]


[SOURce:]FREQuency:LIMit[:STATe] ON|1|OFF|0

[SOURce:]FREQuency:LIMit[:STATe]?


[SOURce:]FREQuency:MODE FIXed|STEP

[SOURce:]FREQuency:MODE?


[SOURce:]FREQuency:TRIGgered < _value_ >|MINimum|MAXimum

[SOURce:]FREQuency:TRIGgered? [MINimum|MAXimum]


**HCOPy Subsystem**


HCOPy:SDUMp:DATA? Returns the display image in BMP format.



Sets the immediate AC frequency and optionally the frequency soft limits.


Sets the lower or upper AC frequency soft limit.


Enables or disables frequency soft limits.


Sets the transition mode for frequency settings.


Sets the triggered AC output frequency when frequency

mode is STEP.



Keysight AC6800B Series Operating and Service Guide 113


3 SCPI Programming Reference


**IEEE-488 Subsystem**


**Command/Query Description**


*CLS Clear status command.



*ESE < _value_ 
*ESE?



Event status enable command and query.



*ESR? Event status event query.


*IDN? Identification Query. Returns the instrument’s identification string.


*LRN? Learn query. Returns the SCPI strings that reproduce the present instrument settings.


*OPC Sets the OPC (operation complete) bit in the standard event register.


*OPC? Returns a 1 to the output buffer when all pending operations complete.


*OPT? Returns a string identifying any installed options.



*PSC 0|1

*PSC?



Enables (1) or disables (0) the clearing of certain enable registers at power on.



*RCL < _0 to 10_ - Recalls a saved instrument state.


*RST Resets the instrument to default values.


*SAV <0 to 10> Saves the instrument state to a nonvolatile memory location.



*SRE < _value_ 
*SRE?



Service request enable command and query.



*STB? Status byte query. Reads the Status Byte Register, which contains the status summary bits and the Output
Queue MAV bit.


*TRG Trigger command. Applies a software trigger (equivalent to IEEE 488.1 Device Trigger) for both TRANsient and
ACQuire trigger groups.


*TST? Self-test. Returns the errors found during the most recent power-on self-test.


**INITiate Subsystem**


**Command/Query** **Description**


INITiate[:IMMediate]:ACQuire Initiates the ACQuire (measurement) operation.


INITiate[:IMMediate]:TRANsient Initiates the TRANsient operation.


INITiate:CONTinuous:ACQuire ON|1|OFF|0 Initiates a new measurement and enables or disables "continuous" mode.


**LXI Subsystem**


**Command/Query** **Description**



LXI:IDENtify[:STATe] ON|1|OFF|0
LXI:IDENtify[:STATe]?



Turns the front-panel LXI identify indicator (blinking "Lan" annunciator) on or off.



114 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference



**OUTPut Subsystem**


**Command/Query** **Description**



OUTPut[:STATe] ON|1|OFF|0
OUTPut[:STATe]?


OUTPut:COUPling AC|DC|ACDC
OUTPut:COUPling?


OUTPut:INHibit:MODE LATChing|LIVE|OFF

OUTPut:INHibit:MODE?


OUTPut:PON:STATe RST|RCL0|AUTO

OUTPut:PON:STATe?



Enables or disables the instrument's output.


Sets the output coupling mode.


Sets the operating mode of the remote inhibit digital pin.


Sets the power-on output state.



OUTPut:PROTection:CLEar Resets the latched protection.



OUTPut:PROTection:WDOG[:STATe] ON|1|OFF|0
OUTPut:PROTection:WDOG[:STATe]?


OUTPut:PROTection:WDOG:DELay < _value_ >|MINimum|MAXimum
OUTPut:PROTection:WDOG:DELay? [MINimum|MAXimum]


**SENSe Subsytem**



Enables or disables the I/O watchdog timer.


Sets the watchdog delay time.



SENSe:AVERage1|2|4|8|16
SENSe:AVERage?



Sets the averaging count for measurements.



SENSe:CURRent[:PEAK]:HOLD:CLEar Clears the peak held current measurement.


Keysight AC6800B Series Operating and Service Guide 115


3 SCPI Programming Reference


**STATus Subsystem**


**Command/Query** **Description**


STATus:OPERation[:EVENt] Queries the event register for the Operation Status group.


STATus:OPERation:CONDition? Queries the operation condition register.



STATus:OPERation:ENABle < _value_ 
STATus:OPERation:ENABle?


STATus:OPERation:NTRansition

< _value_ 
STATus:OPERation:NTRansition?


STATus:OPERation:PTRansition

< _value_ 
STATus:OPERation:PTRansition?



Sets the value of the enable register for the Operation Status group.


Sets and queries the value of the NTR (Negative-Transition) and PTR (Positive-Transition)
registers.



STATus:PRESet Initializes the transition and enable filters for both SCPI register groups (OPERation and
QUEStionable).


STATus:QUEStionable[:EVENt]? Queries the event register for the Questionable Status group.


STATus:QUEStionable:CONDition? Queries the condition register for the Questionable Status group.



STATus:QUEStionable:ENABle

< _value_ 
STATus:QUEStionable:ENABle?


STATus:QUEStionable:NTRansition

< _value_ 
STATus:QUEStionable:NTRansition?


STATus:QUEStionable:PTRansition

< _value_ 
STATus:QUEStionable:PTRansition?



Sets the value of the enable register for the Questionable Status group.


Sets and queries the value of the NTR (Negative-Transition) and PTR (Positive-Transition)
registers.



116 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference



**SYSTem Subsystem**


**Command/Query** **Description**


SYSTem:BEEPer[:IMMediate] Issues a single beep.



SYSTem:BEEPer:KCLick ON|1|OFF|0

SYSTem:BEEPer:KCLick?


SYSTem:BEEPer:STATe ON|1|OFF|0

SYSTem:BEEPer:STATe?


SYSTem:COMMunicate:RLSTate

LOCal|REMote|RWLock

SYSTem :COMMunicate:RLSTate?



Disables or enables the click tone heard when you press a front-panel key.


Disables or enables the beep heard when an error is generated.


Configures the remote/local/lockout state of the instrument.



SYSTem:ERRor[:NEXT]? Reads and clears one error from the error queue.


SYSTem:ERRor:COUNt? Returns the number of errors in the error queue.


SYSTem:SECurity:IMMediate Clears all user memory (including stored states) and reboots the instrument in

the *RST state.



SYSTem:SSAVer[:STATe] ON|1|OFF|0
SYSTem:SSAVer[:STATe]?


SYSTem:SSAVer:DELay < _seconds_ >|MINimum|MAXimum
SYSTem:SSAVer:DELay? [MINimum|MAXimum]


SYSTem:SSAVer:RWAKeup ON|1|OFF|0
SYSTem:SSAVer:RWAKeup?



Enables or disables the screen saver.


Sets the delay time for screen saver activation.


Enables or disables remote screen saver wake-up.



SYSTem:VERSion? Returns the version of SCPI that the instrument uses.


**TRIGger Subsystem**


**Command/Query** **Description**


TRIGger:ACQuire[:IMMediate] Sends a software trigger to the ACQuire subsystem.



TRIGger:ACQuire:SOURce IMMediate|BUS
TRIGger:ACQuire:SOURce?


TRIGger:SYNChronize:SOURce IMMediate|PHASe
TRIGger:SYNChronize:SOURce?


TRIGger:SYNChronize:PHASe[:ON] < _value_ >|MINimum|MAXimum
TRIGger:SYNChronize:PHASe? [MINimum|MAXimum]



Sets the trigger source that starts the measurement after
INIT:ACQ.


Sets the output-on phase control when OUTPut ON is sent.


Sets the phase angle of the output-on phase control in degrees.



TRIGger:TRANsient[:IMMediate] Triggers the TRANsient subsystem.



TRIGger:TRANsient:SOURce IMMediate|BUS
TRIGger:TRANsient:SOURce?



Sets the trigger source for changing the setting value after

INIT:TRAN.



Keysight AC6800B Series Operating and Service Guide 117


3 SCPI Programming Reference


**VOLTage Subsystem**


**Command/Query** **Description**




[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] < _value_ >|MINimum|MAXimum[,
< _low_limit_ >|MINimum|MAXimum, < _high_limit_ >|MINimum|MAXimum]

[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum]


[SOURce:]VOLTage[:LEVel]:LIMit[:STATe] ON|1|OFF|0

[SOURce:]VOLTage[:LEVel]:LIMit[:STATe]?


[SOURce:]VOLTage[:LEVel]:LIMit:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage[:LEVel]:LIMit:LOWer? [MINimum|MAXimum]


[SOURce:]VOLTage[:LEVel]:LIMit:UPPer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage[:LEVel]:LIMit:UPPer? [MINimum|MAXimum]


[SOURce:]VOLTage[:LEVel]:MODE FIXed|STEP

[SOURce:]VOLTage[:LEVel]:MODE?


[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude]? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet[:IMMediate] < _value_ >|MINimum|MAXimum [,< _low__
_limit_ >|MINimum|MAXimum,< _high_limit_ >|MINimum|MAXimum]

[SOURce:]VOLTage:OFFSet[:IMMediate]? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet:LIMit[:STATe] ON|1|OFF|0

[SOURce:]VOLTage:OFFSet:LIMit[:STATe]?


[SOURce:]VOLTage:OFFSet:LIMit:LOWer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:OFFSet:LIMit:LOWer? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet:LIMit:UPPer < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:OFFSet:LIMit:UPPer? [MINimum|MAXimum]


[SOURce:]VOLTage:OFFSet:MODE FIXed|STEP

[SOURce:]VOLTage:OFFSet:MODE?


[SOURce:]VOLTage:OFFSet:TRIGgered < _value_ >|MINimum|MAXimum

[SOURce:]VOLTage:OFFSet:TRIGgered? [MINimum|MAXimum]


[SOURce:]VOLTage:PROGramming:SOURce INTernal|EXTAC|EXTDC

[SOURce:]VOLTage:PROGramming:SOURce?


[SOURce:]VOLTage:RANGe[:UPPer] 155|310|MINimum|MAXimum

[SOURce:]VOLTage:RANGe[:UPPer]? [MINimum|MAXimum]


[SOURce:]VOLTage:RANGe:AUTO ON|1|OFF|0

[SOURce:]VOLTage:RANGe:AUTO?



Sets the immediate AC voltage level, and
optionally the soft limits.


Enables or disables soft limits for AC

voltage.


Sets the lower and upper AC voltage limits.


Sets the trigger transient mode for voltage
settings.


Sets the triggered AC voltage level.


Sets the immediate DC voltage in VDC, and
also optionally sets the soft limits.


Enables or disables soft limits for DC

voltage.


Sets the lower and upper DC voltage soft

limits.


Sets the trigger transient mode for DC
voltage settings.


Sets the triggered DC voltage in VDC.


Sets the voltage programming source.


Sets the voltage range.


Enables or disables voltage autoranging.



118 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **ABORt Subsystem**


ABORt commands cancel any triggered actions and returns the trigger system back to the Idle state.
Send TRIG:SYNC:SOUR IMM instead of ABORt to abort phase synchronization. ABORt commands are

also executed with the *RST command.


**ABORt[:ALL]**


Aborts both TRANsient and ACQuire operations.


(none) (none)


The trigger status at power-on is the same as that after an ABORt.


If an ABORt is sent when a transient or acquisition is in progress, the ACQuire group's acquired data

remains invalid.


If an ABORt is sent when the ACQuire group is not initiated and the acquired data that is held is valid,
the acquired data is retained.


**ABORt:ACQuire**


Aborts all ACQuire operations.


(none) (none)


This command resets the WTG-meas and MEAS-active bits in the Operation Status registers.


If INIT:CONT:ACQ is ON, the ABORt:ACQuire command aborts the measurement but the instrument

immediately re-initiates new measurements.


**ABORt:TRANsient**


Aborts all TRANsient actions.


(none) (none)


This command resets the WTG-tran bit in the Operation Status registers.


Keysight AC6800B Series Operating and Service Guide 119


3 SCPI Programming Reference

##### **CALibrate Subsystem**


This subsystem calibrates the instrument.


Follow the procedure in the calibration section before calibrating. Improper
calibration can reduce accuracy and reliability.


**CALibrate:COUNt?**


Returns the calibration count.


(none) +14


The calibration count is incremented whenever any of the following commands is executed:


– CALibrate:SAVE ( [Menu] > System > Admin > Cal > Save )


– CALibrate:DATE ( [Menu] > System > Admin > Cal > Date )


– CALibrate:PASSword ( [Menu]    - System > Admin > Password )


– CALibrate:PASSword:RESet


You may execute this query regardless of the CALibrate:STATe value.


**CALibrate:CURRent:OFFSet <** _**value**_ **>**


Starts the DC current limit calibration for the specified voltage range.


**Parameter** **Typical Return**


The maximum current of the output range being calibrated, as shown in the table below. (none)


Calibrate the 10-A current range: CAL:CURR:OFFS 10


The CALibrate:STATe must be ON to execute this command.


The < _value_ - that you specify will select the range to be calibrated, as shown in the table below. For
example, a < _value_ - of 8 will select the 310 V range on the AC6803B, but the 155 V range on the

AC6802B.


310 V Range 2.0 4.0 8.0 16.0


155 V Range 4.0 8.0 16.0 32.0


Do not use any unit suffix (such as A) in this command.


120 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**CALibrate:DATA <** _**value**_ **>**


Enters the calibration value read from a multimeter.


Numeric value (none)


Specify calibration value 0.0237: CAL:DATA 2.37E-2


The CALibrate:STATe must be ON to execute this command.


Do not use any unit suffix (such as A) in this command.


The CURRent calibration unit is always amps (DC) or amps-rms (AC).


The VOLTage calibration unit is always volts (DC) or volts-rms (AC).


**CALibrate:DATE "<** _**date**_ **>"**

**CALibrate:DATE?**


Sets the calibration date. Use CALibrate:SAVE to store the date into nonvolatile memory.


"< _date_ >" "2013 Jul 26"


Enter the calibration date: CAL:DATE "2014 Jul 26"


The CALibrate:STATe must be ON to execute this command.


This command increments the calibration count by 1.


The date may be up to 15 characters.


Use the format shown above (or the local language equivalent) to avoid confusion. For example, there
may be confusion as to whether "14/06/13" is June 14, 2013 or June 13, 2014. The format "2013 Jun
14" avoids that problem.


You may query the value regardless of whether the calibration state is on.


Keysight AC6800B Series Operating and Service Guide 121


3 SCPI Programming Reference


**CALibrate:LEVel P1|P2|P3|P4|P5**


Advances to the next calibration point. P1 is the first level; P2 is the second, and so on.


Some calibration steps may generate very high voltages (such as +155 VAC, +310 VAC,
and ±380 VDC). This procedure should only be performed by people with appropriate training, always exercising appropriate caution.


P1|P2|P3|P4|P5 (none)


Select the first calibration point: CAL:LEV P1


The CALibrate:STATe must be ON to execute this command.


Some calibration sequences may require some settling time after sending CAL:LEV but before reading
the data from the DVM and sending CAL:DATA.


AC calibration is performed at 55 Hz for P1 and P2, and higher frequency for P3, P4, and P5.


The calibration steps for each item are shown below:


CAL:VOLT 155 10% FS 90% FS 90% FS @ 300 Hz 90% FS @ 400 Hz 90% FS @ 500 Hz


CAL:VOLT 310


CAL:VOLT 219 0% FS 90% FS


CAL:VOLT 438
n/a

CAL:CURR < _low_val_ 

CAL:CURR < _high_val_ 

The voltage full-scale (FS) values are 155 VAC (LOW) and 310 VAC (HIGH) for all models.


The current full-scale (FS) values are shown below.


AC6801B 4.0 A 2.0 A


AC6802B 8.0 A 4.0 A


AC6803B 16.0 A 8.0 A


AC6804B 32.0 A 16.0 A


122 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**CALibrate:PASSword "<** _**password**_ **>"**


Sets the Admin password, which is used for calibration.


an alphanumeric string of 4 to 15 characters (none)


Set the password to 12345: CAL:PASS "12345"


The password must be 4 to 15 characters long and may include any of these characters:


A-Z Upper case letters


a-z Lower case letters


0-9 Numeric digits


+ - ( ) ., <space> Plus, minus, parentheses, period, comma, space


The CALibrate:STATe must be ON to execute this command.


This command increments the calibration count by 1.


The password is case sensitive.


To change the password: unsecure calibration memory with the old code, then set the new code.


If you enter an empty string as the new password and save it with CAL:SAVE once, no password will
be required to newly enter the calibration mode the next time.


This setting is non-volatile; it will not be changed by power cycling or *RST.


**CALibrate:SAVE**


Saves the calibration data and date, not the administration password, into nonvolatile memory. Do
this at the end of the calibration to avoid losing changes.


(none) (none)


The CALibrate:STATe must be ON to execute this command.


This command increments the calibration count by 1.


Keysight AC6800B Series Operating and Service Guide 123


3 SCPI Programming Reference


**CALibrate:STATe ON|1|OFF|0 [,<password>]**
**CALibrate:STATe?**


Enables or disables calibration mode. Calibration mode must be enabled for the instrument to accept
any calibration commands.


ON|1|OFF|0, default OFF 0 (OFF) or 1 (ON)


< _password_ - a numeric value up to 15 digits (none)


Disable calibration: CAL:STAT OFF


Enable calibration: CAL:STAT ON,"abc123"


The < _password_ - is required if it has been set. The factory default is no password, but if one has been
set, you must send CALibrate:STATe ON,12345 (replacing 12345 with your instrument's password).


You cannot enable front-panel calibration and SCPI calibration simultaneously.


**CALibrate:VOLTage[:LEVel] <** _**value**_ **>**
**CALibrate:VOLTage:OFFset <** _**value**_ **>**


Selects the voltage range to calibrate.


Either 219 or 438 (for DC OFFset) or 155 or 310 (for AC LEVel). (none)


Calibrate the voltage of the 310 V range: CAL:VOLT:LEV 310


The CALibrate:STATe must be ON to execute this command.


The CALibrate:VOLTage[:LEVel] command is for AC voltage, and the CALibrate:VOLTage:OFFSet
command is for DC voltage.


Do not use unit suffixes, such as V or mV, with this command.


124 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **CURRent Subsytem**


The CURRent subsystem limits the instrument's output current.


**[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum]**


Sets the immediate AC current limit in amps (rms).



AC6801B 0.1 to 5.2 A

AC6802B 0.2 to 10.5 A

AC6803B 0.4 to 21.0 A

AC6804B 0.8 to 42.0 A



+9.00000E-01



Sets the current limit to 900 milliamps: CURR 900 MA


Current units (UA, MA, A) are allowed.


Values exceeding the instrument's maximum value will be set to the instrument's maximum value.


The *RST default is the instrument's MAXimum value.


**[SOURce:]CURRent:OFFSet[:IMMediate] <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]CURRent:OFFSet[:IMMediate]? [MINimum|MAXimum]**


Sets the immediate DC current limit in amps.



AC6801B 0.1 to 4.2 A

AC6802B 0.2 to 8.4 A

AC6803B 0.4 to 16.8 A

AC6804B 0.8 to 33.6 A



+3.00000E+00



Set the DC current limit to 3 A: CURR:OFFS 3


Current units (UA, MA, A) are allowed.


Values exceeding the instrument's maximum value will be set to the instrument's maximum value.


The *RST default is the instrument's MAXimum value.


Keysight AC6800B Series Operating and Service Guide 125


3 SCPI Programming Reference


**[SOURce:]CURRent:PROTection:STATe ON|1|OFF|0**

**[SOURce:]CURRent:PROTection:STATe?**


Enables or disables current protection.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Enable limit control operation: CURR:PROT:STATe OFF


ON (TRIP): Turns OUTPUT off and activates the alarm for overload conditions longer than three

seconds.


OFF (LIMIT CONTROL): Decreases output voltage to keep current at or below the limit when an

overload occurs.


To clear an overcurrent condition, remove the condition's cause and send OUTPut:PROTection:CLEar.


If overcurrent protection is enabled and the output goes into current limit, the output is disabled and
the Questionable Condition status register OCP bit is set.


The *RST command sets this parameter to ON.


126 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **DIGital Subsystem**


The Digital subsystem has just one command.


This command only applies when option AC68BFIU (the Fault Inhibit interface board) is

installed in the AC source.


**[SOURce:]DIGital:PIN:POLarity POSitive|NEGative**

**[SOURce:]DIGital:PIN:POLarity?**


Sets the polarity of the remote inhibit digital pin. POSitive means a logical true signal is a voltage high
at the pin. NEGative means a logical true signal is a voltage low at the pin.


POSitive|NEGative POS or NEG


Sets the digital pin to negative polarity: DIG:PIN:POL NEG


The pin polarities are saved in non-volatile memory.


Keysight AC6800B Series Operating and Service Guide 127


3 SCPI Programming Reference

##### **DISPlay Subsystem**


The DISPlay subsystem controls the front-panel display.


**DISPlay[:WINDow]:METer:COUPling AC|DC|ACDC**
**DISPlay[:WINDow]:METer:COUPling?**


Selects the data to show on the meter display.


AC|DC|ACDC AC, DC, or ACDC


AC shows pure AC measurement data, DC shows DC measurement data, and ACDC shows all AC

and DC measurement data.


The *RST command sets this parameter to ACDC.


**DISPlay[:WINDow][:STATe] ON|1|OFF|0**
**DISPlay[:WINDow][:STATe]?**


Turns the front-panel display on or off.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Turn the front-panel display off: DISP:STAT OFF


Setting this OFF stops the refreshing of the display, turns off the display's backlight, changes the LINE
LED to orange, and disables all front-panel key operations.


The *RST command sets this parameter to ON.


**DISPlay[:WINDow]:TEXT "<** _**string**_ **>"**
**DISPlay[:WINDow]:TEXT?**


Displays a text message on the display.


Quoted string of up to 127 characters, default "". "Test running. Do not touch."


Show message on display: DISP:TEXT "Test in progress..."


While a message is displayed, instrument operation information is not sent to the front-panel display.


To clear the text, send the DISPlay:TEXT:CLEar command or cycle power.


The display text is unaffected by *RCL and *RST`.


128 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**DISPlay[:WINDow]:TEXT:CLEar**


Clears the text message from the display.


(none) (none)


The display text is unaffected by *RCL and *RST`.


**DISPlay[:WINDow]:VIEW METER_VI|METER_VIP|METER_ALL**
**DISPlay[:WINDow]:VIEW?**


Selects the parameters to display on the front panel.


METER_VI|METER_VP|METER_VIP, *RST METER_VI METER_VI, METER_VIP, or METER_ALL


Display voltage and current: DISP:VIEW METER_VI


Examples of the various displays are shown in the table below.


METER_VI


METER_VIP


METER_ALL


Keysight AC6800B Series Operating and Service Guide 129


3 SCPI Programming Reference

##### **FETCh and MEASure Subsystems**


The FETCh and MEASure subsystems return measured data. The FETCh and MEASure queries do not
take any parameters, and they have the identical syntax (such as FETCh:VOLTage:AC? and
MEASure:VOLTage:AC?).


Measurements may be acquired continuously or non-continuously, as specified by
INITiate:CONTinuous:ACQuire. The *RST and *RCL commands disable continuous operation.


**Non-continuous measurement mode**


**MEASure Subsystem**


A MEASure query or an IMMediate or BUS trigger generates a new measurement that is stored in a

buffer and then returned to the user.


Before the instrument can accept an IMMediate or BUS trigger, you must first send INITiate:ACQuire.
Otherwise, the instrument will generate SCPI error -211,"Trigger ignored" . The buffer retains the
measured data until cleared by another MEASure query, an accepted IMMediate or BUS trigger, a
*RST or INITiate:ACQuire command, or a power cycle.


**FETCh Subsystem**


A FETCh query immediately returns the previously acquired measurement from this buffer. If no
measurement is in the buffer, the instrument generates SCPI error -230,"Data corrupt or stale".


**General Measurement Operation**


Data acquisition occurs at 333 ms intervals, which includes a digitization aperture of 100 to 125 ms. If
there are no MEASure queries or triggers, the data is discarded.


When a MEASure query or IMMediate or BUS trigger is received, the instrument waits for the current
digitization to finish, discards the data, and waits for the next measurement interval to finish. The

instrument then stores the next measurement into the buffer to ensure that the entire measurement

was sampled after the MEASure query or IMMediate or BUS trigger. Therefore, a measurement could
require from 333 to 666 ms to complete.


The acquired measurement includes both the instantaneous voltage and current. The FETCh query
may return any calculated measurement item derived from this data, including FETC:CURR:DC?,
FETC:VOLT:AC?, and FETC:POW:ACDC?.


For averaged measurements of 2, 4, 8, or 16 intervals, the instrument stores and averages multiple
measurements. Therefore, a measurement could take up to 17 x 333 ms to complete.


**Continuous measurement mode**


Internal data acquisition occurs at 333 ms update intervals, including the aperture as described
above, and each completed measurement overwrites the previous data in the buffer. Averaged


130 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


measurements are a moving average of the latest 2, 4, 8, or 16 measurements.


All MEASure and FETCh queries return the measurement presently stored in the buffer, and both BUS
and IMMediate triggers are ignored.


**FETCh:ALL?**

**MEASure:ALL?**


Returns all measurements, except FREQuency, as a comma-separated list. Every returned item is
available, regardless of the measurement coupling or output coupling.


(none) +1.23456E+00


Return the average measured AC output current:

FETCh:CURRent:AC?


The values are returned in the order shown below, using the format 1.23456E+00.


The formulas are similar for voltage and current. All raw measurements (individual A/D conversions
and associated array elements), are understood to be DC coupled. This discussion does not consider
the effects of windowing (used to ensure stable readings) and adjustments that must be made to
correct for window gain (always less than unity when compared to a rectangular window).


General definitions are as follows:


Array Length: N


Array Index Variable: n = 0, 1, 2, ..., N-1


Voltage Array: Volt[N]


Current Array: Current[N]


**FETCh/MEASure Query** **Notes**



CURRent[:DC]?


Average DC current (A)


CURRent:AC?


AC current (Arms)



This command applies to DC, AC+DC, and EXT-DC.


Set the averaging period using SENS:AVER.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.



Keysight AC6800B Series Operating and Service Guide 131


3 SCPI Programming Reference


**FETCh/MEASure Query** **Notes**



CURRent:ACDC?


Current, AC+DC (Arms)


CURRent:AMPLitude:MAXimum[:INSTant]?


Peak current (A)


CURRent:AMPLitude:MAXimum:HOLD?


Peak AC current held value (A)


CURRent:CREStfactor?


Crest factor


FREQuency?


AC output frequency (Hz)


POWer[:DC]?


Average DC power (W)


POWer:AC[:REAL]?


AC active power (W)


POWer:AC:APParent?


AC apparent power (VA)


POWer:AC:PFACtor?


AC power factor



This command applies to AC+DC.


This command applies to all output modes.


This command applies to all output modes.


You can clear the peak value with SENS:CURR:PEAK:CLE.


This returns the maximum peak current since the power-on or
since it was explicitly cleared (SENSe:CURRent:PEAK:CLEar).


The peak current (held value) is not cleared by *RST or

*RCL.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The crest factor is the AC current divided by the AC peak

current.


The crest factor for sine waves is √2.


This returns the frequency setting for AC, ACDC, and EXT-AC
coupling, or +9.91000E+37 for DC and EXT-DC coupling. The
instrument does not measure frequency.


This command applies to DC, AC+DC, and EXT-DC.


Set the averaging period using SENS:AVER.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The power factor indicates the efficiency degradation caused
by the phase difference between the AC voltage and AC

current.



132 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference



**FETCh/MEASure Query** **Notes**



POWer:AC:REACtive?


AC reactive power (VAR)


POWer:ACDC[:REAL]?


AC active power (W)


POWer:ACDC:APParent?


AC+DC apparent power (VA)


POWer:ACDC:PFACtor?


AC+DC power factor


POWer:ACDC:REACtive?


AC+DC reactive power (VAR)


VOLTage[:DC]?


Average DC voltage (V)


VOLTage:AC?


AC output voltage (Vrms)


VOLTage:ACDC?


Voltage, AC+DC (Vrms)



This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The power factor indicates the efficiency degradation caused
by the phase difference between the AC voltage and AC

current.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to DC, AC+DC, and EXT-DC.


Set the averaging period using SENS:AVER.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.



Keysight AC6800B Series Operating and Service Guide 133


3 SCPI Programming Reference


**FETCh:<** _**measurement**_ **>**

**MEASure:<** _**measurement**_ **>**


Returns the < _measurement_ - in the form 1.23456E+00. The < _measurement_ - may be any of the queries
listed in the table above, such as VOLTage:AC? or VOLTage:ACDC?


**Examples:**


FETC:VOLTage:AC?


MEASure:VOLTage:ACDC?


134 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **FREQuency Subsystem**


FREQuency commands configure the instrument's output frequency.


**[SOURce:]FREQuency[:CW] <** _**value**_ **>|MINimum|MAXimum[,<** _**lower_limit**_ **>,<** _**upper_limit**_ **>]**

**[SOURce:]FREQuency[:CW]? [MINimum|MAXimum]**

**[SOURce:]FREQuency[:IMMediate] <** _**value**_ **>|MINimum|MAXimum[,<** _**lower_limit**_ **>,<** _**upper_**_
_**limit**_ **>]**

**[SOURce:]FREQuency[:IMMediate]? [MINimum|MAXimum]**


Sets the immediate AC frequency and optionally the frequency soft limits.


40.0 to 500.0 Hz +5.00000E+01


Set the output frequency to 50 Hz: FREQ 50


This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST default is 60 Hz.


You should set this value between the lower and upper frequency soft limits.


Frequency suffixes (HZ and KHZ) are allowed.


**[SOURce:]FREQuency:LIMit:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]FREQuency:LIMit:LOWer? [MINimum|MAXimum]**


**[SOURce:]FREQuency:LIMit:UPPer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]FREQuency:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower or upper AC frequency soft limit.



40.0 to 500.0 Hz

Default 500 (UPPer) and 40 (LOWer)



+1.25000E+02



Set the frequency limits to 125 and 300 Hz:
FREQ:LIM:LOW 125

FREQ:LIM:UPP 300

FREQ:LIM:STAT ON


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


Keysight AC6800B Series Operating and Service Guide 135


3 SCPI Programming Reference


**[SOURce:]FREQuency:LIMit[:STATe] ON|1|OFF|0**

**[SOURce:]FREQuency:LIMit[:STATe]?**


Enables or disables frequency soft limits.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Enable the frequency soft limits: FREQ:LIM:STAT ON


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST command sets this parameter to OFF.


**[SOURce:]FREQuency:MODE FIXed|STEP**

**[SOURce:]FREQuency:MODE?**


Sets the transition mode for frequency settings.


FIXed|STEP FIX


Specifying FIX (default) disables the trigger function; specifying STEP enables it.


The *RST command sets this parameter to FIXed.


**[SOURce:]FREQuency:TRIGgered <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]FREQuency:TRIGgered? [MINimum|MAXimum]**


Sets the triggered AC output frequency when frequency mode is STEP.


40.0 to 500.0 Hz +9.00000E+01


Set the triggered frequency to 90 Hz: FREQ:TRIG 90


This command applies to AC, AC+DC, EXT-DC, and EXT-AC.


The *RST default is 60 Hz.


Frequency suffixes (HZ and KHZ) are allowed.


136 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **HCOPy Subsystem**


**HCOPy:SDUMp:DATA?**


Returns the display image in BMP format.


(none) <Block>


The image is a grey-scale Windows .BMP which has fixed 2,110 bytes. The response format is
IEEE488.2 definite-length block data -- #N<length><imagebody>, where N is always 4 and <length> is
always 2,110, therefore 2,116 bytes in total.


Keysight AC6800B Series Operating and Service Guide 137


3 SCPI Programming Reference

##### **IEEE-488 Common Commands**


IEEE-488 Common commands generally control overall instrument functions, such as reset, status,
and synchronization. All common commands consist of a three-letter mnemonic preceded by an

asterisk: *RST *IDN? *SRE 8.


***CLS**


Clear status command. Clears the event registers in all register groups. Also clears the status byte
and error queue. If *CLS immediately follows a program message terminator (<NL>), then the output
queue and the MAV bit are also cleared. Refer to Status Overview for more information.


(none) (none)


***ESE <** _**value**_ **>**

***ESE?**


Event status enable command and query. Sets the value in the enable register for the Standard Event
Status group. Each set bit of the register enables a corresponding event. All enabled events are
logically OR-ed into the ESB bit of the status byte. The query reads the enable register. Refer to

Status Overview for more information.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8), and bit 7
(value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 3 and 4 in the enable register: *ESE 24


The value returned is the binary-weighted sum of all bits set in the register.



< _bit value_ 


Any or all conditions can be reported to the ESB bit through the enable register. To set the enable
register mask, write a decimal value to the register using *ESE.


A *CLS does not clear the enable register, but does clear the event register.


This parameter is not affected by *RST or *RCL.


138 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


***ESR?**


Event status event query. Reads and clears the event register for the Standard Event Status group.
The event register is a read-only register that latches all standard events. Refer to Status Overview

for more information.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.


Any or all conditions can be reported to the ESB bit through the enable register. To set the enable
register mask, write a decimal value to the register using *ESE.


Once a bit is set, it remains set until cleared by this query or *CLS.


***IDN?**


Identification Query. Returns the instrument’s identification string.


(none) Keysight,AC6801B,JPUB002121,A.01.00.0067


The fields are in the following order: manufacturer name, model number, serial number, firmware

revision.


***LRN?**


Learn query. Returns the SCPI strings that reproduce the present instrument settings.


**Parameter Typical Return**


(none) :INIT:CONT:ACQ OFF;:ABOR;:OUTP 0;:OUTP:PROT:WDOG:DEL 60;STAT 0;:OUTP:COUP AC;:VOLT:PROG:SOUR

INT;:VOLT:RANG 155;:VOLT:PROG:SOUR INT;:VOLT:IMM 0.0,0.0,157.5;MODE FIX;LIM:STAT 0;:FREQ:IMM

60.0,40.0,500.0;MODE FIX;LIM:STAT 0;:CURR 5.25;:CURR:OFFS 4.20;:CURR:PROT:STAT 1;:SENS:AVER 1;VOLT:EQU

0;:DISP:STAT 1;VIEW METER_VI;MET:COUP ACDC;:TRIG:TRAN:SOUR IMM;:TRIG:ACQ:SOUR IMM;:TRIG:SYNC:SOUR

IMM;PHAS:ON 0;:OUTP 0


Return the instrument's learn string: *LRN?


The returned string can be more than 500 characters.


Keysight AC6800B Series Operating and Service Guide 139


3 SCPI Programming Reference


***OPC**


Sets the OPC (operation complete) bit in the standard event register. This occurs at the completion of
the pending operation. Refer to Status Overview for more information.


(none) (none)


The purpose of this command is to synchronize your application with the instrument.


Used in conjunction with initiated acquisitions, initiated transients, output state changes, and output
to settling time to provide a way to poll or interrupt the computer when these pending operations
complete.


Other commands may be executed before the operation complete bit is set.


The difference between *OPC and *OPC? is that *OPC? returns "1" to the output buffer when the
current operation completes. No further commands can be sent after an *OPC? until it has responded.
In this way an explicit polling loop can be avoided. That is, the IO driver will wait for the response.


***OPC?**


Returns a 1 to the output buffer when all pending operations complete.


(none) +1


The purpose of this command is to synchronize your application with the instrument.


Other commands cannot be executed until this command completes.


***OPT?**


Returns a string identifying any installed options.


(none) AC68GPBU, AC68BALGU, or 0


0 (no options installed)


AC68GPBU (GPIB installed), or AC68BALGU (analog interface board installed).


140 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


***PSC 0|1**

***PSC?**


Enables (1) or disables (0) the clearing of certain enable registers at power on. These include:


Questionable Data Register (STATus:QUEStionable:ENABle)


Standard Operation Register (STATus:OPERation:ENABle)


Status Byte Condition Register (*SRE)


Standard Event Enable Register (*ESE)


The *PSC command does not affect the clearing of the condition or event registers,
just the enable registers. For more information on the SCPI Status System, see
Status Subsystem Introduction.


0|1, default 1 0 or 1


Disable power-on clearing of affected registers: *PSC 0


***RCL <** _**0 to 10**_ **>**


Recalls a saved instrument state.


0 to 10 (none)


Recall state from location 1: *RCL 1


This command restores the instrument to a state that was previously stored with the *SAV command.


All instrument settings are recalled except: (1) trigger systems are set to the Idle state, (2) calibration
is disabled, (3) nonvolatile settings are not affected.


This command also aborts TRANsient and ACQuire operations.


Location 0 is recalled at power-on when the Output Power-On state is RCL0.


Stored instrument states are not affected by *RST.


***RST**


Resets the instrument to default values. These settings are described in Default Settings.


(none) (none)


Keysight AC6800B Series Operating and Service Guide 141


3 SCPI Programming Reference


The *RST command forces the ABORt commands. This cancels any measurement or transient actions
presently in process. It resets the WTG-meas, MEAS-active, WTG-tran, and TRAN-active bits in the
Operation Status registers.


This command also aborts TRANsient and ACQuire operations.


***SAV <0 to 10>**


Saves the instrument state to a nonvolatile memory location.


0 to 10 (none)


Save state to location 1: *SAV 1


If a particular state is desired at power-on, it should be stored in location 0. Location 0 is recalled at
power-on when the Output Power-On state is RCL0.


The calibration state are NOT saved as part of the *SAV operation.


When shipped, state storage locations 1 through 10 are empty.


***SRE <** _**value**_ **>**

***SRE?**


Service request enable command and query. This sets the value of the Service Request Enable
register. This determines which bits from the Status Byte Register are summed to set the Master
Status Summary (MSS) bit and the Request for Service (RQS) summary bit. A 1 in any Service Request
Enable register bit position enables the corresponding Status Byte register bit. All such enabled bits
are then logically OR-ed to cause the MSS bit of the Status Byte register to be set. Refer to Status

Overview for more information.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8), and bit 7
(value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 3 and 4 in the enable register: *SRE 24



< _bit value_ 


When a serial poll is conducted in response to SRQ, the RQS bit is cleared, but the MSS bit is not.
When *SRE is cleared (by programming it with 0), the instrument cannot generate an SRQ to the

controller.


The register contents are not affected by *RST or *RCL.


142 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


***STB?**


Status byte query. Reads the Status Byte Register, which contains the status summary bits and the
Output Queue MAV bit.


(none) < _bit value_ 

The Status Byte is a read-only register and the bits are not cleared when it is read. Refer to Status

Overview for more information.


***TRG**


Trigger command. Applies a software trigger (equivalent to IEEE 488.1 Device Trigger) for both
TRANsient and ACQuire trigger groups.


(none) (none)


Generate an immediate trigger:


***TST?**


Self-test. Returns the errors found during the most recent power-on self-test.


(none) 0 (pass) or +1 (failed)


The instrument’s self-test function is performed when the instrument starts up. If a failure is detected,
the corresponding SCPI error is generated, and bit 2 of the STATus:QUEStionable register will be set.


When no failure is found the response will be +0. When one or more failures are found the response
value will be -330. In this case, SYST:ERR? will return specific failure messages (at first -330,"Self-test
error", and then the self-test error codes). See SCPI Error Messages for more information.


The *TST? command does not execute an actual self-test.


Keysight AC6800B Series Operating and Service Guide 143


3 SCPI Programming Reference

##### **INITiate Subsystem**


The INITiate commands initialize the trigger system. This moves the trigger system from the "idle"
state to the "wait-for-trigger" state, which enables the instrument to receive triggers. An event on the
selected trigger source causes the trigger to occur.


**INITiate[:IMMediate]:ACQuire**


Initiates the ACQuire (measurement) operation.


(none) (none)


If the ACQuire operation is already initiated, this command will produce error -213,"Init ignored".


**INITiate[:IMMediate]:TRANsient**


Initiates the TRANsient operation.


(none) (none)


Initiate a TRANsient operation:

INIT:TRAN


If the TRANsient operation is already initiated, this command will produce error -213,"Init ignored".


**INITiate:CONTinuous:ACQuire ON|1|OFF|0**


Initiates a new measurement and enables or disables "continuous" mode.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Initiate continuous acquisition mode: INIT:CONT:ACQ ON


If continuous mode is on, acquired data is automatically refreshed without the need for an INIT.


You should normally leave continuous mode off (default).


If the TRANsient operation is already initiated, this command will produce error -213,"Init ignored".


The *RST and *RCL commands set this parameter to 0.


144 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **LXI Subsystem**


The LXI subsystem has just one command.


**LXI:IDENtify[:STATe] ON|1|OFF|0**
**LXI:IDENtify[:STATe]?**


Turns the front-panel LXI identify indicator (blinking "Lan" annunciator) on or off. When turned on, the
front-panel "LAN" annunciator blinks to identify the instrument that is being addressed.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Blink the front panel LXI annunciator: LXI:IDENT ON


The *RST command, the *RCL command, a LAN reset, and a LAN restart all turn the LXI indicator off.


Keysight AC6800B Series Operating and Service Guide 145


3 SCPI Programming Reference

##### **OUTPut Subsystem**


The OUTPut subsystem configures the output state, power-on state, coupling mode, digital pin, and
protection.


**OUTPut[:STATe] ON|1|OFF|0**
**OUTPut[:STATe]?**


Enables or disables the instrument's output.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Turn on the output: OUTP ON


The *RST command sets this parameter to 0.


**OUTPut:COUPling AC|DC|ACDC**
**OUTPut:COUPling?**


Sets the output coupling mode.



AC|DC|ACDC

Default AC



AC, DC, or ACDC



Set the output mode to ACDC: OUTP:COUP ACDC


AC (default) produces AC output


DC produces DC output


ACDC combines AC and DC output


The *RST command sets this parameter to AC.


146 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**OUTPut:INHibit:MODE LATChing|LIVE|OFF**
**OUTPut:INHibit:MODE?**


This command only applies when option AC68BFIU (the Fault Inhibit interface board) is

installed in the AC source.


Sets the operating mode of the remote inhibit digital pin. The inhibit function shuts down the output in
response to an external signal on the Inhibit input pin. The Inhibit mode is stored in non-volatile
memory. You can specify the polarity of the remote inhibit digital pin using

[SOURce:]DIGital:PIN:POLarity POSitive|NEGative.


LATChing|LIVE|OFF LATC,LIVE, or OFF


Sets the inhibit input to Live mode: OUTP:INH:MODE LIVE


LATChing - a logic-true signal on the Inhibit input causes the output state to latch OFF. The output
remains disabled until the Inhibit input is returned to logic-false and the latched INH status bit is
cleared by sending the OUTPut:PROTection:CLEar command or a protection clear command from the
front panel. Note that the output remains disabled after the protection is cleared and must be reenabled with the OUTPut ON command to turn it on again.


LIVE - allows the enabled output to follow the state of the Inhibit input.


OFF - the Inhibit input is ignored.


The inhibit mode is stored in non-volatile memory.


**OUTPut:PON:STATe RST|RCL0|AUTO**

**OUTPut:PON:STATe?**


Sets the power-on output state.


RST|RCL0|AUTO RST, RCL0, or AUTO


Set the power-on state to the *RST state: OUTP:PON:STAT RST


RST specifies that the instrument powers on in a state equivalent to *RST.


RCL0 loads the state stored in state storage memory location 0.


AUTO specifies that the instrument powers on in the power-down state. The output state is always off.


This parameter is not affected by *RST or *RCL.


This parameter is saved in nonvolatile memory.


Keysight AC6800B Series Operating and Service Guide 147


3 SCPI Programming Reference


**OUTPut:PROTection:CLEar**


Resets the latched protection. This clears the latched protection status that disables the output when
a protection condition occurs. It also clears the latched Inhibit Input function.


(none) (none)


All conditions that generate the fault must be removed before the latched status can be cleared. The
output remains in the OFF state after the fault/inhibit condition is cleared.


**OUTPut:PROTection:WDOG[:STATe] ON|1|OFF|0**
**OUTPut:PROTection:WDOG[:STATe]?**


Enables or disables the I/O watchdog timer.


0|OFF|1|ON 0 (OFF) or 1 (ON)


Enables the watchdog timer protection: OUTP:PROT:WDOG ON


When the watchdog timer is enabled, the output will be disabled if there is no I/O activity on any
remote interface within the time period specified by OUTput:PROTection:WDOG:DELay. The output is
latched off but the programmed output state is not changed.


The watchdog timer function is NOT reset by front-panel activity; the output will still shut down after
the time period has elapsed.


The *RST command sets this parameter to 0.


**OUTPut:PROTection:WDOG:DELay <** _**value**_ **>|MINimum|MAXimum**
**OUTPut:PROTection:WDOG:DELay? [MINimum|MAXimum]**


Sets the watchdog delay time.


1 to 3600 (seconds) +6.00000E+02


Sets a watchdog delay for 600 seconds: OUTP:PROT:WDOG:DEL 600


Values (seconds) must be whole numbers.


The *RST command sets this parameter to 60.


148 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **SENSe Subsytem**


**SENSe:AVERage 1|2|4|8|16**
**SENSe:AVERage?**


Sets the averaging count for measurements.


1, 2, 4, 8, or 16 +1


Set the moving average period to 4 counts: SENS:AVER 4


This parameter does not apply to the peak current measurement.


The *RST command sets this parameter to ON.


**SENSe:CURRent[:PEAK]:HOLD:CLEar**


Clears the peak held current measurement.


(none) (none)


Use FETCh:CURRent:AMPLitude:MAXimum:HOLD? to query the peak held current measurement.


Keysight AC6800B Series Operating and Service Guide 149


3 SCPI Programming Reference

##### **STATus Subsystem**


Status register programming lets you determine the operating condition of the instrument at any time.
The instrument has three groups of status registers; Operation, Questionable, and Standard Event.
The Operation and Questionable status groups each consist of the Condition, Enable, and Event
registers as well as NTR and PTR filters.


The Status subsystem is also programmed using Common commands. Common commands control
additional status functions such as the Service Request Enable and the Status Byte registers. Refer to

Status Overview for more information.


**STATus:OPERation[:EVENt]**


Queries the event register for the Operation Status group. This is a read-only register, which stores
(latches) all events that are passed by the Operation NTR and/or PTR filter. Reading the Operation
Status Event register clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


*RST does not affect this register unless the reset causes an event that the filters are configured to

capture.


**STATus:OPERation:CONDition?**


Queries the operation condition register. This is a read-only register, which holds the instrument's live
(unlatched) operational status. Reading the Operation Status Condition register does not clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, with bit 3
(value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query returns +40.


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


150 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**STATus:OPERation:ENABle <** _**value**_ **>**

**STATus:OPERation:ENABle?**


Sets the value of the enable register for the Operation Status group. The enable register is a mask for
enabling specific bits from the Operation Event register to set the OPER (operation summary) bit of the
Status Byte register. The STATus:PRESet command clears all enable register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8), and
bit 7 (value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 3 and 4 in the enable register: STAT:OPER:ENAB 24



< _bit value_ 


For example, with bit 3 (value 8) and bit 5 (value 32) set (and corresponding bits enabled), the query

returns +40.


A *CLS does not clear the enable register, but does clear the event register.


**STATus:OPERation:NTRansition <** _**value**_ **>**

**STATus:OPERation:NTRansition?**


**STATus:OPERation:PTRansition <** _**value**_ **>**

**STATus:OPERation:PTRansition?**


Sets and queries the value of the NTR (Negative-Transition) and PTR (Positive-Transition) registers.
These registers serve as a polarity filter between the Operation Condition and Operation Event
registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the Operation
Condition register causes that bit in the Operation Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the Operation
Condition register causes that bit in the Operation Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8), and bit
7 (value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 3 and 4 in the NTR register: STAT:OPER:NTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Operation Condition register sets the corresponding bit in the Operation Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Operation Condition register can set the corresponding bit in the Operation Event register.


The value returned is the binary-weighted sum of all bits set in the register.


Keysight AC6800B Series Operating and Service Guide 151


3 SCPI Programming Reference


**STATus:PRESet**


Initializes the transition and enable filters for both SCPI register groups (OPERation and
QUEStionable).


The STATus:PRESet command only affects the ENABle register and the transition filter register of the
status data structure. It does not clear any event registers or the error/event queue. To reset all event
registers and the queue within the device status reporting mechanism, use *CLS.


For status data required by SCPI, STAT:PRES sets the transition filter registers so that only positive
transitions are detected and sets the ENABle register to all zeros. The settings of the service request
enable register, parallel poll enable register, memory registers related to the *SAV command, the
instrument address, output queue, and power on status clear flag are not affected by this command.


STAT:OPER:ENAB STAT:QUES:ENAB 0 - all bits disabled


STAT:OPER:NTR STAT:QUES:NTR 0 - all bits disabled


STAT:OPER:PTR 65535 all bits enabled


STAT:QUES:PTR 65535 all bits enabled


(none) (none)


**STATus:QUEStionable[:EVENt]?**


Queries the event register for the Questionable Status group. This is a read-only register, which
stores (latches) all events that are passed by the Operation NTR and/or PTR filter. Reading the
Questionable Status Event register clears it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, to enable bit
2 (value 4) and bit 4 (value 16), the corresponding decimal value would be 20 (4 + 16).


*RST does not affect this register.


152 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**STATus:QUEStionable:CONDition?**


Queries the condition register for the Questionable Status group. This is a read-only register, which
holds the instrument's live (unlatched) operational status. Reading the Questionable Status
Condition register does not clear it.


(none) < _bit value_ 

The value returned is the binary-weighted sum of all bits set in the register.For example, to enable bit
2 (value 4) and bit 4 (value 16), the corresponding decimal value would be 20 (4 + 16).


The condition register bits reflect the current condition. If a condition goes away, the corresponding bit

is cleared.


A *RST clears this register, other than those bits where the condition still exists after *RST.


**STATus:QUEStionable:ENABle <** _**value**_ **>**

**STATus:QUEStionable:ENABle?**


Sets the value of the enable register for the Questionable Status group. The enable register is a mask
for enabling specific bits from the Questionable Event register to set the QUES (questionable
summary) bit of the Status Byte register. The STATus:PRESet command clears all enable register bits.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8),
and bit 7 (value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 2 and 4 in the questionable enable register: STAT:QUES1:ENAB 24



< _bit value_ 


For example, to enable bit 2 (value 4) and bit 4 (value 16), the corresponding decimal value would be
20 (4 + 16).


A *CLS does not clear the enable register, but does clear the event register.


Keysight AC6800B Series Operating and Service Guide 153


3 SCPI Programming Reference


**STATus:QUEStionable:NTRansition <** _**value**_ **>**

**STATus:QUEStionable:NTRansition?**


**STATus:QUEStionable:PTRansition <** _**value**_ **>**

**STATus:QUEStionable:PTRansition?**


Sets and queries the value of the NTR (Negative-Transition) and PTR (Positive-Transition) registers.
These registers serve as a polarity filter between the Questionable Condition and Questionable Event
registers.


When a bit in the NTR register is 1, then a 1-to-0 transition of the corresponding bit in the
Questionable Condition register causes that bit in the Questionable Event register to be set.


When a bit in the PTR register is 1, then a 0-to-1 transition of the corresponding bit in the Questionable
Condition register causes that bit in the Questionable Event register to be set.


The STATus:PRESet command sets all bits in the PTR registers and clears all bits in the NTR registers.


**Parameter** **Typical Return**



Decimal sum of the bits in the register, default 0. For example, to enable bit 2 (value 4), bit 3 (value 8), and
bit 7 (value 128), use decimal sum 140 (4 + 8 + 128). Default 0.


Enable bits 3 and 4 in the questionable PTR register: STAT:QUES:PTR 24



< _bit value_ 


If the same bits in both NTR and PTR registers are set to 1, then any transition of that bit at the
Questionable Condition register sets the corresponding bit in the Questionable Event register.


If the same bits in both NTR and PTR registers are set to 0, then no transition of that bit at the
Questionable Condition register can set the corresponding bit in the Questionable Event register.


The value returned is the binary-weighted sum of all bits set in the register.


154 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **SYSTem Subsystem**


The SYSTem subsystem controls instrument functions that are not directly related to output control,
measurement, or status functions. Note that IEEE-488 Common commands also control system
functions such as state management.


**SYSTem:BEEPer[:IMMediate]**


Issues a single beep.


(none) (none)


This command overrides the current beeper state . You can issue a beep even if the beeper is turned
off by SYSTem:BEEPer:STATe.


Programmed beeps may be useful for program development and troubleshooting.


**SYSTem:BEEPer:KCLick ON|1|OFF|0**
**SYSTem:BEEPer:KCLick?**


Disables or enables the click tone heard when you press a front-panel key.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Disable front-panel key click: SYST:BEEP:KCLick OFF


The front-panel key click and instrument beeper settings do not affect each other.


This parameter is not affected by *RST or *RCL.


**SYSTem:BEEPer:STATe ON|1|OFF|0**

**SYSTem:BEEPer:STATe?**


Disables or enables the beep heard when an error is generated.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Disable the beeper state: SYST:BEEP:STAT OFF


The front-panel key click and instrument beeper settings do not affect each other.


A beep is always emitted (even with beep state OFF) when SYSTem:BEEPer is sent.


This parameter is not affected by *RST or *RCL.


Keysight AC6800B Series Operating and Service Guide 155


3 SCPI Programming Reference


**SYSTem:COMMunicate:RLSTate LOCal|REMote|RWLock**
**SYSTem :COMMunicate:RLSTate?**


Configures the remote/local/lockout state of the instrument.


LOCal|REMote|RWLock LOC, REM, or RWL


Sets the remote/local state to remote: SYST:COMM:RLST REM


The LOCal parameter (the power-on default), disables the remote interface, the REMote parameter
enables remote control of the instrument, and RWLock enables remote control of the instrument and

locks out front-panel operation. The RWLocal parameter also locks out the operation of the Sense
switch on the rear panel.


The remote/local instrument state can also be set by other interface commands over the GPIB and
other I/O interfaces. If multiple remote interfaces are active, the interface with the most recently
changed remote/local state takes precedence.


This parameter is not affected by *RST or *RCL.


**SYSTem:ERRor[:NEXT]?**


Reads and clears one error from the error queue.


(none)


The Err annunciator turns on when any error is in the error queue. Error retrieval is first-in-first-out,
and errors are cleared as you read them. When you have read all errors from the error queue, the Err

annunciator turns off.


If more than 16 errors have occurred, the most recent error is replaced with -350,"Error queue
overflow". No additional errors are stored until you remove errors from the queue. Reading the error
queue when it is empty yields the message +0,"No error".


The error queue is cleared by *CLS and power cycling. It is not cleared by *RST.


Errors have the following format (the error string may contain up to 255 characters).
< _error code_ >,< _error string_ 
For a list of error codes and error strings, see SCPI Error Messages.


156 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**SYSTem:ERRor:COUNt?**


Returns the number of errors in the error queue.


(none) +11


The returned value always begins with a + character, even if the number is 0.


The Err annunciator turns on when any error is in the error queue. Error retrieval is first-in-first-out,
and errors are cleared as you read them. When you have read all errors from the error queue, the Err

annunciator turns off.


If more than 16 errors have occurred, the most recent error is replaced with -350,"Error queue
overflow". No additional errors are stored until you remove errors from the queue. Reading the error
queue when it is empty yields the message +0,"No error".


The error queue is cleared by *CLS and power cycling. It is not cleared by *RST.


Errors have the following format (the error string may contain up to 255 characters).
< _error code_ >,< _error string_ 
For a list of error codes and error strings, see SCPI Error Messages.


**SYSTem:SECurity:IMMediate**


Clears all user memory (including stored states) and reboots the instrument in the *RST state. This
command is typically used to prepare the instrument for removal from a secure area. Instrument
identification data (instrument firmware, model number, serial number, MAC address) and calibration

data is not erased.


The sanitization procedure is not recommended for use in routine applications because of the
possibility of unintended loss of data.


(none) (none)


Keysight AC6800B Series Operating and Service Guide 157


3 SCPI Programming Reference


**SYSTem:SSAVer[:STATe] ON|1|OFF|0**
**SYSTem:SSAVer[:STATe]?**


Enables or disables the screen saver.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Turn the screen saver off: SYST:SSAV OFF


This parameter is not affected by *RST or *RCL.


This parameter is not affected by power cycling.


**SYSTem:SSAVer:DELay <** _**seconds**_ **>|MINimum|MAXimum**
**SYSTem:SSAVer:DELay? [MINimum|MAXimum]**


Sets the delay time for screen saver activation. The value is the time since the last front-panel access
or the last access since remote (with remote wake-up enabled).


Integer from 60 to 59940 +6.60000E+02


Set the delay to five minutes: SYST:SSAV DEL 300


This parameter is not affected by *RST or *RCL.


The unit is in seconds, but the value will be rounded to a the nearest minute. For example, a setting of
100 s will be rounded to 120 s (exactly two minutes).


This parameter is not affected by power cycling.


**SYSTem:SSAVer:RWAKeup ON|1|OFF|0**
**SYSTem:SSAVer:RWAKeup?**


Enables or disables remote screen saver wake-up.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Enable remote screen saver wake-up: SYST:SSAV:RWAK ON


When this feature is on, the screen saver is never activated as long as remote IO is operating and will
wake-up by a remote access if the screen saver is active.


This parameter is not affected by *RST or *RCL.


This parameter is not affected by power cycling.


158 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**SYSTem:VERSion?**


Returns the version of SCPI that the instrument uses.


(none) 1999.0


The query always returns 1999.0


The SCPI version cannot be determined from front panel.


Keysight AC6800B Series Operating and Service Guide 159


3 SCPI Programming Reference

##### **TRIGger Subsystem**


The TRIGger subsystem sets the measurement operation and trigger functions for various instrument
operations:


TRIGger:ACQuire Triggers measurement acquisition.


TRIGger:SYNChronize Synchronizes output phase using OUTPut ON instead of a software trigger.


TRIGger[:TRANsient] Triggers synchronizes output changes.


**TRIGger:ACQuire[:IMMediate]**


Sends a software trigger to the ACQuire subsystem.


(none) (none)


Configure and trigger the ACQuire subsystem:


**TRIGger:ACQuire:SOURce IMMediate|BUS**
**TRIGger:ACQuire:SOURce?**


Sets the trigger source that starts the measurement after INIT:ACQ. Specify IMMediate to start the
measurement immediately, or BUS (the default) to wait for a software trigger (*TRG, TRIG:ACQ, or
TRIG:ACQ).


IMMediate|BUS IMM or BUS


Set the trigger source to BUS:
TRIG:ACQ:SOUR BUS


This parameter is set to BUS at power-on or after *RCL or *RST.


160 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**TRIGger:SYNChronize:SOURce IMMediate|PHASe**
**TRIGger:SYNChronize:SOURce?**


Sets the output-on phase control when OUTPut ON is sent.


IMMediate|PHASe IMM or PHAS


Enable output-on phase control:

TRIG:SYNC:SOUR PHASE


This parameter is set to IMMediate at power-on or after *RCL or *RST.


**TRIGger:SYNChronize:PHASe[:ON] <** _**value**_ **>|MINimum|MAXimum**
**TRIGger:SYNChronize:PHASe? [MINimum|MAXimum]**


Sets the phase angle of the output-on phase control in degrees.


An integer from 0 to 359 degrees +1.80000E+02


Set the OUTPUT on phase angle to 180°:

TRIG:SYNC:PHAS 180


This command supports the parameter suffix DEG (degrees), but not RAD (radians).


The *RST command sets this parameter to 0.


**TRIGger:TRANsient[:IMMediate]**


Triggers the TRANsient subsystem.


(none) (none)


Configure and trigger the TRANsient subsystem:


Keysight AC6800B Series Operating and Service Guide 161


3 SCPI Programming Reference


**TRIGger:TRANsient:SOURce IMMediate|BUS**
**TRIGger:TRANsient:SOURce?**


Sets the trigger source for changing the setting value after INIT:TRAN. Specify IMMediate (the default)
to start the measurement immediately, or BUS to wait for a software trigger (*TRG, TRIG, or
TRIG:TRAN).


IMMediate|BUS IMM or BUS


Set the trigger source to BUS:

TRIG:SOUR BUS


This parameter is set to IMMediate at power-on or after *RCL or *RST.


162 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **VOLTage Subsystem**


Voltage commands program the output voltage of the instrument.


Soft limits apply only if the corresponding limit state is on, and they only apply to voltage
settings executed after the limit state is set on. Soft limits do not retroactively apply to the
existing voltage setting.


Keysight AC6800B Series Operating and Service Guide 163


3 SCPI Programming Reference


**[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] <** _**value**_ **>|MINimum|MAXimum[, <** _**low_**_
_**limit**_ **>|MINimum|MAXimum, <** _**high_limit**_ **>|MINimum|MAXimum]**

**[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? [MINimum|MAXimum]**


**[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude]? [MINimum|MAXimum]**


Sets the immediate or triggered AC voltage level, and optionally the soft limits for the immediate
voltage. The triggered level is the value that is transferred to the output when an output step is
triggered. Units are in volts (rms).


**Parameter** **Typical Return**



AC mode 155 V range: 0.0 to 157.5 V


AC mode 310 V range: 0.0 to 315.0 V


AC+DC mode: The total voltage must be within the voltage limits, and the AC+DC peak must be between 
445 and +445 V.



+2.00000E+01



This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to AC and AC+DC.


Voltage units (MV, V, KV) are supported.


The *RST command sets this parameter to 0.


When VOLT:LIM:STAT is OFF, only individual range checking for each argument is performed. The
< _value_ - must be within its own MINimum-MAXimum range considering VOLTage:RANGe and the
existing DC component setting if in ACDC coupling mode. The typical MAXimum in OUTP:COUP:AC
mode is 157.5 or 315.0 VAC, but in ACDC mode with nonzero DC components the value will be
reduced. The lower and upper soft limits must be within their MINimum-MAXimum range as defined by
the active VOLT:RANG setting. Both have the same 0 to +157.5 or +315.0 VAC limits regardless of any
operational conditions other than VOLT:RANG.


When VOLT:LIM:STAT is ON, the range checking described above is performed, and an additional
check ensures that the < _value_ - does not go beyond the high and low soft limits.


The allowable range for MINimum and MAXimum varies depending on whether LIM:STAT is ON or
OFF. When LIM:STAT is OFF, the MINimum and MAXimum values are simply calculated from the
VOLT:RANG and the DC component if it is already set in ACDC mode.


164 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**[SOURce:]VOLTage[:LEVel]:LIMit[:STATe] ON|1|OFF|0**

**[SOURce:]VOLTage[:LEVel]:LIMit[:STATe]?**


**[SOURce:]VOLTage:OFFSet:LIMit[:STATe] ON|1|OFF|0**

**[SOURce:]VOLTage:OFFSet:LIMit[:STATe]?**


Enables or disables soft limits for voltage. The OFFSet version of the command applies to DC voltage;
the [:LEVel] version applies to AC voltage.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Limit the AC voltage to between 150 and 250 Vrms:

VOLT:LIM:LOW 150

VOLT:LIM:UPP 250

VOLT:LIM ON


The *RST command sets this parameter to OFF.


Once the soft-limit function is enabled, any new voltage values from the VOLT command are validated
against that lower and upper soft limits.


**[SOURce:]VOLTage[:LEVel]:LIMit:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage[:LEVel]:LIMit:LOWer? [MINimum|MAXimum]**


**[SOURce:]VOLTage[:LEVel]:LIMit:UPPer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage[:LEVel]:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower and upper AC voltage limits.


0 to 157.5 V in lower range, 0 to 315.0 V in upper range +2.50000E+02


Limit the AC voltage to between 150 and 250 Vrms:

VOLT:LIM:LOW 150

VOLT:LIM:UPP 250

VOLT:LIM ON


This command applies to AC and AC+DC.


If the new setting conflicts with the existing immediate voltage setting (because the limit range
becomes tighter), this command coerces the voltage setting to fit with the new upper and lower limits.


A SCPI error (-222,"Data out of range") occurs if the entered parameter value is outside the range.


The *RST command sets both limits to 0.


Keysight AC6800B Series Operating and Service Guide 165


3 SCPI Programming Reference


**[SOURce:]VOLTage[:LEVel]:MODE FIXed|STEP**

**[SOURce:]VOLTage[:LEVel]:MODE?**


Sets the trigger transient mode for voltage settings.


FIXed|STEP FIX


Set the trigger function control of the AC voltage to STEP:

VOLT:MODE STEP


Specifying FIX (default) disables the trigger function; specifying STEP enables it.


The *RST command sets this parameter to FIXed.


**[SOURce:]VOLTage:OFFSet[:IMMediate] <** _**value**_ **>|MINimum|MAXimum [,<** _**low_limit**_ **>|MINim-**
**um|MAXimum,<** _**high_limit**_ **>|MINimum|MAXimum]**

**[SOURce:]VOLTage:OFFSet[:IMMediate]? [MINimum|MAXimum]**


**[SOURce:]VOLTage:OFFSet:TRIGgered <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:TRIGgered? [MINimum|MAXimum]**


Sets the immediate or triggered DC voltage in VDC, and optionally sets the soft limits for the
immediate DC voltage.


**Parameter** **Typical Return**



DC mode 155 V range: -222.5 to +222.5 V


DC mode 310 V range: -445.0 to +445.0 V


AC+DC mode: The total voltage must be within the voltage limits, and the AC+DC peak must be between -445

and +445 V.


Set the immediate DC voltage to 150 V:

VOLT:OFFS 150



+1.50000E+02



This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to DC and AC+DC.


Error message +160 is generated if the IMMediate voltage is set outside the MIN/MAX limits, and error
message +161 is generated if the TRIGgered voltage is set outside the MIN/MAX limits.


Voltage units (MV, V, KV) are supported.


The *RST command sets this parameter to 0.


166 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


**[SOURce:]VOLTage:OFFSet:LIMit:LOWer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:LIMit:LOWer? [MINimum|MAXimum]**


**[SOURce:]VOLTage:OFFSet:LIMit:UPPer <** _**value**_ **>|MINimum|MAXimum**

**[SOURce:]VOLTage:OFFSet:LIMit:UPPer? [MINimum|MAXimum]**


Sets the lower and upper DC voltage soft limits.



155 V range: -222.5 to 222.5 V
310 V range: -445.0 to 445.0 V



(none)



Set the voltage limits to the be 100 and 350 V:

VOLT:OFFS:LIM:LOW 100

VOLT:OFFS:LIM:UPP 350


This command takes one or three arguments. You cannot have a low or high limit without the other.


This command applies to DC and AC+DC.


Error message +166 is generated if the lower limit is beyond the MIN/MAX limits, and error message
+167 is generated if the upper limit is beyond the MIN/MAX limits.


Voltage units (MV, V, KV) are supported.


**[SOURce:]VOLTage:OFFSet:MODE FIXed|STEP**

**[SOURce:]VOLTage:OFFSet:MODE?**


Sets the trigger transient mode for DC voltage settings. Specifying FIX (default) disables the trigger
function; specifying STEP enables it.


FIXed|STEP FIX


Set the voltage offset mode to FIXed

VOLT:OFF:MODE FIXed


The *RST command sets this parameter to FIXed.


Keysight AC6800B Series Operating and Service Guide 167


3 SCPI Programming Reference


**[SOURce:]VOLTage:PROGramming:SOURce INTernal|EXTAC|EXTDC**

**[SOURce:]VOLTage:PROGramming:SOURce?**


Sets the voltage programming source.


INTernal|EXTAC|EXTDC INT, EXTAC, or EXTDC


Set the voltage programming source to external AC:

VOLT:PROG:SOUR EXTAC


The EXTAC and EXTDC modes require the analog interface card.


The *RST command sets this parameter to INTernal.


**[SOURce:]VOLTage:RANGe[:UPPer] 155|310|MINimum|MAXimum**

**[SOURce:]VOLTage:RANGe[:UPPer]? [MINimum|MAXimum]**


Sets the voltage range. If the voltage range is switched, the VOLT:TRIG and VOLT:OFFS:TRIG settings
are cleared, and ABORt[:ALL] is applied.


155|310|MINimum|MAXimum +2.70000E+02


Set the voltage range to 310 V

VOLT:RANG MAX


You may only set this parameter if output is OFF.


Setting this parameter turns voltage autoranging OFF and aborts both TRANsient and ACQuire
operations.


The *RST command sets this parameter to 155.


If you enter a value between 0 and 155, the 155 V range will be chosen. If you enter a value above 155
V, the 310 V range will be chosen.


**[SOURce:]VOLTage:RANGe:AUTO ON|1|OFF|0**

**[SOURce:]VOLTage:RANGe:AUTO?**


Enables or disables voltage autoranging. If this parameter changes, the VOLT:TRIG and
VOLT:OFFS:TRIG settings are cleared, and all operations are aborted.


ON|1|OFF|0 0 (OFF) or 1 (ON)


Turn on voltage autoranging:

VOLT:RANG:AUTO 1


The *RST command sets this parameter to OFF.


168 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference

##### **Default Settings**


OUTPut[:STATe] (statefilevalue) OFF


OUTPut:COUPling (statefilevalue) AC


OUTPut:PROTection:WDOG[:STATe] (statefilevalue) OFF


OUTPut:PROTection:WDOG:DELay (statefilevalue) 60


[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] (statefilevalue) MAXimum for instrument


[SOURce:]CURRent:OFFSet[:IMMediate] (statefilevalue) MAXimum for instrument


[SOURce:]CURRent:PROTection:STATe (statefilevalue) ON


[SOURce:]FREQuency[:CW] (statefilevalue) 60


[SOURce:]FREQuency:LIMit[:STATe] (statefilevalue) OFF


[SOURce:]FREQuency:LIMit:LOWer (statefilevalue) 40


[SOURce:]FREQuency:LIMit:UPPer (statefilevalue) 500


[SOURce:]FREQuency:MODE (statefilevalue) FIXed


[SOURce:]FREQuency:TRIGgered (statefilevalue) (immediatelevel)


[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] (statefilevalue) 0


[SOURce:]VOLTage[:LEVel]:LIMit[:STATe] (statefilevalue) OFF


[SOURce:]VOLTage[:LEVel]:LIMit:LOWer (statefilevalue) 0


[SOURce:]VOLTage[:LEVel]:LIMit:UPPer (statefilevalue) 157.5


[SOURce:]VOLTage[:LEVel]:MODE (statefilevalue) FIXed


[SOURce:]VOLTage[:LEVel]:TRIGgered[:AMPLitude] (immediatelevel) (immediatelevel)


[SOURce:]VOLTage:OFFSet[:IMMediate] (statefilevalue) 0


[SOURce:]VOLTage:OFFSet:LIMit[:STATe] (statefilevalue) OFF


[SOURce:]VOLTage:OFFSet:LIMit:LOWer (statefilevalue) -222.5


[SOURce:]VOLTage:OFFSet:LIMit:UPPer (statefilevalue) 222.5


[SOURce:]VOLTage:OFFSet:MODE (statefilevalue) FIXed


[SOURce:]VOLTage:OFFSet:TRIGgered (statefilevalue) (immediatelevel)


[SOURce:]VOLTage:PROGramming:SOURce (statefilevalue) INTernal


[SOURce:]VOLTage:RANGe[:UPPer] (statefilevalue) 155.0


[SOURce:]VOLTage:RANGe:AUTO (statefilevalue) OFF


INITiate:CONTinuous:ACQuire OFF OFF


TRIGger:TRANsient:SOURce (statefilevalue) BUS


TRIGger:SYNChronize:SOURce (statefilevalue) IMMediate


TRIGger:SYNChronize:PHASe[:ON] (statefilevalue) 0


TRIGger:ACQuire:SOURce (statefilevalue) BUS


DISPlay[:WINDow][:STATe] (statefilevalue) 1


DISPlay[:WINDow]:METer:COUPling (statefilevalue) ACDC


SENSe:AVERage (statefilevalue) 1


Keysight AC6800B Series Operating and Service Guide 169


3 SCPI Programming Reference

##### **SCPI Error Messages**


The error messages below are in the following format, with a numeric error code and a quoted error
string separated by a comma:


-222,"Data out of range"


The error/event queue is empty and returns 0,"No error" when the *CLS common command is sent,
the last item in the error queue is read, or the instrument cycles power.







-100 to -199 Command

errors


-200 to -299 Execution

errors


-300 to -399 Device
specific

errors


-400 to -499 Query

errors


100 to 399 Specific

SCPI

Errors


900 and above Self-test


errors



An IEEE 488 syntax error has been detected by the instrument's parser. (n/a)


An error has been detected by the instrument's execution control block. (n/a)


A device-specific error has been detected, possibly related to a hardware failure. (n/a)



The instrument's output queue control has detected a problem with the message
exchange protocol.


An error in the instrument's operation has occurred, such as a settings conflict or an
attempt to execute a command when the instrument is in an incorrect mode.



(n/a)


(n/a)



A self-test error has occurred. 2



-101 Invalid character


-102 Syntax error


-103 Invalid separator


-108 Parameter not allowed


-109 Missing parameter


-110 Command header error


-112 Program mnemonic too long


-113 Undefined header


-114 Header suffix out of range


-115 Unexpected number of parameters


-120 Numeric data error


-128 Numeric data not allowed


170 Keysight AC6800B Series Operating and Service Guide


3 SCPI Programming Reference


-131 Invalid suffix


-138 Suffix not allowed


-140 Character data error


-141 Invalid character data


-144 Character data too long


-150 String data error


-151 Invalid string data


-158 String data not allowed


-211 Trigger ignored


-213 Init ignored


-214 Trigger deadlock


-220 Parameter error


-221 Settings conflict


-222 Data out of range


-224 Illegal parameter value


-230 Data corrupt or stale


-241 Hardware missing; option not installed


-310 System Error


-311 Memory Error


-313 Calibration memory lost


-314 Save/recall memory lost


-315 Configuration memory lost


-330 Self-test error


-350 Queue overflow


-363 Input buffer overrun


-410 Query INTERRUPTED


-420 Query UNTERMINATED


-430 Query DEADLOCKED


+101 Calibration state is off


+102 Calibration password is incorrect


+104 Bad sequence of calibration commands


+107 Programming cal constants out of range


+108 Measurement cal constants out of range


+117 Calibration error


+130 Remote calibration is inhibited by local operation


+131 Operation conflicts with OUTPUT ON state


+132 Operation conflicts with protection state


Keysight AC6800B Series Operating and Service Guide 171


3 SCPI Programming Reference


+133 Operation conflicts with OUTPUT COUPLE setting


+134 Operation conflicts with AUTO RANGE


+155 Operation conflicts with EXT-AC or EXT-DC program source


+140 LOW RANGE conflicts with existing VOLT[:IMM] setting


+141 LOW RANGE conflicts with existing VOLT:TRIG setting


+142 LOW RANGE conflicts with existing VOLT:OFFS[:IMM] setting


+143 LOW RANGE conflicts with existing VOLT:OFFS:TRIG setting


+150 Overlaid peak value of AC (IMM) and DC (IMM) components is too large


+151 Overlaid peak value of AC (IMM) and DC (TRIG) components is too large


+152 Overlaid peak value of AC (TRIG) and DC (IMM) components is too large


+153 Overlaid peak value of AC (TRIG) and DC (TRIG) components is too large


+160 IMM setting is out of range


+161 TRIG setting is out of range


+162 Overlaid peak value with existing AC (IMM) component is too large


+163 Overlaid peak value with existing AC (TRIG) component is too large


+164 Overlaid peak value with existing DC (IMM) component is too large


+165 Overlaid peak value with existing DC (TRIG) component is too large


+166 LIM:LOW setting is out of range


+167 LIM:UPP setting is out of range


+168 IMM setting value and soft-limits conflict with LOWER<=VALUE<=UPPER condition


+169 TRIG setting value and soft-limits conflict with LOWER<=VALUE<=UPPER condition


+302 Option not installed


+309 Cannot initiate, voltage and frequency in fixed mode


+901 HW failure (DSP DETECT state)


+902 HW failure (DSP VCC state)


+903 HW failure (DSP INPUT state)


+904 HW failure (DSP Communication Failure)


172 Keysight AC6800B Series Operating and Service Guide


Keysight AC6800B Series Operating and Service Guide

## 4 Calibration, Verification, and Service


**Calibration Overview**


**Calibration Procedure**


**Performance Test Records**


**Performance Verification**


**Service and Maintenance**


4 Calibration, Verification, and Service

##### **Calibration Overview**


This section contains information regarding the instrument's calibration.Be sure to read through the
overview first, and follow the steps in order.


**Closed–Case Electronic Calibration**


The instrument uses closed-case electronic calibration; no internal adjustments are required. The
instrument calculates correction factors based on reference signals that you apply and then the user
stores the correction factors in nonvolatile memory. This data is not changed by cycling power or

*RST.


**Keysight Technologies Calibration Services**


Keysight Technologies offers calibration services using automated calibration systems that enable
Keysight to provide calibration at competitive prices. See Contacting Keysight for information on
contacting Keysight.


**Calibration Interval**


The instrument should be calibrated on a regular interval determined by the accuracy requirements of
your application. A 1-year interval is adequate for most applications. Accuracy specifications are
warranted only if adjustment is made at regular calibration intervals. Accuracy specifications are not
warranted beyond the 1-year calibration interval.


**Time Required for Calibration**


The instrument can be automatically calibrated under computer control, including complete
calibration procedure and performance verification tests, in approximately 30 minutes once the
instrument is warmed–up.


174 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service

##### **Calibration Procedure**


**Enter Calibration Mode**


**DC Voltage Low Range**


**DC Voltage High Range**


**AC Voltage Low Range**


**AC Voltage High Range**


**Current Low Range**


**Current High Range**


**End Calibration**


**Enter Calibration Mode**


To begin the calibration process, log in using the Admin password. The factory default is no password.



|Front Panel|SCPI|
|---|---|
|Select** System > Admin > Login**. Then enter the password and press<br>**[Select]**.|CAL:STAT ON<br>[, <_password_>]|


**DC Voltage Low Range**





Connect the voltage input of the 3458A DMM to the instrument output as shown below and configure
the 3458A for DCV measurements. You can use the following SCPI commands (or their front-panel
equivalents) to configure the DMM:


*RST

DCV AUTO, AZERO ON, FIXEDZ OFF

NPLC 100


Keysight AC6800B Series Operating and Service Guide 175


4 Calibration, Verification, and Service









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select the low voltage<br>range.|**[Menu] > System > Admin > Cal > DC**<br>Then select the** Low** range.|CAL:VOLT:OFFS 219|
|2|Select the first voltage<br>calibration point.|Select** Next**. The information field should indicate:<br>**Enter P1 measured data**|CAL:LEV P1<br>*OPC?|
|3|Measure the output<br>voltage with the DMM<br>and enter the data.|Enter the measurement from the external DMM in<br>the** Measured Data** field. Press** [Select]** or<br>**[Enter]** when done.|CAL:DATA <_data_>|
|4|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|
|5|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|


**DC Voltage High Range**


Use the same DMM configuration as for the DC Voltage Low Range calibration.









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select the high voltage<br>range.|**[Menu] > System > Admin > Cal > DC**<br>Then select the** High** range.|CAL:VOLT:OFFs 438|
|2|Select the first voltage<br>calibration point.|Select** Next**. The information field should<br>indicate:<br>**Enter P1 measured data**|CAL:LEV P1<br>*OPC?|
|3|Measure the output<br>voltage with the DMM<br>and enter the data.|Enter the measurement from the external DMM<br>in the** Measured Data** field. Press** [Select]** or<br>**[Enter]** when done.|CAL:DATA <_data_>|
|4|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|Repeat steps 2 and 3 for calibration point 2 (P2). Note that the SCPI command in step 2 will change to<br>CAL:LEV P2, and the front-panel message will read** Enter P2 measured data**.|
|5|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|


**AC Voltage Low Range**


Use the same physical configuration that you used for DC Voltage calibration, but configure the 3458A
for AC voltage measurements. You can use the following SCPI commands (or their front-panel
equivalents) to configure the DMM:


*RST

SETACV ANA, RANGE AUTO, ACBAND 20, 2E6

ACV


176 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service











|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select the low voltage range.|**[Menu] > System > Admin > Cal >**<br>**AC.**<br>Then select the** Low** range.|CAL:VOLT 155|
|2|Select the first voltage<br>calibration point.|Select** Next**. The information field should<br>indicate:<br>**Enter P1 measured data**|CAL:LEV P1<br>*OPC?|
|3|Measure the output voltage<br>with the DMM and enter the<br>data.|Enter the measurement from the external<br>DMM in the** Measured Data** field. Press<br>**[Select]** or** [Enter]** when done.|CAL:DATA <_data_>|
|4|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|
|5|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|


**AC Voltage High Range**


Use the same DMM configuration as for the AC Voltage Low Range calibration.









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select the high voltage<br>range.|**[Menu] > System > Admin > Cal >**<br>**AC.**<br>Then select the** High** range.|CAL:VOLT 310|
|2|Select the first voltage<br>calibration point.|Select** Next**. The information field should<br>indicate:<br>**Enter P1 measured data**|CAL:LEV P1<br>*OPC?|
|3|Measure the output voltage<br>with the DMM and enter the<br>data.|Enter the measurement from the external<br>DMM in the** Measured Data** field. Press<br>**[Select]** or** [Enter]** when done.|CAL:DATA <_data_>|
|4|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|Repeat steps 2 and 3 for the second, third, fourth, and fifth calibration points. Note that the SCPI com-<br>mand should use P2, P3, P4, and P5 in place of P1. Similarly, the front-panel** Enter P1 measured**<br>**data** message from step 2 will indicate P2, P3, P4, or P5.|
|5|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|Select** Next** or press** Back** to finish this part of the calibration.|


**Current Low Range**


Connect a precision shunt resistor to the output. The shunt resistor should be able to measure at least
120% of the output’s full-scale current. Connect the Keysight 3458A DMM across the shunt resistor.
Connect the Keysight 6060B electronic load to the AC source's output. Select the full-scale (FS), Low
Range current measurement value from the Current Table for the model being calibrated, 4 A for the

AC6801B.


Keysight AC6800B Series Operating and Service Guide 177


4 Calibration, Verification, and Service


Ensure that the 6060B is in Constant Current (CC) mode. For the 3458, use the following commands to
configure the instrument after it powers up in the factory default configuration:


DCV AUTO, AZERO ON, FIXEDZ OFF

NPLC 100









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select** Low** range.|**[Menu] > System > Admin > Cal >**<br>**Current > Low_Range**|CAL:CURR:OFFS 2|
|2|Disconnect the load and shunt.|Disconnect the load and shunt.|Disconnect the load and shunt.|
|3|Go to next step.|Select** Next**.|CAL:LEV P1<br>*OPC?|
|4|Wait five minutes.|Wait five minutes.|Wait five minutes.|
|5|Go to next step.|Select** Next**.||
|6|Reconnect the load and shunt.|Reconnect the load and shunt.|Reconnect the load and shunt.|
|7||Select** Next**.||
|8|Enter a 0 (zero).|(no action required)|CAL:DATA 0|
|9||Select** Next**.||
|10|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 3.6 A for the AC6801B.|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 3.6 A for the AC6801B.|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 3.6 A for the AC6801B.|
|11|Go to next step.|Select** Next**.|CAL:LEV P2<br>*OPC?|
|12|Wait five minutes.|Wait five minutes.|Wait five minutes.|
|13||Select** Next**.||
|14|Calculate the shunt current<br>(I=V/R) and enter the data.|Enter the computed current from the<br>external DMM measurement and Load<br>resistance and press** [Select]** or<br>**[Enter]**. Select** Next** or press** Back** to<br>finish the calibration step.|CAL:DATA <_data_>|


**Current Table**







|Col1|AC6801B|AC6802B|AC6803B|AC6804B|
|---|---|---|---|---|
|Low Range|4.0 A|8.0 A|16.0 A|32.0 A|


178 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Current High Range**


Use the same setup as used for Current Low Range calibration.









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Select** High** range.|**[Menu] > System > Admin > Cal >**<br>**Current > High_Range**|CAL:CURR:OFFS 2|
|2|Disconnect the load and shunt.|Disconnect the load and shunt.|Disconnect the load and shunt.|
|3|Go to next step.|Select** Next**.|CAL:LEV P1<br>*OPC?|
|4|Wait five minutes.|Wait five minutes.|Wait five minutes.|
|5|Go to next step.|Select** Next**.||
|6|Reconnect the load and shunt.|Reconnect the load and shunt.|Reconnect the load and shunt.|
|7||Select** Next**.||
|8|Enter a 0 (zero).|(no action required)|CAL:DATA 0|
|9||Select** Next**.||
|10|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 1.8 A for the AC6801B.|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 1.8 A for the AC6801B.|Set the Keysight 6060B to Constant Current Mode and set the load current to 90% of the value from<br>the table below for the model being calibrated. For example, use 1.8 A for the AC6801B.|
|11|Go to next step.|Select** Next**.|CAL:LEV P2<br>*OPC?|
|12|Wait five minutes.|Wait five minutes.|Wait five minutes.|
|13||Select** Next**.||
|14|Calculate the shunt current<br>(I=V/R) and enter the data.|Enter the computed current from the<br>external DMM measurement and Load<br>resistance and press** [Select]** or<br>**[Enter]**. Select** Next** or press** Back** to<br>finish the calibration step.|CAL:DATA <_data_>|


**Current Table, High Range**







|Col1|AC6801B|AC6802B|AC6803B|AC6804B|
|---|---|---|---|---|
|High Range|2.0 A|4.0 A|8.0 A|16.0 A|


Keysight AC6800B Series Operating and Service Guide 179


4 Calibration, Verification, and Service


**End Calibration**


This section describes the final steps in the calibration process.


Storing calibration constants overwrites the existing ones in nonvolatile memory. If you
do not save the calibration data, the new calibration constants will only be used until you
exit the calibration state or cycle power. For the front panel, leaving the calibration state
is defined as exiting the menu levels at or below [Menu] > System > Admin > Cal . For
SCPI, leaving the calibration state is accomplished by CAL:STAT OFF.


If you are not sure you want to permanently store the new constants, do not save the
data when you exit the calibration mode.









|Step|Description|Front Panel|SCPI|
|---|---|---|---|
|1|Enter the calibration<br>date.|**[Menu] > System > Admin > Cal > Date**<br>Enter the calibration date in the** Data** field. Press<br>**[Select]** or** [Enter]** to finish.|CAL:DATE "<<br>_string_>"|
|2|Save calibration data.|**[Menu] > System > Admin > Cal > Save**|CAL:SAVE|
|3|Log out.|**[Menu] > System > Admin > Logout**|CAL:STAT OFF|


180 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service

##### **Performance Verification**


This chapter provides performance test verification procedures for the AC6800B Series instruments:


Test equipment required


Front-panel instructions


Performance test records


SHOCK HAZARD


These tests should only be performed by qualified personnel. During the performance of
these tests, hazardous voltages may be present at the output of the unit.


**Test equipment required**



Digital Voltmeter Resolution: 10 nV @ 1V


Readout: 8½ digits


Accuracy: 20 ppm



Keysight 3458A



AC Current Monitor 0.01 Ω +/-200 ppm 25 A Guildline 7350-.01


AC Current Monitor (AC6804B only) 0.01 Ω +/-200 ppm 50 A Guildline 7340-50A


DC Current Shunt 0.1 Ω 30 A Guildline 9230A-30


DC Current Shunt (AC6804B only) 0.05 Ω 50 A Guildline 9230A-50


Frequency Counter Accuracy @1 KHZ < 0.001% Keysight 53210A


Load Resistors 16 Ω, 5 A, 400 W minimum (AC6801B)


64 Ω, 2.5 A, 400 W minimum (AC6801B)


8 Ω, 10 A, 800 W minimum (AC6802B)


32 Ω, 5 A, 800 W minimum (AC6802B)


4 Ω, 20 A, 1600 W minimum (AC6803B)


16 Ω, 10 A, 1600 W minimum (AC6803B)


2 Ω, 40 A, 3200 W minimum (AC6804B)


8 Ω, 20 A, 3200 W minimum (AC6804B)


**Current Monitoring Resistor**


A four-terminal current monitoring resistor (current shunt) listed above is required to eliminate output
current measurement error caused by voltage drops in the load leads and connections.


Keysight AC6800B Series Operating and Service Guide 181


4 Calibration, Verification, and Service


**DMM Configuration**


For all tests besides the DC Voltage and Current tests, the DMM should be set to measure AC volts
and 100 NPLCs. For the DC voltage tests, the DMM should be set to measure DC volts and 100 NPLC.


**Constant Voltage Tests**


If more than one meter or a meter and an oscilloscope are used, connect each to the sense terminals
by separate leads to avoid mutual coupling effects.


182 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Front-panel instructions**


In these procedures, the term "UUT" means "unit under test," or the AC6801B, AC6802B,
AC6803B, or AC6804B.


The tables below provide the test procedures for verifying the AC6801B, AC6802B, AC6803B, and the
AC6804B, in compliance with the instrument's specifications. Please refer to the calibration procedure
if you observe out-of specification performance. The performance test specifications are listed in the
Performance Test Records at the end of this chapter. You can record the actual measured values in
the columns provided. When performing the load tests select an adequate gauge wire using the
procedures given in the User’s Guide for connecting the load.


**Voltage Programming and Readback Accuracy**


This procedure verifies that the voltage programming and readback functions are within specifications.


**Step Action** **Normal Result**


1 Turn off the UUT and connect the DMM across the output as shown above.



2 Turn on the UUT. Program output to:


RANGE 155 V, VOLT 155, FREQ 60



Voltage at 155 Vrms


CV annunciator on


Output current near zero



3 Record voltage readings at DMM and on front panel display. Reading within specified low range limits



4 Turn on the UUT. Program output to:


RANGE 310 V, VOLT 310, FREQ 60



Voltage at 310 Vrms


CV annunciator on


Output current near zero



5 Record voltage readings at DMM and on front panel display. Reading within specified high range limits


6 Program RANGE155 V, VOLT 155, FREQ 500 Voltage at 155 Vrms


CV annunciator on


Output current near zero


7 Record voltage readings at DMM and on front panel display. Reading within specified low range limits


8 Program RANGE 310 V, VOLT 310, FREQ 500 Voltage at 310 Vrms


CV annunciator on


Output current near zero


9 Record voltage readings at DMM and on front panel display. Reading within specified high range limits


Keysight AC6800B Series Operating and Service Guide 183


4 Calibration, Verification, and Service


**Voltage Load Regulation**


This test measures the change in output voltage resulting from a change in output current from full
load to no-load or no-load to full-load.


**Step Action** **Normal Result**


1 Turn off the UUT and connect DMM and appropriate

load resistors as shown above.


2 Open S1.



3 Turn on the UUT. Program output to RANGE 155 V,
VOLT 80, FREQ 60


4 Record voltage reading of DMM.


5 Disable UUT. Close S1.


6 Turn on the UUT. Program output to RANGE 155 V,
VOLT 80, FREQ 60


7 Record voltage reading of DMM.



Voltage at 80 Vrms, output current near zero


Voltage at 80 Vrms, CV annunciator on, output current near:


5 A for AC6801B

10 A for AC6802B

20 A for AC6803B

40 A for AC6804B



8 Check test results The difference between the DMM readings in Steps 4 & 7 are
within specified load effect limits.


9 Disable the UUT. Open S1.



10 Turn on the UUT. Program output to RANGE 155 V,
VOLT 80, FREQ 500.


11 Record voltage reading of DMM.


12 Disable UUT. Close S1.



Voltage at 80 Vrms, output current near zero



13 Turn on the UUT. Voltage at 80 Vrms, CV annunciator on, output current near:


5 A for AC6801B

10 A for AC6802B

20 A for AC6803B

40 A for AC6804B


14 Record voltage reading of DMM.


15 Check test results The difference between the DMM readings in Steps 11 & 14 are
within specified load effect limits.


16 Disable UUT. Open S1.



17 Turn on the UUT. Program output to RANGE 310 V,
VOLT 160, FREQ 60


18 Record voltage reading of DMM.



Voltage at 160 Vrms


Output current near zero



19 Close S1. Turn on the UUT. Voltage at 160 Vrms, CV annunciator on, output current near:


2.5 A for AC6801B

5 A for AC6802B

10 A for AC6803B

20 A for AC6804B


184 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Step Action** **Normal Result**


20 Record voltage reading of DMM.


21 Check test results The difference between the DMM readings in Steps 18 and 20 are
within specified load effect limits.


22 Disable UUT. Open S1.



23 Turn on the UUT. Program output to RANGE 310 V,
VOLT 160, FREQ 500


24 Record voltage reading of DMM.



Voltage at 160 V rms, Output current near zero



25 Close S1. Turn on the UUT. Voltage at 160 V rms, CV annunciator on, output current near:


2.5 A for AC6801B

5 A for AC6802B

10 A for AC6803B

20 A for AC6804B


26 Record voltage reading of DMM.


27 Check test results The difference between the DMM readings in Steps 24 and 26 are
within specified load effect limits.


Keysight AC6800B Series Operating and Service Guide 185


4 Calibration, Verification, and Service


**RMS Current Measurement Accuracy**


This test verifies the measurement accuracy of the rms current readback.


**Step Action** **Normal Result**


1 Turn off the UUT and connect the appropriate load resistor, current

shunt and DMM as shown above.



2 Turn on the UUT. Program the output to RANGE 155 V,VOLT 80, FREQ

60.



Output Voltage at 80 Vrms, CV annunciator on,

output current near:


5 A for AC6801B

10 A for AC6802B

20 A for AC6803B

40 A for AC6804B



3 Record DMM reading and calculate rms current Readings are within specified current limits.


4 Program output to RANGE 155 V,VOLT 80, FREQ 500. Output Voltage at 80 Vrms, CV annunciator on,

output current near:


5 A for AC6801B

10 A for AC6802B

20 A for AC6803B

40 A for AC6804B


5 Record DMM reading and calculate rms current. Readings are within specified current limits.



6 Turn on the UUT. Program the output to RANGE 310 V,VOLT 160, FREQ

60.



Output Voltage at 160 Vrms, CV annunciator on,

output current near:


2.5 A for AC6801B

5 A for AC6802B

10 A for AC6803B

20 A for AC6804B



7 Record DMM reading and calculate rms current Readings are within specified current limits.



8 Turn on the UUT. Program the output to RANGE 310 V,VOLT 160, FREQ

500.



Output Voltage at 160 Vrms, CV annunciator on,

output current near:


2.5 A for AC6801B

5 A for AC6802B

10 A for AC6803B

20 A for AC6804B



9 Record DMM reading and calculate rms current. Readings are within specified current limits.


186 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Frequency Accuracy**


This test verifies the frequency programming and measurement accuracy of the output voltage

waveform.


**Step Action** **Normal Result**


1 Turn off the UUT. Connect the Frequency Counter to the output as shown

above.


2 Turn on the UUT. Program output to RANGE 155 V,VOLT 20, FREQ 500 Output at 80 Vrms, CV annunciator on, Current

near 0


3 Record the output frequency reading from counter Readings are within specified limits


**DC Voltage Programming and Readback Accuracy**


This test verifies the DC voltage programming and front panel readback functions are within
specifications.


**Step Action** **Normal Result**


1 Turn off the UUT. Connect the DMM directly to the output terminals. The DMM
negative lead is connected to the Neutral output terminal.



2 Turn on UUT. Program output to RANGE 155 V, VOLT 0, OUTP:COUP DC,

OFFSET 219



Output at 219 VDC, output current near 0



3 Record DC voltage at DMM and readback from front panel display. Readings within specified DC voltage programming and readback limits.


4 Program output to RANGE 310 V, VOLT 0, OUTP:COUP DC, OFFSET 438 Output at 438 VDC, output current near 0


5 Record DC voltage at DMM and readback from front panel display. Readings within specified DC voltage programming and readback limits.


**DC Current Measurement Accuracy**


This test verifies that the DC voltage programming and front-panel readback functions are within
specifications.


**Step Action** **Normal Result**


1 Turn off the UUT and connect the appropriate load resistor, DC current shunt
and DMM as shown in the diagram above.



2 Turn on UUT. Program output voltage to RANGE 155 V, VOLT 0, OUTP:COUP
DC, OFFSET 64.



Output voltage at 64 VDC, CV annunciator

on, output current near:

4 A for AC6801B

8 A for AC6802B

16 A for AC6803B

32 A for AC6804B



3 Record DVM reading and calculate current. Readings within specified DC current read
back limits.


Keysight AC6800B Series Operating and Service Guide 187


4 Calibration, Verification, and Service


**Step Action** **Normal Result**


4 Program output to RANGE 310, VOLT 0, OUTP:COUP DC, OFFSET 128. Output voltage at 128 VDC, CV annunciator

on, output current near:

2 A for AC6801B

4 A for AC6802B

8 A for AC6803B

16 A for AC6804B


5 Record DVM reading and calculate current. Readings within specified dc current read
back limits.


188 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service

##### **Performance Test Records**


**Performance Test Record - AC6801B**

|Test Description|Minimum Spec.|Results|Maximum Spec.|
|---|---|---|---|
|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|
|155 Vrms @ 60Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 1.125 V|______ V|Vrms + 1.125 V|
|155 Vrms @ 500Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 2.085 V|______ V|Vrms + 2.085 V|
|310 Vrms @ 60Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 2.25 V|______ V|Vrms + 2.25 V|
|310 Vrms @ 500Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 4.17 V|______ V|Vrms + 4.17 V|
|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|
|CV Load Effect Low Range @ 60 Hz|Vout - 0.15 V|______ V|Vout + 0.15 V|
|CV Load Effect Low Range @ 500 Hz|Vout - 0.50 V|______ V|Vout + 0.50 V|
|CV Load Effect High Range @ 60 Hz|Vout - 0.30 V|______ V|Vout + 0.30 V|
|CV Load Effect High Range @ 500 Hz|Vout - 1.00 V|______ V|Vout + 1.00 V|
|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|
|Low Range Measurement @ 60 Hz|Iout – 45 mA|______ A|Iout + 45 mA|
|Low Range Measurement @ 500 Hz|Iout – 75 mA|______ A|Iout + 75 mA|
|High Range Measurement @ 60 Hz|Iout – 22.5 mA|______ A|Iout + 22.5 mA|
|High Range Measurement @ 500 Hz|Iout – 37.5 mA|______ A|Iout + 37.5 mA|
|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|
|Program 500 Hz|499.9 Hz|______ Hz|500.1 Hz|
|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|
|+219 VDC Output|218.343 VDC|______ VDC|219.657|
|Front Panel Measurement|Vout - 1.445 VDC|______ VDC|Vout + 1.445 VDC|
|+438 VDC Output|436.905 VDC|______ VDC|439.095|
|Front Panel Measurement|Vout - 2.89 VDC|______ VDC|Vout + 2.89 VDC|
|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|
|Low Range Measurement|Iout – 40 mA|______ A|Iout + 40 mA|
|High Range Measurement|Iout – 20 mA|______ A|Iout + 20 mA|



Keysight AC6800B Series Operating and Service Guide 189


4 Calibration, Verification, and Service


**Performance Test Record - AC6802B**

|Test Description|Minimum Spec.|Results|Maximum Spec.|
|---|---|---|---|
|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|
|155 Vrms @ 60Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 1.125 V|______ V|Vrms + 1.125 V|
|155 Vrms @ 500Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 2.085 V|______ V|Vrms + 2.085 V|
|310 Vrms @ 60Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 2.25 V|______ V|Vrms + 2.25 V|
|310 Vrms @ 500Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 4.17 V|______ V|Vrms + 4.17 V|
|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|
|CV Load Effect Low Range @ 60 Hz|Vout - 0.15 V|______ V|Vout + 0.15 V|
|CV Load Effect Low Range @ 500 Hz|Vout - 0.50 V|______ V|Vout + 0.50 V|
|CV Load Effect High Range @ 60 Hz|Vout - 0.30 V|______ V|Vout + 0.30 V|
|CV Load Effect High Range @ 500 Hz|Vout - 1.00 V|______ V|Vout + 1.00 V|
|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|
|Low Range Measurement @ 60 Hz|Iout – 90 mA|______ A|Iout + 90 mA|
|Low Range Measurement @ 500 Hz|Iout – 150 mA|______ A|Iout + 150 mA|
|High Range Measurement @ 60 Hz|Iout – 45 mA|______ A|Iout + 45 mA|
|High Range Measurement @ 500 Hz|Iout – 75 mA|______ A|Iout + 75 mA|
|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|
|Program 500 Hz|499.9 Hz|______ Hz|500.1 Hz|
|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|
|+219 VDC Output|218.343 VDC|______ VDC|219.657|
|Front Panel Measurement|Vout - 1.445 VDC|______ VDC|Vout + 1.445 VDC|
|+438 VDC Output|436.905 VDC|______ VDC|439.095|
|Front Panel Measurement|Vout - 2.89 VDC|______ VDC|Vout + 2.89 VDC|
|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|
|Low Range Measurement|Iout – 80 mA|______ A|Iout + 80 mA|
|High Range Measurement|Iout – 40 mA|______ A|Iout + 40 mA|



190 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Performance Test Record - AC6803B**

|Test Description|Minimum Spec.|Results|Maximum Spec.|
|---|---|---|---|
|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|
|155 Vrms @ 60Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 1.125 V|______ V|Vrms + 1.125 V|
|155 Vrms @ 500Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 2.085 V|______ V|Vrms + 2.085 V|
|310 Vrms @ 60Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 2.25 V|______ V|Vrms + 2.25 V|
|310 Vrms @ 500Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 4.17 V|______ V|Vrms + 4.17 V|
|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|
|CV Load Effect Low Range @ 60 Hz|Vout - 0.15 V|______ V|Vout + 0.15 V|
|CV Load Effect Low Range @ 500 Hz|Vout - 0.50 V|______ V|Vout + 0.50 V|
|CV Load Effect High Range @ 60 Hz|Vout - 0.30 V|______ V|Vout + 0.30 V|
|CV Load Effect High Range @ 500 Hz|Vout - 1.00 V|______ V|Vout + 1.00 V|
|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|
|Low Range Measurement @ 60 Hz|Iout – 180 mA|______ A|Iout + 180 mA|
|Low Range Measurement @ 500 Hz|Iout – 300 mA|______ A|Iout + 300 mA|
|High Range Measurement @ 60 Hz|Iout – 90 mA|______ A|Iout + 90 mA|
|High Range Measurement @ 500 Hz|Iout – 150 mA|______ A|Iout + 150 mA|
|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|
|Program 500 Hz|499.9 Hz|______ Hz|500.1 Hz|
|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|
|+219 VDC Output|218.343 VDC|______ VDC|219.657|
|Front Panel Measurement|Vout - 1.445 VDC|______ VDC|Vout + 1.445 VDC|
|+438 VDC Output|436.905 VDC|______ VDC|439.095|
|Front Panel Measurement|Vout - 2.89 VDC|______ VDC|Vout + 2.89 VDC|
|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|
|Low Range Measurement|Iout – 160 mA|______ A|Iout + 160 mA|
|High Range Measurement|Iout – 80 mA|______ A|Iout + 80 mA|



Keysight AC6800B Series Operating and Service Guide 191


4 Calibration, Verification, and Service


**Performance Test Record - AC6804B**

|Test Description|Minimum Spec.|Results|Maximum Spec.|
|---|---|---|---|
|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|**AC Voltage Programming and Readback Accuracy**|
|155 Vrms @ 60Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 1.125 V|______ V|Vrms + 1.125 V|
|155 Vrms @ 500Hz|154.4575|______ V|155.5425|
|Front Panel Measurement|Vrms - 2.085 V|______ V|Vrms + 2.085 V|
|310 Vrms @ 60Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 2.25 V|______ V|Vrms + 2.25 V|
|310 Vrms @ 500Hz|308.915|______ V|311.085|
|Front Panel Measurement|Vrms - 4.17 V|______ V|Vrms + 4.17 V|
|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|**Voltage Load Regulation**|
|CV Load Effect Low Range @ 60 Hz|Vout - 0.15 V|______ V|Vout + 0.15 V|
|CV Load Effect Low Range @ 500 Hz|Vout - 0.50 V|______ V|Vout + 0.50 V|
|CV Load Effect High Range @ 60 Hz|Vout - 0.30 V|______ V|Vout + 0.30 V|
|CV Load Effect High Range @ 500 Hz|Vout - 1.00 V|______ V|Vout + 1.00 V|
|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|**RMS Current Measurement Accuracy**|
|Low Range Measurement @ 60 Hz|Iout – 360 mA|______ A|Iout + 360 mA|
|Low Range Measurement @ 500 Hz|Iout – 600 mA|______ A|Iout + 600 mA|
|High Range Measurement @ 60 Hz|Iout – 180 mA|______ A|Iout + 180 mA|
|High Range Measurement @ 500 Hz|Iout – 300 mA|______ A|Iout + 300 mA|
|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|**Frequency Accuracy**|
|Program 500 Hz|499.9 Hz|______ Hz|500.1 Hz|
|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|**DC Voltage Programming and Readback Accuracy**|
|+219 VDC Output|218.343 VDC|______ VDC|219.657|
|Front Panel Measurement|Vout - 1.445 VDC|______ VDC|Vout + 1.445 VDC|
|+438 VDC Output|436.905 VDC|______ VDC|439.095|
|Front Panel Measurement|Vout - 2.89 VDC|______ VDC|Vout + 2.89 VDC|
|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|**DC Current Readback Accuracy**|
|Low Range Measurement|Iout – 320 mA|______ A|Iout + 320 mA|
|High Range Measurement|Iout – 160 mA|______ A|Iout + 160 mA|



192 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service

##### **Service and Maintenance**


Types of Service Available


Self-test


Before Returning the Unit


Repackaging for Shipment


User-replaceable parts


Installing an Optional Interface Board


Updating the Firmware


Log into and out of the System Administration Menu


Change the System Administration Password


Sanitize the Instrument per the NISPOM Standard


Cleaning the Outside of the Instrument


Cleaning and Replacing the Filter


**Types of Service Available**


If your instrument fails during the warranty period, Keysight Technologies will repair or replace it under
the terms of your warranty. After your warranty expires, Keysight offers repair services at competitive
prices. You may also consider purchasing a service contract that extends coverage after the standard

warranty.


To obtain service for your instrument, contact your nearest Keysight Technologies Service Center.
They will arrange to have your unit repaired or replaced, and can provide warranty or repair–cost
information where applicable. Ask the Keysight Technologies Service Center for shipping instructions,
including what components to ship. Keysight recommends that you retain the original shipping carton
for return shipments.


**Self-test**


The instrument performs an automatic self-test at power-on. This test verifies the basic functionality
of the instrument's logic and power mesh subsystems without placing any voltages on the output.


The *TST? query returns the result of that test, but it does not run another self-test. If self-test fails,
turn the instrument off, remove all connections (front and rear), and turn the instrument back on. The

purpose of this is to eliminate signals present on external wiring that can act as antennae.


See SCPI Error Messages for a list of error messages.


Keysight AC6800B Series Operating and Service Guide 193


4 Calibration, Verification, and Service


**Before Returning the Unit**


If the unit is inoperative, verify that the AC power is securely connected to the instrument, the mains
outlet is live, and the power switch is on. Before returning the unit, cycle power on the unit to run the
self-test as described above. Press [Error] to view errors in the error queue. Then report those errors
to Keysight Support and be sure that the instrument was calibrated within the last year.


**Repackaging for Shipment**


To ship the unit to Keysight for service or repair:


Attach a tag to the unit identifying the owner, model number, serial number, and required service.


Place the unit in its original container with appropriate packaging material.


Secure the container with strong tape or metal bands.


If the original shipping container is unavailable, use a container that will ensure at least 10 cm (4 in.) of
compressible packaging material around the entire instrument. Use static-free packaging materials.


Keysight suggests that you always insure shipments.


**User-replaceable parts**


5188-9178 Ferrite core All


AC6802-800003 AC input cover AC6802B


5003-2136 AC input cover AC6803B


5003-2137 AC input cover AC6804B


5066-1893 Dust filter AC6801B, AC6802B, AC6803B


5066-1894 Dust filter AC6804B


AC6801-800003 Front grille AC6801B, AC6802B, AC6803B


AC6804-800003 Front grille AC6804B


5067-6052 Rubber foot kit (qty 4) AC6801B, AC6802B, AC6803B


5067-6053 Rubber foot kit (qty 4) AC6804B


AC6801-800004 Output cover AC6801B, AC6802B, AC6803B


AC6804-800004 Output cover AC6804B


AC6800-800001 Sense kit (cover and plug) All


5067-6057 Option slot cover All


194 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Updating the Firmware**


1. Using the Web browser on your PC, go to [www.keysight.com/find/ac6801b](http://www.keysight.com/find/ac6801b) (replace ac6801b with

your ac source model).


2. Under the "Support" tab, select "Drivers, Firmware & Software".


3. Select "AC6800 Basic AC Source Series Firmware" and download the current firmware (DFU file) from


this page to a folder on your PC.


4. If you have already installed the STMicroelectronics software to your PC, proceed to step 8. Other
wise, install the STMicroelectronics software to your PC. This software is required to update the firmware. Go to: [http://www2.st.com/content/st_com/en/products/development-tools/software-](http://www2.st.com/content/st_com/en/products/development-tools/software-development-tools/stm32-software-development-tools/stm32-programmers/stsw-stm32080.html)
[development-tools/stm32-software-development-tools/stm32-programmers/stsw-](http://www2.st.com/content/st_com/en/products/development-tools/software-development-tools/stm32-software-development-tools/stm32-programmers/stsw-stm32080.html)
[stm32080.html](http://www2.st.com/content/st_com/en/products/development-tools/software-development-tools/stm32-software-development-tools/stm32-programmers/stsw-stm32080.html) . Select “Get Software” on the bottom of the page.


5. Accept the license agreement and provide your name and email. You will now receive an email from

STMicroelectronics.c. Select “Download now” in the email. Scroll to the bottom of the page and
select “Get Software”. You will now be able to download and then unzip “en.stsw-stm32080.zip to the
folder you created in step 3. After unzipping, the following files should be installed:


Keysight AC6800B Series Operating and Service Guide 195


4 Calibration, Verification, and Service


6. Run the “DfuSe_Demo_V3.0.6_Setup.exe” installation package.


7. After the STMicroelectronics software has installed, you must also install the USB driver.

**If you have a 64-bit operating system**, go to C:\Program Files (x86)\STMicroelectronics\Software\DfuSe v3.0.5\Bin\Driver\Win7\x64. Then run dpinst_amd64.exe
**If you have a 32-bit operating system**, follow the same path except go to the x86 folder and run
dpinst_x86.exe


8. Turn off the ac source and remove the two screws that hold the optional interface board or slot cover

in place on the instrument's rear panel. Set the interface board or slot cover aside.


9. Push the switch at the back, bottom of the slot into the down position.


196 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


10. On your PC desktop, find and open the DfuSeDemo application. When the screen shown below

appears, click **Choose...** in the Upgrade or Verify Action section of the screen.


11. Browse to the folder you created in step 3. Select the DFU file, and click **Open** .


Keysight AC6800B Series Operating and Service Guide 197


4 Calibration, Verification, and Service


12. Verify that the message at the bottom of the screen indicates that the file loaded correctly.


13. Connect a USB cable from the square "USB device" port on the instrument's rear panel to a USB port

on your PC. This update must be done via USB, not over LAN or the optional GPIB.


14. Turn on the instrument and wait for the Available DFU Devices field at the top of the screen to indicate

that the instrument has been recognized. The selected target should be "00 Internal Flash". Check
"Verify after download", and click **Upgrade** .


198 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


15. Select Yes in the popup that appears and wait for the upgrade to complete.


16. When the message at the bottom of the screen shows “Verify Successful !” the download is complete.


17. Exit the DfuSe Demonstration software, turn off the instrument, disconnect the USB cable, return the

option slot switch to the up position, and replace the optional card or slot cover, screwing it securely
in place. The firmware update is complete.


18. When you first turn on the instrument after the firmware installation, the front panel will display an

"Err" annunciator . This is normal. Press the front panel **Error** key and you will see "Error -315, Configuration memory is lost". This is normal. Press the **Meter** key to clear the error.


Keysight AC6800B Series Operating and Service Guide 199


4 Calibration, Verification, and Service


**Log into and out of the System Administration Menu**


To access any of the functions under the system administration menu, you must log in with a password
(the default password is blank). These functions include:


Calibrating the instrument


Configuring instrument I/O


Sanitizing the instrument per the NISPOM standard


Changing the system administration password


To log in, select [Menu] > System > Admin > Login .


The password must be 4 to 15 characters long and may include any of these characters:


A-Z Upper case letters


a-z Lower case letters


0-9 Numeric digits


+ - ( ) ., <space> Plus, minus, parentheses, period, comma, space


Always remember to log out after completing your administrative tasks if password protection has
been enabled. Press [Menu] > System > Admin > Logout > Logout .


**Change the System Administration Password**


Select [Menu] > System > Admin > Password to change the instrument's system administration
password.


The rules for a valid password are described above.


200 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


**Sanitize the Instrument per the NISPOM Standard**


Select [Menu] > System > Admin > Sanitize to sanitize the user data in the instrument per the National
Industrial Security Program Operating Manual (NISPOM). Factory data (instrument firmware, model
number, serial number, MAC address and calibration data) is not erased. After the data is cleared, the

instrument is rebooted. This procedure is typically performed only when you need to remove an
instrument from a secure area. The sanitization procedure is not recommended for use in routine
applications because of the possibility of unintended loss of data.


**Cleaning the Outside of the Instrument**


SHOCK HAZARD To prevent electric shock, unplug the unit before cleaning.


Clean the outside of the instrument with a soft, lint-free cloth, slightly damp with water. Do not use
detergent. Do not disassemble the instrument for cleaning and do not clean the rear panel in order to
avoid getting moisture near the connections. Ensure that the instrument is completely dry before
turning it on.


Keysight AC6800B Series Operating and Service Guide 201


4 Calibration, Verification, and Service


**Cleaning and Replacing the Filter**


The following procedure illustrates the AC6804B instrument, but the same procedure applies to all

the AC6800B units.


Step 1. With your thumbs, press on the two dimples located on the front panel grill.


Step 2. While pressing on the dimples, use your forefingers to press on the grips and slide the grill

down.


Step 3. Detach the front grill.


Step 4. Remove the filter and clean it by rinsing it in water. Ensure that the filter is completely dry
before reinstalling it.


Step 5. Align the plastic fingers on the back of the grille with the slots on the sheet metal.


Step 6. With your thumbs, press the two dimples located on the front panel grill.


Step 7. While pressing on the dimples, use your forefingers to press on the grips and slide the grill up.


202 Keysight AC6800B Series Operating and Service Guide


4 Calibration, Verification, and Service


Keysight AC6800B Series Operating and Service Guide 203


License Files

#### **License Files**


/*

- Sonic 0.2

- -
- https://github.com/padolsey/Sonic

- -
- This program is free software. It comes without any warranty, to

- the extent permitted by applicable law. You can redistribute it

- and/or modify it under the terms of the Do What The ---- You Want

- To Public License, Version 2, as published by Sam Hocevar. See

[* http://sam.zoy.org/wtfpl/COPYING for more details. */](http://sam.zoy.org/wtfpl/COPYING)


204 Keysight AC6800B Series Operating and Service Guide


