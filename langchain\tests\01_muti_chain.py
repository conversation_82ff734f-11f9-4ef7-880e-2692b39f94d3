from operator import itemgetter
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate

# 使用通用模型而非推理模型
llm = ChatOllama(model="qwen2.5:7b")

# 创建提示模板1
prompt1 = ChatPromptTemplate.from_messages([
    HumanMessagePromptTemplate.from_template(
        "请回答{person}关联的主要城市（仅返回城市名，不要额外解释）。"
        "如果无法确定，直接返回“未知城市”。"
    )
])

# 创建提示模板2
prompt2 = ChatPromptTemplate.from_messages([
    HumanMessagePromptTemplate.from_template("{city}在哪个国家？以{language}响应。")
])

# 构建链1
chain1 = prompt1 | llm | StrOutputParser()

# 构建链2
chain2 = (
    {"city": chain1, "language": itemgetter("language")}
    | prompt2
    | llm
    | StrOutputParser()
)

# print(chain2.invoke({"person": "杨洋", "language": "Chinese"}))

# 流式输出：使用 stream 方法替代 invoke，遍历每个片段并打印
for chunk in chain2.stream({"person": "邓紫棋", "language": "Chinese"}):
    print(chunk, end="", flush=True)  # end="" 避免换行，flush=True 强制实时输出