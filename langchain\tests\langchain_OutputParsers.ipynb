{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "输出解析器的使用", "id": "f81c713b7f2a76b8"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T02:02:51.407707Z", "start_time": "2025-07-14T02:02:48.715099Z"}}, "cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "\n", "prompt = PromptTemplate(\n", "    template=\"使用中文列举五个不同的{subject}。\",\n", "    input_variables=[\"subject\"],\n", ")\n", "\n", "# 直接将llm的输出作为最终结果\n", "chain = prompt | llm\n", "\n", "# 调用链并传入subject变量\n", "output = chain.invoke({\"subject\": \"汽车品牌\"})\n", "\n", "print(output)"], "id": "d0e6bce722d25840", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当然可以，以下是五个不同品牌的汽车：\n", "\n", "1. **宝马（BMW）** - 德国豪华汽车品牌。\n", "2. **丰田（Toyota）** - 日本知名汽车制造商，以生产可靠耐用的车型著称。\n", "3. **特斯拉（Tesla）** - 美国电动车及能源公司，专注于电动汽车的研发和制造。\n", "4. **红旗（Hongqi）** - 中国一汽集团旗下的高端汽车品牌。\n", "5. **起亚（<PERSON><PERSON>）** - 韩国现代汽车集团下的子品牌，提供多种经济型到中高端车型。\n"]}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T02:03:21.902609Z", "start_time": "2025-07-14T02:03:21.459316Z"}}, "cell_type": "code", "source": ["from langchain_core.output_parsers import CommaSeparatedListOutputParser\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "# LLMChain 是传统的链式构建方法，在新版本LangChain中，\n", "# 更推荐使用 LangChain Expression Language (LCEL) 的链式操作符 `|` 来构建链。\n", "# from langchain.chains import LLMChain\n", "\n", "llm = OllamaLLM(model=\"qwen2.5:7b\")\n", "output_parser = CommaSeparatedListOutputParser()\n", "\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "prompt = PromptTemplate(\n", "    template=\"使用中文列举五个不同的{subject}。\\n{format_instructions}\",\n", "    input_variables=[\"subject\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")\n", "\n", "# 完善链的构建：将llm的输出传递给output_parser进行解析\n", "chain = prompt | llm | output_parser\n", "\n", "# 调用链并传入subject变量\n", "output = chain.invoke({\"subject\": \"汽车品牌\"})\n", "\n", "print(output)"], "id": "9f5b5f63cab1185b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['奔驰', '宝马', '大众', '丰田', '沃尔沃']\n"]}], "execution_count": 18}, {"metadata": {}, "cell_type": "markdown", "source": ["使用 `CommaSeparatedListOutputParser` 的好处：\n", "\n", "- 输出格式标准化，便于后续程序处理\n", "- 自动清理多余的标点和格式字符\n", "- 确保结果是一个列表类型，方便进行索引或迭代操作"], "id": "c5e80f616f74c578"}, {"metadata": {}, "cell_type": "markdown", "source": "$Pydantic \\ \\ JSON$ 输出解释器", "id": "4e039510103cebfc"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T02:05:59.724260Z", "start_time": "2025-07-14T02:05:59.067776Z"}}, "cell_type": "code", "source": ["from langchain_core.prompts import (\n", "    PromptTemplate,\n", ")\n", "from langchain_ollama import OllamaLLM\n", "from langchain_core.output_parsers import PydanticOutputParser\n", "from pydantic import BaseModel, Field, ValidationError, field_validator # 导入 field_validator\n", "from typing import List\n", "import json\n", "\n", "llm = OllamaLLM(model=\"qwen2.5:7b\",\n", "                temperature=0.5)\n", "\n", "class Joke(BaseModel):\n", "    \"\"\"Joke model.\"\"\"\n", "\n", "    setup: str = Field(description=\"question to set up a joke\")\n", "    punchline: str = Field(description=\"answer to resolve the joke\")\n", "\n", "    # 修正 Pydantic V2 兼容性问题：使用 @field_validator\n", "    # @field_validator 装饰的方法需要是 @classmethod\n", "    @field_validator(\"setup\")\n", "    @classmethod\n", "    def question_ends_with_question_mark(cls, value):\n", "        # 修正中文问号 '？' 的判断\n", "        # 同时检查半角问号 '?' 和全角问号 '？'\n", "        if value[-1] not in [\"?\", \"？\"]:\n", "            raise ValueError(\"Badly formed question!\")\n", "        return value\n", "\n", "joke_query = \"\"\"\n", "请用中文讲一个笑话。\n", "\"\"\"\n", "\n", "parser = PydanticOutputParser(pydantic_object=Joke)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"Answer the query.\\n{format_instructions}\\n{query}\\n\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "chain = prompt | llm | parser\n", "\n", "try:\n", "    parsed_output = chain.invoke({\"query\": joke_query})\n", "    print(parsed_output)\n", "except ValidationError as e:\n", "    print(f\"Pydantic 验证失败：{e}\")\n", "    # 在验证失败时，为了帮助调试，我们重新调用 LLM 获取原始输出\n", "    raw_llm_output = (prompt | llm).invoke({\"query\": joke_query})\n", "    print(f\"LLM 原始输出（未解析）：\\n{raw_llm_output}\")\n", "    # 尝试打印原始输出，以便查看 LLM 实际生成了什么\n", "    # 如果原始输出是 JSON 字符串，可以尝试美化打印\n", "    try:\n", "        print(\"尝试解析原始输出为 JSON (如果适用):\")\n", "        print(json.dumps(json.loads(raw_llm_output), indent=2, ensure_ascii=False))\n", "    except json.JSONDecodeError:\n", "        print(\"LLM 原始输出不是有效的 JSON 格式。\")\n", "except Exception as e:\n", "    print(f\"发生未知错误：{e}\")"], "id": "357a9804c1d05a3b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["setup='为什么电脑经常生病？' punchline='因为它窗户（Windows）总是开着！'\n"]}], "execution_count": 32}, {"metadata": {}, "cell_type": "markdown", "source": "结构化输出解释器", "id": "166b532935eeb23e"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-14T02:19:05.459903Z", "start_time": "2025-07-14T02:19:03.592258Z"}}, "cell_type": "code", "source": ["from langchain.output_parsers import StructuredOutputParser, ResponseSchema\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import OllamaLLM\n", "from langchain_core.exceptions import OutputParserException\n", "from langchain_core.runnables import RunnableLambda # 导入 RunnableLambda 用于在链中插入自定义函数\n", "import re # 导入正则表达式模块\n", "\n", "llm = OllamaLLM(model=\"glm4:9b\") # 使用你指定的新模型\n", "\n", "# 1. 定义输出结构 (ResponseSchema)\n", "response_schemas = [\n", "    ResponseSchema(name=\"city_name\", description=\"The name of the city.\"),\n", "    ResponseSchema(name=\"country\", description=\"The country where the city is located.\"),\n", "    ResponseSchema(name=\"population\", description=\"The approximate population of the city in millions.\", type=\"float\")\n", "]\n", "\n", "# 2. 实例化 StructuredOutputParser\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "# 3. 定义 PromptTemplate\n", "prompt = PromptTemplate(\n", "    template=\"\"\"请提供以下城市的信息，并严格按照指定格式输出。\n", "城市: {city}\n", "\n", "{format_instructions}\n", "注意：你的输出必须是纯粹的 JSON 对象，不要包含任何注释、markdown代码块界定符（如```json）或任何额外文本。\n", "\"\"\", # 增加了更严格的提示词，要求纯 JSON\n", "    input_variables=[\"city\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", ")\n", "\n", "# 新增：一个用于清理 LLM 输出的函数\n", "def clean_llm_output(text: str) -> str:\n", "    \"\"\"\n", "    清理 LLM 生成的文本，移除 markdown 代码块界定符和 JSON 注释。\n", "    \"\"\"\n", "    # 尝试匹配并提取 ```json ... ``` 块中的内容\n", "    match = re.search(r\"```json\\s*(.*?)\\s*```\", text, re.DOTALL)\n", "    if match:\n", "        text = match.group(1) # 提取括号内的内容\n", "\n", "    # 移除 JSON 中的行尾注释（// style comments）\n", "    # 这会移除所有行中第一个 // 之后的内容\n", "    cleaned_lines = []\n", "    for line in text.split('\\n'):\n", "        if '//' in line:\n", "            # 只保留 // 之前的部分\n", "            line = line.split('//')[0].strip()\n", "        cleaned_lines.append(line)\n", "\n", "    # 重新组合行，并移除任何可能多余的空白行\n", "    cleaned_text = '\\n'.join(line for line in cleaned_lines if line.strip())\n", "\n", "    return cleaned_text.strip() # 确保最终没有多余的空白\n", "\n", "# 4. 构建 LCEL 链：在 LLM 和 Parser 之间插入清理函数\n", "chain = prompt | llm | RunnableLambda(clean_llm_output) | output_parser\n", "\n", "# 5. 调用链并处理输出 (保持不变)\n", "city_query = \"东京\"\n", "print(f\"正在查询城市信息：{city_query}\")\n", "\n", "try:\n", "    parsed_output = chain.invoke({\"city\": city_query})\n", "    print(\"\\n--- 解析结果 ---\")\n", "    print(f\"城市名称: {parsed_output['city_name']}\")\n", "    print(f\"国家: {parsed_output['country']}\")\n", "    print(f\"人口: {parsed_output['population']} 百万\")\n", "    print(\"\\n--- 原始解析器对象 ---\")\n", "    print(parsed_output)\n", "\n", "except OutputParserException as e:\n", "    print(f\"\\n错误：输出解析失败。可能LLM没有按照指定格式输出。\\n错误详情：{e}\")\n", "    raw_llm_output = (prompt | llm).invoke({\"city\": city_query})\n", "    print(f\"\\nLLM 原始输出（未解析）：\\n{raw_llm_output}\")\n", "except Exception as e:\n", "    print(f\"\\n发生未知错误：{e}\")\n", "\n", "print(\"\\n----------------------\\n\")\n", "\n", "city_query_2 = \"上海\"\n", "print(f\"正在查询城市信息：{city_query_2}\")\n", "try:\n", "    parsed_output_2 = chain.invoke({\"city\": city_query_2})\n", "    print(\"\\n--- 解析结果 ---\")\n", "    print(f\"城市名称: {parsed_output_2['city_name']}\")\n", "    print(f\"国家: {parsed_output_2['country']}\")\n", "    print(f\"人口: {parsed_output_2['population']} 百万\")\n", "    print(\"\\n--- 原始解析器对象 ---\")\n", "    print(parsed_output_2)\n", "except OutputParserException as e:\n", "    print(f\"\\n错误：输出解析失败。可能LLM没有按照指定格式输出。\\n错误详情：{e}\")\n", "    raw_llm_output = (prompt | llm).invoke({\"city\": city_query_2})\n", "    print(f\"\\nLLM 原始输出（未解析）：\\n{raw_llm_output}\")\n", "except Exception as e:\n", "    print(f\"\\n发生未知错误：{e}\")"], "id": "dbec0bd63a99d8bf", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在查询城市信息：东京\n", "\n", "--- 解析结果 ---\n", "城市名称: 东京\n", "国家: 日本\n", "人口: 13.9 百万\n", "\n", "--- 原始解析器对象 ---\n", "{'city_name': '东京', 'country': '日本', 'population': 13.9}\n", "\n", "----------------------\n", "\n", "正在查询城市信息：上海\n", "\n", "--- 解析结果 ---\n", "城市名称: 上海\n", "国家: 中国\n", "人口: 24.0 百万\n", "\n", "--- 原始解析器对象 ---\n", "{'city_name': '上海', 'country': '中国', 'population': 24.0}\n"]}], "execution_count": 34}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}