{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "## __使用提示词模板构建提示词__", "id": "71f45afd7872656f"}, {"metadata": {}, "cell_type": "markdown", "source": ["### __PromptTemplate包装器__\n", "\n", "`PromptTemplate` 是 LangChain 中最基础的提示词模板，用于根据预定义的模板和输入变量生成提示词字符串。"], "id": "95b11e3cd592bc61"}, {"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-07-09T07:24:04.406353Z", "start_time": "2025-07-09T07:24:04.401346Z"}}, "source": ["from langchain import PromptTemplate\n", "from langchain_chroma import Chroma\n", "from langchain_community.graphs.rdf_graph import prefixes\n", "from onnxruntime.transformers.models.stable_diffusion.benchmark import example_prompts\n", "from torch.utils.flop_counter import suffixes\n", "\n", "template = \"\"\"\n", "你是一位资深的数据科学家，擅长构建深度学习模型。\n", "用几行解释 {concept} 的概念。\n", "\"\"\"\n", "# 实例化模板的第一种方法\n", "prompt = PromptTemplate(template=template, input_variables=[\"concept\"])\n", "print(prompt.format(concept=\"机器学习\"))\n", "\n", "# 实例化模板的第二种方法\n", "prompt1 = PromptTemplate.from_template(template)\n", "# 将用户的输入通过format方法嵌入提示词模板，并且做格式化处理\n", "final_prompt = prompt1.format(concept=\"机器学习\")\n", "print(final_prompt)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "你是一位资深的数据科学家，擅长构建深度学习模型。\n", "用几行解释 机器学习 的概念。\n", "\n", "\n", "你是一位资深的数据科学家，擅长构建深度学习模型。\n", "用几行解释 机器学习 的概念。\n", "\n"]}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": ["### __ChatPromptTemplate包装器__\n", "\n", "`ChatPromptTemplate` 专为聊天模型设计，它允许你定义不同角色的消息（如系统消息、人类消息、AI消息），并将其组合成一个完整的聊天提示。"], "id": "fecf23737afc7320"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-09T07:31:27.460035Z", "start_time": "2025-07-09T07:31:27.454032Z"}}, "cell_type": "code", "source": ["from langchain.prompts import (\n", "    ChatPromptTemplate,\n", "    PromptTemplate,\n", "    SystemMessagePromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    AIMessagePromptTemplate\n", ")\n", "template = \"\"\"\n", "你是一位资深的数据科学家，擅长构建深度学习模型。\n", "\"\"\"\n", "system_message_prompt = SystemMessagePromptTemplate.from_template(template)\n", "human_template = \"用几行解释 {concept} 的概念。\"\n", "human_message_prompt = HumanMessagePromptTemplate.from_template(human_template)\n", "chat_prompt = ChatPromptTemplate.from_messages([system_message_prompt, human_message_prompt])\n", "print(chat_prompt, end=\"\\n\\n\")\n", "\n", "print(chat_prompt.format_prompt(concept=\"机器学习\").to_messages())"], "id": "d63ddf74fdb7df64", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input_variables=['concept'] input_types={} partial_variables={} messages=[SystemMessagePromptTemplate(prompt=PromptTemplate(input_variables=[], input_types={}, partial_variables={}, template='\\n你是一位资深的数据科学家，擅长构建深度学习模型。\\n'), additional_kwargs={}), HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['concept'], input_types={}, partial_variables={}, template='用几行解释 {concept} 的概念。'), additional_kwargs={})]\n", "\n", "[SystemMessage(content='\\n你是一位资深的数据科学家，擅长构建深度学习模型。\\n', additional_kwargs={}, response_metadata={}), HumanMessage(content='用几行解释 机器学习 的概念。', additional_kwargs={}, response_metadata={})]\n"]}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["## __使用少样提示词模板__\n", "\n", "少样本提示词（Few-Shot Prompting）是指在提示词中包含少量示例，以帮助语言模型理解任务的模式和期望的输出格式。"], "id": "ea649ca922b45117"}, {"metadata": {}, "cell_type": "markdown", "source": ["### __FewShotPromptTemplate类与PromptTemplate类__\n", "\n", "`FewShotPromptTemplate` 用于创建包含少量示例的提示词。它结合了一个 `PromptTemplate` 来格式化每个示例，并通过 `examples` 列表提供具体的示例数据。"], "id": "896b6f947d465a64"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T01:22:45.941430Z", "start_time": "2025-07-10T01:22:45.388679Z"}}, "cell_type": "code", "source": ["from langchain.prompts import (\n", "    FewShotPromptTemplate,\n", "    PromptTemplate,\n", "    FewShotChatMessagePromptTemplate\n", ")\n", "\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"input\", \"output\"],\n", "    template=\"词语: {input}\\n\"\n", "             \"反义词: {output}\\n\",\n", ")\n", "\n", "examples = [\n", "        {\"input\": \"兴高采烈\", \"output\": \"闷闷不乐\"},\n", "        {\"input\": \"无精打采\", \"output\": \"生龙活虎\"},\n", "        {\"input\": \"黑色\", \"output\": \"白色\"},\n", "        {\"input\": \"冷\", \"output\": \"热\"},\n", "        {\"input\": \"高兴\", \"output\": \"悲伤\"},\n", "]\n", "\n", "few_shot_prompt = FewShotPromptTemplate(\n", "    # 示例列表\n", "    examples=examples,\n", "    # 提示词模板\n", "    example_prompt=example_prompt,\n", "    example_separator=\"\\n\",\n", "    prefix=\"来玩反义词接龙游戏，我说词语，你给我一个反义词。\\n\",\n", "    suffix=\"词语: {input}\\n反义词: \",\n", "    # 提示词模板的输入变量\n", "    input_variables=[\"input\"],\n", ")\n", "\n", "example_prompt.format(**examples[0])"], "id": "e1327fcb87629aee", "outputs": [{"data": {"text/plain": ["'词语: 兴高采烈\\n反义词: 闷闷不乐\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T01:23:07.236778Z", "start_time": "2025-07-10T01:23:07.231945Z"}}, "cell_type": "code", "source": "few_shot_prompt.format(input=\"蓝色\")", "id": "bfd27b59a2a88341", "outputs": [{"data": {"text/plain": ["'来玩反义词接龙游戏，我说词语，你给我一个反义词。\\n\\n词语: 兴高采烈\\n反义词: 闷闷不乐\\n\\n词语: 无精打采\\n反义词: 生龙活虎\\n\\n词语: 黑色\\n反义词: 白色\\n\\n词语: 冷\\n反义词: 热\\n\\n词语: 高兴\\n反义词: 悲伤\\n\\n词语: 蓝色\\n反义词: '"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": "通过 LangChain 的 `ChatOllama` 模型可以调用 Ollama 服务，并将 `few_shot_prompt` 格式化后的提示词传递给模型以获取响应。", "id": "df925ec33e27b02b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T01:24:43.448901Z", "start_time": "2025-07-10T01:24:07.175108Z"}}, "cell_type": "code", "source": ["from langchain_ollama import ChatOllama\n", "\n", "chat = ChatOllama(model=\"gemma3:12b\")\n", "prompt = few_shot_prompt.format(input=\"寒冷\")\n", "response = chat.invoke(prompt)\n", "print(response.content)"], "id": "aca079fd913f033c", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["温暖\n", "\n"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": "少样本提示词模板", "id": "9189c4c615733268"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T01:25:20.719137Z", "start_time": "2025-07-10T01:25:20.708284Z"}}, "cell_type": "code", "source": ["from langchain.prompts.example_selector import LengthBasedExampleSelector\n", "\n", "example_selector = LengthBasedExampleSelector(\n", "    examples=examples,\n", "    example_prompt=example_prompt,\n", "    max_length=10,\n", ")\n", "\n", "example_selector_prompt = FewShotPromptTemplate(\n", "    example_selector=example_selector,\n", "    example_prompt=example_prompt,\n", "    example_separator=\"\\n\",\n", "    prefix=\"来玩反义词接龙游戏，我说词语，你给我一个反义词。\\n\",\n", "    suffix=\"现在轮到你了，词语: {input}\\n反义词: \",\n", "    input_variables=[\"input\"],\n", ")\n", "example_selector_prompt.format(input=\"黑色\")"], "id": "f931fb74a35a9e60", "outputs": [{"data": {"text/plain": ["'来玩反义词接龙游戏，我说词语，你给我一个反义词。\\n\\n词语: 兴高采烈\\n反义词: 闷闷不乐\\n\\n现在轮到你了，词语: 黑色\\n反义词: '"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["### __示例选择器__\n", "\n", "当少样本示例数量较多时，我们不希望将所有示例都包含在提示词中，因为这可能会超出模型的上下文窗口限制或增加成本。 LangChain 提供了多种“示例选择器”来动态选择最相关的示例。"], "id": "a01994572cdba2ac"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-09T09:26:15.969026Z", "start_time": "2025-07-09T09:26:15.354026Z"}}, "cell_type": "code", "source": ["from langchain_ollama import OllamaEmbeddings\n", "from langchain_core.prompts import PromptTemplate, FewShotPromptTemplate\n", "from langchain_community.vectorstores import Chroma\n", "from langchain.prompts.example_selector import (\n", "    LengthBasedExampleSelector,\n", "    MaxMarginalRelevanceExampleSelector,\n", "    NGramOverlapExampleSelector,\n", "    SemanticSimilarityExampleSelector\n", ")\n", "\n", "# 修正示例模板变量名，与示例数据一致\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"word\", \"antonym\"],\n", "    template=\"词语: {word}\\n反义词: {antonym}\\n\"\n", ")\n", "\n", "# 定义示例\n", "examples = [\n", "    {\"word\": \"高兴\", \"antonym\": \"悲伤\"},\n", "    {\"word\": \"快乐\", \"antonym\": \"悲伤\"},\n", "    {\"word\": \"大\", \"antonym\": \"小\"},\n", "    {\"word\": \"巨大\", \"antonym\": \"渺小\"},\n", "    {\"word\": \"快\", \"antonym\": \"慢\"},\n", "    {\"word\": \"迅速\", \"antonym\": \"迟缓\"},\n", "]\n", "\n", "# 初始化 Ollama 嵌入模型\n", "# 确保你的 Ollama 服务已运行，并且 \"nomic-embed-text:v1.5\" 模型已下载\n", "ollama_embeddings = OllamaEmbeddings(model=\"nomic-embed-text:v1.5\")\n", "\n", "# 1. 长度限制选择器 (LengthBasedExampleSelector)\n", "# 这个选择器主要用于控制提示词的总长度，而不是基于语义相关性。\n", "example_selector_lb = LengthBasedExampleSelector(\n", "    examples=examples,\n", "    example_prompt=example_prompt,\n", "    max_length=50, # 增大阈值以确保能选择到示例\n", ")\n", "\n", "# 2. 最大边际相关性选择器 (MaxMarginalRelevanceExampleSelector - MMR)\n", "# MMR 选择器旨在挑选与输入相关且彼此之间具有多样性的示例。\n", "example_selector_mmr = MaxMarginalRelevanceExampleSelector.from_examples(\n", "    examples=examples,\n", "    embeddings=ollama_embeddings,\n", "    vectorstore_cls=Chroma, # 指定向量存储类\n", "    k=2, # 选择2个最相关且多样化的示例\n", "    fetch_k=3 # 先检索3个候选，再从中选出最具多样性的2个\n", ")\n", "\n", "# 3. N-Gram 重叠选择器 (NGramOverlapExampleSelector)\n", "# 这个选择器根据输入和示例之间共享的 N-Gram 数量来选择示例。\n", "example_selector_ngram = NGramOverlapExampleSelector(\n", "    examples=examples,\n", "    example_prompt=example_prompt,\n", "    threshold=0.3, # 相似度阈值\n", "    k=2 # 选择最相似的2个示例\n", ")\n", "\n", "# 4. 语义相似度选择器 (SemanticSimilarityExampleSelector)\n", "# 这通常是选择相关示例最有效的方法。首先，从你的示例中创建向量存储。\n", "texts = [example[\"word\"] for example in examples]\n", "metadatas = examples\n", "\n", "vectorstore = Chroma.from_texts(\n", "    texts=texts,\n", "    embedding=ollama_embeddings,\n", "    metadatas=metadatas,\n", "    collection_name=\"antonym-examples\" # 为 Chroma 集合命名\n", ")\n", "\n", "example_selector_ss = SemanticSimilarityExampleSelector(\n", "    vectorstore=vectorstore,\n", "    k=2 # 选择2个语义上与查询最相似的示例\n", ")\n", "\n", "# **重要提示:** # 鉴于你遇到的 `CombinedExampleSelector` 导入错误，以及 LangChain 的最新实践，\n", "# 我们将直接使用 **语义相似度选择器** 来构建少样本提示词。\n", "# 语义相似度通常是处理这种任务的最佳选择。\n", "# 如果未来 LangChain 提供了新的组合选择器机制，你可以再进行调整。\n", "\n", "# 5. 使用语义相似度选择器构建少样本模板\n", "few_shot_prompt = FewShotPromptTemplate(\n", "    example_selector=example_selector_ngram, # 直接使用语义相似度选择器\n", "    example_prompt=example_prompt,\n", "    prefix=\"请为以下词语生成反义词：\",\n", "    suffix=\"词语: {input}\\n反义词:\",\n", "    input_variables=[\"input\"]\n", ")\n", "\n", "# 测试少样本提示词的效果\n", "print(few_shot_prompt.format(input=\"快速\"))"], "id": "f2c01ea2d717274e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["请为以下词语生成反义词：\n", "\n", "词语: 快\n", "反义词: 慢\n", "\n", "\n", "词语: 快\n", "反义词: 慢\n", "\n", "\n", "词语: 快速\n", "反义词:\n"]}], "execution_count": 44}, {"metadata": {}, "cell_type": "markdown", "source": ["### __Partial 提示词模板：预填充与复用__\n", "\n", "`partial()` 方法允许你预先填充提示词模板中的一部分变量，生成一个新的、参数更少的模板，便于复用。"], "id": "1ada0cc23d6f0472"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-09T09:43:46.157182Z", "start_time": "2025-07-09T09:43:46.151508Z"}}, "cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# 原始的通用营销文案模板，需要产品和功能两个变量\n", "general_template = \"请为 {product} 的 {feature} 功能写一个简短的营销文案。\"\n", "general_prompt = PromptTemplate.from_template(general_template)\n", "\n", "print(\"--- 原始模板格式化示例 ---\")\n", "print(general_prompt.format(product=\"智能手机\", feature=\"长续航电池\"))\n", "print(\"-\" * 30+\"\\n\")\n", "\n", "# 使用 partial() 方法，预先固定产品为“智能手机”\n", "# partial_prompt 现在只需要“feature”一个变量\n", "partial_prompt = general_prompt.partial(product=\"智能手机\")\n", "\n", "print(\"--- 部分填充模板格式化示例（产品已固定为智能手机）---\")\n", "print(partial_prompt.format(feature=\"超广角摄像头\"))\n", "print(partial_prompt.format(feature=\"快速充电\"))\n", "print(\"-\" * 30+\"\\n\")\n", "\n", "# 💡 提示：你也可以在实例化 PromptTemplate 时，就通过 `partial_variables` 参数进行部分填充\n", "template_with_partial_vars = PromptTemplate(\n", "    template=\"请为 {product} 的 {feature} 功能写一个简短的营销文案。\",\n", "    input_variables=[\"product\", \"feature\"],\n", "    partial_variables={\"product\": \"智能手表\"} # 直接在初始化时预填充\n", ")\n", "\n", "print(\"--- 初始化时使用 partial_variables 示例（产品已固定为智能手表）---\")\n", "print(template_with_partial_vars.format(feature=\"心率监测\"))\n", "print(\"-\" * 30+\"\\n\")\n", "\n", "# 观察模板输入变量的变化：\n", "print(f\"原始模板的输入变量: {general_prompt.input_variables}\")\n", "print(f\"部分填充模板的输入变量: {partial_prompt.input_variables}\")\n", "print(f\"实例化时带 partial_variables 的模板输入变量: {template_with_partial_vars.input_variables}\")\n"], "id": "a12ae9759efcd085", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 原始模板格式化示例 ---\n", "请为 智能手机 的 长续航电池 功能写一个简短的营销文案。\n", "------------------------------\n", "\n", "--- 部分填充模板格式化示例（产品已固定为智能手机）---\n", "请为 智能手机 的 超广角摄像头 功能写一个简短的营销文案。\n", "请为 智能手机 的 快速充电 功能写一个简短的营销文案。\n", "------------------------------\n", "\n", "--- 初始化时使用 partial_variables 示例（产品已固定为智能手表）---\n", "请为 智能手表 的 心率监测 功能写一个简短的营销文案。\n", "------------------------------\n", "\n", "原始模板的输入变量: ['feature', 'product']\n", "部分填充模板的输入变量: ['feature']\n", "实例化时带 partial_variables 的模板输入变量: ['feature']\n"]}], "execution_count": 48}, {"metadata": {}, "cell_type": "markdown", "source": ["### __PipelinePrompt 组合模板：LCEL 的强大流水线__\n", "\n", "LangChain 表达式语言 (LCEL) 允许你将多个组件（如提示词、LLM、输出解析器、自定义函数）组合成一个可运行的链条（pipeline）。"], "id": "e2ab2b8c722a192e"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T01:56:03.793517Z", "start_time": "2025-07-10T01:54:40.772775Z"}}, "cell_type": "code", "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import ChatOllama\n", "from langchain_core.output_parsers import StrOutputParser # 导入字符串输出解析器\n", "from langchain_core.runnables import RunnablePassthrough,RunnableLambda\n", "from operator import itemgetter # 用于方便地从字典中提取值\n", "\n", "# 确保你的 Ollama 服务已启动，并下载了 相关 模型\n", "# 例如: o<PERSON><PERSON> pull llama2\n", "\n", "# 步骤 1: 定义第一个提示词模板 - 用于生成一个问题\n", "question_prompt = PromptTemplate.from_template(\n", "    \"根据以下主题，请生成一个富有深度的问题：\\n\\n主题: {topic}\\n\\n问题:\"\n", ")\n", "\n", "# 步骤 2: 定义 LLM 模型实例\n", "llm = ChatOllama(model=\"gemma3:12b\")\n", "\n", "# 步骤 3: 定义输出解析器，将 LLM 的输出直接解析为字符串\n", "parser = StrOutputParser()\n", "\n", "# 步骤 4: 定义第二个提示词模板 - 用于回答上一步生成的问题\n", "answer_prompt = PromptTemplate.from_template(\n", "    \"请详细回答以下问题：\\n\\n问题: {question}\\n\\n答案:\"\n", ")\n", "\n", "# 步骤 5: 构建强大的 LCEL 组合链\n", "# 这个链将完成以下步骤：\n", "# 1. 接收一个包含 'topic' 的字典作为初始输入。\n", "# 2. **生成问题阶段**:\n", "#    - `question_prompt`: 使用初始输入的 'topic' 格式化第一个提示词。\n", "#    - `llm`: 调用 LLM 来生成问题。\n", "#    - `parser`: 将 LLM 的输出（即生成的问题）解析为字符串。\n", "# 3. **准备回答阶段**:\n", "#    - `{ \"question\": ..., \"topic\": ... }`: 这里是 LCEL 的精髓。\n", "#      `RunnablePassthrough.assign(...)` 用于在链的当前上下文中添加或修改键值对。\n", "#      我们通过 `question_generator` 链生成了 `question`，并使用 `RunnablePassthrough()` 将原始的 `topic` 也传递下去。\n", "#      这样，后续的 `answer_prompt` 就能同时访问到 `question` 和 `topic`（尽管 `answer_prompt` 只需要 `question`）。\n", "# 4. **生成答案阶段**:\n", "#    - `answer_prompt`: 使用上一步得到的 'question' 格式化第二个提示词。\n", "#    - `llm`: 调用 LLM 来生成答案。\n", "#    - `parser`: 将最终答案解析为字符串。\n", "\n", "# 创建一个子链来生成问题\n", "question_generator = question_prompt | llm | parser\n", "# 创建完整的组合链\n", "# 注意：这里的字典 `{ \"question\": question_generator, \"topic\": RunnablePassthrough() }`\n", "# 表示：\n", "# - \"question\" 这个键的值由 `question_generator` 子链的输出决定。\n", "# - \"topic\" 这个键的值由原始输入（`RunnablePassthrough()` 表示传递原始输入）决定。\n", "# 这样，`answer_prompt` 就可以从这个复合字典中获取它需要的 `question`。\n", "full_pipeline_chain = (\n", "    {\n", "        \"question\": question_generator, # 调用子链生成问题\n", "        # 写法1：使用itemgetter（更简洁）\n", "        \"topic\": itemgetter(\"topic\")\n", "        # # 写法2：使用RunnableLambda（更直观）\n", "        # \"topic\": RunnableLambda(lambda x: x[\"topic\"])\n", "    }\n", "    # 使用 RunnableLambda 添加调试打印\n", "    | RunnableLambda(lambda x: (print(f\"生成关于{x['topic']}的问题: {x['question']}\\n\\n\"), x)[1])\n", "    | answer_prompt # 使用第二个提示词模板\n", "    | llm         # 再次调用 LLM 生成答案\n", "    | parser      # 解析最终答案\n", ")\n", "\n", "print(\"--- LCEL 组合模板实践 (两阶段问答) ---\")\n", "# 运行组合链，提供初始主题\n", "result = full_pipeline_chain.invoke({\"topic\": \"量子计算\"})\n", "print(f\"关于“量子计算”的问答结果:\\n{result}\")\n", "print(\"-\" * 30)\n", "\n", "# 💡 另一个更简单的 LCEL 链式组合示例：\n", "# 提示词 -> LLM -> 解析器\n", "simple_chain = PromptTemplate.from_template(\"请给我一个关于{subject}的冷笑话。\") | llm | parser\n", "\n", "print(\"--- 简单链式组合示例 ---\")\n", "joke = simple_chain.invoke({\"subject\": \"程序员\"})\n", "print(f\"关于“程序员”的冷笑话:\\n{joke}\")\n", "print(\"-\" * 30)\n"], "id": "ea8aa3d5d2458c6d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- LCEL 组合模板实践 (两阶段问答) ---\n", "生成关于量子计算的问题: 好的，以下是一个围绕量子计算主题，并力求具有深度的问题：\n", "\n", "**问题：**\n", "\n", "量子计算的潜力在于超越经典计算在特定问题上的能力。然而，量子算法的开发仍处于起步阶段，并且目前找到适用于广泛问题的量子优势仍然具有挑战性。考虑到量子计算的硬件和软件发展面临的现实限制（例如：量子比特的退相干、错误纠正的复杂性、可扩展性等），您认为未来五年内，量子计算在哪个特定领域或应用场景（例如：药物发现、材料科学、金融建模等）最有可能实现突破性进展，并能证明其在实际问题解决上的显著价值？请详细阐述您的理由，并讨论这种突破性进展可能带来的潜在影响及面临的伦理或社会风险。\n", "\n", "**这个问题为什么“有深度”？**\n", "\n", "*   **结合了技术、应用和未来预测：** 它不只是问量子计算是什么，而是要求预测未来的发展趋势。\n", "*   **需要批判性思维：** 要求评估当前量子计算的局限性，并对潜在的应用场景进行评估，不只是简单的列举。\n", "*   **涉及多维度思考：** 需要考虑技术可行性、潜在影响（经济、科学、社会）以及伦理风险。\n", "*   **需要具体且有依据：** 不能只是泛泛而谈，需要选择一个特定领域并给出支持其突破性进展的理由。\n", "*   **鼓励深入研究：** 问题的解答需要对量子计算的现状，不同应用领域的现状，以及潜在的挑战进行深入的了解。\n", "\n", "希望这个问题能够激发深入的思考和讨论！\n", "\n", "\n", "关于“量子计算”的问答结果:\n", "好的，这是一个非常深刻的问题，需要对量子计算的现状、潜在应用以及面临的挑战进行综合考虑。下面我将详细阐述我对未来五年内量子计算最有可能实现突破性进展的领域，并讨论其潜在影响和风险。\n", "\n", "**我认为，未来五年内量子计算最有可能在“材料科学，特别是能源材料设计”领域实现突破性进展，并能证明其在实际问题解决上的显著价值。**\n", "\n", "**理由：**\n", "\n", "1.  **量子化学模拟是天然的优势：** 材料的性质很大程度上取决于其电子结构。量子化学模拟，特别是计算分子和固体的基态能量和激发态，是量子计算的天然优势领域。经典计算在处理大型分子和复杂材料时会面临指数级的计算量问题，导致模拟精度和规模受限。量子计算，特别是变分量子本征求解器(VQE)和量子相位估计(QPE)等算法，有望在量子比特数量和精度达到一定程度后，能够精确模拟更大的分子和固体体系，从而揭示其内在机制和预测其性质。\n", "    *   **特别强调能源材料：**  能源材料（如太阳能电池、燃料电池、新型电池材料、电解质等）的性能优化往往需要精细地控制其电子结构和化学反应。经典模拟难以准确预测这些材料的行为，而量子计算有望解决这一瓶颈。\n", "2.  **硬件的针对性发展：**\n", "    *   **量子化学计算的硬件优化：** 目前已经有针对量子化学计算优化的量子计算硬件平台出现。例如，一些量子计算机架构，如基于超导量子比特的架构，在设计时就考虑了量子化学计算的需求，针对特定的化学算符进行了优化。\n", "    *   **低噪声量子比特的开发：** 能源材料模拟对量子比特的保真度要求较高，低噪声量子比特的开发是关键。目前在超导、离子阱和光子量子比特领域，都在积极探索降低噪声的方法。\n", "3.  **算法的成熟与加速：** VQE和QPE等量子化学算法在近年来的发展迅速，不断有新的变体出现，在精度和效率上都有所提升。同时，对经典算法与量子算法的混合（Hybrid Classical-Quantum Approach）正在变得越来越重要，在有限的量子资源下，能够最大化计算结果的质量。\n", "4.  **与其他技术的融合：**  量子计算与机器学习 (尤其是用于数据驱动材料发现的机器学习) 的融合，可以加速材料发现的流程。  例如，利用量子计算进行初步筛选，再利用机器学习对数据进行分析和预测，可以有效地缩短材料研发周期。\n", "5.  **已有的实验验证：** 已经有一些早期的实验验证表明，量子计算在模拟某些简单的分子体系时，能够取得比经典计算更准确的结果。 虽然这些成果还很有限，但它们预示着量子计算在材料科学领域的巨大潜力。\n", "\n", "**突破性进展可能带来的潜在影响：**\n", "\n", "*   **新型能源材料的发现和设计：**  量子计算有望加速新型太阳能电池材料、高效燃料电池电解质、长寿命和高能量密度的电池材料的设计和发现，从而推动能源转型。\n", "*   **材料性能的优化：**  通过精确的量子模拟，可以深入理解材料的内在机制，从而优化材料的性能，例如提高太阳能电池的光电转换效率，降低电池内阻等。\n", "*   **降低研发成本和周期：**  量子计算可以加速材料研发流程，减少实验次数和成本，缩短材料推向市场的周期。\n", "*   **促进新产业的诞生：**  量子计算在材料科学领域的突破，将催生新的产业，例如量子材料设计服务、量子材料模拟软件等。\n", "\n", "**面临的伦理或社会风险：**\n", "\n", "*   **加剧资源竞争：**  如果量子计算能够加速新型能源材料的开发，可能会加剧对稀有资源的竞争，例如锂、钴等。\n", "*   **潜在的安全风险：**  如果量子计算能够设计出具有特殊功能的材料，可能会被用于制造具有潜在安全风险的武器或设备。例如，高性能电池材料可能被用于武器装备。\n", "*   **就业结构变化：**  量子计算的普及可能会导致一些传统材料科学领域的工作岗位减少，需要进行就业结构的调整。\n", "*   **算法滥用：** 量子算法如果被用于材料合成等过程，可能会带来环境污染或安全隐患。\n", "\n", "**为什么我没有选择其他领域？**\n", "\n", "*   **药物发现：** 虽然药物发现是另一个重要的量子计算应用领域，但其复杂性极高，涉及蛋白质折叠、分子动力学等多个方面，量子计算的突破需要更长的时间。\n", "*   **金融建模：** 金融建模对计算精度要求极高，而且经典计算已经比较成熟，量子计算的优势不明显。\n", "*   **人工智能：** 量子人工智能目前还处于早期研究阶段，其应用前景尚不明确。\n", "*   **密码学：** 虽然量子计算机对现有加密算法构成威胁，但量子密码学也在发展，而且其影响需要更长的时间才能显现。\n", "\n", "**总结：**\n", "\n", "虽然量子计算的发展面临诸多挑战，但其在材料科学，尤其是能源材料设计领域，展现出巨大的潜力。 未来五年内，随着量子硬件和算法的不断发展，以及与其他技术的融合，量子计算有望在这一领域取得突破性进展，为能源转型和社会发展带来深远影响。 然而，我们也需要关注其潜在的伦理和安全风险，并采取措施加以防范，确保量子计算为人类带来福祉。\n", "------------------------------\n", "--- 简单链式组合示例 ---\n", "关于“程序员”的冷笑话:\n", "为什么程序员总是把万圣节和圣诞节混淆？\n", "\n", "因为 Oct 31 == Dec 25! （八进制的31等于十进制的25）\n", "\n", "😂\n", "\n", "------------------------------\n"]}], "execution_count": 9}, {"metadata": {}, "cell_type": "markdown", "source": ["### __模板序列化：持久化与共享__\n", "\n", "LangChain 的提示词模板可以被序列化（保存）到文件或字符串，也可以从文件或字符串反序列化（加载），这使得模板可以方便地进行持久化、共享和版本控制。"], "id": "6710d434ea40aa90"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T02:13:37.430827Z", "start_time": "2025-07-10T02:13:37.409550Z"}}, "cell_type": "code", "source": ["import os\n", "import json\n", "from langchain_core.prompts import (\n", "    PromptTemplate,\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    SystemMessagePromptTemplate\n", ")\n", "from langchain_core.load import dumpd, load  # 新的序列化工具\n", "\n", "# --- PromptTemplate 的序列化与反序列化 ---\n", "simple_prompt = PromptTemplate.from_template(\"你好，我的名字是 {name}，我住在 {city}。\")\n", "\n", "# 序列化为字典（可进一步转为 JSON 字符串）\n", "prompt_dict = dumpd(simple_prompt)  # 替代 to_json_string()\n", "json_string = json.dumps(prompt_dict, ensure_ascii=False, indent=2)\n", "print(\"--- PromptTemplate 序列化为 JSON 字符串 ---\")\n", "print(json_string)\n", "print(\"-\" * 30)\n", "\n", "# 从 JSON 字符串反序列化\n", "loaded_dict = json.loads(json_string)\n", "loaded_prompt = load(loaded_dict)  # 替代 from_json_string()\n", "print(\"--- 从 JSON 字符串反序列化后的 PromptTemplate ---\")\n", "print(loaded_prompt.format(name=\"张三\", city=\"北京\"))\n", "print(\"-\" * 30)\n", "\n", "# --- ChatPromptTemplate 的序列化与反序列化 ---\n", "chat_prompt = ChatPromptTemplate.from_messages([\n", "    SystemMessagePromptTemplate.from_template(\"你是一个友好的助手，擅长回答关于 {topic} 的问题。\"),\n", "    HumanMessagePromptTemplate.from_template(\"请问 {query}？\")\n", "])\n", "\n", "# 序列化为 JSON 字符串\n", "chat_dict = dumpd(chat_prompt)\n", "chat_json_string = json.dumps(chat_dict, ensure_ascii=False, indent=2)\n", "print(\"--- ChatPromptTemplate 序列化为 JSON 字符串 ---\")\n", "print(chat_json_string)\n", "print(\"-\" * 30)\n", "\n", "# 从 JSON 字符串反序列化\n", "loaded_chat_dict = json.loads(chat_json_string)\n", "loaded_chat_prompt = load(loaded_chat_dict)\n", "print(\"--- 从 JSON 字符串反序列化后的 ChatPromptTemplate ---\")\n", "formatted_messages = loaded_chat_prompt.format_messages(topic=\"历史\", query=\"秦始皇是谁\")\n", "for msg in formatted_messages:\n", "    print(f\"{msg.type}: {msg.content}\")\n", "print(\"-\" * 30)\n", "\n", "# --- 模板保存到文件和从文件加载 ---\n", "file_path = \"my_prompt_template.json\"\n", "\n", "# 保存到文件（直接用 dumpd + json.dump）\n", "with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(dumpd(simple_prompt), f, ensure_ascii=False, indent=2)\n", "print(f\"PromptTemplate 已保存到文件: {file_path}\")\n", "\n", "# 从文件加载\n", "with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "    loaded_from_file = load(json.load(f))\n", "print(f\"PromptTemplate 已从文件加载: {file_path}\")\n", "print(loaded_from_file.format(name=\"李四\", city=\"上海\"))\n", "print(\"-\" * 30)\n", "\n", "# 清理文件\n", "if os.path.exists(file_path):\n", "    os.remove(file_path)\n", "    print(f\"已删除文件: {file_path}\")"], "id": "9d1ce8fa12252f33", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- PromptTemplate 序列化为 JSON 字符串 ---\n", "{\n", "  \"lc\": 1,\n", "  \"type\": \"constructor\",\n", "  \"id\": [\n", "    \"langchain\",\n", "    \"prompts\",\n", "    \"prompt\",\n", "    \"PromptTemplate\"\n", "  ],\n", "  \"kwargs\": {\n", "    \"input_variables\": [\n", "      \"city\",\n", "      \"name\"\n", "    ],\n", "    \"template\": \"你好，我的名字是 {name}，我住在 {city}。\",\n", "    \"template_format\": \"f-string\"\n", "  },\n", "  \"name\": \"PromptTemplate\"\n", "}\n", "------------------------------\n", "--- 从 JSON 字符串反序列化后的 PromptTemplate ---\n", "你好，我的名字是 张三，我住在 北京。\n", "------------------------------\n", "--- ChatPromptTemplate 序列化为 JSON 字符串 ---\n", "{\n", "  \"lc\": 1,\n", "  \"type\": \"constructor\",\n", "  \"id\": [\n", "    \"langchain\",\n", "    \"prompts\",\n", "    \"chat\",\n", "    \"ChatPromptTemplate\"\n", "  ],\n", "  \"kwargs\": {\n", "    \"input_variables\": [\n", "      \"query\",\n", "      \"topic\"\n", "    ],\n", "    \"messages\": [\n", "      {\n", "        \"lc\": 1,\n", "        \"type\": \"constructor\",\n", "        \"id\": [\n", "          \"langchain\",\n", "          \"prompts\",\n", "          \"chat\",\n", "          \"SystemMessagePromptTemplate\"\n", "        ],\n", "        \"kwargs\": {\n", "          \"prompt\": {\n", "            \"lc\": 1,\n", "            \"type\": \"constructor\",\n", "            \"id\": [\n", "              \"langchain\",\n", "              \"prompts\",\n", "              \"prompt\",\n", "              \"PromptTemplate\"\n", "            ],\n", "            \"kwargs\": {\n", "              \"input_variables\": [\n", "                \"topic\"\n", "              ],\n", "              \"template\": \"你是一个友好的助手，擅长回答关于 {topic} 的问题。\",\n", "              \"template_format\": \"f-string\"\n", "            },\n", "            \"name\": \"PromptTemplate\"\n", "          }\n", "        }\n", "      },\n", "      {\n", "        \"lc\": 1,\n", "        \"type\": \"constructor\",\n", "        \"id\": [\n", "          \"langchain\",\n", "          \"prompts\",\n", "          \"chat\",\n", "          \"HumanMessagePromptTemplate\"\n", "        ],\n", "        \"kwargs\": {\n", "          \"prompt\": {\n", "            \"lc\": 1,\n", "            \"type\": \"constructor\",\n", "            \"id\": [\n", "              \"langchain\",\n", "              \"prompts\",\n", "              \"prompt\",\n", "              \"PromptTemplate\"\n", "            ],\n", "            \"kwargs\": {\n", "              \"input_variables\": [\n", "                \"query\"\n", "              ],\n", "              \"template\": \"请问 {query}？\",\n", "              \"template_format\": \"f-string\"\n", "            },\n", "            \"name\": \"PromptTemplate\"\n", "          }\n", "        }\n", "      }\n", "    ]\n", "  },\n", "  \"name\": \"ChatPromptTemplate\"\n", "}\n", "------------------------------\n", "--- 从 JSON 字符串反序列化后的 ChatPromptTemplate ---\n", "system: 你是一个友好的助手，擅长回答关于 历史 的问题。\n", "human: 请问 秦始皇是谁？\n", "------------------------------\n", "PromptTemplate 已保存到文件: my_prompt_template.json\n", "PromptTemplate 已从文件加载: my_prompt_template.json\n", "你好，我的名字是 李四，我住在 上海。\n", "------------------------------\n", "已删除文件: my_prompt_template.json\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25036\\1217352481.py:23: Lang<PERSON><PERSON>nBetaWarning: The function `load` is in beta. It is actively being worked on, so the API may change.\n", "  loaded_prompt = load(loaded_dict)  # 替代 from_json_string()\n"]}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": ["### __动态特征注入：上下文的魔法__\n", "\n", "在实际应用中，提示词往往需要根据用户的实时信息、历史数据或外部API调用结果等动态特征进行填充，这被称为“动态特征注入”。"], "id": "1d61278c0b13c3c"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:14:28.910402Z", "start_time": "2025-07-10T03:13:14.801706Z"}}, "cell_type": "code", "source": ["import datetime\n", "from langchain_core.runnables import RunnableLambda\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import ChatOllama\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "# 1. 动态特征1：获取当前时间（精确到小时）\n", "def get_current_time():\n", "    return datetime.datetime.now().strftime(\"%Y年%m月%d日 %H:%M\")\n", "\n", "# 2. 动态特征2：获取用户年龄（模拟从用户系统中读取）\n", "def get_user_age(user_id):\n", "    # 实际场景中可能从数据库/用户信息中读取\n", "    user_info = {\"user_001\": 28, \"user_002\": 65}  # 模拟用户数据\n", "    return user_info.get(user_id, 30)  # 默认30岁\n", "\n", "# 3. 动态特征3：获取用户历史浏览类别（模拟从历史记录中读取）\n", "def get_history_categories(user_id):\n", "    history = {\"user_001\": [\"科技\", \"体育\"], \"user_002\": [\"养生\", \"戏曲\"]}  # 模拟历史数据\n", "    return history.get(user_id, [\"综合\"])\n", "\n", "# 定义基础模板，包含动态特征占位符\n", "news_prompt_template = \"\"\"\n", "请为用户推荐3条新闻，要求：\n", "1. 结合用户年龄{user_age}岁，推荐适合该年龄段的内容；\n", "2. 参考用户历史浏览类别：{history_categories}；\n", "3. 提示当前时间：{current_time}；\n", "4. 语言风格贴合用户年龄，结尾加一句互动提问。\n", "\n", "推荐格式：\n", "1. [标题]：内容简介\n", "2. [标题]：内容简介\n", "3. [标题]：内容简介\n", "\"\"\"\n", "news_prompt = PromptTemplate(\n", "    template=news_prompt_template,\n", "    input_variables=[\"user_age\", \"history_categories\", \"current_time\"]\n", ")\n", "\n", "# 定义LLM（用Ollama的模型为例）\n", "llm = ChatOllama(model=\"gemma3:12b\")\n", "\n", "# 构建动态特征注入链\n", "# 输入：用户ID（如{\"user_id\": \"user_001\"}）\n", "dynamic_chain = (\n", "    # 第一步：从输入（user_id）中提取动态特征，组装成字典\n", "    RunnablePassthrough.assign(\n", "        user_age=lambda x: get_user_age(x[\"user_id\"]),  # 注入用户年龄\n", "        history_categories=lambda x: get_history_categories(x[\"user_id\"]),  # 注入历史类别\n", "        current_time=lambda x: get_current_time()  # 注入当前时间\n", "    )\n", "    # 第二步：将动态特征传入Prompt模板\n", "    | news_prompt\n", "    # 第三步：调用LLM生成结果\n", "    | llm\n", ")\n", "\n", "# 测试：给不同用户ID生成推荐\n", "print(\"--- 用户user_001（28岁，浏览科技/体育）的推荐 ---\")\n", "result1 = dynamic_chain.invoke({\"user_id\": \"user_001\"})\n", "print(result1.content)\n", "\n", "print(\"\\n--- 用户user_002（65岁，浏览养生/戏曲）的推荐 ---\")\n", "result2 = dynamic_chain.invoke({\"user_id\": \"user_002\"})\n", "print(result2.content)"], "id": "989014f816d6dd70", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 用户user_001（28岁，浏览科技/体育）的推荐 ---\n", "Alright，收到！考虑到你28岁，喜欢科技和体育，现在是2025年07月10日 11:13，我为你准备了这三条新闻，保证有趣有料：\n", "\n", "1. **Meta发布XR眼镜“Horizon Leap”：未来社交将是沉浸式体验？**：Meta终于亮出了他们的下一代XR眼镜——Horizon Leap。这玩意儿不仅仅是看电影，而是可以让你“身临其境”参与虚拟活动，和朋友在虚拟世界里一起嗨。技术细节爆棚，据说能大幅提升沉浸感和交互性，这次Meta是真要搞事情？看看未来的社交会变成什么样，这绝对值得关注！\n", "\n", "2. **足坛新星“风暴”：21岁葡萄牙天才阿尔瓦雷斯横空出世！**：世界杯预选赛上，一位名叫阿尔瓦雷斯的葡萄牙天才横空出世，凭借超强的爆发力和精准的射门，直接带队反超！他的比赛视频已经刷爆社交媒体，各大豪门球队都在疯狂扫荡。想重温一下姆巴佩的惊艳时刻？这小子绝对值得关注！\n", "\n", "3. **智能家居新趋势：AI助手“Aurora” 接入健康数据，打造个性化生活模式！**：除了控制灯光和温度，现在的智能家居可玩性更高了！Aurora智能助手，最近接入了健康数据，可以根据你的睡眠、饮食习惯，自动调节室内环境，甚至给出运动建议。简直就是为你量身定制的生活管家！科技改变生活的速度，真是不停加速啊！\n", "\n", "怎么样？这几条新闻你感兴趣吗？你觉得智能家居的未来发展方向是什么？在评论区说说你的看法吧！\n", "\n", "\n", "--- 用户user_002（65岁，浏览养生/戏曲）的推荐 ---\n", "您好，值此2025年7月10日上午11点14分，为您准备了三条可能感兴趣的新闻，都是考虑到您喜欢养生和戏曲，希望您喜欢：\n", "\n", "1. **【中老年健康指南】夏季养生：防暑降温，健体怡情**：夏季酷暑难耐，这篇报道详细介绍了适合中老年人预防中暑、降燥健体的食疗方、运动方式和生活小窍门。特别是针对容易出现的高血压、心脏病等常见老年人群体，特别提醒了注意事项。 照顾好身体，才能更好地享受晚年生活啊！\n", "\n", "2. **【戏曲新锐】“梅派第三代”演员崭露头角，传承经典与创新并行**：您喜欢戏曲，这篇报道讲的是一位年轻的梅派演员如何在继承传统的基础上进行创新，给梅派戏曲注入了新的活力。文章里有图片和视频，能感受到年轻一代对戏曲的传承和发展热情，看着也很舒服。\n", "\n", "3. **【养生小贴士】“食补”虽好，更要讲究“量”与“时”**： 您对养生关注很多，这篇报道提醒大家在食补的时候，要注意补益的剂量和时间。 补得太猛反而会伤身，有些药材更适合在特定的时间服用。 希望这篇报道能给您的养生之路带来一些新的启发。\n", "\n", "您觉得这几条新闻怎么样呢？有没有您觉得特别有意思的，也欢迎和我分享一下！\n", "\n"]}], "execution_count": 13}, {"metadata": {}, "cell_type": "markdown", "source": ["### __注入历史对话特征__\n", "\n", "在多轮对话中，将历史对话记录作为动态特征注入提示词，可以帮助模型理解上下文，并给出连贯的回复。"], "id": "2baa7392ebd8f0e1"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:19:25.724693Z", "start_time": "2025-07-10T03:19:20.806048Z"}}, "cell_type": "code", "source": ["# 注入历史对话特征\n", "def get_conversation_history(user_id):\n", "    # 模拟历史对话（实际可从数据库读取）\n", "    history = {\n", "        \"user_001\": [\"用户问：最近有什么新手机？\", \"助手答：iPhone 15刚发布...\"]\n", "    }\n", "    return \"\\n\".join(history.get(user_id, []))\n", "\n", "# 多轮对话动态链\n", "multi_turn_chain = (\n", "    RunnablePassthrough.assign(\n", "        history=lambda x: get_conversation_history(x[\"user_id\"]),  # 注入历史对话\n", "        current_time=lambda x: get_current_time()\n", "    )\n", "    | PromptTemplate.from_template(\"\"\"\n", "        基于历史对话：\n", "        {history}\n", "        当前时间：{current_time}\n", "        请回应用户最新问题：{user_question}\n", "    \"\"\")\n", "    | llm\n", ")\n", "\n", "# 测试：结合历史对话回答新问题\n", "result = multi_turn_chain.invoke({\n", "    \"user_id\": \"user_001\",\n", "    \"user_question\": \"那它的续航怎么样？\"  # 承接历史中“新手机”的问题\n", "})\n", "print(result.content)  # 会基于历史对话（iPhone 15）回答续航，而非重新开始"], "id": "f7aab0d2894f3ef6", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["iPhone 15 发布已经有一段时间了。关于它的续航，普遍反馈是相比前代有所提升，但具体表现会因使用情况而异。 整体来说，正常使用情况下可以满足一天的使用，如果重度使用，可能需要一天多次充电。 你对iPhone 15 的续航还有什么具体想了解的吗？ 比如你主要的使用场景是什么？\n", "\n"]}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["### __模板验证：确保输入的正确性__\n", "\n", "在将用户输入填充到提示词模板之前进行验证至关重要，这可以确保数据的完整性、正确性和安全性，避免模型生成错误或不相关的输出。"], "id": "154591b2352ce40e"}, {"metadata": {}, "cell_type": "markdown", "source": ["#### 1. 基础验证：必填变量检查\n", "\n", "确保输入包含模板所需的所有 `input_variables`（如 `name`、`age`、`email` 缺一不可）。"], "id": "291bf8158a1ee75a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:27:18.579411Z", "start_time": "2025-07-10T03:27:18.525747Z"}}, "cell_type": "code", "source": ["from langchain_core.runnables import RunnableLambda\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_ollama import ChatOllama\n", "\n", "# 定义模板\n", "prompt = PromptTemplate(\n", "    template=\"用户信息：姓名{name}，年龄{age}，邮箱{email}\",\n", "    input_variables=[\"name\", \"age\", \"email\"]\n", ")\n", "\n", "# 1. 验证：检查必填变量是否齐全\n", "def validate_required_vars(input_data):\n", "    required = prompt.input_variables\n", "    missing = [var for var in required if var not in input_data]\n", "    if missing:\n", "        raise ValueError(f\"缺少必填变量：{missing}，请补充后重试\")\n", "    return input_data  # 验证通过，返回原始数据\n", "\n", "# 2. 构建包含验证步骤的链\n", "validation_chain = (\n", "    RunnableLambda(validate_required_vars)  # 第一步：验证\n", "    | prompt  # 第二步：渲染模板\n", "    | ChatOllama(model=\"gemma3:12b\")  # 第三步：调用LLM\n", ")\n", "\n", "# 测试：\n", "try:\n", "    # 缺少email，预期触发错误\n", "    validation_chain.invoke({\"name\": \"张三\", \"age\": 30})\n", "except ValueError as e:\n", "    print(\"验证失败：\", e)  # 输出：缺少必填变量：['email']，请补充后重试"], "id": "91328fed505e7ce4", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["验证失败： 缺少必填变量：['email']，请补充后重试\n"]}], "execution_count": 16}, {"metadata": {}, "cell_type": "markdown", "source": ["#### 2. 类型验证：确保变量类型正确\n", "例如：`age`必须是整数，`score`必须是浮点数等。"], "id": "7c19040acdd0a90e"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:27:58.759827Z", "start_time": "2025-07-10T03:27:58.707095Z"}}, "cell_type": "code", "source": ["def validate_types(input_data):\n", "    # 检查age是否为整数\n", "    if not isinstance(input_data[\"age\"], int):\n", "        raise TypeError(f\"age必须是整数，当前类型：{type(input_data['age'])}\")\n", "    # 检查email是否为字符串\n", "    if not isinstance(input_data[\"email\"], str):\n", "        raise TypeError(f\"email必须是字符串，当前类型：{type(input_data['email'])}\")\n", "    return input_data\n", "\n", "# 组合验证链（先检查必填，再检查类型）\n", "validation_chain = (\n", "    RunnableLambda(validate_required_vars)\n", "    | RunnableLambda(validate_types)\n", "    | prompt\n", "    | ChatOllama(model=\"gemma3:12b\")\n", ")\n", "\n", "# 测试：\n", "try:\n", "    # age是字符串，预期触发错误\n", "    validation_chain.invoke({\"name\": \"张三\", \"age\": \"三十\", \"email\": \"zhang<PERSON>@test.com\"})\n", "except TypeError as e:\n", "    print(\"验证失败：\", e)  # 输出：age必须是整数，当前类型：<class 'str'>"], "id": "8322b0cc38bd2204", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["验证失败： age必须是整数，当前类型：<class 'str'>\n"]}], "execution_count": 17}, {"metadata": {}, "cell_type": "markdown", "source": ["#### 3. 范围验证：确保变量值在合理区间\n", "例如：`age应在` 0-120 之间，`email`需包含@符号。"], "id": "eda1adcdea5d7bec"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:28:41.137193Z", "start_time": "2025-07-10T03:28:41.072432Z"}}, "cell_type": "code", "source": ["import re\n", "\n", "def validate_ranges(input_data):\n", "    # 验证age范围\n", "    if not (0 <= input_data[\"age\"] <= 120):\n", "        raise ValueError(f\"age必须在0-120之间，当前值：{input_data['age']}\")\n", "    # 验证email格式（简单正则）\n", "    if not re.match(r\".+@.+\\..+\", input_data[\"email\"]):\n", "        raise ValueError(f\"email格式无效，当前值：{input_data['email']}\")\n", "    return input_data\n", "\n", "# 完善验证链（必填 → 类型 → 范围）\n", "validation_chain = (\n", "    RunnableLambda(validate_required_vars)\n", "    | RunnableLambda(validate_types)\n", "    | RunnableLambda(validate_ranges)\n", "    | prompt\n", "    | ChatOllama(model=\"gemma3:12b\")\n", ")\n", "\n", "# 测试：\n", "try:\n", "    # email缺少@，预期触发错误\n", "    validation_chain.invoke({\"name\": \"张三\", \"age\": 30, \"email\": \"zhangsantest.com\"})\n", "except ValueError as e:\n", "    print(\"验证失败：\", e)  # 输出：email格式无效，当前值：zhangsantest.com"], "id": "27a70966ad5de38b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["验证失败： email格式无效，当前值：zhangsantest.com\n"]}], "execution_count": 18}, {"metadata": {}, "cell_type": "markdown", "source": "", "id": "183e503eb5c22878"}, {"metadata": {}, "cell_type": "markdown", "source": ["#### 4. 自动修正：对轻微异常值进行修复\n", "对于可修复的输入（如`age`是字符串 `\"30\"`，可转为整数），可以自动修正而非直接报错。"], "id": "c9ff2ab5e0ebf28c"}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-10T03:29:33.579897Z", "start_time": "2025-07-10T03:29:29.240920Z"}}, "cell_type": "code", "source": ["def auto_fix_input(input_data):\n", "    # 尝试将age从字符串转为整数（如\"30\" → 30）\n", "    if isinstance(input_data[\"age\"], str) and input_data[\"age\"].isdigit():\n", "        input_data[\"age\"] = int(input_data[\"age\"])\n", "        print(f\"自动修正：age从字符串转为整数 {input_data['age']}\")\n", "    # 尝试补全email中的@（简单逻辑）\n", "    if \"@\" not in input_data[\"email\"] and \".\" in input_data[\"email\"]:\n", "        input_data[\"email\"] = input_data[\"email\"].replace(\".\", \"@\", 1)\n", "        print(f\"自动修正：email补全@ → {input_data['email']}\")\n", "    return input_data\n", "\n", "# 最终验证链（自动修正 → 必填 → 类型 → 范围）\n", "final_chain = (\n", "    RunnableLambda(auto_fix_input)  # 先尝试自动修正\n", "    | RunnableLambda(validate_required_vars)\n", "    | RunnableLambda(validate_types)\n", "    | RunnableLambda(validate_ranges)\n", "    | prompt\n", "    | ChatOllama(model=\"gemma3:12b\")\n", ")\n", "\n", "# 测试：输入存在可修复的问题\n", "result = final_chain.invoke({\n", "    \"name\": \"张三\",\n", "    \"age\": \"30\",  # 字符串\"30\"，可转为整数\n", "    \"email\": \"zhangsan.test.com\"  # 缺少@，可修正为*****************\n", "})\n", "print(\"\\n最终渲染结果：\")\n", "print(result.content)"], "id": "c8a6fd90efe9367f", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["自动修正：age从字符串转为整数 30\n", "自动修正：email补全@ → <EMAIL>\n", "\n", "最终渲染结果：\n", "好的，已记录用户信息：\n", "\n", "*   姓名：张三\n", "*   年龄：30\n", "*   邮箱：zhang<PERSON>@test.com\n", "\n", "有什么需要我做的吗？\n", "\n"]}], "execution_count": 19}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}